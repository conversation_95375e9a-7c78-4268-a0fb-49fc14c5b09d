"use client";
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import UserProfile from '../../../components/userProfile/page';

export default function CurrentUserProfilePage() {
  const router = useRouter();
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);

  useEffect(() => {
    // Check for authentication token
    const checkAuth = () => {
      const token = localStorage.getItem('token') ||
        localStorage.getItem('jwt_token') ||
        localStorage.getItem('wedzat_token');

      if (!token) {
        console.warn('No authentication token found, redirecting to login');
        router.push('/');
        setIsAuthenticated(false);
        return;
      }

      // Try to extract user ID from JWT token
      try {
        const tokenParts = token.split('.');
        if (tokenParts.length === 3) {
          // It's a JWT, decode the payload
          const payload = JSON.parse(atob(tokenParts[1]));
          
          if (payload.user_id) {
            setCurrentUserId(payload.user_id);
          } else {
            // If no user_id in token, use a placeholder
            setCurrentUserId('current_user_id');
          }
        } else {
          // Not a JWT, use a placeholder
          setCurrentUserId('current_user_id');
        }
      } catch (err) {
        console.error('Error extracting user info from token:', err);
        setCurrentUserId('current_user_id');
      }

      setIsAuthenticated(true);
    };

    checkAuth();

    // Add event listener for storage changes (e.g., token deletion)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'token' || e.key === 'jwt_token' || e.key === 'wedzat_token') {
        if (!e.newValue) {
          console.warn('Token removed, redirecting to login');
          router.push('/');
          setIsAuthenticated(false);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [router]);

  // Show loading state while checking authentication
  if (isAuthenticated === null) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-600"></div>
      </div>
    );
  }

  // Only render the profile if authenticated
  return isAuthenticated ? (
    <div>
      <UserProfile />
    </div>
  ) : null;
}
