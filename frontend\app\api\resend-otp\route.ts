// frontend/app/api/resend-otp/route.ts
import { cookies } from 'next/headers';
import { generateOTP } from '../../../lib/otp';
import { sendOtpEmail } from '../../../lib/nodemailer';
import { NextResponse } from 'next/server';

interface ResponseData {
  success: boolean;
  message: string;
  useFirebase?: boolean;
  devOtp?: string; // Only for development
}

export async function POST(request: Request): Promise<NextResponse<ResponseData>> {
  try {
    const verifyMethod = (await cookies()).get('verifyMethod')?.value;
    const email = (await cookies()).get('verifyEmail')?.value;
    const phone = (await cookies()).get('verifyPhone')?.value;
    const useFirebase = (await cookies()).get('useFirebase')?.value === 'true';
    
    if (!verifyMethod || (!email && !phone)) {
      return NextResponse.json({ 
        success: false, 
        message: "No verification in progress" 
      }, { status: 400 });
    }
    
    // For phone verification with Firebase
    if (verifyMethod === 'phone' && useFirebase) {
      // With Firebase, we just tell the client to resend through Firebase
      return NextResponse.json({ 
        success: true, 
        message: "Please use the resend button in the verification form",
        useFirebase: true
      });
    }
    
    // For email verification with Nodemailer - same as before
    // Generate a new OTP - 6 digits for email
    const newOTP: string = generateOTP(6);
    
    // Update OTP in cookie
    (await cookies()).set({
      name: 'pendingOtp',
      value: newOTP,
      httpOnly: true,
      maxAge: 600, // 10 minutes
      path: '/',
      sameSite: 'strict',
    });
    
    let sent = false;
    
    // Email verification via Nodemailer
    if (verifyMethod === 'email' && email) {
      try {
        await sendOtpEmail(email, newOTP);
        sent = true;
        console.log(`Email verification code resent to ${email}`);
      } catch (error) {
        console.error('Failed to resend email verification:', error);
        
        // For development - fallback to console logging
        if (process.env.NODE_ENV === 'development') {
          console.log(`DEV MODE - New Email OTP for ${email}: ${newOTP}`);
          sent = true; // Pretend it was sent for development
        }
      }
    }
    
    // Check if resend was successful
    if (!sent && process.env.NODE_ENV !== 'development') {
      return NextResponse.json({ 
        success: false, 
        message: "Failed to resend verification code" 
      }, { status: 500 });
    }
    
    // For development mode, always return success and include OTP for testing
    if (process.env.NODE_ENV === 'development') {
      return NextResponse.json({ 
        success: true, 
        message: "Verification code resent successfully", 
        devOtp: newOTP
      });
    }
    
    return NextResponse.json({ 
      success: true, 
      message: "Verification code resent successfully" 
    });
  } catch (error) {
    console.error('Error in resend-otp:', error);
    return NextResponse.json({ 
      success: false, 
      message: "Internal server error" 
    }, { status: 500 });
  }
}