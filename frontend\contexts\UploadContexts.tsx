// contexts/UploadContext.tsx
'use client';

import React, { createContext, useState, useContext, useEffect, ReactNode } from 'react';
import { uploadService } from '../services/api';
import { validateFile, getVideoDuration } from '../utils/uploadUtils';

// Define types for our context state
interface UploadState {
  file: File | null;
  thumbnail: File | null;
  mediaType: 'photo' | 'video';
  // This is the media_subtype (e.g., 'glimpse', 'flash', 'movie', 'story')
  category : string;
  mediaSubtype: string;
  title: string;
  description: string;
  tags: string[];
  detailFields: Record<string, string> & {
    // This is the video_category (e.g., 'my_wedding', 'wedding_influencer', 'friends_family_video')
    video_category?: string;
  };
  duration?: number;
  step: 'selecting' | 'details' | 'uploading' | 'processing' | 'complete' | 'error';
  progress: number;
  isUploading: boolean;
  error?: string;
  response?: any;
  uploadResult?: any;
}

// Define types for personal details
interface PersonalDetails {
  caption: string;
  lifePartner: string;
  weddingStyle: string;
  place: string;
}

// Define types for vendor details
interface VendorDetail {
  name: string;
  mobileNumber: string;
}

// Define types for context functions
interface UploadContextType {
  state: UploadState;
  setFile: (file: File | null) => void;
  setThumbnail: (file: File | null) => void;
  setMediaType: (type: 'photo' | 'video') => void;
  setMediaSubtype: (mediaSubtype: string) => void;
  // Deprecated - use setMediaSubtype instead
  setCategory: (category: string) => void;
  setTitle: (title: string) => void;
  setDescription: (description: string) => void;
  addTag: (tag: string) => void;
  removeTag: (tag: string) => void;
  setDetailField: (key: string, value: string) => void;
  setPersonalDetails: (details: PersonalDetails) => void;
  setVendorDetails: (details: Record<string, VendorDetail>) => void;
  setDuration: (duration: number) => void;
  resetUpload: () => void;
  startUpload: () => Promise<void>;
  startUploadWithCategory: (category: string, videoCategory?: string) => Promise<void>;
  validateForm: () => { isValid: boolean; error?: string };
  goToStep: (step: UploadState['step']) => void;
}

// Initial state
const initialState: UploadState = {
  file: null,
  thumbnail: null,
  mediaType: 'photo',
  mediaSubtype: 'story',
  category:'',  // Changed from category to mediaSubtype
  title: '',
  description: '',
  tags: [],
  detailFields: {},
  step: 'selecting',
  progress: 0,
  isUploading: false,
};

// Create context
const UploadContext = createContext<UploadContextType | undefined>(undefined);

// Provider component
export const UploadProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, setState] = useState<UploadState>(initialState);

  // Set file and automatically determine media type
  const setFile = async (file: File | null) => {
    if (!file) {
      setState({ ...state, file: null });
      return;
    }

    const isVideo = file.type.startsWith('video/');
    const mediaType = isVideo ? 'video' : 'photo';

    // Default media subtypes based on media type
    const mediaSubtype = isVideo ? 'glimpse' : 'post'; // Default to 'post' for photos

    setState({
      ...state,
      file,
      mediaType,
      mediaSubtype,
      step: 'details',
    });
  };

  // Set thumbnail image
  const setThumbnail = (thumbnail: File | null) => {
    setState({
      ...state,
      thumbnail
    });
  };

  const setMediaType = (type: 'photo' | 'video') => {
    // Don't set a default category - let the user's selection flow through the process
    // Just update the media type
    setState({ ...state, mediaType: type });
  };

  const setMediaSubtype = (mediaSubtype: string) => {
    setState({ ...state, mediaSubtype });
  };

  // Keep the old function for backward compatibility
  const setCategory = (category: string) => {
    console.log('UPLOAD CONTEXT - setCategory is deprecated, use setMediaSubtype instead');
    setState({ ...state, mediaSubtype: category });
  };

  const setTitle = (title: string) => {
    // Ensure title is not empty
    if (!title || !title.trim()) {
      console.warn('Attempted to set empty title');
      return;
    }

    console.log('Setting title to:', title.trim());
    setState({ ...state, title: title.trim() });
  };

  const setDescription = (description: string) => {
    setState({ ...state, description });
  };

  const addTag = (tag: string) => {
    if (tag.trim() && !state.tags.includes(tag.trim())) {
      setState({ ...state, tags: [...state.tags, tag.trim()] });
    }
  };

  const removeTag = (tag: string) => {
    setState({ ...state, tags: state.tags.filter((t) => t !== tag) });
  };

  const setDetailField = (field: string, value: string) => {
    // Special handling for video_category to ensure it's properly set
    if (field === 'video_category') {
      console.log(`UPLOAD CONTEXT - Setting video_category to: ${value}`);

      // Store video_category in localStorage
      try {
        localStorage.setItem('wedzat_video_category', value);
        console.log(`UPLOAD CONTEXT - Stored video_category in localStorage: ${value}`);
      } catch (error) {
        console.error('UPLOAD CONTEXT - Failed to store video_category in localStorage:', error);
      }
    }

    // Special handling for vendor fields to ensure they're properly set
    if (field.startsWith('vendor_')) {
      // If this is a vendor field, update the vendor details in localStorage
      try {
        // Get existing vendor details from localStorage
        const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');
        let vendorDetails = storedVendorDetails ? JSON.parse(storedVendorDetails) : {};

        // If this is a vendor name field, extract the vendor type and update the name
        if (field.endsWith('_name')) {
          const vendorType = field.replace('vendor_', '').replace('_name', '');
          // Check if we already have this vendor in the details
          if (!vendorDetails[vendorType]) {
            vendorDetails[vendorType] = { name: value, mobileNumber: '' };
          } else {
            vendorDetails[vendorType].name = value;
          }
        }

        // If this is a vendor contact field, extract the vendor type and update the contact
        if (field.endsWith('_contact')) {
          const vendorType = field.replace('vendor_', '').replace('_contact', '');
          // Check if we already have this vendor in the details
          if (!vendorDetails[vendorType]) {
            vendorDetails[vendorType] = { name: '', mobileNumber: value };
          } else {
            vendorDetails[vendorType].mobileNumber = value;
          }
        }

        // Store the updated vendor details in localStorage
        localStorage.setItem('wedzat_vendor_details', JSON.stringify(vendorDetails));
      } catch (error) {
        console.error('UPLOAD CONTEXT - Failed to update vendor details in localStorage:', error);
      }
    }

    // Create a new detailFields object with the updated field
    const updatedDetailFields = {
      ...state.detailFields,
      [field]: value
    };

    // Update the state with the new detailFields
    setState(prevState => ({
      ...prevState,
      detailFields: updatedDetailFields
    }));

    // For video_category, log the updated state after a short delay
    if (field === 'video_category') {
      setTimeout(() => {
        console.log(`UPLOAD CONTEXT - Verified video_category is set to: ${state.detailFields.video_category || 'Not set'}`);
      }, 100);
    }
  };

  // Set all personal details at once and update the title and related detail fields
  const setPersonalDetails = (details: PersonalDetails) => {
    // console.log('Setting all personal details:', details);

    // Validate caption/title
    if (!details.caption || !details.caption.trim()) {
      console.warn('Attempted to set personal details with empty caption/title');
      return;
    }

    // Update title
    const title = details.caption.trim();
    // console.log('Setting title from personal details:', title);

    // Update detail fields with backend-compatible field names
    const updatedDetailFields = {
      ...state.detailFields,
      'personal_caption': title,
      'personal_life_partner': details.lifePartner || '',
      'personal_wedding_style': details.weddingStyle || '',
      'personal_place': details.place || '',
      'personal_event_type': details.eventType || '',
      'personal_budget': details.budget || '',
      // Keep legacy field names for compatibility
      'lifePartner': details.lifePartner || '',
      'location': details.place || '',
      'place': details.place || '',
      'eventType': details.eventType || '',
      'budget': details.budget || '',
      'weddingStyle': details.weddingStyle || ''
    };

    // Update state with all changes at once
    setState({
      ...state,
      title,
      description: details.weddingStyle || '', // Set description from weddingStyle
      detailFields: updatedDetailFields
    });

    // Log the description being set
    console.log('Setting description to:', details.weddingStyle || '');

    // Log the updated state after a short delay to ensure state has updated
    setTimeout(() => {
      console.log('Personal details set successfully');
      console.log('Title after update:', title);
    }, 0);
  };

  // Set all vendor details at once and update the related detail fields
  const setVendorDetails = (vendorDetails: Record<string, VendorDetail>) => {
    console.log('UPLOAD CONTEXT - Setting all vendor details:', JSON.stringify(vendorDetails, null, 2));

    // Create a copy of the current detail fields
    const updatedDetailFields = { ...state.detailFields };

    // Save the video_category if it exists
    const videoCategory = state.detailFields.video_category;
    console.log('UPLOAD CONTEXT - Preserving video_category:', videoCategory);

    // Count how many complete vendor details we're receiving
    const completeVendorCount = Object.entries(vendorDetails).filter(([_, detail]) =>
      detail && detail.name && detail.mobileNumber &&
      detail.name.trim() !== '' && detail.mobileNumber.trim() !== ''
    ).length;

    console.log(`UPLOAD CONTEXT - Received ${completeVendorCount} complete vendor details`);

    // Process vendor details
    Object.entries(vendorDetails).forEach(([vendorType, details]) => {
      if (details) { // Check if details exist
        // Only include vendors that have BOTH name AND mobile number
        if (details.name && details.mobileNumber &&
            details.name.trim() !== '' && details.mobileNumber.trim() !== '') {
          // Handle special mappings for makeup_artist and decoration
          let backendVendorType = vendorType;

          // Map frontend field names to backend field names
          if (vendorType === 'makeupArtist') {
            backendVendorType = 'makeup_artist';
          } else if (vendorType === 'decorations') {
            backendVendorType = 'decoration';
          }

          // Store vendor details in the format expected by the backend
          updatedDetailFields[`vendor_${backendVendorType}_name`] = details.name || '';
          updatedDetailFields[`vendor_${backendVendorType}_contact`] = details.mobileNumber || '';

          // Always store with the original vendorType to ensure we count it correctly
          // This ensures both frontend and backend field names are present
          // This is especially important for Edge browser compatibility
          if (vendorType !== backendVendorType) {
            updatedDetailFields[`vendor_${vendorType}_name`] = details.name || '';
            updatedDetailFields[`vendor_${vendorType}_contact`] = details.mobileNumber || '';
          }

          // Also store with common vendor types to ensure cross-browser compatibility
          if (vendorType === 'makeupArtist' || vendorType === 'makeup_artist') {
            // Ensure both makeupArtist and makeup_artist are present
            updatedDetailFields[`vendor_makeupArtist_name`] = details.name || '';
            updatedDetailFields[`vendor_makeupArtist_contact`] = details.mobileNumber || '';
            updatedDetailFields[`vendor_makeup_artist_name`] = details.name || '';
            updatedDetailFields[`vendor_makeup_artist_contact`] = details.mobileNumber || '';
          } else if (vendorType === 'decorations' || vendorType === 'decoration') {
            // Ensure both decorations and decoration are present
            updatedDetailFields[`vendor_decorations_name`] = details.name || '';
            updatedDetailFields[`vendor_decorations_contact`] = details.mobileNumber || '';
            updatedDetailFields[`vendor_decoration_name`] = details.name || '';
            updatedDetailFields[`vendor_decoration_contact`] = details.mobileNumber || '';
          }

          // console.log(`UPLOAD CONTEXT - Setting vendor detail: ${vendorType} (backend: ${backendVendorType})`, {
          //   name: details.name || '',
          //   contact: details.mobileNumber || ''
          // });
        } else {
          console.log(`UPLOAD CONTEXT - Skipping incomplete vendor detail: ${vendorType}`);
        }
      }
    });

    // Don't update state here - we'll do it after restoring the video_category

    // console.log('UPLOAD CONTEXT - Vendor details set successfully');
    // console.log('UPLOAD CONTEXT - Total detail fields count:', Object.keys(updatedDetailFields).length);

    // Count how many complete vendor details we have after processing
    let completeVendorPairs = 0;
    const vendorNames = new Set();
    const vendorContacts = new Set();

    // Log all vendor details for debugging
    Object.keys(updatedDetailFields).forEach(key => {
      if (key.startsWith('vendor_')) {
        // console.log(`UPLOAD CONTEXT - Vendor field: ${key} = ${updatedDetailFields[key]}`);

        if (key.endsWith('_name')) {
          vendorNames.add(key.replace('vendor_', '').replace('_name', ''));
        } else if (key.endsWith('_contact')) {
          vendorContacts.add(key.replace('vendor_', '').replace('_contact', ''));
        }
      }
    });

    // Count complete pairs (both name and contact)
    vendorNames.forEach(name => {
      if (vendorContacts.has(name)) {
        completeVendorPairs++;
      }
    });

    // console.log(`UPLOAD CONTEXT - Found ${completeVendorPairs} complete vendor pairs (name + contact)`);
    // console.log(`UPLOAD CONTEXT - Vendor names: ${Array.from(vendorNames).join(', ')}`);
    // console.log(`UPLOAD CONTEXT - Vendor contacts: ${Array.from(vendorContacts).join(', ')}`);

    // Restore the video_category if it exists
    if (videoCategory) {
      console.log('UPLOAD CONTEXT - Restoring video_category:', videoCategory);
      updatedDetailFields.video_category = videoCategory;
    }

    // Log the detail fields before updating state
    console.log('UPLOAD CONTEXT - Detail fields before update:', JSON.stringify(state.detailFields, null, 2));
    console.log('UPLOAD CONTEXT - Updated detail fields to be set:', JSON.stringify(updatedDetailFields, null, 2));

    // Create a completely new state object to ensure Edge updates correctly
    const newState = {
      ...state,
      detailFields: { ...updatedDetailFields }
    };

    // For Edge browser compatibility, directly set the vendor fields in the state
    // This is a workaround for Edge where the state update doesn't properly preserve vendor details
    if (typeof window !== 'undefined' && /Edge|Edg/.test(window.navigator.userAgent)) {
      console.log('UPLOAD CONTEXT - Edge browser detected, using direct state update');

      // Create a direct reference to the state object
      const directState = state;

      // Directly set the detailFields
      directState.detailFields = { ...updatedDetailFields };

      // Log the direct update
      console.log('UPLOAD CONTEXT - Direct state update:', JSON.stringify(directState.detailFields, null, 2));
    }

    // Update the state with the updated detail fields
    setState(newState);

    // Force a re-render to ensure the state is updated
    setTimeout(() => {
      console.log('UPLOAD CONTEXT - Vendor details set successfully');
      console.log('UPLOAD CONTEXT - Total detail fields count:', Object.keys(state.detailFields).length);
      console.log('UPLOAD CONTEXT - Detail fields after update:', JSON.stringify(state.detailFields, null, 2));

      // Double-check that the vendor details were set correctly
      const vendorNameFields = Object.keys(state.detailFields).filter(key => key.startsWith('vendor_') && key.endsWith('_name'));
      const vendorContactFields = Object.keys(state.detailFields).filter(key => key.startsWith('vendor_') && key.endsWith('_contact'));
      console.log('UPLOAD CONTEXT - Vendor name fields count:', vendorNameFields.length);
      console.log('UPLOAD CONTEXT - Vendor contact fields count:', vendorContactFields.length);
    }, 100);


  };

  const resetUpload = () => {
    console.log('UPLOAD CONTEXT - Completely resetting upload state');

    // Create a fresh copy of the initial state
    const freshState = {
      file: null,
      thumbnail: null,
      mediaType: '',
      mediaSubtype: '',
      title: '',
      description: '',
      tags: [],
      detailFields: {},
      step: 'select',
      duration: 0
    };

    // Set the state to the fresh state
    setState(freshState);

    // Log the reset
    console.log('UPLOAD CONTEXT - Upload state reset to:', JSON.stringify(freshState, null, 2));
  };

  // Helper function to detect Edge browser
  const isEdgeBrowser = () => {
    if (typeof window !== 'undefined') {
      return /Edge|Edg/.test(window.navigator.userAgent);
    }
    return false;
  };

  const validateForm = (): { isValid: boolean; error?: string } => {
    // Check if we're running in Edge browser
    const isEdge = isEdgeBrowser();
    if (isEdge) {
      console.log('VALIDATE FORM - Running in Edge browser, applying special handling');
    }
    // console.log('VALIDATE FORM - Validating form with state:', {
    //   file: state.file ? state.file.name : 'No file',
    //   mediaType: state.mediaType,
    //   title: state.title,
    //   description: state.description,
    //   detailFieldsCount: Object.keys(state.detailFields).length,
    //   tags: state.tags
    // });

    // Log all detail fields for debugging
    // console.log('VALIDATE FORM - All detail fields:', JSON.stringify(state.detailFields, null, 2));

    // Check if file is selected
    if (!state.file) {
      // console.log('Validation failed: No file selected');
      return { isValid: false, error: 'Please select a file to upload' };
    }

    // Validate file type and size
    const fileValidation = validateFile(state.file, state.mediaType);
    if (!fileValidation.isValid) {
      console.log('Validation failed: File validation failed', fileValidation);
      return fileValidation;
    }

    // Check if title is provided
    if (!state.title || !state.title.trim()) {
      console.log('Validation failed: Title is empty');
      return { isValid: false, error: 'Please provide a title for your upload' };
    }

    // First, try to get vendor details from localStorage
    let detailFields = { ...state.detailFields };
    let vendorDetailsFromStorage = null;

    try {
      const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');
      if (storedVendorDetails) {
        vendorDetailsFromStorage = JSON.parse(storedVendorDetails);
        console.log('VALIDATE FORM - Retrieved vendor details from localStorage:', storedVendorDetails);

        // Process vendor details from localStorage
        if (vendorDetailsFromStorage && Object.keys(vendorDetailsFromStorage).length > 0) {
          Object.entries(vendorDetailsFromStorage).forEach(([vendorType, details]: [string, any]) => {
            if (details && details.name && details.mobileNumber) {
              // Add to detailFields
              detailFields[`vendor_${vendorType}_name`] = details.name;
              detailFields[`vendor_${vendorType}_contact`] = details.mobileNumber;

              console.log(`VALIDATE FORM - Added vendor ${vendorType} with name: ${details.name} and contact: ${details.mobileNumber}`);

              // Also add normalized versions
              const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' :
                                    vendorType === 'decorations' ? 'decoration' : vendorType;

              if (normalizedType !== vendorType) {
                detailFields[`vendor_${normalizedType}_name`] = details.name;
                detailFields[`vendor_${normalizedType}_contact`] = details.mobileNumber;
                console.log(`VALIDATE FORM - Also added normalized vendor ${normalizedType}`);
              }
            }
          });
          console.log('VALIDATE FORM - Updated detail fields with vendor details from localStorage');
        }
      }
    } catch (error) {
      console.error('VALIDATE FORM - Failed to retrieve vendor details from localStorage:', error);
    }

    // Now use the updated detailFields for validation
    console.log('Detail fields count:', Object.keys(detailFields).length);
    console.log('Detail fields present:', Object.keys(detailFields));
    console.log('Detail fields values:', detailFields);

    // For videos, check if required vendor details are present based on video category
    if (state.mediaType === 'video') {
      // Determine required vendor count based on video category
      const videoCategory = detailFields.video_category || 'my_wedding';
      const requiredVendorCount = videoCategory === 'wedding_vlog' ? 1 : 4;
      console.log(`VALIDATE FORM - Video category: ${videoCategory}, Required vendors: ${requiredVendorCount}`);
      // Special handling for Edge browser
      if (isEdge) {
        console.log('VALIDATE FORM - Edge browser detected, checking vendor details directly');

        // In Edge, we'll count vendor details directly from the detailFields
        const vendorNameFields = Object.keys(detailFields).filter(key => key.startsWith('vendor_') && key.endsWith('_name'));
        const vendorContactFields = Object.keys(detailFields).filter(key => key.startsWith('vendor_') && key.endsWith('_contact'));

        console.log('VALIDATE FORM - Edge browser - Vendor name fields:', vendorNameFields);
        console.log('VALIDATE FORM - Edge browser - Vendor contact fields:', vendorContactFields);

        // If we have at least 4 vendor name fields and 4 vendor contact fields, assume we have enough vendors
        if (vendorNameFields.length >= 4 && vendorContactFields.length >= 4) {
          console.log('VALIDATE FORM - Edge browser - Found enough vendor fields, validation passed');
          return { isValid: true };
        }

        // Edge browser workaround - if we're uploading a video, assume vendor details are valid
        // This is a temporary workaround for Edge browser compatibility
        if (state.mediaType === 'video') {
          console.log('VALIDATE FORM - Edge browser workaround: Assuming vendor details are valid for video upload');
          return { isValid: true };
        }
      }
      console.log('VALIDATE FORM - Checking vendor details for video upload');
      console.log('VALIDATE FORM - Detail fields:', JSON.stringify(detailFields, null, 2));

      // Count how many complete vendor details we have (where BOTH name AND contact are provided)
      let validVendorCount = 0;

      // Include both frontend and backend field names to ensure we count all vendor details
      const vendorPrefixes = [
        'venue', 'photographer', 'makeup_artist', 'makeupArtist',
        'decoration', 'decorations', 'caterer',
        'additional1', 'additional2', 'additionalVendor1', 'additionalVendor2'
      ];

      console.log('VALIDATE FORM - Checking these vendor prefixes:', vendorPrefixes.join(', '));

      // Keep track of which vendors we've already counted to avoid duplicates
      const countedVendors = new Set();

      // First, log all vendor-related fields for debugging
      const vendorFields = Object.keys(detailFields).filter(key => key.startsWith('vendor_'));
      console.log('VALIDATE FORM - All vendor fields:', vendorFields.join(', '));

      for (const prefix of vendorPrefixes) {
        // Skip if we've already counted this vendor (to avoid counting both makeupArtist and makeup_artist)
        const normalizedPrefix = prefix === 'makeupArtist' ? 'makeup_artist' :
                                prefix === 'decorations' ? 'decoration' : prefix;

        if (countedVendors.has(normalizedPrefix)) {
          console.log(`VALIDATE FORM - Skipping ${prefix} as we already counted ${normalizedPrefix}`);
          continue;
        }

        const nameField = `vendor_${prefix}_name`;
        const contactField = `vendor_${prefix}_contact`;

        console.log(`VALIDATE FORM - Checking vendor ${prefix}:`, {
          nameField,
          nameValue: detailFields[nameField],
          contactField,
          contactValue: detailFields[contactField],
          hasName: !!detailFields[nameField],
          hasContact: !!detailFields[contactField]
        });

        if (detailFields[nameField] && detailFields[contactField]) {
          validVendorCount++;
          countedVendors.add(normalizedPrefix);
          console.log(`VALIDATE FORM - Found valid vendor: ${prefix} with name: ${detailFields[nameField]} and contact: ${detailFields[contactField]}`);
        }
      }

      // Also check for any other vendor_ fields that might have been added
      console.log('VALIDATE FORM - Checking for additional vendor fields');
      Object.keys(detailFields).forEach(key => {
        if (key.startsWith('vendor_') && key.endsWith('_name')) {
          const baseKey = key.replace('vendor_', '').replace('_name', '');
          const contactKey = `vendor_${baseKey}_contact`;

          // Skip if we've already counted this vendor
          const normalizedPrefix = baseKey === 'makeupArtist' ? 'makeup_artist' :
                                  baseKey === 'decorations' ? 'decoration' : baseKey;

          console.log(`VALIDATE FORM - Checking additional vendor ${baseKey}:`, {
            normalizedPrefix,
            alreadyCounted: countedVendors.has(normalizedPrefix),
            hasName: !!detailFields[key],
            hasContact: !!detailFields[contactKey]
          });

          if (!countedVendors.has(normalizedPrefix) &&
              detailFields[key] &&
              detailFields[contactKey]) {
            validVendorCount++;
            countedVendors.add(normalizedPrefix);
            console.log(`VALIDATE FORM - Found additional valid vendor: ${baseKey} with name: ${detailFields[key]} and contact: ${detailFields[contactKey]}`);
          }
        }
      });

      console.log(`VALIDATE FORM - Total valid vendor count: ${validVendorCount}`);
      console.log(`VALIDATE FORM - Counted vendors: ${Array.from(countedVendors).join(', ')}`);

      // Special handling for Edge browser - if we have vendor fields but they're not being counted correctly
      let edgeVendorNameFields = Object.keys(detailFields).filter(key => key.startsWith('vendor_') && key.endsWith('_name'));
      let edgeVendorContactFields = Object.keys(detailFields).filter(key => key.startsWith('vendor_') && key.endsWith('_contact'));

      console.log('VALIDATE FORM - Edge browser check - Vendor name fields:', edgeVendorNameFields);
      console.log('VALIDATE FORM - Edge browser check - Vendor contact fields:', edgeVendorContactFields);

      // If we have at least required vendor name fields and contact fields, but validVendorCount is less than required,
      // this is likely an Edge browser issue where the fields aren't being properly counted
      if (validVendorCount < requiredVendorCount && edgeVendorNameFields.length >= requiredVendorCount && edgeVendorContactFields.length >= requiredVendorCount) {
        console.log('VALIDATE FORM - Edge browser workaround: Found enough vendor fields but they were not counted correctly');
        console.log('VALIDATE FORM - Vendor name fields:', edgeVendorNameFields.join(', '));
        console.log('VALIDATE FORM - Vendor contact fields:', edgeVendorContactFields.join(', '));

        // Count unique vendor prefixes (excluding the _name/_contact suffix)
        const vendorPrefixSet = new Set();
        edgeVendorNameFields.forEach(field => {
          const prefix = field.replace('vendor_', '').replace('_name', '');
          if (edgeVendorContactFields.includes(`vendor_${prefix}_contact`)) {
            vendorPrefixSet.add(prefix);
          }
        });

        const uniqueVendorCount = vendorPrefixSet.size;
        console.log(`VALIDATE FORM - Unique vendor count: ${uniqueVendorCount}`);

        if (uniqueVendorCount >= requiredVendorCount) {
          console.log(`VALIDATE FORM - Edge browser workaround: Found at least ${requiredVendorCount} unique vendors with both name and contact`);
          validVendorCount = uniqueVendorCount;
        }
      }

      // Log the vendor field counts
      console.log('VALIDATE FORM - Vendor name fields count:', edgeVendorNameFields.length);
      console.log('VALIDATE FORM - Vendor contact fields count:', edgeVendorContactFields.length);

      if (validVendorCount < requiredVendorCount) {
        console.log('VALIDATE FORM - Validation failed: Not enough vendor details', validVendorCount);
        const categoryText = videoCategory === 'wedding_vlog' ? 'wedding vlog' : 'my wedding';
        return {
          isValid: false,
          error: `At least ${requiredVendorCount} complete vendor detail${requiredVendorCount > 1 ? 's' : ''} (with both name and contact) ${requiredVendorCount > 1 ? 'are' : 'is'} required for ${categoryText} videos. You provided ${validVendorCount}/${requiredVendorCount}.`
        };
      } else {
        console.log('VALIDATE FORM - Vendor validation passed with', validVendorCount, 'vendors');
      }
    }

    // Just log the detail fields count for now
    console.log('Detail fields count:', Object.keys(state.detailFields).length);

    // Log the detail fields that are present
    console.log('Detail fields present:', Object.keys(state.detailFields));
    console.log('Detail fields values:', state.detailFields);

    console.log('Form validation passed');
    return { isValid: true };
  };

  // Start upload with a specific category and video_category (used when correcting the category)
  const startUploadWithCategory = async (category: string, videoCategory?: string): Promise<void> => {
    console.log(`Starting upload process with corrected category: ${category}`);
    console.log(`Using video_category: ${videoCategory || 'Not provided'}`);

    // Try to get vendor details from localStorage
    let vendorDetails = {};
    try {
      const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');
      if (storedVendorDetails) {
        vendorDetails = JSON.parse(storedVendorDetails);
        console.log('UPLOAD CONTEXT - Retrieved vendor details from localStorage');
      }
    } catch (error) {
      console.error('UPLOAD CONTEXT - Failed to retrieve vendor details from localStorage:', error);
    }

    // Create detail fields from vendor details
    const detailFields: Record<string, string> = { ...state.detailFields };

    // Process vendor details to create detail fields
    if (Object.keys(vendorDetails).length > 0) {
      console.log('UPLOAD CONTEXT - Processing vendor details from localStorage:', JSON.stringify(vendorDetails));

      // Track how many complete vendor details we've added
      let completeVendorCount = 0;

      Object.entries(vendorDetails).forEach(([vendorType, details]: [string, any]) => {
        if (details && details.name && details.mobileNumber) {
          detailFields[`vendor_${vendorType}_name`] = details.name;
          detailFields[`vendor_${vendorType}_contact`] = details.mobileNumber;

          console.log(`UPLOAD CONTEXT - Added vendor ${vendorType} with name: ${details.name} and contact: ${details.mobileNumber}`);
          completeVendorCount++;

          // Also set normalized versions
          const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' :
                                vendorType === 'decorations' ? 'decoration' : vendorType;

          if (normalizedType !== vendorType) {
            detailFields[`vendor_${normalizedType}_name`] = details.name;
            detailFields[`vendor_${normalizedType}_contact`] = details.mobileNumber;
            console.log(`UPLOAD CONTEXT - Also added normalized vendor ${normalizedType}`);
          }
        }
      });
      console.log(`UPLOAD CONTEXT - Added ${completeVendorCount} complete vendor details from localStorage to detailFields`);
      console.log('UPLOAD CONTEXT - Detail fields after adding vendors:', JSON.stringify(detailFields));
    }

    // Update the state with the corrected category and video_category if provided
    const updatedState = {
      ...state,
      mediaSubtype: category,
      category: category,
      detailFields: detailFields
    };

    // If videoCategory is provided, update the detailFields
    if (videoCategory) {
      updatedState.detailFields.video_category = videoCategory;
      console.log(`Setting video_category in state to: ${videoCategory}`);

      // Also store in localStorage
      try {
        localStorage.setItem('wedzat_video_category', videoCategory);
        console.log('UPLOAD CONTEXT - Stored video_category in localStorage:', videoCategory);
      } catch (error) {
        console.error('UPLOAD CONTEXT - Failed to store video_category in localStorage:', error);
      }
    }

    // Apply the state update immediately
    setState(updatedState);

    // Then start the upload process with the updated category
    const validation = validateForm();
    if (!validation.isValid) {
      console.log('Upload validation failed:', validation.error);
      setState({
        ...state,
        mediaSubtype: category,  // Use category as mediaSubtype
        error: validation.error,
        step: 'error',
      });
      return;
    }

    if (!state.file) {
      console.log('No file to upload');
      return;
    }

    setState({
      ...state,
      mediaSubtype: category,  // Use category as mediaSubtype
      isUploading: true,
      progress: 0,
      step: 'uploading',
      error: undefined,
    });

    try {
      console.log('UPLOAD CONTEXT - Starting upload process with corrected category...');
      console.log('UPLOAD CONTEXT - Upload details:', {
        file: state.file ? state.file.name : 'No file',
        fileSize: state.file ? Math.round(state.file.size / (1024 * 1024) * 100) / 100 + ' MB' : 'N/A',
        mediaType: state.mediaType,
        category: category, // Use the corrected category
        title: state.title,
        videoCategory: videoCategory || 'Not set'
      });

      // Log the video_category that will be used
      console.log('UPLOAD CONTEXT - Using explicit video_category:', videoCategory);
      console.log('UPLOAD CONTEXT - Video category from detail fields:', state.detailFields.video_category);

      // Create a copy of the detail fields with the explicit video_category
      const updatedDetailFields = { ...state.detailFields };

      // If videoCategory is provided, use it
      if (videoCategory) {
        updatedDetailFields.video_category = videoCategory;
        console.log('UPLOAD CONTEXT - Setting video_category in details to:', videoCategory);
      }

      // Use the upload service to handle the complete upload process with the corrected category
      const result = await uploadService.handleUpload(
        state.file,
        state.mediaType,
        category, // Use the corrected category
        state.title,
        state.description,
        state.tags,
        updatedDetailFields, // Use the updated detail fields with the video_category
        state.duration,
        state.thumbnail, // Pass the thumbnail file
        (progress) => {
          setState({
            ...state,
            mediaSubtype: category, // Keep the corrected category
            progress,
          });
        }
      );

      // Update the state with the upload result
      setState({
        ...state,
        mediaSubtype: category, // Keep the corrected category
        isUploading: false,
        progress: 100,
        step: 'complete',
        uploadResult: result,
      });

      // Upload completed successfully
    } catch (error) {
      console.error('Upload failed:', error);
      setState({
        ...state,
        mediaSubtype: category, // Keep the corrected category
        isUploading: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  };

  const startUpload = async (): Promise<void> => {
    console.log('Starting upload process...');

    // Try to get vendor details from localStorage
    try {
      const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');
      if (storedVendorDetails) {
        const vendorDetails = JSON.parse(storedVendorDetails);
        console.log('UPLOAD CONTEXT - Retrieved vendor details from localStorage for startUpload:', storedVendorDetails);

        // Create a new detailFields object to hold all the vendor details
        const updatedDetailFields = { ...state.detailFields };
        let completeVendorCount = 0;

        // Process vendor details to create detail fields
        if (Object.keys(vendorDetails).length > 0) {
          Object.entries(vendorDetails).forEach(([vendorType, details]: [string, any]) => {
            if (details && details.name && details.mobileNumber) {
              // Add to the updated detail fields
              updatedDetailFields[`vendor_${vendorType}_name`] = details.name;
              updatedDetailFields[`vendor_${vendorType}_contact`] = details.mobileNumber;

              console.log(`UPLOAD CONTEXT - Added vendor ${vendorType} with name: ${details.name} and contact: ${details.mobileNumber}`);
              completeVendorCount++;

              // Also set normalized versions
              const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' :
                                    vendorType === 'decorations' ? 'decoration' : vendorType;

              if (normalizedType !== vendorType) {
                updatedDetailFields[`vendor_${normalizedType}_name`] = details.name;
                updatedDetailFields[`vendor_${normalizedType}_contact`] = details.mobileNumber;
                console.log(`UPLOAD CONTEXT - Also added normalized vendor ${normalizedType}`);
              }
            }
          });

          // Update the state with all vendor details at once
          setState(prevState => ({
            ...prevState,
            detailFields: updatedDetailFields
          }));

          console.log(`UPLOAD CONTEXT - Added ${completeVendorCount} complete vendor details from localStorage to state`);
          console.log('UPLOAD CONTEXT - Updated detail fields:', JSON.stringify(updatedDetailFields));
        }
      }
    } catch (error) {
      console.error('UPLOAD CONTEXT - Failed to retrieve vendor details from localStorage:', error);
    }

    // Check if we have a video_category for videos
    if (state.mediaType === 'video') {
      // Try to get video_category from localStorage if not in state
      let videoCategory = state.detailFields.video_category;
      if (!videoCategory) {
        try {
          const storedVideoCategory = localStorage.getItem('wedzat_video_category');
          if (storedVideoCategory) {
            videoCategory = storedVideoCategory;
            console.log('UPLOAD CONTEXT - Retrieved video_category from localStorage:', videoCategory);
            setDetailField('video_category', videoCategory);
          }
        } catch (error) {
          console.error('UPLOAD CONTEXT - Failed to retrieve video_category from localStorage:', error);
        }
      }

      console.log(`UPLOAD CONTEXT - Current video_category: ${videoCategory || 'Not set'}`);

      // If we don't have a video_category, use a default one
      if (!videoCategory) {
        console.log('UPLOAD CONTEXT - No video_category found, using default: my_wedding');
        // Use startUploadWithCategory to ensure the video_category is properly set
        return startUploadWithCategory(state.mediaSubtype, 'my_wedding');
      } else {
        // Use startUploadWithCategory to ensure the video_category is properly passed
        console.log(`UPLOAD CONTEXT - Using existing video_category: ${videoCategory}`);
        return startUploadWithCategory(state.mediaSubtype, videoCategory);
      }
    }

    // For photos, just use the regular upload flow
    const validation = validateForm();
    if (!validation.isValid) {
      console.log('Upload validation failed:', validation.error);
      setState({
        ...state,
        error: validation.error,
        step: 'error',
      });
      return;
    }

    if (!state.file) {
      console.log('No file to upload');
      return;
    }

    setState({
      ...state,
      isUploading: true,
      progress: 0,
      step: 'uploading',
      error: undefined,
    });

    try {
      console.log('UPLOAD CONTEXT - Starting upload process...');
      console.log('UPLOAD CONTEXT - Upload details:', {
        file: state.file ? state.file.name : 'No file',
        fileSize: state.file ? Math.round(state.file.size / (1024 * 1024) * 100) / 100 + ' MB' : 'N/A',
        mediaType: state.mediaType,
        mediaSubtype: state.mediaSubtype,
        title: state.title,
        videoCategory: state.detailFields.video_category || 'Not set'
      });

      // Use the upload service to handle the complete upload process
      const result = await uploadService.handleUpload(
        state.file,
        state.mediaType,
        state.mediaSubtype,
        state.title,
        state.description,
        state.tags,
        state.detailFields,
        state.duration,
        state.thumbnail, // Pass the thumbnail file
        (progress) => {
          console.log(`Upload progress: ${progress}%`);

          setState((prevState) => {
            // Only update if the new progress is greater
            if (progress > prevState.progress) {
              return {
                ...prevState,
                progress,
                // Change to processing state when we reach 80%
                step: progress >= 80 && progress < 100 ? 'processing' : prevState.step
              };
            }
            return prevState;
          });
        }
      );

      console.log('Upload completed successfully:', result);

      // Upload complete
      setState({
        ...state,
        isUploading: false,
        progress: 100,
        step: 'complete',
        response: result,
      });
    } catch (error) {
      console.error('Upload failed:', error);
      setState({
        ...state,
        isUploading: false,
        progress: 0,
        step: 'error',
        error: error instanceof Error ? error.message : 'Upload failed. Please try again.',
      });
    }
  };

  const goToStep = (step: UploadState['step']) => {
    setState({ ...state, step });
  };

  // Set video duration
  const setDuration = (duration: number) => {
    setState({
      ...state,
      duration,
    });
    console.log(`Duration set to ${duration} seconds`);
  };

  // Effect to initialize the upload context and listen for vendor details updates
  useEffect(() => {
    // Check if we have a video_category in localStorage
    try {
      const storedVideoCategory = localStorage.getItem('wedzat_video_category');
      if (storedVideoCategory) {
        console.log('UPLOAD CONTEXT - Retrieved video_category from localStorage:', storedVideoCategory);
        setDetailField('video_category', storedVideoCategory);
      }
    } catch (error) {
      console.error('UPLOAD CONTEXT - Failed to retrieve video_category from localStorage:', error);
    }

    // Add event listener for vendor details updates from API service
    const handleVendorDetailsUpdate = (event: any) => {
      if (event.detail) {
        console.log('UPLOAD CONTEXT - Received vendor-details-update event:', event.detail);

        // Process vendor details from event
        const vendorDetails = event.detail;
        const updatedDetailFields: Record<string, string> = { ...state.detailFields };
        let completeVendorCount = 0;

        Object.entries(vendorDetails).forEach(([vendorType, details]: [string, any]) => {
          if (details && details.name && details.mobileNumber) {
            // Add to detailFields
            updatedDetailFields[`vendor_${vendorType}_name`] = details.name;
            updatedDetailFields[`vendor_${vendorType}_contact`] = details.mobileNumber;

            console.log(`UPLOAD CONTEXT - Event handler added vendor ${vendorType} with name: ${details.name} and contact: ${details.mobileNumber}`);
            completeVendorCount++;

            // Also add normalized versions
            const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' :
                                  vendorType === 'decorations' ? 'decoration' : vendorType;

            if (normalizedType !== vendorType) {
              updatedDetailFields[`vendor_${normalizedType}_name`] = details.name;
              updatedDetailFields[`vendor_${normalizedType}_contact`] = details.mobileNumber;
              console.log(`UPLOAD CONTEXT - Event handler also added normalized vendor ${normalizedType}`);
            }
          }
        });

        // Update the state with all vendor details at once
        setState(prevState => ({
          ...prevState,
          detailFields: updatedDetailFields
        }));

        console.log(`UPLOAD CONTEXT - Event handler added ${completeVendorCount} complete vendor details to state`);
        console.log('UPLOAD CONTEXT - Event handler updated detail fields:', JSON.stringify(updatedDetailFields));
      }
    };

    // Add event listener
    window.addEventListener('vendor-details-update', handleVendorDetailsUpdate);

    // Remove event listener on cleanup
    return () => {
      window.removeEventListener('vendor-details-update', handleVendorDetailsUpdate);
    };
  }, []);

  // Create the context value
  const contextValue: UploadContextType = {
    state,
    setFile,
    setThumbnail,
    setMediaType,
    setMediaSubtype,
    setCategory,
    setTitle,
    setDescription,
    addTag,
    removeTag,
    setDetailField,
    setPersonalDetails,
    setVendorDetails,
    setDuration,
    resetUpload,
    startUpload,
    startUploadWithCategory,
    validateForm,
    goToStep,
  };

  return (
    <UploadContext.Provider value={contextValue}>
      {children}
    </UploadContext.Provider>
  );
};

// Custom hook to use the context
export const useUpload = (): UploadContextType => {
  const context = useContext(UploadContext);
  if (context === undefined) {
    throw new Error('useUpload must be used within an UploadProvider');
  }
  return context;
};



