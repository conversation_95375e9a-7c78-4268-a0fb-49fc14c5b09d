"use client";
import React, { useState } from "react";
import { ChevronDown, Filter, X } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";

interface SearchFiltersProps {
  onFiltersChange?: (filters: SearchFilters) => void;
}

export interface SearchFilters {
  types: string[];
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

const SearchFilters: React.FC<SearchFiltersProps> = ({ onFiltersChange }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isOpen, setIsOpen] = useState(false);
  
  // Get current filters from URL
  const currentTypes = searchParams?.get('types')?.split(',') || [];
  const currentSortBy = searchParams?.get('sort_by') || 'created_at';
  const currentSortOrder = (searchParams?.get('sort_order') as 'asc' | 'desc') || 'desc';
  
  const [selectedTypes, setSelectedTypes] = useState<string[]>(currentTypes);
  const [sortBy, setSortBy] = useState(currentSortBy);
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>(currentSortOrder);

  const contentTypes = [
    { id: 'flashes', label: 'Flashes', description: 'Short video clips' },
    { id: 'glimpses', label: 'Glimpses', description: 'Quick video previews' },
    { id: 'movies', label: 'Movies', description: 'Full wedding videos' },
    { id: 'photos', label: 'Photos', description: 'Wedding photographs' }
  ];

  const sortOptions = [
    { value: 'created_at', label: 'Date Created' },
    { value: 'views', label: 'Views' },
    { value: 'likes', label: 'Likes' }
  ];

  const handleTypeToggle = (typeId: string) => {
    const newTypes = selectedTypes.includes(typeId)
      ? selectedTypes.filter(t => t !== typeId)
      : [...selectedTypes, typeId];
    
    setSelectedTypes(newTypes);
  };

  const applyFilters = () => {
    const currentQuery = searchParams?.get('q') || '';
    const currentUsername = searchParams?.get('username') || '';
    const currentTags = searchParams?.get('tags') || '';
    const currentPlace = searchParams?.get('place') || '';
    
    // Build new URL with filters
    const params = new URLSearchParams();
    if (currentQuery) params.set('q', currentQuery);
    if (currentUsername) params.set('username', currentUsername);
    if (currentTags) params.set('tags', currentTags);
    if (currentPlace) params.set('place', currentPlace);
    if (selectedTypes.length > 0) params.set('types', selectedTypes.join(','));
    if (sortBy !== 'created_at') params.set('sort_by', sortBy);
    if (sortOrder !== 'desc') params.set('sort_order', sortOrder);
    
    router.push(`/api/search?${params.toString()}`);
    setIsOpen(false);
    
    // Call callback if provided
    if (onFiltersChange) {
      onFiltersChange({
        types: selectedTypes,
        sortBy,
        sortOrder
      });
    }
  };

  const clearFilters = () => {
    setSelectedTypes([]);
    setSortBy('created_at');
    setSortOrder('desc');
    
    const currentQuery = searchParams?.get('q') || '';
    const currentUsername = searchParams?.get('username') || '';
    const currentTags = searchParams?.get('tags') || '';
    const currentPlace = searchParams?.get('place') || '';
    
    // Build URL without filters
    const params = new URLSearchParams();
    if (currentQuery) params.set('q', currentQuery);
    if (currentUsername) params.set('username', currentUsername);
    if (currentTags) params.set('tags', currentTags);
    if (currentPlace) params.set('place', currentPlace);
    
    router.push(`/api/search?${params.toString()}`);
    setIsOpen(false);
  };

  const hasActiveFilters = selectedTypes.length > 0 || sortBy !== 'created_at' || sortOrder !== 'desc';

  return (
    <div className="relative">
      {/* Filter Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
          hasActiveFilters 
            ? 'border-red-500 bg-red-50 text-red-700' 
            : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
        }`}
      >
        <Filter size={16} />
        <span className="text-sm font-medium">Filters</span>
        {hasActiveFilters && (
          <span className="bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
            {selectedTypes.length || 1}
          </span>
        )}
        <ChevronDown size={16} className={`transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Filter Dropdown */}
      {isOpen && (
        <div className="absolute top-full right-0 mt-2 w-80 bg-white rounded-lg shadow-xl border border-gray-200 z-50">
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-800">Search Filters</h3>
              <button
                onClick={() => setIsOpen(false)}
                className="p-1 rounded-full hover:bg-gray-100"
              >
                <X size={16} className="text-gray-500" />
              </button>
            </div>

            {/* Content Types */}
            <div className="mb-6">
              <h4 className="text-sm font-medium text-gray-700 mb-3">Content Types</h4>
              <div className="space-y-2">
                {contentTypes.map((type) => (
                  <label
                    key={type.id}
                    className="flex items-start space-x-3 cursor-pointer hover:bg-gray-50 p-2 rounded"
                  >
                    <input
                      type="checkbox"
                      checked={selectedTypes.includes(type.id)}
                      onChange={() => handleTypeToggle(type.id)}
                      className="mt-1 w-4 h-4 text-red-600 border-gray-300 rounded focus:ring-red-500"
                    />
                    <div>
                      <div className="text-sm font-medium text-gray-900">{type.label}</div>
                      <div className="text-xs text-gray-500">{type.description}</div>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* Sort Options */}
            <div className="mb-6">
              <h4 className="text-sm font-medium text-gray-700 mb-3">Sort By</h4>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 text-sm"
              >
                {sortOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              
              <div className="mt-2 flex space-x-2">
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="radio"
                    name="sortOrder"
                    value="desc"
                    checked={sortOrder === 'desc'}
                    onChange={(e) => setSortOrder(e.target.value as 'desc')}
                    className="w-4 h-4 text-red-600 border-gray-300 focus:ring-red-500"
                  />
                  <span className="text-sm text-gray-700">Newest First</span>
                </label>
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="radio"
                    name="sortOrder"
                    value="asc"
                    checked={sortOrder === 'asc'}
                    onChange={(e) => setSortOrder(e.target.value as 'asc')}
                    className="w-4 h-4 text-red-600 border-gray-300 focus:ring-red-500"
                  />
                  <span className="text-sm text-gray-700">Oldest First</span>
                </label>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-2">
              <button
                onClick={clearFilters}
                className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Clear All
              </button>
              <button
                onClick={applyFilters}
                className="flex-1 px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 transition-colors"
              >
                Apply Filters
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SearchFilters;
