// frontend/app/api/send-wedding-invitation/route.ts
import { NextResponse } from 'next/server';
import { sendWeddingInvitation } from '../../../lib/nodemailer';

interface RequestBody {
  email: string;
  guestName: string;
  coupleName: string;
  websiteUrl: string;
  responseUrl: string;
  guestId: string;
}

interface ResponseData {
  success: boolean;
  message: string;
}

export async function POST(request: Request): Promise<NextResponse<ResponseData>> {
  try {
    const body: RequestBody = await request.json();
    const { email, guestName, coupleName, websiteUrl, responseUrl, guestId } = body;
    
    // Validate required fields
    if (!email || !guestName || !websiteUrl || !responseUrl) {
      return NextResponse.json({ 
        success: false, 
        message: "Missing required fields" 
      }, { status: 400 });
    }
    
    // Log the invitation details (for debugging)
    console.log(`Sending wedding invitation to ${email} (${guestName})`);
    
    // Send the invitation email
    await sendWeddingInvitation(
      email,
      guestName,
      coupleName || 'The Couple',
      websiteUrl,
      responseUrl
    );
    
    return NextResponse.json({ 
      success: true, 
      message: `Wedding invitation sent to ${guestName} (${email})` 
    });
  } catch (error) {
    console.error('Error sending wedding invitation:', error);
    
    // For development - provide more detailed error
    if (process.env.NODE_ENV === 'development') {
      return NextResponse.json({ 
        success: false, 
        message: `Failed to send invitation: ${error instanceof Error ? error.message : String(error)}` 
      }, { status: 500 });
    }
    
    return NextResponse.json({ 
      success: false, 
      message: "Failed to send wedding invitation" 
    }, { status: 500 });
  }
}
