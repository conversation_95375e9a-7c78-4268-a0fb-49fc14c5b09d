import os
import json
import uuid
import jwt
import psycopg2
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv
from datetime import datetime, date
from decimal import Decimal

# Load environment variables
load_dotenv()

# Database and JWT configuration
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_PORT = os.getenv('DB_PORT')
DB_NAME = os.getenv('DB_NAME')
JWT_SECRET = os.getenv('JWT_SECRET')

# Create a fresh connection for each function call
def get_db_connection():
    return psycopg2.connect(
        host=DB_HOST,
        user=DB_USER,
        password=DB_PASSWORD,
        port=DB_PORT,
        dbname=DB_NAME
    )


def validate_token(headers):
    """Validate the JWT token from the authorization header"""
    token = headers.get('Authorization')
    
    if not token:
        return None, {
            'statusCode': 401,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET, POST",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": "Token is required"})
        }
        
    try:
        token = token.split()[1]
            
        data = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
        return data['user_id'], None
    except jwt.ExpiredSignatureError:
        return None, {
            'statusCode': 401,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET, POST",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": "Token expired"})
        }
    except jwt.InvalidTokenError:
        return None, {
            'statusCode': 401,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET, POST",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": "Invalid token"})
        }

# Checklist API endpoints
def get_checklist(event):
    """Get all checklist items for a user"""
    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response
    
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        # Check if user has any checklist items
        cursor.execute(
            "SELECT COUNT(*) FROM wedding_checklist WHERE user_id = %s",
            (user_id,)
        )
        count = cursor.fetchone()['count']
        
        # If no items exist, create defaults
        if count == 0:
            create_default_checklist_items(user_id)
        
        # Get all checklist items
        cursor.execute(
            '''SELECT * FROM wedding_checklist 
            WHERE user_id = %s 
            ORDER BY due_date ASC, created_at ASC''',
            (user_id,)
        )
        
        checklist_items = cursor.fetchall()
        
        # Convert date objects to strings for JSON serialization
        serializable_items = []
        for item in checklist_items:
            item_dict = dict(item)
            for key, value in item_dict.items():
                if isinstance(value, (datetime, date)):
                    item_dict[key] = value.isoformat()
            serializable_items.append(item_dict)
        
        return {
            'statusCode': 200,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'checklist': serializable_items})
        }
    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }
    finally:
        if conn:
            conn.close()
def create_default_checklist_items(user_id):
    """Create default checklist items for a new user"""
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        # Default checklist items organized by timeline and category
        default_items = [
            # Planning Category
            {
                "task": "Set your wedding budget",
                "category": "Planning",
                "status": "pending"
            },
            {
                "task": "Create guest list",
                "category": "Planning",
                "status": "pending"
            },
            {
                "task": "Choose wedding date",
                "category": "Planning",
                "status": "pending"
            },
            {
                "task": "Create wedding website",
                "category": "Planning",
                "status": "pending"
            },
            {
                "task": "Send save-the-dates",
                "category": "Planning",
                "status": "pending"
            },

            # Venue Category
            {
                "task": "Book ceremony venue",
                "category": "Venue",
                "status": "pending"
            },
            {
                "task": "Book reception venue",
                "category": "Venue",
                "status": "pending"
            },
            {
                "task": "Book hotel room blocks for guests",
                "category": "Venue",
                "status": "pending"
            },

            # Vendors Category
            {
                "task": "Start researching wedding vendors",
                "category": "Vendors",
                "status": "pending"
            },
            {
                "task": "Book photographer",
                "category": "Vendors",
                "status": "pending"
            },
            {
                "task": "Book videographer",
                "category": "Vendors",
                "status": "pending"
            },
            {
                "task": "Book wedding planner",
                "category": "Vendors",
                "status": "pending"
            },
            {
                "task": "Book florist",
                "category": "Vendors",
                "status": "pending"
            },

            # Food & Drink Category
            {
                "task": "Book caterer",
                "category": "Food & Drink",
                "status": "pending"
            },
            {
                "task": "Schedule tastings",
                "category": "Food & Drink",
                "status": "pending"
            },
            {
                "task": "Order wedding cake",
                "category": "Food & Drink",
                "status": "pending"
            },
            {
                "task": "Plan bar service",
                "category": "Food & Drink",
                "status": "pending"
            },

            # Entertainment Category
            {
                "task": "Book DJ or band",
                "category": "Entertainment",
                "status": "pending"
            },
            {
                "task": "Create playlist for ceremony",
                "category": "Entertainment",
                "status": "pending"
            },
            {
                "task": "Create playlist for reception",
                "category": "Entertainment",
                "status": "pending"
            },

            # Attire Category
            {
                "task": "Start shopping for wedding attire",
                "category": "Attire",
                "status": "pending"
            },
            {
                "task": "Schedule dress fittings",
                "category": "Attire",
                "status": "pending"
            },
            {
                "task": "Choose bridesmaids dresses",
                "category": "Attire",
                "status": "pending"
            },
            {
                "task": "Choose groomsmen attire",
                "category": "Attire",
                "status": "pending"
            },
            {
                "task": "Purchase wedding rings",
                "category": "Attire",
                "status": "pending"
            },

            # Stationery Category
            {
                "task": "Design and order invitations",
                "category": "Stationery",
                "status": "pending"
            },
            {
                "task": "Address and mail invitations",
                "category": "Stationery",
                "status": "pending"
            },
            {
                "task": "Create ceremony programs",
                "category": "Stationery",
                "status": "pending"
            },
            {
                "task": "Create menu cards",
                "category": "Stationery",
                "status": "pending"
            },

            # Gifts & Favors Category
            {
                "task": "Register for gifts",
                "category": "Gifts & Favors",
                "status": "pending"
            },
            {
                "task": "Purchase wedding party gifts",
                "category": "Gifts & Favors",
                "status": "pending"
            },
            {
                "task": "Purchase parent gifts",
                "category": "Gifts & Favors",
                "status": "pending"
            },
            {
                "task": "Order wedding favors",
                "category": "Gifts & Favors",
                "status": "pending"
            },

            # Ceremony Category
            {
                "task": "Book officiant",
                "category": "Ceremony",
                "status": "pending"
            },
            {
                "task": "Plan ceremony details",
                "category": "Ceremony",
                "status": "pending"
            },
            {
                "task": "Write vows",
                "category": "Ceremony",
                "status": "pending"
            },
            {
                "task": "Apply for marriage license",
                "category": "Ceremony",
                "status": "pending"
            },

            # Transportation Category
            {
                "task": "Book transportation for wedding party",
                "category": "Transportation",
                "status": "pending"
            },
            {
                "task": "Arrange guest shuttles",
                "category": "Transportation",
                "status": "pending"
            },

            # Honeymoon Category
            {
                "task": "Research honeymoon destinations",
                "category": "Honeymoon",
                "status": "pending"
            },
            {
                "task": "Book honeymoon travel",
                "category": "Honeymoon",
                "status": "pending"
            },
            {
                "task": "Apply for passports if needed",
                "category": "Honeymoon",
                "status": "pending"
            }
        ]

        # Insert default items
        for item in default_items:
            item_id = str(uuid.uuid4())

            cursor.execute(
                '''INSERT INTO wedding_checklist
                (item_id, user_id, task, category, status, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, NOW(), NOW())''',
                (item_id, user_id, item["task"], item["category"], item["status"])
            )

        conn.commit()
        print(f"Added {len(default_items)} default checklist items for user {user_id}")
        return True
    except Exception as e:
        if conn:
            conn.rollback()
        print(f"Error creating default checklist items: {e}")
        return False
    finally:
        if conn:
            conn.close()


def add_checklist_item(event):
    """Add a new checklist item"""
    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response
        
    try:
        data = json.loads(event['body'])
        task = data.get('task')
        category = data.get('category')
        due_date = data.get('due_date')
        status = data.get('status', 'pending')
        
        if not task:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Task is required'})
            }
        
        conn = None
        try:
            conn = get_db_connection()
            # Use RealDictCursor to get results as dictionaries
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            item_id = str(uuid.uuid4())
            
            cursor.execute(
                '''INSERT INTO wedding_checklist 
                (item_id, user_id, task, category, due_date, status, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, NOW(), NOW())
                RETURNING *''',
                (item_id, user_id, task, category, due_date, status)
            )
            
            new_item = cursor.fetchone()
            
            # Convert to a regular Python dict
            item_dict = dict(new_item)
            
            # Convert date objects to strings
            for key, value in item_dict.items():
                if isinstance(value, (datetime, date)):
                    item_dict[key] = value.isoformat()
            
            conn.commit()
            
            return {
                'statusCode': 201,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'item': item_dict})
            }
        except Exception as e:
            if conn:
                conn.rollback()
            return {
                'statusCode': 500,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': str(e)})
            }
        finally:
            if conn:
                conn.close()
    except Exception as e:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, POST',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }

def update_checklist_item(event):
    """Update a checklist item"""
    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        print(f"Token validation failed: {error_response}")
        return error_response
    
    try:
        data = json.loads(event['body'])
        item_id = data.get('item_id')
        task = data.get('task')
        category = data.get('category')
        due_date = data.get('due_date')
        status = data.get('status')
        
        print(f"Updating item {item_id} with task={task}, category={category}, due_date={due_date}, status={status} for user {user_id}")
        
        if not item_id:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Item ID is required'})
            }
        
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            # Check if the item belongs to the user
            cursor.execute(
                '''SELECT * FROM wedding_checklist 
                WHERE item_id = %s AND user_id = %s''',
                (item_id, user_id)
            )
            
            existing_item = cursor.fetchone()
            if not existing_item:
                print(f"Item {item_id} not found for user {user_id}")
                return {
                    'statusCode': 404,
                    'headers': {
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                    },
                    'body': json.dumps({'error': 'Item not found'})
                }
            
            # Update all fields
            cursor.execute(
                '''UPDATE wedding_checklist 
                SET task = COALESCE(%s, task),
                    category = COALESCE(%s, category),
                    due_date = COALESCE(%s, due_date),
                    status = COALESCE(%s, status),
                    updated_at = NOW()
                WHERE item_id = %s AND user_id = %s
                RETURNING *''',
                (task, category, due_date, status, item_id, user_id)
            )
            
            updated_item = cursor.fetchone()
            
            # Convert the updated item to a regular dictionary
            item_dict = dict(updated_item)
            
            # Convert date objects to strings
            for key, value in item_dict.items():
                if isinstance(value, (datetime, date)):
                    item_dict[key] = value.isoformat()
            
            conn.commit()
            
            print(f"Successfully updated item {item_id}")
            
            return {
                'statusCode': 200,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'item': item_dict})
            }
        except Exception as e:
            print(f"Database error: {str(e)}")
            if conn:
                conn.rollback()
            return {
                'statusCode': 500,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': str(e)})
            }
        finally:
            if conn:
                conn.close()
    except Exception as e:
        print(f"General error: {str(e)}")
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }

def delete_checklist_item(event):
    """Delete a checklist item"""
    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response
    
    try:
        # Extract item_id from request body instead of path parameters
        data = json.loads(event.get('body', '{}'))
        item_id = data.get('item_id')
        
        if not item_id:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Item ID is required'})
            }
        
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # Check if the item belongs to the user
            cursor.execute(
                '''SELECT * FROM wedding_checklist 
                WHERE item_id = %s AND user_id = %s''',
                (item_id, user_id)
            )
            
            existing_item = cursor.fetchone()
            if not existing_item:
                return {
                    'statusCode': 404,
                    'headers': {
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                    },
                    'body': json.dumps({'error': 'Item not found'})
                }
            
            # Delete the item
            cursor.execute(
                '''DELETE FROM wedding_checklist 
                WHERE item_id = %s AND user_id = %s''',
                (item_id, user_id)
            )
            
            conn.commit()
            
            return {
                'statusCode': 200,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'message': 'Item deleted successfully'})
            }
        except Exception as e:
            if conn:
                conn.rollback()
            return {
                'statusCode': 500,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': str(e)})
            }
        finally:
            if conn:
                conn.close()
    except Exception as e:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }
        
# Vendor API endpoints
def get_vendors(event):
    """Get all vendors for a user"""
    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response
    
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute(
            '''SELECT * FROM wedding_vendors 
            WHERE user_id = %s 
            ORDER BY category, created_at ASC''',
            (user_id,)
        )
        
        vendors = cursor.fetchall()
        
        return {
            'statusCode': 200,
            'headers': {
                'Access-Control-Allow-Origin': '*',  # Replace with your frontend domain in production
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'vendors': vendors})
        }
    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                'Access-Control-Allow-Origin': '*',  # Replace with your frontend domain in production
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }
    finally:
        if conn:
            conn.close()

def add_vendor(event):
    """Add a new vendor"""
    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response
    
    try:
        data = json.loads(event['body'])
        name = data.get('name')
        category = data.get('category')
        contact = data.get('contact')
        email = data.get('email')
        website = data.get('website')
        notes = data.get('notes')
        price = data.get('price')
        
        if not name or not category:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',  # Replace with your frontend domain in production
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Name and category are required'})
            }
        
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            vendor_id = str(uuid.uuid4())
            
            cursor.execute(
                '''INSERT INTO wedding_vendors 
                (vendor_id, user_id, name, category, contact, email, website, notes, price, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                RETURNING *''',
                (vendor_id, user_id, name, category, contact, email, website, notes, price)
            )
            
            new_vendor = cursor.fetchone()
            conn.commit()
            
            return {
                'statusCode': 201,
                'headers': {
                    'Access-Control-Allow-Origin': '*',  # Replace with your frontend domain in production
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'vendor': new_vendor})
            }
        except Exception as e:
            if conn:
                conn.rollback()
            return {
                'statusCode': 500,
                'headers': {
                    'Access-Control-Allow-Origin': '*',  # Replace with your frontend domain in production
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': str(e)})
            }
        finally:
            if conn:
                conn.close()
    except Exception as e:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',  # Replace with your frontend domain in production
                'Access-Control-Allow-Methods': 'OPTIONS, POST',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }

# Budget API endpoints
# Budget API endpoints
def get_budget(event):
    """Get budget data for a user including categories, expenses and payments"""
    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response
    
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        # Check if user has any budget categories
        cursor.execute(
            "SELECT COUNT(*) FROM wedding_budget_categories WHERE user_id = %s",
            (user_id,)
        )
        count = cursor.fetchone()['count']
        
        # If no categories exist, create defaults
        if count == 0:
            create_default_budget_categories(user_id)
        
        # Get all categories
        cursor.execute(
            '''SELECT * FROM wedding_budget_categories 
            WHERE user_id = %s 
            ORDER BY name ASC''',
            (user_id,)
        )
        
        categories = cursor.fetchall()
        
        # Get all expenses with category names
        cursor.execute(
            '''SELECT e.*, c.name as category_name
            FROM wedding_budget_expenses e
            JOIN wedding_budget_categories c ON e.category_id = c.category_id
            WHERE e.user_id = %s
            ORDER BY c.name, e.name ASC''',
            (user_id,)
        )
        
        expenses = cursor.fetchall()
        
        # Get all payments with expense details
        cursor.execute(
            '''SELECT p.*, e.name as expense_name, c.name as category_name
            FROM wedding_budget_payments p
            JOIN wedding_budget_expenses e ON p.expense_id = e.expense_id
            JOIN wedding_budget_categories c ON e.category_id = c.category_id
            WHERE p.user_id = %s
            ORDER BY p.payment_date DESC''',
            (user_id,)
        )
        
        payments = cursor.fetchall()
        
        # Calculate budget totals
        cursor.execute(
            '''SELECT 
                SUM(estimated_budget) as total_estimated,
                SUM(final_cost) as total_final,
                SUM(amount_paid) as total_paid
            FROM wedding_budget_expenses
            WHERE user_id = %s''',
            (user_id,)
        )
        
        totals = cursor.fetchone()
        
        # Convert date objects and Decimal objects to JSON serializable types
        serializable_categories = []
        for category in categories:
            category_dict = dict(category)
            for key, value in category_dict.items():
                if isinstance(value, (datetime, date)):
                    category_dict[key] = value.isoformat()
                elif isinstance(value, Decimal):
                    category_dict[key] = float(value)
            serializable_categories.append(category_dict)
        
        serializable_expenses = []
        for expense in expenses:
            expense_dict = dict(expense)
            for key, value in expense_dict.items():
                if isinstance(value, (datetime, date)):
                    expense_dict[key] = value.isoformat()
                elif isinstance(value, Decimal):
                    expense_dict[key] = float(value)
            serializable_expenses.append(expense_dict)
        
        serializable_payments = []
        for payment in payments:
            payment_dict = dict(payment)
            for key, value in payment_dict.items():
                if isinstance(value, (datetime, date)):
                    payment_dict[key] = value.isoformat()
                elif isinstance(value, Decimal):
                    payment_dict[key] = float(value)
            serializable_payments.append(payment_dict)
        
        # Convert totals to a regular dict and handle Decimal objects
        totals_dict = {}
        if totals:
            totals_dict = dict(totals)
            for key, value in totals_dict.items():
                if isinstance(value, Decimal):
                    totals_dict[key] = float(value)
        
        return {
            'statusCode': 200,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({
                'categories': serializable_categories,
                'expenses': serializable_expenses,
                'payments': serializable_payments,
                'totals': totals_dict
            })
        }
    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }
    finally:
        if conn:
            conn.close()

def create_default_budget_categories(user_id):
    """Create default budget categories and expenses for a new user"""
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        # Default budget categories with sample expenses
        default_categories = [
            {
                "name": "Venue",
                "expenses": [
                    {"name": "Ceremony venue", "estimated_budget": 2000},
                    {"name": "Reception venue", "estimated_budget": 5000}
                ]
            },
            {
                "name": "Food & Drink",
                "expenses": [
                    {"name": "Catering", "estimated_budget": 6000},
                    {"name": "Wedding cake", "estimated_budget": 500},
                    {"name": "Bar service", "estimated_budget": 1500}
                ]
            },
            {
                "name": "Photography & Video",
                "expenses": [
                    {"name": "Photographer", "estimated_budget": 3000},
                    {"name": "Videographer", "estimated_budget": 2500}
                ]
            },
            {
                "name": "Attire & Beauty",
                "expenses": [
                    {"name": "Wedding dress", "estimated_budget": 1500},
                    {"name": "Suit/Tuxedo", "estimated_budget": 800},
                    {"name": "Hair and makeup", "estimated_budget": 500}
                ]
            },
            {
                "name": "Flowers & Decor",
                "expenses": [
                    {"name": "Bouquets", "estimated_budget": 300},
                    {"name": "Centerpieces", "estimated_budget": 500},
                    {"name": "Ceremony decor", "estimated_budget": 400}
                ]
            },
            {
                "name": "Music & Entertainment",
                "expenses": [
                    {"name": "DJ/Band", "estimated_budget": 1500},
                    {"name": "Ceremony music", "estimated_budget": 500}
                ]
            },
            {
                "name": "Stationery",
                "expenses": [
                    {"name": "Save the dates", "estimated_budget": 150},
                    {"name": "Invitations", "estimated_budget": 400},
                    {"name": "Thank you cards", "estimated_budget": 200}
                ]
            },
            {
                "name": "Transportation",
                "expenses": [
                    {"name": "Wedding party transportation", "estimated_budget": 500},
                    {"name": "Guest shuttle", "estimated_budget": 800}
                ]
            },
            {
                "name": "Gifts",
                "expenses": [
                    {"name": "Wedding party gifts", "estimated_budget": 400},
                    {"name": "Parent gifts", "estimated_budget": 200}
                ]
            },
            {
                "name": "Miscellaneous",
                "expenses": [
                    {"name": "Marriage license", "estimated_budget": 100},
                    {"name": "Wedding rings", "estimated_budget": 1000},
                    {"name": "Honeymoon", "estimated_budget": 5000}
                ]
            }
        ]
        
        # Insert default categories and expenses
        for category in default_categories:
            category_id = str(uuid.uuid4())
            
            # Insert category
            cursor.execute(
                '''INSERT INTO wedding_budget_categories 
                (category_id, user_id, name, created_at, updated_at)
                VALUES (%s, %s, %s, NOW(), NOW())
                RETURNING *''',
                (category_id, user_id, category["name"])
            )
            
            # Insert expenses for this category
            for expense in category["expenses"]:
                expense_id = str(uuid.uuid4())
                cursor.execute(
                    '''INSERT INTO wedding_budget_expenses 
                    (expense_id, category_id, user_id, name, estimated_budget, final_cost, amount_paid, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, 0, 0, NOW(), NOW())''',
                    (expense_id, category_id, user_id, expense["name"], expense["estimated_budget"])
                )
        
        conn.commit()
        print(f"Added default budget categories and expenses for user {user_id}")
        return True
    except Exception as e:
        if conn:
            conn.rollback()
        print(f"Error creating default budget categories: {e}")
        return False
    finally:
        if conn:
            conn.close()

def add_budget_category(event):
    """Add a new budget category"""
    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response
    
    try:
        data = json.loads(event['body'])
        name = data.get('name')
        
        if not name:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Category name is required'})
            }
        
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            category_id = str(uuid.uuid4())
            
            cursor.execute(
                '''INSERT INTO wedding_budget_categories 
                (category_id, user_id, name, created_at, updated_at)
                VALUES (%s, %s, %s, NOW(), NOW())
                RETURNING *''',
                (category_id, user_id, name)
            )
            
            new_category = cursor.fetchone()
            
            # Convert to a regular Python dict
            category_dict = dict(new_category)
            
            # Convert date objects to strings
            for key, value in category_dict.items():
                if isinstance(value, (datetime, date)):
                    category_dict[key] = value.isoformat()
            
            conn.commit()
            
            return {
                'statusCode': 201,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'category': category_dict})
            }
        except Exception as e:
            if conn:
                conn.rollback()
            return {
                'statusCode': 500,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': str(e)})
            }
        finally:
            if conn:
                conn.close()
    except Exception as e:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, POST',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }

def add_budget_expense(event):
    """Add a new budget expense"""
    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response
    
    try:
        data = json.loads(event['body'])
        category_id = data.get('category_id')
        name = data.get('name')
        estimated_budget = data.get('estimated_budget')
        final_cost = data.get('final_cost', 0)
        amount_paid = data.get('amount_paid', 0)
        notes = data.get('notes')
        
        if not category_id or not name or estimated_budget is None:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Category ID, name, and estimated budget are required'})
            }
        
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            # Check if the category exists and belongs to the user
            cursor.execute(
                '''SELECT * FROM wedding_budget_categories 
                WHERE category_id = %s AND user_id = %s''',
                (category_id, user_id)
            )
            
            category = cursor.fetchone()
            if not category:
                return {
                    'statusCode': 404,
                    'headers': {
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'OPTIONS, POST',
                        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                    },
                    'body': json.dumps({'error': 'Category not found'})
                }
            
            expense_id = str(uuid.uuid4())
            
            cursor.execute(
                '''INSERT INTO wedding_budget_expenses 
                (expense_id, category_id, user_id, name, estimated_budget, final_cost, amount_paid, notes, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                RETURNING *''',
                (expense_id, category_id, user_id, name, estimated_budget, final_cost, amount_paid, notes)
            )
            
            new_expense = cursor.fetchone()
            
            # Get the category name
            cursor.execute(
                '''SELECT name FROM wedding_budget_categories WHERE category_id = %s''',
                (category_id,)
            )
            category_name = cursor.fetchone()['name']
            
            # Convert to a regular Python dict
            expense_dict = dict(new_expense)
            expense_dict['category_name'] = category_name
            
            # Convert date objects and Decimal objects to JSON serializable types
            for key, value in expense_dict.items():
                if isinstance(value, (datetime, date)):
                    expense_dict[key] = value.isoformat()
                elif isinstance(value, Decimal):
                    expense_dict[key] = float(value)
            
            conn.commit()
            
            return {
                'statusCode': 201,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'expense': expense_dict})
            }
        except Exception as e:
            if conn:
                conn.rollback()
            return {
                'statusCode': 500,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': str(e)})
            }
        finally:
            if conn:
                conn.close()
    except Exception as e:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, POST',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }

def add_budget_payment(event):
    """Add a new payment for a budget expense"""
    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response
    
    try:
        data = json.loads(event['body'])
        expense_id = data.get('expense_id')
        amount = data.get('amount')
        payment_date = data.get('payment_date')
        payment_method = data.get('payment_method')
        notes = data.get('notes')
        
        if not expense_id or amount is None or not payment_date:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Expense ID, amount, and payment date are required'})
            }
        
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            # Check if the expense exists and belongs to the user
            cursor.execute(
                '''SELECT e.*, c.name as category_name 
                FROM wedding_budget_expenses e
                JOIN wedding_budget_categories c ON e.category_id = c.category_id
                WHERE e.expense_id = %s AND e.user_id = %s''',
                (expense_id, user_id)
            )
            
            expense = cursor.fetchone()
            if not expense:
                return {
                    'statusCode': 404,
                    'headers': {
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'OPTIONS, POST',
                        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                    },
                    'body': json.dumps({'error': 'Expense not found'})
                }
            
            payment_id = str(uuid.uuid4())
            
            # Insert the payment
            cursor.execute(
                '''INSERT INTO wedding_budget_payments 
                (payment_id, expense_id, user_id, amount, payment_date, payment_method, notes, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                RETURNING *''',
                (payment_id, expense_id, user_id, amount, payment_date, payment_method, notes)
            )
            
            new_payment = cursor.fetchone()
            
            # Update the amount_paid in the expense
            cursor.execute(
                '''UPDATE wedding_budget_expenses 
                SET amount_paid = amount_paid + %s,
                    updated_at = NOW()
                WHERE expense_id = %s
                RETURNING *''',
                (amount, expense_id)
            )
            
            updated_expense = cursor.fetchone()
            
            # Convert to regular Python dicts
            payment_dict = dict(new_payment)
            expense_dict = dict(updated_expense)
            
            # Add expense details to payment response
            payment_dict['expense_name'] = expense['name']
            payment_dict['category_name'] = expense['category_name']
            
            # Convert date objects and Decimal objects to JSON serializable types
            for key, value in payment_dict.items():
                if isinstance(value, (datetime, date)):
                    payment_dict[key] = value.isoformat()
                elif isinstance(value, Decimal):
                    payment_dict[key] = float(value)
            
            for key, value in expense_dict.items():
                if isinstance(value, (datetime, date)):
                    expense_dict[key] = value.isoformat()
                elif isinstance(value, Decimal):
                    expense_dict[key] = float(value)
            
            conn.commit()
            
            return {
                'statusCode': 201,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({
                    'payment': payment_dict,
                    'updated_expense': expense_dict
                })
            }
        except Exception as e:
            if conn:
                conn.rollback()
            return {
                'statusCode': 500,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': str(e)})
            }
        finally:
            if conn:
                conn.close()
    except Exception as e:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, POST',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }

def update_budget_expense(event):
    """Update a budget expense"""
    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response
    
    try:
        data = json.loads(event['body'])
        expense_id = data.get('expense_id')
        category_id = data.get('category_id')
        name = data.get('name')
        estimated_budget = data.get('estimated_budget')
        final_cost = data.get('final_cost')
        notes = data.get('notes')
        
        if not expense_id:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Expense ID is required'})
            }
        
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            # Check if the expense belongs to the user
            cursor.execute(
                '''SELECT * FROM wedding_budget_expenses 
                WHERE expense_id = %s AND user_id = %s''',
                (expense_id, user_id)
            )
            
            existing_expense = cursor.fetchone()
            if not existing_expense:
                return {
                    'statusCode': 404,
                    'headers': {
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                    },
                    'body': json.dumps({'error': 'Expense not found'})
                }
            
            # If category_id is provided, check if it exists and belongs to the user
            if category_id:
                cursor.execute(
                    '''SELECT * FROM wedding_budget_categories 
                    WHERE category_id = %s AND user_id = %s''',
                    (category_id, user_id)
                )
                
                category = cursor.fetchone()
                if not category:
                    return {
                        'statusCode': 404,
                        'headers': {
                            'Access-Control-Allow-Origin': '*',
                            'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                        },
                        'body': json.dumps({'error': 'Category not found'})
                    }
            
            # Update the expense
            cursor.execute(
                '''UPDATE wedding_budget_expenses 
                SET category_id = COALESCE(%s, category_id),
                    name = COALESCE(%s, name),
                    estimated_budget = COALESCE(%s, estimated_budget),
                    final_cost = COALESCE(%s, final_cost),
                    notes = COALESCE(%s, notes),
                    updated_at = NOW()
                WHERE expense_id = %s AND user_id = %s
                RETURNING *''',
                (category_id, name, estimated_budget, final_cost, notes, expense_id, user_id)
            )
            
            updated_expense = cursor.fetchone()
            
            # Get the category name
            cursor.execute(
                '''SELECT name FROM wedding_budget_categories WHERE category_id = %s''',
                (updated_expense['category_id'],)
            )
            category_name = cursor.fetchone()['name']
            
            # Convert to a regular Python dict
            expense_dict = dict(updated_expense)
            expense_dict['category_name'] = category_name
            
            # Convert date objects and Decimal objects to JSON serializable types
            for key, value in expense_dict.items():
                if isinstance(value, (datetime, date)):
                    expense_dict[key] = value.isoformat()
                elif isinstance(value, Decimal):
                    expense_dict[key] = float(value)
            
            conn.commit()
            
            return {
                'statusCode': 200,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'expense': expense_dict})
            }
        except Exception as e:
            if conn:
                conn.rollback()
            return {
                'statusCode': 500,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': str(e)})
            }
        finally:
            if conn:
                conn.close()
    except Exception as e:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }

def delete_budget_expense(event):
    """Delete a budget expense"""
    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response
    
    try:
        data = json.loads(event.get('body', '{}'))
        expense_id = data.get('expense_id')
        
        if not expense_id:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Expense ID is required'})
            }
        
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # Check if the expense belongs to the user
            cursor.execute(
                '''SELECT * FROM wedding_budget_expenses 
                WHERE expense_id = %s AND user_id = %s''',
                (expense_id, user_id)
            )
            
            existing_expense = cursor.fetchone()
            if not existing_expense:
                return {
                    'statusCode': 404,
                    'headers': {
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                    },
                    'body': json.dumps({'error': 'Expense not found'})
                }
            
            # Delete the expense (cascade will delete related payments)
            cursor.execute(
                '''DELETE FROM wedding_budget_expenses 
                WHERE expense_id = %s AND user_id = %s''',
                (expense_id, user_id)
            )
            
            conn.commit()
            
            return {
                'statusCode': 200,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'message': 'Expense deleted successfully'})
            }
        except Exception as e:
            if conn:
                conn.rollback()
            return {
                'statusCode': 500,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': str(e)})
            }
        finally:
            if conn:
                conn.close()
    except Exception as e:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }

def delete_budget_category(event):
    """Delete a budget category"""
    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response
    
    try:
        data = json.loads(event.get('body', '{}'))
        category_id = data.get('category_id')
        
        if not category_id:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Category ID is required'})
            }
        
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # Check if the category belongs to the user
            cursor.execute(
                '''SELECT * FROM wedding_budget_categories 
                WHERE category_id = %s AND user_id = %s''',
                (category_id, user_id)
            )
            
            existing_category = cursor.fetchone()
            if not existing_category:
                return {
                    'statusCode': 404,
                    'headers': {
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                    },
                    'body': json.dumps({'error': 'Category not found'})
                }
            
            # Delete the category (cascade will delete related expenses and payments)
            cursor.execute(
                '''DELETE FROM wedding_budget_categories 
                WHERE category_id = %s AND user_id = %s''',
                (category_id, user_id)
            )
            
            conn.commit()
            
            return {
                'statusCode': 200,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'message': 'Category deleted successfully'})
            }
        except Exception as e:
            if conn:
                conn.rollback()
            return {
                'statusCode': 500,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': str(e)})
            }
        finally:
            if conn:
                conn.close()
    except Exception as e:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }
        
# Guest List API endpoints
def get_guest_list(event):
    """Get guest list for a user"""
    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response
    
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute(
            '''SELECT * FROM wedding_guests 
            WHERE user_id = %s 
            ORDER BY name ASC''',
            (user_id,)
        )
        
        guests = cursor.fetchall()
        
        # Get guest count statistics
        cursor.execute(
            '''SELECT 
                COUNT(*) as total_guests,
                SUM(CASE WHEN rsvp_status = 'confirmed' THEN 1 ELSE 0 END) as confirmed,
                SUM(CASE WHEN rsvp_status = 'declined' THEN 1 ELSE 0 END) as declined,
                SUM(CASE WHEN rsvp_status = 'pending' THEN 1 ELSE 0 END) as pending
            FROM wedding_guests 
            WHERE user_id = %s''',
            (user_id,)
        )
        
        stats = cursor.fetchone()
        
        return {
            'statusCode': 200,
            'headers': {
                'Access-Control-Allow-Origin': '*',  # Replace with your frontend domain in production
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({
                'guests': guests,
                'stats': stats
            })
        }
    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                'Access-Control-Allow-Origin': '*',  # Replace with your frontend domain in production
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }
    finally:
        if conn:
            conn.close()

def add_guest(event):
    """Add a new guest"""
    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response
    
    try:
        data = json.loads(event['body'])
        name = data.get('name')
        email = data.get('email')
        phone = data.get('phone')
        group = data.get('group')
        rsvp_status = data.get('rsvp_status', 'pending')
        plus_ones = data.get('plus_ones', 0)
        notes = data.get('notes')
        
        if not name:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',  # Replace with your frontend domain in production
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Name is required'})
            }
        
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            guest_id = str(uuid.uuid4())
            
            cursor.execute(
                '''INSERT INTO wedding_guests 
                (guest_id, user_id, name, email, phone, guest_group, rsvp_status, plus_ones, notes, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                RETURNING *''',
                (guest_id, user_id, name, email, phone, group, rsvp_status, plus_ones, notes)
            )
            
            new_guest = cursor.fetchone()
            conn.commit()
            
            return {
                'statusCode': 201,
                'headers': {
                    'Access-Control-Allow-Origin': '*',  # Replace with your frontend domain in production
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'guest': new_guest})
            }
        except Exception as e:
            if conn:
                conn.rollback()
            return {
                'statusCode': 500,
                'headers': {
                    'Access-Control-Allow-Origin': '*',  # Replace with your frontend domain in production
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': str(e)})
            }
        finally:
            if conn:
                conn.close()
    except Exception as e:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',  # Replace with your frontend domain in production
                'Access-Control-Allow-Methods': 'OPTIONS, POST',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }





