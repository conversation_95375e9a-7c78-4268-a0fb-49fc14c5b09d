"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9b26bbbd1f5f\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xca29tbWlcXE9uZURyaXZlXFxEZXNrdG9wXFxXRURaQVRfXFxmcm9udGVuZFxcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjliMjZiYmJkMWY1ZlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./contexts/UploadContexts.tsx":
/*!*************************************!*\
  !*** ./contexts/UploadContexts.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UploadProvider: () => (/* binding */ UploadProvider),\n/* harmony export */   useUpload: () => (/* binding */ useUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/api */ \"(app-pages-browser)/./services/api.ts\");\n/* harmony import */ var _utils_uploadUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/uploadUtils */ \"(app-pages-browser)/./utils/uploadUtils.ts\");\n// contexts/UploadContext.tsx\n/* __next_internal_client_entry_do_not_use__ UploadProvider,useUpload auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Initial state\nconst initialState = {\n    file: null,\n    thumbnail: null,\n    mediaType: 'photo',\n    mediaSubtype: 'story',\n    category: '',\n    title: '',\n    description: '',\n    tags: [],\n    detailFields: {},\n    isMoments: false,\n    step: 'selecting',\n    progress: 0,\n    isUploading: false\n};\n// Create context\nconst UploadContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Provider component\nconst UploadProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialState);\n    // Set file and automatically determine media type\n    const setFile = async (file)=>{\n        if (!file) {\n            setState({\n                ...state,\n                file: null\n            });\n            return;\n        }\n        const isVideo = file.type.startsWith('video/');\n        const mediaType = isVideo ? 'video' : 'photo';\n        // Default media subtypes based on media type\n        const mediaSubtype = isVideo ? 'glimpse' : 'post'; // Default to 'post' for photos\n        setState({\n            ...state,\n            file,\n            mediaType,\n            mediaSubtype,\n            step: 'details'\n        });\n    };\n    // Set thumbnail image\n    const setThumbnail = (thumbnail)=>{\n        setState({\n            ...state,\n            thumbnail\n        });\n    };\n    const setMediaType = (type)=>{\n        // Don't set a default category - let the user's selection flow through the process\n        // Just update the media type\n        setState({\n            ...state,\n            mediaType: type\n        });\n    };\n    const setMediaSubtype = (mediaSubtype)=>{\n        setState({\n            ...state,\n            mediaSubtype\n        });\n    };\n    // Keep the old function for backward compatibility\n    const setCategory = (category)=>{\n        console.log('UPLOAD CONTEXT - setCategory is deprecated, use setMediaSubtype instead');\n        setState({\n            ...state,\n            mediaSubtype: category\n        });\n    };\n    const setTitle = (title)=>{\n        // Ensure title is not empty\n        if (!title || !title.trim()) {\n            console.warn('Attempted to set empty title');\n            return;\n        }\n        console.log('Setting title to:', title.trim());\n        setState({\n            ...state,\n            title: title.trim()\n        });\n    };\n    const setDescription = (description)=>{\n        setState({\n            ...state,\n            description\n        });\n    };\n    const addTag = (tag)=>{\n        if (tag.trim() && !state.tags.includes(tag.trim())) {\n            setState({\n                ...state,\n                tags: [\n                    ...state.tags,\n                    tag.trim()\n                ]\n            });\n        }\n    };\n    const removeTag = (tag)=>{\n        setState({\n            ...state,\n            tags: state.tags.filter((t)=>t !== tag)\n        });\n    };\n    const setDetailField = (field, value)=>{\n        // For moments (stories), skip all localStorage operations and vendor field handling\n        if (state.mediaSubtype === 'story') {\n            console.log(\"UPLOAD CONTEXT - Moments detected, setting field \".concat(field, \" without localStorage operations\"));\n            setState((prevState)=>({\n                    ...prevState,\n                    detailFields: {\n                        ...prevState.detailFields,\n                        [field]: value\n                    }\n                }));\n            return;\n        }\n        // Special handling for video_category to ensure it's properly set\n        if (field === 'video_category') {\n            console.log(\"UPLOAD CONTEXT - Setting video_category to: \".concat(value));\n            // Store video_category in localStorage\n            try {\n                localStorage.setItem('wedzat_video_category', value);\n                console.log(\"UPLOAD CONTEXT - Stored video_category in localStorage: \".concat(value));\n            } catch (error) {\n                console.error('UPLOAD CONTEXT - Failed to store video_category in localStorage:', error);\n            }\n        }\n        // Special handling for vendor fields to ensure they're properly set\n        if (field.startsWith('vendor_')) {\n            // If this is a vendor field, update the vendor details in localStorage\n            try {\n                // Get existing vendor details from localStorage\n                const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n                let vendorDetails = storedVendorDetails ? JSON.parse(storedVendorDetails) : {};\n                // If this is a vendor name field, extract the vendor type and update the name\n                if (field.endsWith('_name')) {\n                    const vendorType = field.replace('vendor_', '').replace('_name', '');\n                    // Check if we already have this vendor in the details\n                    if (!vendorDetails[vendorType]) {\n                        vendorDetails[vendorType] = {\n                            name: value,\n                            mobileNumber: ''\n                        };\n                    } else {\n                        vendorDetails[vendorType].name = value;\n                    }\n                }\n                // If this is a vendor contact field, extract the vendor type and update the contact\n                if (field.endsWith('_contact')) {\n                    const vendorType = field.replace('vendor_', '').replace('_contact', '');\n                    // Check if we already have this vendor in the details\n                    if (!vendorDetails[vendorType]) {\n                        vendorDetails[vendorType] = {\n                            name: '',\n                            mobileNumber: value\n                        };\n                    } else {\n                        vendorDetails[vendorType].mobileNumber = value;\n                    }\n                }\n                // Store the updated vendor details in localStorage\n                localStorage.setItem('wedzat_vendor_details', JSON.stringify(vendorDetails));\n            } catch (error) {\n                console.error('UPLOAD CONTEXT - Failed to update vendor details in localStorage:', error);\n            }\n        }\n        // Create a new detailFields object with the updated field\n        const updatedDetailFields = {\n            ...state.detailFields,\n            [field]: value\n        };\n        // Update the state with the new detailFields\n        setState((prevState)=>({\n                ...prevState,\n                detailFields: updatedDetailFields\n            }));\n        // For video_category, log the updated state after a short delay\n        if (field === 'video_category') {\n            setTimeout(()=>{\n                console.log(\"UPLOAD CONTEXT - Verified video_category is set to: \".concat(state.detailFields.video_category || 'Not set'));\n            }, 100);\n        }\n    };\n    // Set all personal details at once and update the title and related detail fields\n    const setPersonalDetails = (details)=>{\n        // console.log('Setting all personal details:', details);\n        // Validate caption/title\n        if (!details.caption || !details.caption.trim()) {\n            console.warn('Attempted to set personal details with empty caption/title');\n            return;\n        }\n        // Update title\n        const title = details.caption.trim();\n        // console.log('Setting title from personal details:', title);\n        // Update detail fields with backend-compatible field names\n        const updatedDetailFields = {\n            ...state.detailFields,\n            'personal_caption': title,\n            'personal_life_partner': details.lifePartner || '',\n            'personal_wedding_style': details.weddingStyle || '',\n            'personal_place': details.place || '',\n            'personal_event_type': details.eventType || '',\n            'personal_budget': details.budget || '',\n            // Keep legacy field names for compatibility\n            'lifePartner': details.lifePartner || '',\n            'location': details.place || '',\n            'place': details.place || '',\n            'eventType': details.eventType || '',\n            'budget': details.budget || '',\n            'weddingStyle': details.weddingStyle || ''\n        };\n        // Update state with all changes at once\n        setState({\n            ...state,\n            title,\n            description: details.weddingStyle || '',\n            detailFields: updatedDetailFields\n        });\n        // Log the description being set\n        console.log('Setting description to:', details.weddingStyle || '');\n        // Log the updated state after a short delay to ensure state has updated\n        setTimeout(()=>{\n            console.log('Personal details set successfully');\n            console.log('Title after update:', title);\n        }, 0);\n    };\n    // Set all vendor details at once and update the related detail fields\n    const setVendorDetails = (vendorDetails)=>{\n        console.log('UPLOAD CONTEXT - Setting all vendor details:', JSON.stringify(vendorDetails, null, 2));\n        // Create a copy of the current detail fields\n        const updatedDetailFields = {\n            ...state.detailFields\n        };\n        // Save the video_category if it exists\n        const videoCategory = state.detailFields.video_category;\n        console.log('UPLOAD CONTEXT - Preserving video_category:', videoCategory);\n        // Count how many complete vendor details we're receiving\n        const completeVendorCount = Object.entries(vendorDetails).filter((param)=>{\n            let [_, detail] = param;\n            return detail && detail.name && detail.mobileNumber && detail.name.trim() !== '' && detail.mobileNumber.trim() !== '';\n        }).length;\n        console.log(\"UPLOAD CONTEXT - Received \".concat(completeVendorCount, \" complete vendor details\"));\n        // Process vendor details\n        Object.entries(vendorDetails).forEach((param)=>{\n            let [vendorType, details] = param;\n            if (details) {\n                // Only include vendors that have BOTH name AND mobile number\n                if (details.name && details.mobileNumber && details.name.trim() !== '' && details.mobileNumber.trim() !== '') {\n                    // Handle special mappings for makeup_artist and decoration\n                    let backendVendorType = vendorType;\n                    // Map frontend field names to backend field names\n                    if (vendorType === 'makeupArtist') {\n                        backendVendorType = 'makeup_artist';\n                    } else if (vendorType === 'decorations') {\n                        backendVendorType = 'decoration';\n                    }\n                    // Store vendor details in the format expected by the backend\n                    updatedDetailFields[\"vendor_\".concat(backendVendorType, \"_name\")] = details.name || '';\n                    updatedDetailFields[\"vendor_\".concat(backendVendorType, \"_contact\")] = details.mobileNumber || '';\n                    // Always store with the original vendorType to ensure we count it correctly\n                    // This ensures both frontend and backend field names are present\n                    // This is especially important for Edge browser compatibility\n                    if (vendorType !== backendVendorType) {\n                        updatedDetailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name || '';\n                        updatedDetailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber || '';\n                    }\n                    // Also store with common vendor types to ensure cross-browser compatibility\n                    if (vendorType === 'makeupArtist' || vendorType === 'makeup_artist') {\n                        // Ensure both makeupArtist and makeup_artist are present\n                        updatedDetailFields[\"vendor_makeupArtist_name\"] = details.name || '';\n                        updatedDetailFields[\"vendor_makeupArtist_contact\"] = details.mobileNumber || '';\n                        updatedDetailFields[\"vendor_makeup_artist_name\"] = details.name || '';\n                        updatedDetailFields[\"vendor_makeup_artist_contact\"] = details.mobileNumber || '';\n                    } else if (vendorType === 'decorations' || vendorType === 'decoration') {\n                        // Ensure both decorations and decoration are present\n                        updatedDetailFields[\"vendor_decorations_name\"] = details.name || '';\n                        updatedDetailFields[\"vendor_decorations_contact\"] = details.mobileNumber || '';\n                        updatedDetailFields[\"vendor_decoration_name\"] = details.name || '';\n                        updatedDetailFields[\"vendor_decoration_contact\"] = details.mobileNumber || '';\n                    }\n                // console.log(`UPLOAD CONTEXT - Setting vendor detail: ${vendorType} (backend: ${backendVendorType})`, {\n                //   name: details.name || '',\n                //   contact: details.mobileNumber || ''\n                // });\n                } else {\n                    console.log(\"UPLOAD CONTEXT - Skipping incomplete vendor detail: \".concat(vendorType));\n                }\n            }\n        });\n        // Don't update state here - we'll do it after restoring the video_category\n        // console.log('UPLOAD CONTEXT - Vendor details set successfully');\n        // console.log('UPLOAD CONTEXT - Total detail fields count:', Object.keys(updatedDetailFields).length);\n        // Count how many complete vendor details we have after processing\n        let completeVendorPairs = 0;\n        const vendorNames = new Set();\n        const vendorContacts = new Set();\n        // Log all vendor details for debugging\n        Object.keys(updatedDetailFields).forEach((key)=>{\n            if (key.startsWith('vendor_')) {\n                // console.log(`UPLOAD CONTEXT - Vendor field: ${key} = ${updatedDetailFields[key]}`);\n                if (key.endsWith('_name')) {\n                    vendorNames.add(key.replace('vendor_', '').replace('_name', ''));\n                } else if (key.endsWith('_contact')) {\n                    vendorContacts.add(key.replace('vendor_', '').replace('_contact', ''));\n                }\n            }\n        });\n        // Count complete pairs (both name and contact)\n        vendorNames.forEach((name)=>{\n            if (vendorContacts.has(name)) {\n                completeVendorPairs++;\n            }\n        });\n        // console.log(`UPLOAD CONTEXT - Found ${completeVendorPairs} complete vendor pairs (name + contact)`);\n        // console.log(`UPLOAD CONTEXT - Vendor names: ${Array.from(vendorNames).join(', ')}`);\n        // console.log(`UPLOAD CONTEXT - Vendor contacts: ${Array.from(vendorContacts).join(', ')}`);\n        // Restore the video_category if it exists\n        if (videoCategory) {\n            console.log('UPLOAD CONTEXT - Restoring video_category:', videoCategory);\n            updatedDetailFields.video_category = videoCategory;\n        }\n        // Log the detail fields before updating state\n        console.log('UPLOAD CONTEXT - Detail fields before update:', JSON.stringify(state.detailFields, null, 2));\n        console.log('UPLOAD CONTEXT - Updated detail fields to be set:', JSON.stringify(updatedDetailFields, null, 2));\n        // Create a completely new state object to ensure Edge updates correctly\n        const newState = {\n            ...state,\n            detailFields: {\n                ...updatedDetailFields\n            }\n        };\n        // For Edge browser compatibility, directly set the vendor fields in the state\n        // This is a workaround for Edge where the state update doesn't properly preserve vendor details\n        if ( true && /Edge|Edg/.test(window.navigator.userAgent)) {\n            console.log('UPLOAD CONTEXT - Edge browser detected, using direct state update');\n            // Create a direct reference to the state object\n            const directState = state;\n            // Directly set the detailFields\n            directState.detailFields = {\n                ...updatedDetailFields\n            };\n            // Log the direct update\n            console.log('UPLOAD CONTEXT - Direct state update:', JSON.stringify(directState.detailFields, null, 2));\n        }\n        // Update the state with the updated detail fields\n        setState(newState);\n        // Force a re-render to ensure the state is updated\n        setTimeout(()=>{\n            console.log('UPLOAD CONTEXT - Vendor details set successfully');\n            console.log('UPLOAD CONTEXT - Total detail fields count:', Object.keys(state.detailFields).length);\n            console.log('UPLOAD CONTEXT - Detail fields after update:', JSON.stringify(state.detailFields, null, 2));\n            // Double-check that the vendor details were set correctly\n            const vendorNameFields = Object.keys(state.detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_name'));\n            const vendorContactFields = Object.keys(state.detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_contact'));\n            console.log('UPLOAD CONTEXT - Vendor name fields count:', vendorNameFields.length);\n            console.log('UPLOAD CONTEXT - Vendor contact fields count:', vendorContactFields.length);\n        }, 100);\n    };\n    const resetUpload = ()=>{\n        console.log('UPLOAD CONTEXT - Completely resetting upload state');\n        // Create a fresh copy of the initial state\n        const freshState = {\n            file: null,\n            thumbnail: null,\n            mediaType: '',\n            mediaSubtype: '',\n            title: '',\n            description: '',\n            tags: [],\n            detailFields: {},\n            isMoments: false,\n            step: 'select',\n            duration: 0\n        };\n        // Set the state to the fresh state\n        setState(freshState);\n        // Log the reset\n        console.log('UPLOAD CONTEXT - Upload state reset to:', JSON.stringify(freshState, null, 2));\n    };\n    // Helper function to detect Edge browser\n    const isEdgeBrowser = ()=>{\n        if (true) {\n            return /Edge|Edg/.test(window.navigator.userAgent);\n        }\n        return false;\n    };\n    const validateForm = ()=>{\n        // For moments (stories), only validate file and title - skip all other validations\n        if (state.mediaSubtype === 'story') {\n            console.log('VALIDATE FORM - Moments/Stories detected, using simplified validation');\n            // Check if file is selected\n            if (!state.file) {\n                console.log('Validation failed: No file selected for moments');\n                return {\n                    isValid: false,\n                    error: 'Please select a file to upload'\n                };\n            }\n            // Validate file type and size\n            const fileValidation = (0,_utils_uploadUtils__WEBPACK_IMPORTED_MODULE_3__.validateFile)(state.file, state.mediaType);\n            if (!fileValidation.isValid) {\n                console.log('Validation failed: File validation failed for moments', fileValidation);\n                return fileValidation;\n            }\n            // Check if title is provided\n            if (!state.title || !state.title.trim()) {\n                console.log('Validation failed: Title is empty for moments');\n                return {\n                    isValid: false,\n                    error: 'Please provide a title for your upload'\n                };\n            }\n            console.log('VALIDATE FORM - Moments validation passed');\n            return {\n                isValid: true\n            };\n        }\n        // Check if we're running in Edge browser\n        const isEdge = isEdgeBrowser();\n        if (isEdge) {\n            console.log('VALIDATE FORM - Running in Edge browser, applying special handling');\n        }\n        // console.log('VALIDATE FORM - Validating form with state:', {\n        //   file: state.file ? state.file.name : 'No file',\n        //   mediaType: state.mediaType,\n        //   title: state.title,\n        //   description: state.description,\n        //   detailFieldsCount: Object.keys(state.detailFields).length,\n        //   tags: state.tags\n        // });\n        // Log all detail fields for debugging\n        // console.log('VALIDATE FORM - All detail fields:', JSON.stringify(state.detailFields, null, 2));\n        // Check if file is selected\n        if (!state.file) {\n            // console.log('Validation failed: No file selected');\n            return {\n                isValid: false,\n                error: 'Please select a file to upload'\n            };\n        }\n        // Validate file type and size\n        const fileValidation = (0,_utils_uploadUtils__WEBPACK_IMPORTED_MODULE_3__.validateFile)(state.file, state.mediaType);\n        if (!fileValidation.isValid) {\n            console.log('Validation failed: File validation failed', fileValidation);\n            return fileValidation;\n        }\n        // Check if title is provided\n        if (!state.title || !state.title.trim()) {\n            console.log('Validation failed: Title is empty');\n            return {\n                isValid: false,\n                error: 'Please provide a title for your upload'\n            };\n        }\n        // First, try to get vendor details from localStorage\n        let detailFields = {\n            ...state.detailFields\n        };\n        let vendorDetailsFromStorage = null;\n        try {\n            const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n            if (storedVendorDetails) {\n                vendorDetailsFromStorage = JSON.parse(storedVendorDetails);\n                console.log('VALIDATE FORM - Retrieved vendor details from localStorage:', storedVendorDetails);\n                // Process vendor details from localStorage\n                if (vendorDetailsFromStorage && Object.keys(vendorDetailsFromStorage).length > 0) {\n                    Object.entries(vendorDetailsFromStorage).forEach((param)=>{\n                        let [vendorType, details] = param;\n                        if (details && details.name && details.mobileNumber) {\n                            // Add to detailFields\n                            detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                            detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                            console.log(\"VALIDATE FORM - Added vendor \".concat(vendorType, \" with name: \").concat(details.name, \" and contact: \").concat(details.mobileNumber));\n                            // Also add normalized versions\n                            const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                            if (normalizedType !== vendorType) {\n                                detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                                detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                                console.log(\"VALIDATE FORM - Also added normalized vendor \".concat(normalizedType));\n                            }\n                        }\n                    });\n                    console.log('VALIDATE FORM - Updated detail fields with vendor details from localStorage');\n                }\n            }\n        } catch (error) {\n            console.error('VALIDATE FORM - Failed to retrieve vendor details from localStorage:', error);\n        }\n        // Now use the updated detailFields for validation\n        console.log('Detail fields count:', Object.keys(detailFields).length);\n        console.log('Detail fields present:', Object.keys(detailFields));\n        console.log('Detail fields values:', detailFields);\n        // For videos, check if required vendor details are present based on video category\n        if (state.mediaType === 'video') {\n            // Determine required vendor count based on video category\n            const videoCategory = detailFields.video_category || 'my_wedding';\n            const requiredVendorCount = videoCategory === 'wedding_vlog' ? 1 : 4;\n            console.log(\"VALIDATE FORM - Video category: \".concat(videoCategory, \", Required vendors: \").concat(requiredVendorCount));\n            // Special handling for Edge browser\n            if (isEdge) {\n                console.log('VALIDATE FORM - Edge browser detected, checking vendor details directly');\n                // In Edge, we'll count vendor details directly from the detailFields\n                const vendorNameFields = Object.keys(detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_name'));\n                const vendorContactFields = Object.keys(detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_contact'));\n                console.log('VALIDATE FORM - Edge browser - Vendor name fields:', vendorNameFields);\n                console.log('VALIDATE FORM - Edge browser - Vendor contact fields:', vendorContactFields);\n                // If we have at least 4 vendor name fields and 4 vendor contact fields, assume we have enough vendors\n                if (vendorNameFields.length >= 4 && vendorContactFields.length >= 4) {\n                    console.log('VALIDATE FORM - Edge browser - Found enough vendor fields, validation passed');\n                    return {\n                        isValid: true\n                    };\n                }\n                // Edge browser workaround - if we're uploading a video, assume vendor details are valid\n                // This is a temporary workaround for Edge browser compatibility\n                if (state.mediaType === 'video') {\n                    console.log('VALIDATE FORM - Edge browser workaround: Assuming vendor details are valid for video upload');\n                    return {\n                        isValid: true\n                    };\n                }\n            }\n            console.log('VALIDATE FORM - Checking vendor details for video upload');\n            console.log('VALIDATE FORM - Detail fields:', JSON.stringify(detailFields, null, 2));\n            // Count how many complete vendor details we have (where BOTH name AND contact are provided)\n            let validVendorCount = 0;\n            // Include both frontend and backend field names to ensure we count all vendor details\n            const vendorPrefixes = [\n                'venue',\n                'photographer',\n                'makeup_artist',\n                'makeupArtist',\n                'decoration',\n                'decorations',\n                'caterer',\n                'additional1',\n                'additional2',\n                'additionalVendor1',\n                'additionalVendor2'\n            ];\n            console.log('VALIDATE FORM - Checking these vendor prefixes:', vendorPrefixes.join(', '));\n            // Keep track of which vendors we've already counted to avoid duplicates\n            const countedVendors = new Set();\n            // First, log all vendor-related fields for debugging\n            const vendorFields = Object.keys(detailFields).filter((key)=>key.startsWith('vendor_'));\n            console.log('VALIDATE FORM - All vendor fields:', vendorFields.join(', '));\n            for (const prefix of vendorPrefixes){\n                // Skip if we've already counted this vendor (to avoid counting both makeupArtist and makeup_artist)\n                const normalizedPrefix = prefix === 'makeupArtist' ? 'makeup_artist' : prefix === 'decorations' ? 'decoration' : prefix;\n                if (countedVendors.has(normalizedPrefix)) {\n                    console.log(\"VALIDATE FORM - Skipping \".concat(prefix, \" as we already counted \").concat(normalizedPrefix));\n                    continue;\n                }\n                const nameField = \"vendor_\".concat(prefix, \"_name\");\n                const contactField = \"vendor_\".concat(prefix, \"_contact\");\n                console.log(\"VALIDATE FORM - Checking vendor \".concat(prefix, \":\"), {\n                    nameField,\n                    nameValue: detailFields[nameField],\n                    contactField,\n                    contactValue: detailFields[contactField],\n                    hasName: !!detailFields[nameField],\n                    hasContact: !!detailFields[contactField]\n                });\n                if (detailFields[nameField] && detailFields[contactField]) {\n                    validVendorCount++;\n                    countedVendors.add(normalizedPrefix);\n                    console.log(\"VALIDATE FORM - Found valid vendor: \".concat(prefix, \" with name: \").concat(detailFields[nameField], \" and contact: \").concat(detailFields[contactField]));\n                }\n            }\n            // Also check for any other vendor_ fields that might have been added\n            console.log('VALIDATE FORM - Checking for additional vendor fields');\n            Object.keys(detailFields).forEach((key)=>{\n                if (key.startsWith('vendor_') && key.endsWith('_name')) {\n                    const baseKey = key.replace('vendor_', '').replace('_name', '');\n                    const contactKey = \"vendor_\".concat(baseKey, \"_contact\");\n                    // Skip if we've already counted this vendor\n                    const normalizedPrefix = baseKey === 'makeupArtist' ? 'makeup_artist' : baseKey === 'decorations' ? 'decoration' : baseKey;\n                    console.log(\"VALIDATE FORM - Checking additional vendor \".concat(baseKey, \":\"), {\n                        normalizedPrefix,\n                        alreadyCounted: countedVendors.has(normalizedPrefix),\n                        hasName: !!detailFields[key],\n                        hasContact: !!detailFields[contactKey]\n                    });\n                    if (!countedVendors.has(normalizedPrefix) && detailFields[key] && detailFields[contactKey]) {\n                        validVendorCount++;\n                        countedVendors.add(normalizedPrefix);\n                        console.log(\"VALIDATE FORM - Found additional valid vendor: \".concat(baseKey, \" with name: \").concat(detailFields[key], \" and contact: \").concat(detailFields[contactKey]));\n                    }\n                }\n            });\n            console.log(\"VALIDATE FORM - Total valid vendor count: \".concat(validVendorCount));\n            console.log(\"VALIDATE FORM - Counted vendors: \".concat(Array.from(countedVendors).join(', ')));\n            // Special handling for Edge browser - if we have vendor fields but they're not being counted correctly\n            let edgeVendorNameFields = Object.keys(detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_name'));\n            let edgeVendorContactFields = Object.keys(detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_contact'));\n            console.log('VALIDATE FORM - Edge browser check - Vendor name fields:', edgeVendorNameFields);\n            console.log('VALIDATE FORM - Edge browser check - Vendor contact fields:', edgeVendorContactFields);\n            // If we have at least required vendor name fields and contact fields, but validVendorCount is less than required,\n            // this is likely an Edge browser issue where the fields aren't being properly counted\n            if (validVendorCount < requiredVendorCount && edgeVendorNameFields.length >= requiredVendorCount && edgeVendorContactFields.length >= requiredVendorCount) {\n                console.log('VALIDATE FORM - Edge browser workaround: Found enough vendor fields but they were not counted correctly');\n                console.log('VALIDATE FORM - Vendor name fields:', edgeVendorNameFields.join(', '));\n                console.log('VALIDATE FORM - Vendor contact fields:', edgeVendorContactFields.join(', '));\n                // Count unique vendor prefixes (excluding the _name/_contact suffix)\n                const vendorPrefixSet = new Set();\n                edgeVendorNameFields.forEach((field)=>{\n                    const prefix = field.replace('vendor_', '').replace('_name', '');\n                    if (edgeVendorContactFields.includes(\"vendor_\".concat(prefix, \"_contact\"))) {\n                        vendorPrefixSet.add(prefix);\n                    }\n                });\n                const uniqueVendorCount = vendorPrefixSet.size;\n                console.log(\"VALIDATE FORM - Unique vendor count: \".concat(uniqueVendorCount));\n                if (uniqueVendorCount >= requiredVendorCount) {\n                    console.log(\"VALIDATE FORM - Edge browser workaround: Found at least \".concat(requiredVendorCount, \" unique vendors with both name and contact\"));\n                    validVendorCount = uniqueVendorCount;\n                }\n            }\n            // Log the vendor field counts\n            console.log('VALIDATE FORM - Vendor name fields count:', edgeVendorNameFields.length);\n            console.log('VALIDATE FORM - Vendor contact fields count:', edgeVendorContactFields.length);\n            if (validVendorCount < requiredVendorCount) {\n                console.log('VALIDATE FORM - Validation failed: Not enough vendor details', validVendorCount);\n                const categoryText = videoCategory === 'wedding_vlog' ? 'wedding vlog' : 'my wedding';\n                return {\n                    isValid: false,\n                    error: \"At least \".concat(requiredVendorCount, \" complete vendor detail\").concat(requiredVendorCount > 1 ? 's' : '', \" (with both name and contact) \").concat(requiredVendorCount > 1 ? 'are' : 'is', \" required for \").concat(categoryText, \" videos. You provided \").concat(validVendorCount, \"/\").concat(requiredVendorCount, \".\")\n                };\n            } else {\n                console.log('VALIDATE FORM - Vendor validation passed with', validVendorCount, 'vendors');\n            }\n        }\n        // Just log the detail fields count for now\n        console.log('Detail fields count:', Object.keys(state.detailFields).length);\n        // Log the detail fields that are present\n        console.log('Detail fields present:', Object.keys(state.detailFields));\n        console.log('Detail fields values:', state.detailFields);\n        console.log('Form validation passed');\n        return {\n            isValid: true\n        };\n    };\n    // Start upload with a specific category and video_category (used when correcting the category)\n    const startUploadWithCategory = async (category, videoCategory)=>{\n        console.log(\"Starting upload process with corrected category: \".concat(category));\n        console.log(\"Using video_category: \".concat(videoCategory || 'Not provided'));\n        // Try to get vendor details from localStorage\n        let vendorDetails = {};\n        try {\n            const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n            if (storedVendorDetails) {\n                vendorDetails = JSON.parse(storedVendorDetails);\n                console.log('UPLOAD CONTEXT - Retrieved vendor details from localStorage');\n            }\n        } catch (error) {\n            console.error('UPLOAD CONTEXT - Failed to retrieve vendor details from localStorage:', error);\n        }\n        // Create detail fields from vendor details\n        const detailFields = {\n            ...state.detailFields\n        };\n        // Process vendor details to create detail fields\n        if (Object.keys(vendorDetails).length > 0) {\n            console.log('UPLOAD CONTEXT - Processing vendor details from localStorage:', JSON.stringify(vendorDetails));\n            // Track how many complete vendor details we've added\n            let completeVendorCount = 0;\n            Object.entries(vendorDetails).forEach((param)=>{\n                let [vendorType, details] = param;\n                if (details && details.name && details.mobileNumber) {\n                    detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                    detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                    console.log(\"UPLOAD CONTEXT - Added vendor \".concat(vendorType, \" with name: \").concat(details.name, \" and contact: \").concat(details.mobileNumber));\n                    completeVendorCount++;\n                    // Also set normalized versions\n                    const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                    if (normalizedType !== vendorType) {\n                        detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                        detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                        console.log(\"UPLOAD CONTEXT - Also added normalized vendor \".concat(normalizedType));\n                    }\n                }\n            });\n            console.log(\"UPLOAD CONTEXT - Added \".concat(completeVendorCount, \" complete vendor details from localStorage to detailFields\"));\n            console.log('UPLOAD CONTEXT - Detail fields after adding vendors:', JSON.stringify(detailFields));\n        }\n        // Update the state with the corrected category and video_category if provided\n        const updatedState = {\n            ...state,\n            mediaSubtype: category,\n            category: category,\n            detailFields: detailFields\n        };\n        // If videoCategory is provided, update the detailFields\n        if (videoCategory) {\n            updatedState.detailFields.video_category = videoCategory;\n            console.log(\"Setting video_category in state to: \".concat(videoCategory));\n            // Also store in localStorage\n            try {\n                localStorage.setItem('wedzat_video_category', videoCategory);\n                console.log('UPLOAD CONTEXT - Stored video_category in localStorage:', videoCategory);\n            } catch (error) {\n                console.error('UPLOAD CONTEXT - Failed to store video_category in localStorage:', error);\n            }\n        }\n        // Apply the state update immediately\n        setState(updatedState);\n        // Then start the upload process with the updated category\n        const validation = validateForm();\n        if (!validation.isValid) {\n            console.log('Upload validation failed:', validation.error);\n            setState({\n                ...state,\n                mediaSubtype: category,\n                error: validation.error,\n                step: 'error'\n            });\n            return;\n        }\n        if (!state.file) {\n            console.log('No file to upload');\n            return;\n        }\n        setState({\n            ...state,\n            mediaSubtype: category,\n            isUploading: true,\n            progress: 0,\n            step: 'uploading',\n            error: undefined\n        });\n        try {\n            console.log('UPLOAD CONTEXT - Starting upload process with corrected category...');\n            console.log('UPLOAD CONTEXT - Upload details:', {\n                file: state.file ? state.file.name : 'No file',\n                fileSize: state.file ? Math.round(state.file.size / (1024 * 1024) * 100) / 100 + ' MB' : 'N/A',\n                mediaType: state.mediaType,\n                category: category,\n                title: state.title,\n                videoCategory: videoCategory || 'Not set'\n            });\n            // Log the video_category that will be used\n            console.log('UPLOAD CONTEXT - Using explicit video_category:', videoCategory);\n            console.log('UPLOAD CONTEXT - Video category from detail fields:', state.detailFields.video_category);\n            // Create a copy of the detail fields with the explicit video_category\n            const updatedDetailFields = {\n                ...state.detailFields\n            };\n            // If videoCategory is provided, use it\n            if (videoCategory) {\n                updatedDetailFields.video_category = videoCategory;\n                console.log('UPLOAD CONTEXT - Setting video_category in details to:', videoCategory);\n            }\n            // Use the upload service to handle the complete upload process with the corrected category\n            const result = await _services_api__WEBPACK_IMPORTED_MODULE_2__.uploadService.handleUpload(state.file, state.mediaType, category, state.title, state.description, state.tags, updatedDetailFields, state.duration, state.thumbnail, (progress)=>{\n                setState({\n                    ...state,\n                    mediaSubtype: category,\n                    progress\n                });\n            });\n            // Update the state with the upload result\n            setState({\n                ...state,\n                mediaSubtype: category,\n                isUploading: false,\n                progress: 100,\n                step: 'complete',\n                uploadResult: result\n            });\n        // Upload completed successfully\n        } catch (error) {\n            console.error('Upload failed:', error);\n            setState({\n                ...state,\n                mediaSubtype: category,\n                isUploading: false,\n                error: error instanceof Error ? error.message : 'Unknown error'\n            });\n            throw error;\n        }\n    };\n    // Perform the actual upload without vendor processing\n    const performUpload = async ()=>{\n        console.log('UPLOAD CONTEXT - Starting direct upload for moments');\n        // Simple validation for moments - only check file and title\n        if (!state.file) {\n            console.log('No file to upload');\n            setState({\n                ...state,\n                error: 'No file selected',\n                step: 'error'\n            });\n            return;\n        }\n        if (!state.title || !state.title.trim()) {\n            console.log('No title provided for moments');\n            setState({\n                ...state,\n                error: 'Please provide a title for your upload',\n                step: 'error'\n            });\n            return;\n        }\n        setState({\n            ...state,\n            isUploading: true,\n            progress: 0,\n            step: 'uploading',\n            error: undefined\n        });\n        try {\n            console.log('UPLOAD CONTEXT - Starting upload process for moments...');\n            console.log('UPLOAD CONTEXT - Upload details:', {\n                file: state.file ? state.file.name : 'No file',\n                fileSize: state.file ? Math.round(state.file.size / (1024 * 1024) * 100) / 100 + ' MB' : 'N/A',\n                mediaType: state.mediaType,\n                mediaSubtype: state.mediaSubtype,\n                title: state.title\n            });\n            // Use the upload service to handle the complete upload process\n            const result = await _services_api__WEBPACK_IMPORTED_MODULE_2__.uploadService.handleUpload(state.file, state.mediaType, state.mediaSubtype, state.title, state.description, state.tags, {}, state.duration, state.thumbnail, (progress)=>{\n                console.log(\"Upload progress: \".concat(progress, \"%\"));\n                setState((prevState)=>{\n                    // Only update if the new progress is greater\n                    if (progress > prevState.progress) {\n                        return {\n                            ...prevState,\n                            progress,\n                            // Change to processing state when we reach 80%\n                            step: progress >= 80 && progress < 100 ? 'processing' : prevState.step\n                        };\n                    }\n                    return prevState;\n                });\n            });\n            console.log('UPLOAD CONTEXT - Upload completed successfully for moments:', result);\n            setState({\n                ...state,\n                isUploading: false,\n                progress: 100,\n                step: 'success',\n                error: undefined\n            });\n        // Upload completed successfully\n        } catch (error) {\n            console.error('Upload failed for moments:', error);\n            setState({\n                ...state,\n                isUploading: false,\n                error: error instanceof Error ? error.message : 'Unknown error',\n                step: 'error'\n            });\n            throw error;\n        }\n    };\n    const startUpload = async ()=>{\n        console.log('Starting upload process...');\n        // For moments (stories), skip vendor details processing and go directly to upload\n        if (state.mediaSubtype === 'story') {\n            console.log('UPLOAD CONTEXT - Moments/Stories detected, skipping vendor details processing');\n            await performUpload();\n            return;\n        }\n        // Try to get vendor details from localStorage\n        try {\n            const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n            if (storedVendorDetails) {\n                const vendorDetails = JSON.parse(storedVendorDetails);\n                console.log('UPLOAD CONTEXT - Retrieved vendor details from localStorage for startUpload:', storedVendorDetails);\n                // Create a new detailFields object to hold all the vendor details\n                const updatedDetailFields = {\n                    ...state.detailFields\n                };\n                let completeVendorCount = 0;\n                // Process vendor details to create detail fields\n                if (Object.keys(vendorDetails).length > 0) {\n                    Object.entries(vendorDetails).forEach((param)=>{\n                        let [vendorType, details] = param;\n                        if (details && details.name && details.mobileNumber) {\n                            // Add to the updated detail fields\n                            updatedDetailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                            updatedDetailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                            console.log(\"UPLOAD CONTEXT - Added vendor \".concat(vendorType, \" with name: \").concat(details.name, \" and contact: \").concat(details.mobileNumber));\n                            completeVendorCount++;\n                            // Also set normalized versions\n                            const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                            if (normalizedType !== vendorType) {\n                                updatedDetailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                                updatedDetailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                                console.log(\"UPLOAD CONTEXT - Also added normalized vendor \".concat(normalizedType));\n                            }\n                        }\n                    });\n                    // Update the state with all vendor details at once\n                    setState((prevState)=>({\n                            ...prevState,\n                            detailFields: updatedDetailFields\n                        }));\n                    console.log(\"UPLOAD CONTEXT - Added \".concat(completeVendorCount, \" complete vendor details from localStorage to state\"));\n                    console.log('UPLOAD CONTEXT - Updated detail fields:', JSON.stringify(updatedDetailFields));\n                }\n            }\n        } catch (error) {\n            console.error('UPLOAD CONTEXT - Failed to retrieve vendor details from localStorage:', error);\n        }\n        // Check if we have a video_category for videos\n        if (state.mediaType === 'video') {\n            // Try to get video_category from localStorage if not in state\n            let videoCategory = state.detailFields.video_category;\n            if (!videoCategory) {\n                try {\n                    const storedVideoCategory = localStorage.getItem('wedzat_video_category');\n                    if (storedVideoCategory) {\n                        videoCategory = storedVideoCategory;\n                        console.log('UPLOAD CONTEXT - Retrieved video_category from localStorage:', videoCategory);\n                        setDetailField('video_category', videoCategory);\n                    }\n                } catch (error) {\n                    console.error('UPLOAD CONTEXT - Failed to retrieve video_category from localStorage:', error);\n                }\n            }\n            console.log(\"UPLOAD CONTEXT - Current video_category: \".concat(videoCategory || 'Not set'));\n            // If we don't have a video_category, use a default one\n            if (!videoCategory) {\n                console.log('UPLOAD CONTEXT - No video_category found, using default: my_wedding');\n                // Use startUploadWithCategory to ensure the video_category is properly set\n                return startUploadWithCategory(state.mediaSubtype, 'my_wedding');\n            } else {\n                // Use startUploadWithCategory to ensure the video_category is properly passed\n                console.log(\"UPLOAD CONTEXT - Using existing video_category: \".concat(videoCategory));\n                return startUploadWithCategory(state.mediaSubtype, videoCategory);\n            }\n        }\n        // For photos, just use the regular upload flow\n        const validation = validateForm();\n        if (!validation.isValid) {\n            console.log('Upload validation failed:', validation.error);\n            setState({\n                ...state,\n                error: validation.error,\n                step: 'error'\n            });\n            return;\n        }\n        if (!state.file) {\n            console.log('No file to upload');\n            return;\n        }\n        setState({\n            ...state,\n            isUploading: true,\n            progress: 0,\n            step: 'uploading',\n            error: undefined\n        });\n        try {\n            console.log('UPLOAD CONTEXT - Starting upload process...');\n            console.log('UPLOAD CONTEXT - Upload details:', {\n                file: state.file ? state.file.name : 'No file',\n                fileSize: state.file ? Math.round(state.file.size / (1024 * 1024) * 100) / 100 + ' MB' : 'N/A',\n                mediaType: state.mediaType,\n                mediaSubtype: state.mediaSubtype,\n                title: state.title,\n                videoCategory: state.detailFields.video_category || 'Not set'\n            });\n            // Use the upload service to handle the complete upload process\n            const result = await _services_api__WEBPACK_IMPORTED_MODULE_2__.uploadService.handleUpload(state.file, state.mediaType, state.mediaSubtype, state.title, state.description, state.tags, state.detailFields, state.duration, state.thumbnail, (progress)=>{\n                console.log(\"Upload progress: \".concat(progress, \"%\"));\n                setState((prevState)=>{\n                    // Only update if the new progress is greater\n                    if (progress > prevState.progress) {\n                        return {\n                            ...prevState,\n                            progress,\n                            // Change to processing state when we reach 80%\n                            step: progress >= 80 && progress < 100 ? 'processing' : prevState.step\n                        };\n                    }\n                    return prevState;\n                });\n            });\n            console.log('Upload completed successfully:', result);\n            // Upload complete\n            setState({\n                ...state,\n                isUploading: false,\n                progress: 100,\n                step: 'complete',\n                response: result\n            });\n        } catch (error) {\n            console.error('Upload failed:', error);\n            setState({\n                ...state,\n                isUploading: false,\n                progress: 0,\n                step: 'error',\n                error: error instanceof Error ? error.message : 'Upload failed. Please try again.'\n            });\n        }\n    };\n    const goToStep = (step)=>{\n        setState({\n            ...state,\n            step\n        });\n    };\n    // Set video duration\n    const setDuration = (duration)=>{\n        setState({\n            ...state,\n            duration\n        });\n        console.log(\"Duration set to \".concat(duration, \" seconds\"));\n    };\n    // Set moments flag\n    const setIsMoments = (isMoments)=>{\n        setState({\n            ...state,\n            isMoments\n        });\n        console.log(\"UPLOAD CONTEXT - isMoments set to \".concat(isMoments));\n    };\n    // Effect to initialize the upload context and listen for vendor details updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UploadProvider.useEffect\": ()=>{\n            // For moments (stories), skip localStorage initialization\n            if (state.mediaSubtype === 'story') {\n                console.log('UPLOAD CONTEXT - Moments detected, skipping localStorage initialization');\n                return;\n            }\n            // Check if we have a video_category in localStorage\n            try {\n                const storedVideoCategory = localStorage.getItem('wedzat_video_category');\n                if (storedVideoCategory) {\n                    console.log('UPLOAD CONTEXT - Retrieved video_category from localStorage:', storedVideoCategory);\n                    setDetailField('video_category', storedVideoCategory);\n                }\n            } catch (error) {\n                console.error('UPLOAD CONTEXT - Failed to retrieve video_category from localStorage:', error);\n            }\n            // Add event listener for vendor details updates from API service\n            const handleVendorDetailsUpdate = {\n                \"UploadProvider.useEffect.handleVendorDetailsUpdate\": (event)=>{\n                    if (event.detail) {\n                        console.log('UPLOAD CONTEXT - Received vendor-details-update event:', event.detail);\n                        // Process vendor details from event\n                        const vendorDetails = event.detail;\n                        const updatedDetailFields = {\n                            ...state.detailFields\n                        };\n                        let completeVendorCount = 0;\n                        Object.entries(vendorDetails).forEach({\n                            \"UploadProvider.useEffect.handleVendorDetailsUpdate\": (param)=>{\n                                let [vendorType, details] = param;\n                                if (details && details.name && details.mobileNumber) {\n                                    // Add to detailFields\n                                    updatedDetailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                                    updatedDetailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                                    console.log(\"UPLOAD CONTEXT - Event handler added vendor \".concat(vendorType, \" with name: \").concat(details.name, \" and contact: \").concat(details.mobileNumber));\n                                    completeVendorCount++;\n                                    // Also add normalized versions\n                                    const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                                    if (normalizedType !== vendorType) {\n                                        updatedDetailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                                        updatedDetailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                                        console.log(\"UPLOAD CONTEXT - Event handler also added normalized vendor \".concat(normalizedType));\n                                    }\n                                }\n                            }\n                        }[\"UploadProvider.useEffect.handleVendorDetailsUpdate\"]);\n                        // Update the state with all vendor details at once\n                        setState({\n                            \"UploadProvider.useEffect.handleVendorDetailsUpdate\": (prevState)=>({\n                                    ...prevState,\n                                    detailFields: updatedDetailFields\n                                })\n                        }[\"UploadProvider.useEffect.handleVendorDetailsUpdate\"]);\n                        console.log(\"UPLOAD CONTEXT - Event handler added \".concat(completeVendorCount, \" complete vendor details to state\"));\n                        console.log('UPLOAD CONTEXT - Event handler updated detail fields:', JSON.stringify(updatedDetailFields));\n                    }\n                }\n            }[\"UploadProvider.useEffect.handleVendorDetailsUpdate\"];\n            // Add event listener\n            window.addEventListener('vendor-details-update', handleVendorDetailsUpdate);\n            // Remove event listener on cleanup\n            return ({\n                \"UploadProvider.useEffect\": ()=>{\n                    window.removeEventListener('vendor-details-update', handleVendorDetailsUpdate);\n                }\n            })[\"UploadProvider.useEffect\"];\n        }\n    }[\"UploadProvider.useEffect\"], []);\n    // Create the context value\n    const contextValue = {\n        state,\n        setFile,\n        setThumbnail,\n        setMediaType,\n        setMediaSubtype,\n        setCategory,\n        setTitle,\n        setDescription,\n        addTag,\n        removeTag,\n        setDetailField,\n        setPersonalDetails,\n        setVendorDetails,\n        setDuration,\n        setIsMoments,\n        resetUpload,\n        startUpload,\n        startUploadWithCategory,\n        validateForm,\n        goToStep\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UploadContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\contexts\\\\UploadContexts.tsx\",\n        lineNumber: 1325,\n        columnNumber: 5\n    }, undefined);\n};\n_s(UploadProvider, \"g9yWDQF6ixWa1r5sfsm7YAeGJG4=\");\n_c = UploadProvider;\n// Custom hook to use the context\nconst useUpload = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(UploadContext);\n    if (context === undefined) {\n        throw new Error('useUpload must be used within an UploadProvider');\n    }\n    return context;\n};\n_s1(useUpload, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"UploadProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/UploadContexts.tsx\n"));

/***/ })

});