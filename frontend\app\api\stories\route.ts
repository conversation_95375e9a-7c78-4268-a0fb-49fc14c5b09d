import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

export async function GET(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');

    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization header is required' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = searchParams.get('page') || '1';
    const limit = searchParams.get('limit') || '10';
    const userId = searchParams.get('user_id');

    console.log(`Fetching stories: page=${page}, limit=${limit}, user_id=${userId}`);

    // Build query parameters for backend API
    const queryParams = new URLSearchParams({
      page,
      limit
    });

    if (userId) {
      queryParams.append('user_id', userId);
    }

    // Make API call to backend
    const response = await axios.get(
      `https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/stories?${queryParams.toString()}`,
      {
        headers: {
          'Authorization': authHeader,
          'Content-Type': 'application/json',
        },
        timeout: 15000,
      }
    );

    console.log('Stories API response received:', response.status);
    const res = NextResponse.json(response.data);
    res.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    return res;
  } catch (error: any) {
    console.error('Error fetching stories:', error);

    // Detailed error response
    let errorMessage = 'Failed to fetch stories';
    let statusCode = 500;

    if (error.response) {
      statusCode = error.response.status;
      errorMessage = error.response.data?.error || error.response.data?.message || errorMessage;
      console.error('Backend error response:', error.response.data);
    } else if (error.request) {
      errorMessage = 'No response from backend service';
      console.error('No response received:', error.request);
    } else {
      console.error('Request setup error:', error.message);
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}
