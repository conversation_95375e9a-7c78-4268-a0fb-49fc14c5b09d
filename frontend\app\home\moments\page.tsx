"use client";
import React, { useState, useEffect, useRef } from "react";
import { TopNavigation, SideNavigation, MobileNavigation } from "../../../components/HomeDashboard/Navigation";
import axios from "../../../services/axiosConfig";
import Image from "next/image";
import UserAvatar from "../../../components/HomeDashboard/UserAvatar";
import { useRouter } from "next/navigation";

// Define interface for story items
interface Story {
  content_id: string;
  content_name: string;
  content_url: string;
  content_description?: string;
  thumbnail_url?: string;
  duration?: number;
  created_at: string;
  user_name: string;
  user_id: string;
  user_avatar?: string;
  content_type: 'video' | 'photo';
  is_own_content: boolean;
  viewed?: boolean; // Track if the user has viewed this story
}

interface ApiResponse {
  stories: Story[];
  next_page: boolean;
  total_count: number;
  current_page: number;
}



export default function MomentsPage() {
  const [stories, setStories] = useState<Story[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [isClient, setIsClient] = useState(false);
  const [sidebarExpanded, setSidebarExpanded] = useState(false);
  const router = useRouter();
  // Removed right sidebar state

  useEffect(() => setIsClient(true), []);

  // Fetch stories from API without caching
  useEffect(() => {
    const fetchStories = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get token from localStorage
        const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;

        if (!token) {
          setError('Authentication required');
          setLoading(false);
          return;
        }

        const response = await axios.get<ApiResponse>(`/stories?page=${page}&limit=10`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        if (response.data && response.data.stories) {
          const processedStories = response.data.stories.map(story => {
            const thumbnailUrl = story.content_type === 'photo'
              ? story.content_url
              : (story.thumbnail_url || getDefaultThumbnail(story));
            return {
              ...story,
              viewed: Math.random() > 0.5, // Random viewed status for demo
              thumbnail_url: thumbnailUrl,
              content_url: story.content_url || thumbnailUrl
            };
          });

          if (page === 1) {
            setStories(processedStories);
          } else {
            setStories(prev => [...prev, ...processedStories]);
          }
          setHasMore(response.data.next_page);
        }
      } catch (err) {
        console.error('Error fetching stories:', err);
        setError('Failed to load moments');
      } finally {
        setLoading(false);
      }
    };

    fetchStories();
  }, [page]);

  // Reference for the last item in the list
  const lastStoryRef = useRef<HTMLDivElement | null>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Setup Intersection Observer for lazy loading
  useEffect(() => {
    // Disconnect previous observer if it exists
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    // Don't observe if we're loading or there are no more items
    if (loading || !hasMore) return;

    // Create new observer
    const observer = new IntersectionObserver(
      (entries) => {
        // If the last item is visible and we're not already loading
        if (entries[0]?.isIntersecting && !loading && hasMore) {
          console.log('Last moment is visible, loading more...');
          // Add a small delay to make loading feel more natural
          setTimeout(() => {
            setPage(prevPage => prevPage + 1);
          }, 300);
        }
      },
      { threshold: 0.5, rootMargin: '0px 0px 200px 0px' } // Load when item is 50% visible or 200px before it comes into view
    );

    // Get the last item element
    const lastElement = lastStoryRef.current;
    if (lastElement) {
      observer.observe(lastElement);
    }

    // Save observer to ref
    observerRef.current = observer;

    // Cleanup
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [loading, hasMore, stories.length]);

  // Get default thumbnail based on content type
  const getDefaultThumbnail = (story: Story): string => {
    if (story.content_type === 'video') {
      // Try to extract YouTube thumbnail if it's a YouTube video
      if (story.content_url && story.content_url.includes('youtube')) {
        const videoId = getYoutubeId(story.content_url);
        if (videoId) {
          return `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
        }
      }
      return `/pics/video-placeholder.jpg`;
    } else {
      // For photos, use the content_url directly if available
      return story.content_url || `/pics/placeholder.svg`;
    }
  };

  // Process image URL to handle cloudfront URLs properly
  const processImageUrl = (url: string): string => {
    if (!url) return '/pics/placeholder.svg';

    // If it's already a local URL, return as is
    if (url.startsWith('/')) return url;

    // For cloudfront URLs, ensure they're returned as is
    if (url.includes('cloudfront.net') || url.includes('amazonaws.com')) {
      // console.log('Using CDN URL for story:', url);
      return url;
    }

    // Return the URL as is for other external URLs
    return url;
  };

  // Extract YouTube video ID from URL if needed
  const getYoutubeId = (url: string): string => {
    if (!url) return '';

    // If it's already just an ID, return it
    if (url.length < 20 && !url.includes('/')) return url;

    // Try to extract ID from YouTube URL
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
    const match = url.match(regExp);
    return (match && match[2].length === 11) ? match[2] : '';
  };

  // Handle story click
  const handleStoryClick = (storyId: string) => {
    // Find the index of the clicked story
    const index = stories.findIndex(s => s.content_id === storyId);
    if (index !== -1) {
      router.push(`/home/<USER>/viewer?index=${index}`);
    }
  };

  return (
    <div className={isClient ? "flex flex-col min-h-screen bg-white w-full" : "opacity-0"}>
      {/* Top Navigation Bar */}
      <TopNavigation />

      <div className="flex flex-1 bg-white">
        <SideNavigation
          expanded={sidebarExpanded}
          onExpand={() => setSidebarExpanded(true)}
          onCollapse={() => setSidebarExpanded(false)}
        />

        {/* Main Content */}
        <main
          className={`flex-1 p-4 bg-white mt-20 ${sidebarExpanded ? "md:ml-48" : "md:ml-20"}`}
          style={{
            transition: "all 300ms ease-in-out",
            overflowY: "auto",
            overflowX: "hidden",
          }}
        >
          <div className="max-w-7xl mx-auto">
            <h1 className="text-2xl font-bold mb-6">Moments</h1>

            {/* Loading state for initial load */}
            {loading && page === 1 && (
              <div className="py-10 text-center flex items-center justify-center">
                <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                <span className="text-gray-600">Loading moments</span>
              </div>
            )}

            {/* Error state */}
            {error && !loading && (
              <div className="py-10 text-center text-red-500">{error}</div>
            )}

            {/* Content */}
            {stories.length > 0 && (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
                {stories.map((story, index) => {
                  // Determine if this is the last item to observe
                  const isLastItem = index === stories.length - 1;

                  return (
                  <div
                    key={`${story.content_id}-${index}`}
                    // Apply ref to the last item for intersection observer
                    ref={isLastItem ? lastStoryRef : null}
                    className="flex flex-col items-center cursor-pointer"
                    onClick={() => handleStoryClick(story.content_id)}
                  >
                    <div
                      className={`rounded-full p-1 mb-2 ${
                        !story.viewed
                          ? "bg-gradient-to-tr from-yellow-500 via-red-500 to-purple-600"
                          : "border-2 border-gray-300"
                      }`}
                    >
                      <div className="bg-white p-0.5 rounded-full">
                        <div className="overflow-hidden w-20 h-20 sm:w-24 sm:h-24 rounded-full relative flex items-center justify-center">
                          {/* Display user avatar or first letter of username */}
                          {story.user_avatar ? (
                            <div className="w-full h-full relative">
                              <img
                                src={story.user_avatar}
                                alt={story.user_name || "User"}
                                className="w-full h-full object-cover rounded-full"
                                onError={(e) => {
                                  console.error(`Failed to load user avatar: ${story.user_avatar}`);
                                  // Hide the image and show fallback
                                  const imgElement = e.target as HTMLImageElement;
                                  if (imgElement && imgElement.parentElement) {
                                    imgElement.parentElement.innerHTML = `
                                      <div class="w-full h-full bg-gray-200 rounded-full flex items-center justify-center">
                                        <span class="text-gray-600 font-semibold text-2xl sm:text-3xl">
                                          ${story.user_name ? story.user_name.charAt(0).toUpperCase() : 'U'}
                                        </span>
                                      </div>
                                    `;
                                  }
                                }}
                              />
                            </div>
                          ) : (
                            <div className="w-full h-full bg-gray-200 rounded-full flex items-center justify-center">
                              <span className="text-gray-600 font-semibold text-2xl sm:text-3xl">
                                {story.user_name ? story.user_name.charAt(0).toUpperCase() : 'U'}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    <span className="text-sm font-medium text-center truncate w-full">
                      {story.user_name}
                    </span>
                    <span className="text-xs text-gray-500 truncate w-full text-center">
                      {story.content_name}
                    </span>
                  </div>
                  );
                })}
              </div>
            )}

            {/* Loading more indicator - subtle version */}
            {loading && page > 1 && (
              <div className="py-4 text-center flex items-center justify-center">
                <div className="inline-block w-5 h-5 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                <span className="text-gray-500 text-sm">Loading more</span>
              </div>
            )}

            {/* No more content indicator */}
            {!loading && !hasMore && stories.length > 0 && (
              <div className="py-6 text-center text-gray-500">No more moments to load</div>
            )}

            {/* No content state */}
            {!loading && stories.length === 0 && !error && (
              <div className="py-10 text-center text-gray-500">No moments available</div>
            )}
          </div>
        </main>

        {/* Right sidebar removed */}
      </div>

      <MobileNavigation />
    </div>
  );
}
