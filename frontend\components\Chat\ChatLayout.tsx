"use client";

import React, { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { TopNavigation, SideNavigation } from "../HomeDashboard/Navigation";

type Conversation = {
  conversation_id: string;
  name: string;
  is_group: boolean;
  updated_at: string | null;
  unread_count: number;
  last_message: string | null;
  last_message_time: string | null;
  other_user?: {
    user_id: string;
    name: string;
    profile_picture: string | null;
  };
  participants?: Array<{
    user_id: string;
    name: string;
    profile_picture: string | null;
  }>;
};

interface ChatLayoutProps {
  children: React.ReactNode;
}

export default function ChatLayout({ children }: ChatLayoutProps) {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const params = useParams();
  const activeChatId = params?.chatId as string;
  const [sidebarExpanded, setSidebarExpanded] = useState(false);

  useEffect(() => {
    async function fetchConversations() {
      try {
        // Get auth token from localStorage or your auth provider
        const token =
          localStorage.getItem("token") || sessionStorage.getItem("token");

        if (!token) {
          setError("Authentication token not found");
          setLoading(false);
          return;
        }

        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/conversations`,
          {
            method: "GET",
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (!response.ok) {
          throw new Error(`Failed to fetch conversations: ${response.status}`);
        }

        const responseData = await response.json();
        console.log("API Response:", responseData);

        // Handle different response formats
        let conversationsArray: Conversation[] = [];

        if (responseData.body && typeof responseData.body === "string") {
          // If the API returns data in a body property as a string
          try {
            conversationsArray = JSON.parse(responseData.body);
          } catch (e) {
            console.error("Error parsing conversations from body:", e);
          }
        } else if (Array.isArray(responseData)) {
          // If the API directly returns an array
          conversationsArray = responseData;
        } else if (responseData.data && Array.isArray(responseData.data)) {
          // If the API returns data in a data property
          conversationsArray = responseData.data;
        }

        // Ensure we always have an array
        setConversations(
          Array.isArray(conversationsArray) ? conversationsArray : []
        );
      } catch (error) {
        console.error("Error fetching conversations:", error);
        setError("Failed to load conversations");
      } finally {
        setLoading(false);
      }
    }

    fetchConversations();

    // Set up polling to refresh conversations
    const interval = setInterval(() => {
      fetchConversations();
    }, 15000); // Poll every 15 seconds

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="flex flex-col min-h-screen bg-white w-full">
      {/* Top Navigation Bar */}
      <TopNavigation />

      <div className="flex flex-1 bg-white relative">
        {/* Left Side Navigation */}
        <SideNavigation
          expanded={sidebarExpanded}
          onExpand={() => setSidebarExpanded(true)}
          onCollapse={() => setSidebarExpanded(false)}
        />

        {/* Chat Container - Adjusted to align with left navigation */}
        <div
          className="flex flex-1 bg-gray-100"
          style={{
            marginTop: "84px",
            // minHeight: "calc(100vh - 84px)",
            width: "100%",
            paddingLeft: sidebarExpanded ? "180px" : "80px",
            transition: "all 300ms ease-in-out",
          }}
        >
          {/* Left sidebar - Conversations list - Adjusted to start right of left tab */}
          <div
            className="w-1/3 border-r border-gray-300 bg-white flex flex-col h-full"
            style={{ minWidth: "280px" }}
          >
            <div
              className="p-4 border-b border-gray-300 sticky top-0 z-10"
              style={{
                background:
                  "linear-gradient(179.27deg, #FAE6C4 0.63%, #FFFFFF 149.12%)",
              }}
            >
              <h1 className="text-xl font-semibold text-black">Messages</h1>
            </div>

            {/* Conversations list - independently scrollable */}
            <div
              className="flex-1 overflow-y-auto"
              style={{ maxHeight: "calc(100vh - 140px)" }}
            >
              {loading ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-green-500 mb-4"></div>
                    <p>Loading conversations...</p>
                  </div>
                </div>
              ) : error ? (
                <div className="p-4 text-center">
                  <div className="bg-red-50 text-red-700 p-4 rounded-lg">
                    <p className="font-medium">Error</p>
                    <p>{error}</p>
                    <button
                      onClick={() => window.location.reload()}
                      className="mt-4 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                    >
                      Try Again
                    </button>
                  </div>
                </div>
              ) : !Array.isArray(conversations) ? (
                <div className="p-6 text-center text-red-500">
                  Invalid data format received
                </div>
              ) : conversations.length === 0 ? (
                <div className="p-6 text-center text-gray-500">
                  <div className="mb-4">
                    <svg
                      className="w-16 h-16 mx-auto text-gray-300"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1}
                        d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
                      />
                    </svg>
                  </div>
                  <p>No conversations yet</p>
                  <p className="mt-1 text-sm">
                    Start a new conversation by visiting a profile
                  </p>
                </div>
              ) : (
                conversations.map((conversation) => (
                  <Link
                    key={conversation.conversation_id}
                    href={`/messages/${conversation.conversation_id}`}
                  >
                    <div
                      className={`p-4 hover:bg-gray-50 transition border-b border-gray-100 flex items-center space-x-3 ${
                        activeChatId === conversation.conversation_id ? "" : ""
                      }`}
                      style={{
                        background:
                          activeChatId === conversation.conversation_id
                            ? "linear-gradient(179.27deg, #FAE6C4 0.63%, #FFFFFF 149.12%)"
                            : "",
                      }}
                    >
                      {conversation.is_group ? (
                        // Group chat avatar
                        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                          <span className="text-blue-500 font-medium">
                            {conversation.name?.charAt(0) || "G"}
                          </span>
                        </div>
                      ) : conversation.other_user?.profile_picture ? (
                        // User with profile picture
                        <div className="relative w-12 h-12 rounded-full overflow-hidden flex-shrink-0">
                          <Image
                            src={conversation.other_user.profile_picture}
                            alt={conversation.name || "User"}
                            fill
                            className="object-cover"
                            sizes="48px"
                          />
                        </div>
                      ) : (
                        // User without profile picture
                        <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center flex-shrink-0">
                          <span className="text-gray-500 font-medium">
                            {(
                              conversation.other_user?.name ||
                              conversation.name ||
                              "?"
                            )
                              .charAt(0)
                              .toUpperCase()}
                          </span>
                        </div>
                      )}

                      <div className="flex-1 min-w-0">
                        <div className="flex justify-between items-baseline">
                          <h2 className="text-sm font-medium truncate text-black">
                            {conversation.is_group
                              ? conversation.name
                              : conversation.other_user?.name || "User"}
                          </h2>
                          {conversation.last_message_time && (
                            <span className="text-xs text-gray-500">
                              {formatMessageTime(
                                conversation.last_message_time
                              )}
                            </span>
                          )}
                        </div>

                        <p className="text-sm text-gray-500 truncate">
                          {conversation.last_message || "No messages yet"}
                        </p>
                      </div>

                      {conversation.unread_count > 0 && (
                        <span className="bg-green-500 text-white text-xs font-bold rounded-full h-5 min-w-5 px-1.5 flex items-center justify-center">
                          {conversation.unread_count}
                        </span>
                      )}
                    </div>
                  </Link>
                ))
              )}
            </div>
          </div>

          {/* Right side - Chat content */}
          <div className="w-2/3 flex flex-col h-full overflow-hidden">
            {activeChatId ? (
              children
            ) : (
              <div className="flex-1 flex items-center justify-center bg-gray-50">
                <div className="text-center text-gray-500">
                  <svg
                    className="w-20 h-20 mx-auto text-gray-300 mb-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1}
                      d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                    />
                  </svg>
                  <h2 className="text-xl font-semibold mb-2">
                    Welcome to Messages
                  </h2>
                  <p className="max-w-md mx-auto">
                    Select a conversation from the list to start chatting or
                    visit a profile to start a new conversation.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// Helper function to format message time
function formatMessageTime(timeString: string): string {
  try {
    const date = new Date(timeString);
    const now = new Date();

    // If message is from today, show time
    if (date.toDateString() === now.toDateString()) {
      return date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      });
    }

    // If message is from this year, show month and day
    if (date.getFullYear() === now.getFullYear()) {
      return date.toLocaleDateString([], { month: "short", day: "numeric" });
    }

    // Otherwise show date with year
    return date.toLocaleDateString([], {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  } catch (e) {
    console.error("Error formatting date:", e);
    return timeString;
  }
}
