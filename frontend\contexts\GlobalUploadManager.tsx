// contexts/GlobalUploadManager.tsx
'use client';

import React, { createContext, useState, useContext, ReactNode } from 'react';
import UploadManager from '../components/upload/UploadManager';

interface GlobalUploadContextType {
  isUploadModalOpen: boolean;
  openUploadModal: (initialType?: string, onUploadComplete?: () => void) => void;
  closeUploadModal: () => void;
}

const GlobalUploadContext = createContext<GlobalUploadContextType | undefined>(undefined);

export const GlobalUploadProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [initialType, setInitialType] = useState<string | undefined>(undefined);
  const [onUploadComplete, setOnUploadComplete] = useState<(() => void) | undefined>(undefined);

  const openUploadModal = (type?: string, uploadCompleteCallback?: () => void) => {
    setInitialType(type);
    setOnUploadComplete(() => uploadCompleteCallback);
    setIsUploadModalOpen(true);
  };

  const closeUploadModal = () => {
    setIsUploadModalOpen(false);
    setInitialType(undefined);
    setOnUploadComplete(undefined);
  };

  return (
    <GlobalUploadContext.Provider value={{ isUploadModalOpen, openUploadModal, closeUploadModal }}>
      {children}
      {isUploadModalOpen && <UploadManager onClose={closeUploadModal} initialType={initialType} onUploadComplete={onUploadComplete} />}
    </GlobalUploadContext.Provider>
  );
};

export const useGlobalUpload = (): GlobalUploadContextType => {
  const context = useContext(GlobalUploadContext);
  if (!context) {
    throw new Error('useGlobalUpload must be used within a GlobalUploadProvider');
  }
  return context;
};