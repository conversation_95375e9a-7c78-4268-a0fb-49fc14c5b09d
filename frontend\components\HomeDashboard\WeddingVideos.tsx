"use client";
import React, { useState, useRef, useEffect } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import UserAvatar from "./UserAvatar";
import axios from "../../services/axiosConfig";
import { useLocation } from "../../contexts/LocationContext";
import {
  MdFavorite,
  MdFavoriteBorder,
  MdOutlineModeComment,
  MdOutlineShare,
  MdChevronLeft,
  MdChevronRight,
} from "react-icons/md";

// Define interface for movie video items
interface MovieVideo {
  video_id: string;
  video_name: string;
  video_url: string;
  video_description?: string;
  video_thumbnail?: string;
  video_duration?: number;
  video_category?: string;
  created_at: string;
  user_name?: string;
  user_id?: string; // Added user_id field
  is_own_content?: boolean;
  video_views?: number;
  video_likes?: number;
  video_comments?: number;
}

interface ApiResponse {
  movies: MovieVideo[];
  next_page: boolean;
  total_count: number;
  current_page: number;
}

interface WeddingVideosProps {
  shouldLoad?: boolean;
}

const WeddingVideosSection: React.FC<WeddingVideosProps> = ({ shouldLoad = false }) => {
  const router = useRouter();
  const { selectedLocation } = useLocation();
  const [likedVideos, setLikedVideos] = useState<string[]>([]);
  const [admiringUsers, setAdmiringUsers] = useState<Record<string, boolean>>({});
  const [weddingVideos, setWeddingVideos] = useState<MovieVideo[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const [initialLoadComplete, setInitialLoadComplete] = useState<boolean>(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const loadMoreTriggerRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Fallback data if API fails - using empty array
  const getFallbackMovies = (): MovieVideo[] => [];

  // Function to fetch movies from the API
  const fetchMovies = async (pageNumber: number, isInitialLoad: boolean = false) => {
    // Prevent multiple simultaneous requests
    if ((isInitialLoad && loading) || (!isInitialLoad && loadingMore)) {
      console.log('Request already in progress, skipping');
      return;
    }

    try {
      if (isInitialLoad) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      console.log(`Fetching movies for page ${pageNumber}...`);

      // Simple token retrieval
      const token = localStorage.getItem('token');

      console.log(`Auth token found: ${token ? 'Yes' : 'No'}`);

      if (!token) {
        console.warn('No authentication token found');
        setError('Authentication required');
        setWeddingVideos([]);
        return;
      }

      // Build API URL with location parameter if selected
      let apiUrl = `/movies?page=${pageNumber}&limit=10`;
      if (selectedLocation) {
        apiUrl += `&location=${encodeURIComponent(selectedLocation)}`;
      }

      // Make API request
      const response = await axios.get<ApiResponse>(apiUrl, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      console.log('API response status:', response.status);

      // Process the data
      if (response.data && response.data.movies) {
        console.log(`Loaded ${response.data.movies.length} movies for page ${pageNumber}`);

        // Log the first item for debugging
        if (response.data.movies.length > 0) {
          console.log('Sample movie data:', response.data.movies[0]);
        }

        // Process the response
        const processedMovies = response.data.movies.map(movie => {
          if (!movie.video_thumbnail) {
            console.log(`Movie missing thumbnail: ${movie.video_id}`);
          }
          // Ensure user_id is set - in case the API doesn't provide it
          if (!movie.user_id && movie.user_name) {
            // Create a temporary user_id based on username if not provided
            // This is a fallback and should be replaced with actual user IDs from the API
            console.log(`Movie missing user_id, creating temporary one from username: ${movie.user_name}`);
            movie.user_id = `user-${movie.user_name.toLowerCase().replace(/\s+/g, '-')}`;
          }
          return movie;
        });

        if (pageNumber === 1) {
          setWeddingVideos(processedMovies);
        } else {
          setWeddingVideos(prev => [...prev, ...processedMovies]);
        }

        setHasMore(response.data.next_page);
        setPage(pageNumber); // Update the current page
        setError(null); // Clear any previous errors
      } else {
        console.warn('Unexpected response format:', response.data);
        setError('Failed to load wedding videos - unexpected response format');
        setWeddingVideos([]);
      }
    } catch (err) {
      console.error('API request failed:', err);
      setError('Failed to load wedding videos');
      setWeddingVideos([]);
    } finally {
      // Always clear loading states
      if (isInitialLoad) {
        setLoading(false);
        console.log('Initial loading complete');
      } else {
        setLoadingMore(false);
        console.log('Loading more complete');
      }
    }
  };

  // Fetch first page of movies as soon as the component mounts
  useEffect(() => {
    // Only trigger when shouldLoad changes to true
    if (shouldLoad) {
      console.log('Wedding Videos component is now visible and ready to load data');

      // Set a flag to track initial load
      setInitialLoadComplete(true);

      // IMMEDIATE API request with no conditions or delays
      console.log('Triggering initial movies load IMMEDIATELY...');
      setLoading(true);

      // Skip the fetchMovies function and make the API call directly here
      const token = localStorage.getItem('token');

      if (token) {
        // Build API URL with location parameter if selected
        let apiUrl = `/movies?page=1&limit=10`;
        if (selectedLocation) {
          apiUrl += `&location=${encodeURIComponent(selectedLocation)}`;
        }

        // Make direct API request
        console.log('Making direct API request for movies page 1...');
        axios.get<ApiResponse>(apiUrl, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        })
          .then(response => {
            console.log('API response received for movies page 1');
            if (response.data && response.data.movies) {
              setWeddingVideos(response.data.movies);
              setHasMore(response.data.next_page);
              setPage(1);
              setError(null);
            } else {
              setWeddingVideos([]);
              setError('No wedding videos found');
            }
          })
          .catch(err => {
            console.error('Direct API request failed:', err);
            setError('Failed to load wedding videos');
            setWeddingVideos([]);
          })
          .finally(() => {
            setLoading(false);
            console.log('Initial loading complete');
          });
      } else {
        setLoading(false);
        setError('Authentication required');
      }
    }
  }, [shouldLoad]); // Only depend on shouldLoad

  // Failsafe to ensure content is loaded - only show error after timeout
  useEffect(() => {
    // If we're stuck in loading state for more than 10 seconds, show error
    let timeoutId: NodeJS.Timeout | null = null;

    if (loading && initialLoadComplete) {
      timeoutId = setTimeout(() => {
        console.log('Loading timeout reached - API request may have failed');
        setLoading(false);
        if (weddingVideos.length === 0) {
          setError('Unable to load wedding videos. Please check your network connection.');
        }
      }, 10000); // 10 second timeout for slow networks
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [loading, initialLoadComplete, weddingVideos.length]);

  // Setup Intersection Observer for horizontal lazy loading
  useEffect(() => {
    // Only set up observer after initial load and if there's more content to fetch
    if (!initialLoadComplete || !hasMore) return;

    // Disconnect previous observer if it exists
    if (observerRef.current) {
      observerRef.current.disconnect();
      console.log('Disconnected previous intersection observer');
    }

    // Create new observer
    observerRef.current = new IntersectionObserver(
      (entries) => {
        // If the trigger element is visible and we're not already loading
        if (entries[0]?.isIntersecting && !loading && !loadingMore && hasMore) {
          console.log('Load more trigger is visible, loading next page...');
          console.log('Intersection ratio:', entries[0].intersectionRatio);
          const nextPage = page + 1;
          fetchMovies(nextPage, false);
        }
      },
      // Use the scroll container as the root for the intersection observer
      {
        root: scrollContainerRef.current,
        threshold: 0.1, // Trigger when 10% of the element is visible
        rootMargin: '0px 150px 0px 0px' // Add margin to the right to load earlier
      }
    );

    // Start observing the trigger element
    if (loadMoreTriggerRef.current) {
      observerRef.current.observe(loadMoreTriggerRef.current);
      console.log('Now observing load more trigger element');
    } else {
      console.warn('Load more trigger element not found');
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
        console.log('Disconnected intersection observer');
      }
    };
  }, [hasMore, loading, loadingMore, page, initialLoadComplete]);

  // Load admiring status from localStorage when component mounts
  useEffect(() => {
    try {
      const admiredUsersJson = localStorage.getItem('admiredUsers');
      if (admiredUsersJson) {
        const admiredUsers = JSON.parse(admiredUsersJson);
        setAdmiringUsers(admiredUsers);
        console.log('Loaded admiring status from localStorage:', admiredUsers);
      }
    } catch (error) {
      console.error('Error loading admiring status from localStorage:', error);
    }
  }, []);

  // Function to navigate to a user's profile
  const navigateToUserProfile = (userId?: string, username?: string) => {
    // If we have a userId, make a direct API call to the user profile endpoint
    if (userId) {
      // Get the token from localStorage
      const token = localStorage.getItem('token') ||
        localStorage.getItem('jwt_token') ||
        localStorage.getItem('wedzat_token');

      if (!token) {
        console.warn('No authentication token found');
        return;
      }

      // Navigate to the profile page with the userId
      router.push(`/profile/${userId}`);
    }
    else {
      console.warn('Cannot navigate to profile: missing both userId and username');
    }
  };

  const toggleLike = (videoId: string) => {
    setLikedVideos((prev) =>
      prev.includes(videoId)
        ? prev.filter((id) => id !== videoId)
        : [...prev, videoId]
    );
  };

  // Function to handle Admire button click
  const handleAdmire = async (userId?: string) => {
    if (!userId) {
      console.warn('No user ID provided for Admire action');
      return;
    }

    // Check if already admiring this user
    const isCurrentlyAdmiring = admiringUsers[userId] || false;

    try {
      // Get token from localStorage
      const token = localStorage.getItem('token') ||
        localStorage.getItem('jwt_token') ||
        localStorage.getItem('wedzat_token');

      if (!token) {
        console.warn('No authentication token found');
        return;
      }

      // Optimistically update UI state
      setAdmiringUsers(prev => ({
        ...prev,
        [userId]: !isCurrentlyAdmiring
      }));

      // Make API call to follow/unfollow user
      const endpoint = isCurrentlyAdmiring
        ? 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/unfollow'
        : 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/follow';

      console.log(`Making request to ${endpoint} with user ID: ${userId}`);

      try {
        // Make the API call
        const response = await axios.post(
          endpoint,
          { target_user_id: userId },
          {
            headers: {
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }
        );

        console.log('API response:', response.data);

        // Update localStorage with the new state
        try {
          const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';
          const admiredUsers = JSON.parse(admiredUsersJson);

          if (!isCurrentlyAdmiring) {
            admiredUsers[userId] = true;
          } else {
            delete admiredUsers[userId];
          }

          localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));
          console.log('Updated admired users in localStorage:', admiredUsers);
        } catch (storageError) {
          console.error('Error updating localStorage:', storageError);
        }
      } catch (apiError: any) {
        console.error(`Error ${isCurrentlyAdmiring ? 'unadmiring' : 'admiring'} user:`, apiError);

        // Revert UI state on error
        setAdmiringUsers(prev => ({
          ...prev,
          [userId]: isCurrentlyAdmiring
        }));
      }
    } catch (error) {
      console.error('Unexpected error in handleAdmire:', error);

      // Revert UI state on unexpected errors
      setAdmiringUsers(prev => ({
        ...prev,
        [userId]: isCurrentlyAdmiring
      }));
    }
  };

  const handleManualScroll = (direction: "left" | "right") => {
    if (scrollContainerRef.current) {
      const scrollAmount = direction === "left" ? -694 : 694; // Width + gap
      scrollContainerRef.current.scrollBy({
        left: scrollAmount,
        behavior: "smooth",
      });
    }
  };

  // Get appropriate image source for a movie
  const getImageSource = (video: MovieVideo): string => {
    if (video.video_thumbnail) {
      return video.video_thumbnail;
    }
    return '/pics/placeholder.svg';
  };

  return (
    <section className="mb-8 relative">
      <div className="flex items-center justify-between mb-4 px-2">
        <h2 className="font-inter text-[20px] leading-[18px] font-semibold text-[#000000]">
          MOVIES
        </h2>
        <a
          href="/home/<USER>"
          className="text-red-500 text-sm font-medium hover:underline z-10 relative ml-auto mr-1"
        >
          See all
        </a>
      </div>

      {/* Loading state */}
      {loading && (
        <div className="py-10 text-center">
          <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
          <span className="text-gray-600">Loading wedding videos...</span>
        </div>
      )}

      {/* Error state */}
      {error && !loading && (
        <div className="py-10 text-center">
          <div className="text-red-500 mb-4">{error}</div>
          <button
            onClick={() => {
              console.log('Retrying wedding videos load...');
              setError(null);
              fetchMovies(1, true);
            }}
            className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm"
          >
            Retry
          </button>
        </div>
      )}

      {/* Debug info in development only */}
      {/* {process.env.NODE_ENV === 'development' && (
        <div className="absolute top-0 right-0 bg-black/50 text-white text-xs p-2 rounded-bl-md z-50 max-w-[200px]">
          <div>State: {loading ? 'Loading' : error ? 'Error' : weddingVideos.length === 0 ? 'Empty' : 'Loaded'}</div>
          <div>Count: {weddingVideos.length}</div>
          <div>Page: {page}</div>
          <div>Has more: {hasMore ? 'Yes' : 'No'}</div>
        </div>
      )} */}

      {/* Content */}
      {!loading && !error && (
        <>
          {/* Empty state message when no videos */}
          {weddingVideos.length === 0 && (
            <div className="flex items-center justify-center h-[220px] w-full">
              <div className="text-gray-400">No wedding videos available</div>
            </div>
          )}

          {weddingVideos.length > 0 && (
            <>
              {/* Left scroll button */}
              <button
                onClick={() => handleManualScroll("left")}
                className="absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-white/80 rounded-full p-2 shadow-md hover:bg-white"
                style={{ marginLeft: "-12px" }}
              >
                <MdChevronLeft size={24} />
              </button>

              <div
                ref={scrollContainerRef}
                className="overflow-x-auto scrollbar-hide relative w-full"
                style={{
                  WebkitOverflowScrolling: "touch",
                  scrollbarWidth: "none",
                  msOverflowStyle: "none",
                  scrollBehavior: 'smooth'
                }}
              >
                <div className="flex gap-3 pb-5 flex-nowrap w-full">
                  {weddingVideos.map((video, index) => (
                    <div
                      key={`${video.video_id}-${index}`}
                      className="flex-shrink-0 w-[85vw] sm:w-[75vw] md:w-[65vw] lg:w-[55vw] h-[300px] sm:h-[400px] md:h-[450px] lg:h-[500px] max-w-[676px] border-b border-[#DBDBDB] pb-[15px] sm:pb-[21px]"
                    >
                      {/* Section 1 - User Info */}
                      <div
                        className="w-full h-[40px] sm:h-[50px] md:h-[56px] flex justify-between items-center p-[5px] sm:p-[8px_5px]"
                      >
                        <div
                          className="flex items-center gap-[10px]"
                        >
                          <div
                            className="w-[34px] h-[34px] rounded-[27px] border cursor-pointer"
                            onClick={() => navigateToUserProfile(video.user_id, video.user_name)}
                          >
                            <UserAvatar
                              username={video.user_name || "user"}
                              size="sm"
                              onClick={() => navigateToUserProfile(video.user_id, video.user_name)}
                            />
                          </div>

                          <div
                            className="w-full max-w-[380px] h-[40px] pt-[7px] pr-[50px] sm:pr-[100px] md:pr-[150px] lg:pr-[200px] pb-[10px]"
                          >
                            <span
                              style={{
                                fontFamily: "Inter",
                                fontWeight: 600,
                                fontSize: "14px",
                                lineHeight: "18px",
                                letterSpacing: "0%",
                                verticalAlign: "middle",
                                cursor: "pointer",
                              }}
                              onClick={() => navigateToUserProfile(video.user_id, video.user_name)}
                              className="hover:underline"
                            >
                              {video.user_name || "user"}
                            </span>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => handleAdmire(video.user_id)}
                            className="flex items-center bg-[#B31B1E] text-white px-3 py-1 rounded-full text-sm font-medium hover:bg-red-700 transition-colors"
                          >
                            {admiringUsers[video.user_id || ''] ? 'Admiring' : 'Admire'}
                            {!admiringUsers[video.user_id || ''] && <span className="ml-1">+</span>}
                          </button>
                          <button>•••</button>
                        </div>
                      </div>

                      {/* Section 2 - Video */}
                      <div
                        className="w-[85vw] sm:w-[75vw] md:w-[65vw] lg:w-[55vw] h-[180px] sm:h-[250px] md:h-[300px] lg:h-[350px] max-w-[676px] rounded-[10px] border p-[1px] relative"
                      >
                        {/* Video Thumbnail */}
                        <div className="relative w-full h-full rounded-[10px] overflow-hidden">
                          <Image
                            src={getImageSource(video)}
                            alt={video.video_name || "Wedding Video"}
                            fill
                            sizes="(max-width: 480px) 300px, (max-width: 640px) 350px, (max-width: 768px) 450px, (max-width: 1024px) 550px, 676px"
                            className="object-cover"
                            {...(index < 2 ? { priority: true } : { loading: 'lazy' })} // Priority for first two, lazy loading for rest
                            unoptimized={true} // Skip optimization for all images to avoid issues
                            placeholder="blur" // Show blur placeholder while loading
                            blurDataURL="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImciPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iMjAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNmZmYiIG9mZnNldD0iNTAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iNzAlIiAvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHdpZHRoPSI3MDAiIGhlaWdodD0iNDc1IiBmaWxsPSIjZWVlIiAvPjxyZWN0IGlkPSJyIiB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgZmlsbD0idXJsKCNnKSIgLz48YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTcwMCIgdG89IjcwMCIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPjwvc3ZnPg==" // Base64 encoded SVG loading animation
                            onError={(e) => {
                              console.error(`Failed to load image for video: ${video.video_name}`);
                              // Use placeholder as fallback
                              const imgElement = e.target as HTMLImageElement;
                              if (imgElement) {
                                imgElement.src = '/pics/placeholder.svg';
                              }
                            }}
                          />
                        </div>

                        {/* Play Button Overlay */}
                        <div
                          style={{
                            position: "absolute",
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            zIndex: 2,
                            borderRadius: "10px",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            cursor: "pointer",
                            backgroundColor: "rgba(0, 0, 0, 0.2)",
                          }}
                          onClick={() => {
                            // Navigate to the movie detail page
                            if (video.video_id) {
                              console.log(`Navigating to movie detail page: ${video.video_id}`);
                              window.location.href = `/home/<USER>/${video.video_id}`;
                            }
                          }}
                        >
                          <div className="w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 rounded-full bg-red-500 bg-opacity-80 flex items-center justify-center">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="28"
                              height="28"
                              viewBox="0 0 24 24"
                              fill="white"
                            >
                              <path d="M8 5v14l11-7z" />
                            </svg>
                          </div>
                        </div>
                      </div>                      {/* Video description */}
                      <div
                        style={{
                          padding: "12px 0",
                          fontSize: "14px",
                        }}
                      >
                        <span>{video.video_description || video.video_name}</span>
                      </div>
                    </div>
                  ))}

                  {/* Load more trigger element - this is what IntersectionObserver watches */}
                  {hasMore && (
                    <div
                      ref={loadMoreTriggerRef}
                      className="flex-shrink-0 w-10 h-full opacity-0"
                      style={{
                        position: 'relative',
                        // Add debug outline in development
                        outline: process.env.NODE_ENV === 'development' ? '1px dashed rgba(255, 0, 0, 0.3)' : 'none'
                      }}
                      aria-hidden="true"
                      data-testid="wedding-videos-load-more-trigger"
                    />
                  )}

                  {/* Loading indicator - only show when loading more */}
                  {loadingMore && (
                    <div className="flex-shrink-0 flex items-center justify-center min-w-[100px] h-[300px] sm:h-[400px] md:h-[450px] lg:h-[500px]">
                      <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin"></div>
                      <span className="ml-2 text-sm text-gray-600">Loading more...</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Right scroll button */}
              <button
                onClick={() => handleManualScroll("right")}
                className="absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-white/80 rounded-full p-2 shadow-md hover:bg-white"
                style={{ marginRight: "-12px" }}
              >
                <MdChevronRight size={24} />
              </button>
            </>
          )}
        </>
      )}

      <style jsx>{`
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
      `}</style>
    </section>
  );
};

export default WeddingVideosSection;