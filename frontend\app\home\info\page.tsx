"use client";
import React, { useState, useEffect } from "react";
import {
  TopNavigation,
  SideNavigation,
} from "../../../components/HomeDashboard/Navigation";
import Image from "next/image";

export default function InfoPage() {
  const [sidebarExpanded, setSidebarExpanded] = useState(false);
  const [activeTab, setActiveTab] = useState("vendors");
  const [isClient, setIsClient] = useState(false);

  // Use this effect to prevent hydration mismatch
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Function to render the content based on active tab
  const renderContent = () => {
    switch (activeTab) {
      case "vendors":
        return <VendorsContent />;
      case "style":
        return <StyleOfWeddingContent />;
      case "history":
        return <HistoryContent />;
      case "settings":
        return <ProfileSettingsContent />;
      case "switch":
        return <SwitchAccountContent />;
      default:
        return <VendorsContent />;
    }
  };

  if (!isClient) {
    return null; // Don't render anything on the server
  }

  return (
    <div className="flex flex-col min-h-screen bg-white w-full">
      {/* Top Navigation Bar */}
      <TopNavigation />

      <div className="flex flex-1 bg-white">
        <SideNavigation
          expanded={sidebarExpanded}
          onExpand={() => setSidebarExpanded(true)}
          onCollapse={() => setSidebarExpanded(false)}
        />

        {/* Main Content */}
        <main
          className={`flex-1 p-4 bg-white mt-20 ${sidebarExpanded ? "md:ml-48" : "md:ml-20"
            }`}
          style={{
            transition: "all 300ms ease-in-out",
            overflowY: "auto",
            overflowX: "hidden",
          }}
        >
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-6">
              <h1 className="text-2xl font-bold text-black">Information</h1>
              <button className="bg-red-600 text-white px-4 py-2 rounded-md flex items-center">
                Save
                <svg
                  className="ml-2 w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"
                  ></path>
                </svg>
              </button>
            </div>

            <div
              className="bg-white overflow-hidden border border-gray-200"
              style={{
                width: "1124px",
                height: "503px",
                borderRadius: "3px",
                borderWidth: "1px",
                padding: "1px",
                maxWidth: "100%",
                margin: "0 auto"
              }}
            >
              <div className="flex h-full">
                {/* Left sidebar with tabs */}
                <div className="w-64 border-r border-gray-200">
                  <ul>
                    <li
                      className={`py-3 px-4 cursor-pointer ${activeTab === "vendors"
                        ? "border-l-4 border-red-600 bg-gray-50"
                        : ""
                        }`}
                      onClick={() => setActiveTab("vendors")}
                    >
                      <span className={activeTab === "vendors" ? "text-red-600 font-medium" : ""}>
                        Vendors
                      </span>
                    </li>
                    <li
                      className={`py-3 px-4 cursor-pointer ${activeTab === "style"
                        ? "border-l-4 border-red-600 bg-gray-50"
                        : ""
                        }`}
                      onClick={() => setActiveTab("style")}
                    >
                      <span className={activeTab === "style" ? "text-red-600 font-medium" : ""}>
                        Style of Wedding
                      </span>
                    </li>
                    <li
                      className={`py-3 px-4 cursor-pointer ${activeTab === "history"
                        ? "border-l-4 border-red-600 bg-gray-50"
                        : ""
                        }`}
                      onClick={() => setActiveTab("history")}
                    >
                      <span className={activeTab === "history" ? "text-red-600 font-medium" : ""}>
                        History
                      </span>
                    </li>
                    <li
                      className={`py-3 px-4 cursor-pointer ${activeTab === "settings"
                        ? "border-l-4 border-red-600 bg-gray-50"
                        : ""
                        }`}
                      onClick={() => setActiveTab("settings")}
                    >
                      <span className={activeTab === "settings" ? "text-red-600 font-medium" : ""}>
                        Profile Settings
                      </span>
                    </li>
                    <li
                      className={`py-3 px-4 cursor-pointer ${activeTab === "switch"
                        ? "border-l-4 border-red-600 bg-gray-50"
                        : ""
                        }`}
                      onClick={() => setActiveTab("switch")}
                    >
                      <span className={activeTab === "switch" ? "text-red-600 font-medium" : ""}>
                        Switch to Other account
                      </span>
                    </li>
                  </ul>
                </div>

                {/* Right content area */}
                <div className="flex-1 p-6 overflow-y-auto">
                  {renderContent()}
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}

// Content components for each tab
const VendorsContent = () => {
  return (
    <div className="w-full h-full">
      <p className="text-gray-600 mb-6">
        Helps to Discover your Vendor by using the Filter you're known by.
      </p>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="relative">
          <select
            className="w-full p-4 pr-10 border border-gray-300 rounded-md appearance-none focus:outline-none focus:ring-1 focus:ring-red-600 focus:border-transparent"
            style={{ borderRadius: "3px", borderWidth: "1px" }}
          >
            <option>Photography</option>
            <option>Wedding Photography</option>
            <option>Pre-Wedding Photography</option>
            <option>Candid Photography</option>
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center px-3 pointer-events-none">
            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </div>
        <div className="relative">
          <select
            className="w-full p-4 pr-10 border border-gray-300 rounded-md appearance-none focus:outline-none focus:ring-1 focus:ring-red-600 focus:border-transparent"
            style={{ borderRadius: "3px", borderWidth: "1px" }}
          >
            <option>Mehendi</option>
            <option>Bridal Mehendi</option>
            <option>Arabic Mehendi</option>
            <option>Traditional Mehendi</option>
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center px-3 pointer-events-none">
            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </div>
        <div className="relative">
          <select
            className="w-full p-4 pr-10 border border-gray-300 rounded-md appearance-none focus:outline-none focus:ring-1 focus:ring-red-600 focus:border-transparent"
            style={{ borderRadius: "3px", borderWidth: "1px" }}
          >
            <option>Make up Artists</option>
            <option>Bridal Makeup</option>
            <option>Celebrity Makeup</option>
            <option>HD Makeup</option>
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center px-3 pointer-events-none">
            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </div>
        <div className="relative">
          <select
            className="w-full p-4 pr-10 border border-gray-300 rounded-md appearance-none focus:outline-none focus:ring-1 focus:ring-red-600 focus:border-transparent"
            style={{ borderRadius: "3px", borderWidth: "1px" }}
          >
            <option>Outfits</option>
            <option>Bridal Wear</option>
            <option>Groom Wear</option>
            <option>Designer Outfits</option>
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center px-3 pointer-events-none">
            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </div>
        <div className="relative">
          <select
            className="w-full p-4 pr-10 border border-gray-300 rounded-md appearance-none focus:outline-none focus:ring-1 focus:ring-red-600 focus:border-transparent"
            style={{ borderRadius: "3px", borderWidth: "1px" }}
          >
            <option>Venue</option>
            <option>Banquet Halls</option>
            <option>Hotels</option>
            <option>Destination Venues</option>
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center px-3 pointer-events-none">
            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </div>
        <div className="relative">
          <select
            className="w-full p-4 pr-10 border border-gray-300 rounded-md appearance-none focus:outline-none focus:ring-1 focus:ring-red-600 focus:border-transparent"
            style={{ borderRadius: "3px", borderWidth: "1px" }}
          >
            <option>Decoration</option>
            <option>Floral Decoration</option>
            <option>Lighting</option>
            <option>Theme Decoration</option>
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center px-3 pointer-events-none">
            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </div>
        <div className="relative">
          <select
            className="w-full p-4 pr-10 border border-gray-300 rounded-md appearance-none focus:outline-none focus:ring-1 focus:ring-red-600 focus:border-transparent"
            style={{ borderRadius: "3px", borderWidth: "1px" }}
          >
            <option>Event Planners</option>
            <option>Wedding Planners</option>
            <option>Destination Wedding Planners</option>
            <option>Celebrity Wedding Planners</option>
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center px-3 pointer-events-none">
            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </div>
        <div className="relative">
          <select
            className="w-full p-4 pr-10 border border-gray-300 rounded-md appearance-none focus:outline-none focus:ring-1 focus:ring-red-600 focus:border-transparent"
            style={{ borderRadius: "3px", borderWidth: "1px" }}
          >
            <option>Music & Dance</option>
            <option>DJ</option>
            <option>Live Band</option>
            <option>Choreographers</option>
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center px-3 pointer-events-none">
            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </div>
      </div>
    </div>
  );
};

const StyleOfWeddingContent = () => {
  return (
    <div className="w-full h-full">
      <p className="text-gray-600 mb-6">
        Choose your Style of Wedding Video Category by using the Filter for Discover More Related videos.
      </p>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="relative">
          <select
            className="w-full p-4 pr-10 border border-gray-300 rounded-md appearance-none focus:outline-none focus:ring-1 focus:ring-red-600 focus:border-transparent"
            style={{ borderRadius: "3px", borderWidth: "1px" }}
          >
            <option>Religion</option>
            <option>Hindu</option>
            <option>Muslim</option>
            <option>Christian</option>
            <option>Sikh</option>
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center px-3 pointer-events-none">
            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </div>
        <div className="relative">
          <select
            className="w-full p-4 pr-10 border border-gray-300 rounded-md appearance-none focus:outline-none focus:ring-1 focus:ring-red-600 focus:border-transparent"
            style={{ borderRadius: "3px", borderWidth: "1px" }}
          >
            <option>Wedding Style</option>
            <option>Traditional</option>
            <option>Contemporary</option>
            <option>Destination Wedding</option>
            <option>Royal Wedding</option>
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center px-3 pointer-events-none">
            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </div>
        <div className="relative">
          <select
            className="w-full p-4 pr-10 border border-gray-300 rounded-md appearance-none focus:outline-none focus:ring-1 focus:ring-red-600 focus:border-transparent"
            style={{ borderRadius: "3px", borderWidth: "1px" }}
          >
            <option>State</option>
            <option>Maharashtra</option>
            <option>Delhi</option>
            <option>Karnataka</option>
            <option>Tamil Nadu</option>
            <option>Gujarat</option>
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center px-3 pointer-events-none">
            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </div>
        <div className="relative">
          <select
            className="w-full p-4 pr-10 border border-gray-300 rounded-md appearance-none focus:outline-none focus:ring-1 focus:ring-red-600 focus:border-transparent"
            style={{ borderRadius: "3px", borderWidth: "1px" }}
          >
            <option>Season</option>
            <option>Summer</option>
            <option>Winter</option>
            <option>Spring</option>
            <option>Monsoon</option>
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center px-3 pointer-events-none">
            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </div>
      </div>
    </div>
  );
};

const HistoryContent = () => {
  return (
    <div className="w-full h-full">
      <p className="text-gray-600 mb-6">Your history will be displayed here.</p>
      <div className="border border-gray-200 rounded-md p-6" style={{ borderRadius: "3px", borderWidth: "1px" }}>
        <div className="flex items-center justify-center h-64">
          <p className="text-gray-500 text-center">No history records found. Your recent activities will appear here.</p>
        </div>
      </div>
    </div>
  );
};

const ProfileSettingsContent = () => {
  return (
    <div className="w-full h-full">
      <p className="text-gray-600 mb-6">Manage your profile settings and preferences.</p>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="relative">
          <label className="block text-sm font-medium text-gray-700 mb-1">Display Name</label>
          <input
            type="text"
            className="w-full p-4 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-red-600 focus:border-transparent"
            style={{ borderRadius: "3px", borderWidth: "1px" }}
            placeholder="Your Name"
          />
        </div>
        <div className="relative">
          <label className="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
          <input
            type="email"
            className="w-full p-4 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-red-600 focus:border-transparent"
            style={{ borderRadius: "3px", borderWidth: "1px" }}
            placeholder="<EMAIL>"
          />
        </div>
        <div className="relative">
          <label className="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
          <input
            type="tel"
            className="w-full p-4 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-red-600 focus:border-transparent"
            style={{ borderRadius: "3px", borderWidth: "1px" }}
            placeholder="+91 XXXXX XXXXX"
          />
        </div>
        <div className="relative">
          <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
          <input
            type="text"
            className="w-full p-4 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-red-600 focus:border-transparent"
            style={{ borderRadius: "3px", borderWidth: "1px" }}
            placeholder="City, State"
          />
        </div>
      </div>
    </div>
  );
};

const SwitchAccountContent = () => {
  return (
    <div className="w-full h-full">
      <p className="text-gray-600 mb-6">Switch between your different accounts.</p>
      <div className="space-y-4">
        <div className="border border-gray-200 rounded-md p-4 hover:bg-gray-50 cursor-pointer" style={{ borderRadius: "3px", borderWidth: "1px" }}>
          <div className="flex items-center">
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4">
              <span className="text-red-600 font-semibold">U</span>
            </div>
            <div>
              <h3 className="font-medium">User Account</h3>
              <p className="text-sm text-gray-500"><EMAIL></p>
            </div>
            <div className="ml-auto">
              <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Active</span>
            </div>
          </div>
        </div>

        <div className="border border-gray-200 rounded-md p-4 hover:bg-gray-50 cursor-pointer" style={{ borderRadius: "3px", borderWidth: "1px" }}>
          <div className="flex items-center">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
              <span className="text-blue-600 font-semibold">V</span>
            </div>
            <div>
              <h3 className="font-medium">Vendor Account</h3>
              <p className="text-sm text-gray-500"><EMAIL></p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
