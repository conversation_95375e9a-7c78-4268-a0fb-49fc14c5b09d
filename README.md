# WedZat 

## 📽️ Monetize Your Wedding Memories!

### ₹1L-₹10L Spent on Wedding Videos—But Why Earn Nothing From It?

Every couple dreams of capturing their big day perfectly. That’s why ₹1L-₹10L is spent on wedding videography—lavish shoots, cinematic edits, and stunning visuals.

But after the celebrations? It’s just a sunk cost.

❌ Wedding videos sit idle in hard drives  
❌ No returns on the huge investment  
❌ Memories fade, and the content remains unused  

### 💡 What if your wedding video could become a lifetime digital asset?

## 🚀 Introducing WedZat – The Future of Wedding Videos!

✅ **Monetize your wedding content** – Earn from views & engagement  
✅ **Turn your memories into income** instead of just an expense  
✅ **Create a digital legacy** that grows in value over time  

The ₹70B wedding industry is evolving. Couples are spending big—it’s time they start earning too!

---

## 🛠️ Tech Stack

WedZat is built using modern and scalable technologies to ensure a seamless user experience:

- **Frontend:** Next.js
- **Backend:** Node.js
- **Cloud & Storage:** AWS
- **Database:** :
- **Authentication:** 
- **Video Processing & Hosting:** AWS S3, AWS MediaConvert
- **Deployment:** AWS EC2, Vercel

---

## 📌 Features

✅ **Upload & Store Wedding Videos** securely on the cloud  
✅ **Earn from Views & Engagement** through a unique monetization model  
✅ **AI-Powered Video Enhancements** to improve visibility and quality  
✅ **Personalized Wedding Pages** to showcase videos beautifully  
✅ **Secure & Private Sharing Options** with guests and family  
✅ **Community Engagement & Social Sharing** to increase reach  

---

## 🚀 Getting Started

### Prerequisites
- Node.js (v18+ recommended)
- AWS Account & Credentials
- PostgreSQL Database
- Vercel for frontend deployment (optional)

### Installation
1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/wedzat.git
   cd wedzat
   ```
2. Install dependencies:
   ```bash
   npm install
   ```
3. Set up environment variables (`.env` file):
   ```bash
   DATABASE_URL=your_database_url
   AWS_ACCESS_KEY_ID=your_aws_key
   AWS_SECRET_ACCESS_KEY=your_aws_secret
   NEXT_PUBLIC_API_URL=http://localhost:4000
   ```
4. Run the development server:
   ```bash
   npm run dev
   ```
5. Start the backend:
   ```bash
   npm start
   ```

---

## 🎯 Roadmap

🚀 Add AI-powered video recommendations  
📊 Advanced analytics for video engagement tracking  
🔒 Premium subscription plans for exclusive content  
📱 Mobile app for easy uploads & sharing  

---

## 🤝 Contributing
We welcome contributions! Feel free to fork the repo, create a feature branch, and submit a pull request.

---

## 📩 Contact
For inquiries, partnerships, or feedback, reach out at **[<EMAIL>](mailto:<EMAIL>)**.

---

## 📜 License
This project is licensed under the MIT License - see the LICENSE file for details.

---

### ❤️ Support Us
If you like the idea of WedZat, give this repo a ⭐ and help spread the word!
