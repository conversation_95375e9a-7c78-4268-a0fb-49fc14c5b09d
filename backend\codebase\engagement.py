import os
import json
import uuid
import jwt
import psycopg2
from dotenv import load_dotenv
from datetime import datetime

# Load environment variables
load_dotenv()

# Database connection parameters
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_PORT = os.getenv('DB_PORT')
DB_NAME = os.getenv('DB_NAME')

# JWT secret
JWT_SECRET = os.getenv('JWT_SECRET')

# Create a fresh connection for each function call
def get_db_connection():
    return psycopg2.connect(
        host=DB_HOST,
        user=DB_USER,
        password=DB_PASSWORD,
        port=DB_PORT,
        dbname=DB_NAME
    )

import redis
import os
import json
import ssl

def get_redis_connection():
    host = os.environ.get('REDIS_HOST')
    port = int(os.environ.get('REDIS_PORT', 6379))

    return redis.Redis(
        host=host,
        port=port,
        ssl=True,  # Required for TLS
        ssl_cert_reqs=None,
        decode_responses=True
    )

def validate_token(headers):
    token = headers.get('Authorization')
    
    if not token:
        return None, {
            'statusCode': 401,
            'body': json.dumps({"error": "Token is required"})
        }
        
    try:
        data = jwt.decode(token.split()[1], JWT_SECRET, algorithms=['HS256'])
        return data['user_id'], None
    except jwt.ExpiredSignatureError:
        return None, {
            'statusCode': 401,
            'body': json.dumps({"error": "Token expired"})
        }
    except jwt.InvalidTokenError:
        return None, {
            'statusCode': 401,
            'body': json.dumps({"error": "Invalid token"})
        }

def cors_headers():
    return {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "OPTIONS, POST",
        "Access-Control-Allow-Headers": "Content-Type, Authorization"
    }

def success_response(message):
    return {
        'statusCode': 200,
        'headers': cors_headers(),
        'body': json.dumps({"message": message})
    }

def error_response(status, error_msg):
    return {
        'statusCode': status,
        'headers': cors_headers(),
        'body': json.dumps({"error": error_msg})
    }

def like_content(event):
    conn, cursor = None, None
    try:
        user_id, error = validate_token(event['headers'])
        if error:
            return error

        data = json.loads(event['body'])
        content_id = data.get('content_id')

        if not content_id:
            return error_response(400, "Content ID is required")

        redis_client = get_redis_connection()
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("BEGIN")

        # Check if already liked
        cursor.execute("""
            SELECT status FROM likes
            WHERE user_id = %s AND content_id = %s
        """, (user_id, content_id))
        existing = cursor.fetchone()

        if existing:
            if existing[0]:  # already active like
                return error_response(400, "Already liked")
            else:
                # Reactivate the like
                cursor.execute("""
                    UPDATE likes SET status = TRUE, updated_at = NOW()
                    WHERE user_id = %s AND content_id = %s
                """, (user_id, content_id))
        else:
            # Insert new like
            cursor.execute("""
                INSERT INTO likes (user_id, content_id)
                VALUES (%s, %s)
            """, (user_id, content_id))

        # Redis increment
        redis_client.incr(f"content:{content_id}:likes")

        # Update fallback count in DB (upsert)
        cursor.execute("""
            INSERT INTO content_like_stats (content_id, like_count)
            VALUES (%s, 1)
            ON CONFLICT (content_id) DO UPDATE
            SET like_count = content_like_stats.like_count + 1,
                updated_at = NOW()
        """, (content_id,))

        conn.commit()
        return success_response("Content liked successfully")

    except Exception as e:
        if conn:
            conn.rollback()
        return error_response(500, f"Internal server error: {str(e)}")
    finally:
        if cursor: cursor.close()
        if conn: conn.close()

def unlike_content(event):
    conn, cursor = None, None
    try:
        user_id, error = validate_token(event['headers'])
        if error:
            return error

        data = json.loads(event['body'])
        content_id = data.get('content_id')

        if not content_id:
            return error_response(400, "Content ID is required")

        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("BEGIN")

        # Check if like exists and is active
        cursor.execute("""
            SELECT status FROM likes
            WHERE user_id = %s AND content_id = %s
        """, (user_id, content_id))
        existing = cursor.fetchone()

        if not existing or not existing[0]:
            return error_response(400, "Content not currently liked")

        # Soft delete
        cursor.execute("""
            UPDATE likes SET status = FALSE, updated_at = NOW()
            WHERE user_id = %s AND content_id = %s
        """, (user_id, content_id))

        # Redis decrement (but avoid negative count)
        redis_client = get_redis_connection()
        current_count = redis_client.get(f"content:{content_id}:likes")
        if current_count is not None and int(current_count) > 0:
            redis_client.decr(f"content:{content_id}:likes")

        # Update fallback stats table (avoid negative count)
        cursor.execute("""
            UPDATE content_like_stats
            SET like_count = GREATEST(like_count - 1, 0),
                updated_at = NOW()
            WHERE content_id = %s
        """, (content_id,))

        conn.commit()
        return success_response("Content unliked successfully")

    except Exception as e:
        if conn:
            conn.rollback()
        return error_response(500, f"Internal server error: {str(e)}")
    finally:
        if cursor: cursor.close()
        if conn: conn.close()

def view_content(event):
    conn, cursor = None, None
    try:
        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Parse request
        data = json.loads(event['body'])
        content_id = data.get('content_id')
        if not content_id:
            return error_response(400, "Content ID is required")

        redis_client = get_redis_connection()
        view_key = f"view:{user_id}:{content_id}"

        # Check if view already exists in cache (within 24 hrs)
        if redis_client.exists(view_key):
            return success_response("Already viewed today")

        # Add to PostgreSQL
        conn = get_db_connection()
        cursor = conn.cursor()

        now = datetime.utcnow()
        today = now.date()

        cursor.execute("""
            INSERT INTO content_views (user_id, content_id, viewed_at, view_date)
            VALUES (%s, %s, %s, %s)
            ON CONFLICT DO NOTHING
        """, (user_id, content_id, now, today))

        cursor.execute("""
            INSERT INTO content_view_stats (content_id, view_count)
            VALUES (%s, 1)
            ON CONFLICT (content_id)
            DO UPDATE SET view_count = content_view_stats.view_count + 1
        """, (content_id,))

        # Redis: mark view with 24h expiry
        redis_client.setex(view_key, 86400, "1")

        # Redis: increment content view counter
        redis_client.incr(f"content:{content_id}:views")

        conn.commit()
        return success_response("View recorded")

    except Exception as e:
        if conn:
            conn.rollback()
        return error_response(500, f"Internal server error: {str(e)}")

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

