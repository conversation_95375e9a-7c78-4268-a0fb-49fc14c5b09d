// // WedZat Platform Configuration

// export const WedzatConfig = {
//     // Theme configuration
//     theme: {
//         colors: {
//             primary: {
//                 main: '#9F7AEA', // Purple
//                 light: '#D6BCFA',
//                 dark: '#6B46C1',
//             },
//             secondary: {
//                 main: '#ED64A6', // Pink
//                 light: '#FBB6CE',
//                 dark: '#B83280',
//             },
//             accent: {
//                 main: '#4299E1', // Blue
//                 light: '#90CDF4',
//                 dark: '#2B6CB0',
//             },
//             success: '#48BB78',
//             warning: '#ECC94B',
//             error: '#F56565',
//             background: {
//                 main: '#F7FAFC',
//                 paper: '#FFFFFF',
//             },
//             text: {
//                 primary: '#2D3748',
//                 secondary: '#718096',
//                 disabled: '#A0AEC0',
//             }
//         },
//         fonts: {
//             heading: '"Poppins", sans-serif',
//             body: '"Inter", sans-serif',
//         },
//         borderRadius: {
//             small: '0.25rem',
//             medium: '0.5rem',
//             large: '1rem',
//             full: '9999px',
//         },
//         spacing: {
//             xs: '0.25rem',
//             sm: '0.5rem',
//             md: '1rem',
//             lg: '1.5rem',
//             xl: '2rem',
//             xxl: '3rem',
//         },
//         breakpoints: {
//             xs: '0px',
//             sm: '640px',
//             md: '768px',
//             lg: '1024px',
//             xl: '1280px',
//             xxl: '1536px',
//         },
//         shadows: {
//             sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
//             md: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
//             lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
//             xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
//         },
//     },

//     // Feature flags
//     features: {
//         wedzBot: true,
//         counsellors: true,
//         planningTools: true,
//         marketPlace: true,
//         stories: true,
//         moments: true,
//         coupons: true,
//     },

//     // API endpoints
//     api: {
//         baseUrl: process.env.NODE_ENV === 'development'
//             ? 'http://localhost:3000/api'
//             : 'https://api.wedzat.com',
//         endpoints: {
//             auth: '/auth',
//             users: '/users',
//             vendors: '/vendors',
//             counsellors: '/counsellors',
//             posts: '/posts',
//             messages: '/messages',
//             events: '/events',
//             planning: '/planning',
//         },
//     },

//     // Navigation Structure
//     navigation: {
//         main: [
//             { name: 'Home', path: '/', icon: 'Home' },
//             { name: 'Moments', path: '/moments', icon: 'Users' },
//             { name: 'Discover', path: '/discover', icon: 'Search' },
//             { name: 'Messages', path: '/messages', icon: 'MessageCircle' },
//             { name: 'Notifications', path: '/notifications', icon: 'Bell' },
//         ],
//         categories: [
//             { name: 'Wedding Venues', path: '/category/venues', icon: 'Home' },
//             { name: 'Photographers', path: '/category/photographers', icon: 'Camera' },
//             { name: 'Makeup', path: '/category/makeup', icon: 'Brush' },
//             { name: 'Wedding Decors', path: '/category/decors', icon: 'PenTool' },
//             { name: 'Caterers', path: '/category/caterers', icon: 'Coffee' },
//             { name: 'Outfits', path: '/category/outfits', icon: 'Shirt' },
//         ],
//         planningTools: [
//             { name: 'Hashtag Generator', path: '/tools/hashtag', icon: 'Hash' },
//             { name: 'Checklist', path: '/tools/checklist', icon: 'CheckSquare' },
//             { name: 'Budget', path: '/tools/budget', icon: 'DollarSign' },
//             { name: 'Guests', path: '/tools/guests', icon: 'Users' },
//             { name: 'Wedding Vendors', path: '/tools/vendors', icon: 'Store' },
//             { name: 'Wedding Websites', path: '/tools/websites', icon: 'Globe' },
//         ],
//         profile: [
//             { name: 'Profile', path: '/profile', icon: 'User' },
//             { name: 'Settings', path: '/settings', icon: 'Settings' },
//             { name: 'My Wedding', path: '/my-wedding', icon: 'Heart' },
//             { name: 'Saved Items', path: '/saved', icon: 'Bookmark' },
//             { name: 'Help Center', path: '/help', icon: 'HelpCircle' },
//             { name: 'Logout', path: '/logout', icon: 'LogOut' },
//         ],
//     },

//     // Authentication config
//     auth: {
//         providers: ['email', 'google', 'facebook', 'apple'],
//         tokenExpiry: '7d',
//         refreshTokenExpiry: '30d',
//         verificationRequired: true,
//     },

//     // Content moderation settings
//     moderation: {
//         autoModerate: true,
//         reportCategories: [
//             'Spam',
//             'Inappropriate Content',
//             'Harassment',
//             'False Information',
//             'Other'
//         ],
//         sensitiveContentBlur: true,
//     },

//     // Analytics tracking
//     analytics: {
//         enabled: true,
//         trackPageViews: true,
//         trackEvents: true,
//         trackErrors: true,
//     },

//     // Media upload limitations
//     uploads: {
//         maxFileSize: 10 * 1024 * 1024, // 10MB 
//         allowedImageTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
//         allowedVideoTypes: ['video/mp4', 'video/webm'],
//         allowedDocumentTypes: ['application/pdf', 'application/msword'],
//         imageDimensions: {
//             avatar: { width: 400, height: 400 },
//             cover: { width: 1200, height: 400 },
//             post: { width: 1080, height: 1080 },
//             story: { width: 1080, height: 1920 },
//         },
//     },

//     // Notification settings
//     notifications: {
//         types: [
//             'follow',
//             'like',
//             'comment',
//             'mention',
//             'message',
//             'reminder',
//             'vendor_update'
//         ],
//         defaultEnabled: true,
//         deliveryMethods: ['push', 'email', 'in_app'],
//     },
// }