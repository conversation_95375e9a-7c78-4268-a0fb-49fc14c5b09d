import React from 'react';
import { useMediaUrl } from '../../hooks/useMedia';

interface CachedMediaProps {
  url: string;
  type: 'photo' | 'video';
  className?: string;
  size?: string;
  quality?: number;
  onLoad?: () => void;
  onError?: (error: Error) => void;
}

const CachedMedia: React.FC<CachedMediaProps> = ({
  url,
  type,
  className = '',
  size,
  quality,
  onLoad,
  onError,
}) => {
  const { data: optimizedUrl, isLoading, error } = useMediaUrl(url, { size, quality });

  React.useEffect(() => {
    if (error && onError) {
      onError(error as Error);
    }
  }, [error, onError]);

  if (isLoading) {
    return (
      <div className={`animate-pulse bg-gray-200 ${className}`}>
        <div className="w-full h-full bg-gray-300"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`flex items-center justify-center bg-gray-100 ${className}`}>
        <span className="text-red-500">Failed to load media</span>
      </div>
    );
  }

  if (type === 'photo') {
    return (
      <img
        src={optimizedUrl}
        className={`object-cover ${className}`}
        onLoad={onLoad}
        onError={(e) => {
          console.error('Error loading image:', e);
          if (onError) onError(new Error('Failed to load image'));
        }}
        alt=""
      />
    );
  }

  return (
    <video
      src={optimizedUrl}
      className={`object-cover ${className}`}
      controls
      onLoadedData={onLoad}
      onError={(e) => {
        console.error('Error loading video:', e);
        if (onError) onError(new Error('Failed to load video'));
      }}
    />
  );
};

export default CachedMedia; 