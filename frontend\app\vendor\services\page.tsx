"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { vendorService } from '../../../services/vendor-api';
import Image from 'next/image';

// Define interfaces for our data types
interface BusinessType {
  type_id: string;
  name: string;
  description?: string;
}

interface BusinessSubtype {
  subtype_id: string;
  name: string;
  description?: string;
}

interface VendorService {
  service_id: string;
  service_name: string;
  business_type_name: string;
  business_subtype_name: string;
  description?: string;
  price_range?: string;
  location?: string;
  city?: string;
  state?: string;
  pin_code?: string;
  capacity?: number;
  amenities: string[];
  images: string[];
  is_active: boolean;
  created_at: string;
}

interface ServiceFormData {
  business_type_id: string;
  business_subtype_id: string;
  service_name: string;
  description: string;
  price_range: string;
  location: string;
  city: string;
  state: string;
  pin_code: string;
  capacity: string;
  amenities: string[];
  images: string[];
}

export default function VendorServicesPage() {
  const router = useRouter();
  const [vendorProfile, setVendorProfile] = useState<any>(null);
  const [services, setServices] = useState<VendorService[]>([]);
  const [businessTypes, setBusinessTypes] = useState<BusinessType[]>([]);
  const [businessSubtypes, setBusinessSubtypes] = useState<BusinessSubtype[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [formData, setFormData] = useState<ServiceFormData>({
    business_type_id: '',
    business_subtype_id: '',
    service_name: '',
    description: '',
    price_range: '',
    location: '',
    city: '',
    state: '',
    pin_code: '',
    capacity: '',
    amenities: [],
    images: []
  });
  const [newAmenity, setNewAmenity] = useState('');
  const [newImageUrl, setNewImageUrl] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  // Fetch vendor profile and services on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        setError('');

        // Fetch vendor profile
        const profile = await vendorService.getVendorProfile();
        setVendorProfile(profile);

        // Fetch business types
        const types = await vendorService.getBusinessTypes();
        setBusinessTypes(types);

        // Fetch vendor services
        const vendorServices = await vendorService.getVendorServices();
        setServices(vendorServices);

        setIsLoading(false);
      } catch (err: any) {
        console.error('Error fetching data:', err);
        setError(err.error || 'Failed to load data');
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Hardcoded business subtypes for each business type
  const businessSubtypesMap: Record<string, BusinessSubtype[]> = {
    // These IDs will be replaced with actual business type IDs from your database
    // For now, we'll use the business type name as the key
    "Venues": [
      { subtype_id: "venue-1", name: "Kalyana Mandapam" },
      { subtype_id: "venue-2", name: "Banquet Halls" },
      { subtype_id: "venue-3", name: "Small Party/Engagement Halls" },
      { subtype_id: "venue-4", name: "Outdoor Lawn/Garden" },
      { subtype_id: "venue-5", name: "Resort" },
      { subtype_id: "venue-6", name: "4-Star/5-Star Hotel" },
      { subtype_id: "venue-7", name: "Destination Weddings" }
    ],
    "Photography": [
      { subtype_id: "photo-1", name: "Wedding Photography" },
      { subtype_id: "photo-2", name: "Pre-Wedding Shoots" },
      { subtype_id: "photo-3", name: "Candid Photography" },
      { subtype_id: "photo-4", name: "Traditional Photography" },
      { subtype_id: "photo-5", name: "Drone Photography" }
    ],
    "Videographer": [
      { subtype_id: "video-1", name: "Wedding Films" },
      { subtype_id: "video-2", name: "Pre-Wedding Videos" },
      { subtype_id: "video-3", name: "Cinematic Videos" },
      { subtype_id: "video-4", name: "Drone Videography" },
      { subtype_id: "video-5", name: "Same-Day Edit" }
    ],
    "Makeup Artist": [
      { subtype_id: "makeup-1", name: "Bridal Makeup" },
      { subtype_id: "makeup-2", name: "Party Makeup" },
      { subtype_id: "makeup-3", name: "HD Makeup" },
      { subtype_id: "makeup-4", name: "Airbrush Makeup" },
      { subtype_id: "makeup-5", name: "Celebrity Makeup Artists" }
    ],
    "Decoration": [
      { subtype_id: "decor-1", name: "Wedding Stage Decoration" },
      { subtype_id: "decor-2", name: "Floral Decoration" },
      { subtype_id: "decor-3", name: "Lighting Decoration" },
      { subtype_id: "decor-4", name: "Entrance Decoration" },
      { subtype_id: "decor-5", name: "Mandap Decoration" }
    ],
    "Caterers": [
      { subtype_id: "cater-1", name: "Vegetarian Catering" },
      { subtype_id: "cater-2", name: "Non-Vegetarian Catering" },
      { subtype_id: "cater-3", name: "Chaat & Street Food" },
      { subtype_id: "cater-4", name: "Multi-Cuisine Catering" },
      { subtype_id: "cater-5", name: "Dessert Counters" }
    ],
    "Mehendi": [
      { subtype_id: "mehendi-1", name: "Traditional Mehendi" },
      { subtype_id: "mehendi-2", name: "Arabic Mehendi" },
      { subtype_id: "mehendi-3", name: "Bridal Mehendi" },
      { subtype_id: "mehendi-4", name: "Rajasthani Mehendi" },
      { subtype_id: "mehendi-5", name: "Indo-Arabic Mehendi" }
    ],
    "Bridal Outfit": [
      { subtype_id: "bridal-1", name: "Bridal Lehenga" },
      { subtype_id: "bridal-2", name: "Bridal Saree" },
      { subtype_id: "bridal-3", name: "Designer Wear" },
      { subtype_id: "bridal-4", name: "Indo-Western" },
      { subtype_id: "bridal-5", name: "Reception Outfits" }
    ],
    "Groom Outfit": [
      { subtype_id: "groom-1", name: "Sherwani" },
      { subtype_id: "groom-2", name: "Indo-Western" },
      { subtype_id: "groom-3", name: "Wedding Suits" },
      { subtype_id: "groom-4", name: "Kurta Pajama" },
      { subtype_id: "groom-5", name: "Designer Wear" }
    ],
    "Invitations": [
      { subtype_id: "invite-1", name: "Traditional Invitations" },
      { subtype_id: "invite-2", name: "Digital Invitations" },
      { subtype_id: "invite-3", name: "Handmade Cards" },
      { subtype_id: "invite-4", name: "Boxed Invitations" },
      { subtype_id: "invite-5", name: "Scroll Invitations" }
    ],
    "Music & Dance": [
      { subtype_id: "music-1", name: "DJ Services" },
      { subtype_id: "music-2", name: "Live Band" },
      { subtype_id: "music-3", name: "Choreographers" },
      { subtype_id: "music-4", name: "Sangeet Performers" },
      { subtype_id: "music-5", name: "Folk Musicians" }
    ],
    "Pandits": [
      { subtype_id: "pandit-1", name: "Wedding Ceremonies" },
      { subtype_id: "pandit-2", name: "Pre-Wedding Rituals" },
      { subtype_id: "pandit-3", name: "Muhurat Selection" },
      { subtype_id: "pandit-4", name: "Vedic Ceremonies" },
      { subtype_id: "pandit-5", name: "Regional Specialists" }
    ],
    "Jewelry": [
      { subtype_id: "jewelry-1", name: "Bridal Jewelry" },
      { subtype_id: "jewelry-2", name: "Gold Jewelry" },
      { subtype_id: "jewelry-3", name: "Diamond Jewelry" },
      { subtype_id: "jewelry-4", name: "Kundan Jewelry" },
      { subtype_id: "jewelry-5", name: "Temple Jewelry" }
    ],
    "Dermatologist": [
      { subtype_id: "derm-1", name: "Pre-Wedding Skin Care" },
      { subtype_id: "derm-2", name: "Bridal Packages" },
      { subtype_id: "derm-3", name: "Skin Treatments" },
      { subtype_id: "derm-4", name: "Hair Treatments" },
      { subtype_id: "derm-5", name: "Cosmetic Procedures" }
    ],
    "Esthetic Dentist": [
      { subtype_id: "dentist-1", name: "Teeth Whitening" },
      { subtype_id: "dentist-2", name: "Smile Makeover" },
      { subtype_id: "dentist-3", name: "Dental Veneers" },
      { subtype_id: "dentist-4", name: "Cosmetic Bonding" },
      { subtype_id: "dentist-5", name: "Dental Implants" }
    ],
    "Beauty Grooming": [
      { subtype_id: "beauty-1", name: "Bridal Grooming" },
      { subtype_id: "beauty-2", name: "Groom Grooming" },
      { subtype_id: "beauty-3", name: "Hair Styling" },
      { subtype_id: "beauty-4", name: "Nail Art" },
      { subtype_id: "beauty-5", name: "Spa Treatments" }
    ],
    "Event Planners": [
      { subtype_id: "event-1", name: "Full Wedding Planning" },
      { subtype_id: "event-2", name: "Partial Planning" },
      { subtype_id: "event-3", name: "Day-of Coordination" },
      { subtype_id: "event-4", name: "Destination Wedding Planning" },
      { subtype_id: "event-5", name: "Theme Wedding Planning" }
    ],
    "Transportation": [
      { subtype_id: "transport-1", name: "Luxury Cars" },
      { subtype_id: "transport-2", name: "Vintage Cars" },
      { subtype_id: "transport-3", name: "Decorated Cars" },
      { subtype_id: "transport-4", name: "Horse Carriages" },
      { subtype_id: "transport-5", name: "Guest Transportation" }
    ],
    "Hospitality": [
      { subtype_id: "hosp-1", name: "Guest Accommodation" },
      { subtype_id: "hosp-2", name: "Welcome Kits" },
      { subtype_id: "hosp-3", name: "Guest Management" },
      { subtype_id: "hosp-4", name: "Concierge Services" },
      { subtype_id: "hosp-5", name: "Out-of-Town Guest Services" }
    ],
    "Gifts": [
      { subtype_id: "gift-1", name: "Wedding Favors" },
      { subtype_id: "gift-2", name: "Return Gifts" },
      { subtype_id: "gift-3", name: "Bridesmaid Gifts" },
      { subtype_id: "gift-4", name: "Groomsmen Gifts" },
      { subtype_id: "gift-5", name: "Customized Gifts" }
    ],
    "Astrologers": [
      { subtype_id: "astro-1", name: "Wedding Date Selection" },
      { subtype_id: "astro-2", name: "Horoscope Matching" },
      { subtype_id: "astro-3", name: "Muhurat Consultation" },
      { subtype_id: "astro-4", name: "Vedic Astrology" },
      { subtype_id: "astro-5", name: "Numerology" }
    ],
    "Honeymoon Planners": [
      { subtype_id: "honey-1", name: "Domestic Packages" },
      { subtype_id: "honey-2", name: "International Packages" },
      { subtype_id: "honey-3", name: "Luxury Honeymoons" },
      { subtype_id: "honey-4", name: "Adventure Honeymoons" },
      { subtype_id: "honey-5", name: "Cruise Honeymoons" }
    ],
    "Bridal Shower": [
      { subtype_id: "bshower-1", name: "Bridal Shower Planning" },
      { subtype_id: "bshower-2", name: "Themed Bridal Showers" },
      { subtype_id: "bshower-3", name: "Bridal Shower Venues" },
      { subtype_id: "bshower-4", name: "Bridal Shower Games" },
      { subtype_id: "bshower-5", name: "Bridal Shower Catering" }
    ],
    "Baby Shower": [
      { subtype_id: "babyshower-1", name: "Baby Shower Planning" },
      { subtype_id: "babyshower-2", name: "Themed Baby Showers" },
      { subtype_id: "babyshower-3", name: "Baby Shower Venues" },
      { subtype_id: "babyshower-4", name: "Baby Shower Games" },
      { subtype_id: "babyshower-5", name: "Baby Shower Catering" }
    ],
    "Bachelor Party": [
      { subtype_id: "bachelor-1", name: "Bachelor Party Planning" },
      { subtype_id: "bachelor-2", name: "Bachelor Party Venues" },
      { subtype_id: "bachelor-3", name: "Adventure Activities" },
      { subtype_id: "bachelor-4", name: "Party Games" },
      { subtype_id: "bachelor-5", name: "Party Packages" }
    ],
    "Wedding Planner": [
      { subtype_id: "wplanner-1", name: "Full Service Planning" },
      { subtype_id: "wplanner-2", name: "Month-of Coordination" },
      { subtype_id: "wplanner-3", name: "Destination Wedding Planning" },
      { subtype_id: "wplanner-4", name: "Budget Planning" },
      { subtype_id: "wplanner-5", name: "Vendor Management" }
    ],
    "Others": [
      { subtype_id: "other-1", name: "Wedding Insurance" },
      { subtype_id: "other-2", name: "Wedding Loans" },
      { subtype_id: "other-3", name: "Legal Services" },
      { subtype_id: "other-4", name: "Name Change Services" },
      { subtype_id: "other-5", name: "Custom Services" }
    ]
  };

  // Handle business type change and set subtypes from our map
  const handleBusinessTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const businessTypeId = e.target.value;
    setFormData({ ...formData, business_type_id: businessTypeId, business_subtype_id: '' });

    if (businessTypeId) {
      // Find the selected business type name
      const selectedType = businessTypes.find(type => type.type_id === businessTypeId);
      if (selectedType && selectedType.name in businessSubtypesMap) {
        // Set subtypes from our map using the business type name
        setBusinessSubtypes(businessSubtypesMap[selectedType.name]);
      } else {
        setBusinessSubtypes([]);
      }
    } else {
      setBusinessSubtypes([]);
    }
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  // Add amenity to the list
  const handleAddAmenity = () => {
    if (newAmenity.trim()) {
      setFormData({
        ...formData,
        amenities: [...formData.amenities, newAmenity.trim()]
      });
      setNewAmenity('');
    }
  };

  // Remove amenity from the list
  const handleRemoveAmenity = (index: number) => {
    const updatedAmenities = [...formData.amenities];
    updatedAmenities.splice(index, 1);
    setFormData({ ...formData, amenities: updatedAmenities });
  };

  // Add image URL to the list
  const handleAddImage = () => {
    if (newImageUrl.trim()) {
      setFormData({
        ...formData,
        images: [...formData.images, newImageUrl.trim()]
      });
      setNewImageUrl('');
    }
  };

  // Remove image from the list
  const handleRemoveImage = (index: number) => {
    const updatedImages = [...formData.images];
    updatedImages.splice(index, 1);
    setFormData({ ...formData, images: updatedImages });
  };

  // Submit the form to add a new service
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setIsLoading(true);
      setError('');

      // Find the selected business type
      const selectedType = businessTypes.find(type => type.type_id === formData.business_type_id);
      if (!selectedType) {
        throw new Error('Invalid business type selected');
      }

      // Find the selected subtype name
      const selectedSubtype = businessSubtypes.find(subtype => subtype.subtype_id === formData.business_subtype_id);
      if (!selectedSubtype) {
        throw new Error('Invalid business subtype selected');
      }

      // Create a modified form data with the actual business type ID
      // and the subtype name directly (not the ID)
      const serviceData = {
        business_type_id: formData.business_type_id,
        business_subtype_name: selectedSubtype.name, // Send the name directly instead of ID
        service_name: formData.service_name,
        description: formData.description,
        price_range: formData.price_range,
        location: formData.location,
        city: formData.city,
        state: formData.state,
        pin_code: formData.pin_code,
        capacity: formData.capacity ? parseInt(formData.capacity) : undefined,
        amenities: formData.amenities,
        images: formData.images
      };

      console.log('Sending service data:', serviceData);

      // Add the service
      await vendorService.addVendorService(serviceData);

      // Reset form and fetch updated services
      setFormData({
        business_type_id: '',
        business_subtype_id: '',
        service_name: '',
        description: '',
        price_range: '',
        location: '',
        city: '',
        state: '',
        pin_code: '',
        capacity: '',
        amenities: [],
        images: []
      });
      setShowAddForm(false);

      // Show success message
      setSuccessMessage('Service added successfully!');
      setTimeout(() => setSuccessMessage(''), 3000);

      // Refresh services list
      const updatedServices = await vendorService.getVendorServices();
      setServices(updatedServices);

      setIsLoading(false);
    } catch (err: any) {
      console.error('Error adding service:', err);
      setError(err.error?.message || err.message || 'Failed to add service');
      setIsLoading(false);
    }
  };

  // Delete a service
  const handleDeleteService = async (serviceId: string) => {
    if (window.confirm('Are you sure you want to delete this service?')) {
      try {
        setIsLoading(true);
        setError('');

        // Delete the service
        await vendorService.deleteVendorService(serviceId);

        // Show success message
        setSuccessMessage('Service deleted successfully!');
        setTimeout(() => setSuccessMessage(''), 3000);

        // Refresh services list
        const updatedServices = await vendorService.getVendorServices();
        setServices(updatedServices);

        setIsLoading(false);
      } catch (err: any) {
        console.error('Error deleting service:', err);
        setError(err.error || 'Failed to delete service');
        setIsLoading(false);
      }
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4 md:p-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold">Vendor Services</h1>
            <button
              onClick={() => router.push('/vendor/dashboard')}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition duration-200"
            >
              Back to Dashboard
            </button>
          </div>

          {/* Success message */}
          {successMessage && (
            <div className="mt-4 p-3 bg-green-100 text-green-700 rounded-md">
              {successMessage}
            </div>
          )}

          {/* Error message */}
          {error && (
            <div className="mt-4 p-3 bg-red-100 text-red-700 rounded-md">
              {error}
            </div>
          )}
        </div>

        {/* Add Service Button */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Your Services</h2>
            <button
              onClick={() => setShowAddForm(!showAddForm)}
              className="px-4 py-2 bg-red-700 text-white rounded-md hover:bg-red-800 transition duration-200"
            >
              {showAddForm ? 'Cancel' : 'Add New Service'}
            </button>
          </div>

          {/* Add Service Form */}
          {showAddForm && (
            <div className="mt-6 p-4 border border-gray-200 rounded-md">
              <h3 className="text-lg font-medium mb-4">Add New Service</h3>

              <form onSubmit={handleSubmit}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  {/* Business Type */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Business Type *
                    </label>
                    <select
                      name="business_type_id"
                      value={formData.business_type_id}
                      onChange={handleBusinessTypeChange}
                      className="w-full p-2 border border-gray-300 rounded-md"
                      required
                    >
                      <option value="">Select Business Type</option>
                      {businessTypes.map((type) => (
                        <option key={type.type_id} value={type.type_id}>
                          {type.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Business Subtype */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Business Subtype *
                    </label>
                    <select
                      name="business_subtype_id"
                      value={formData.business_subtype_id}
                      onChange={handleInputChange}
                      className="w-full p-2 border border-gray-300 rounded-md"
                      required
                      disabled={!formData.business_type_id}
                    >
                      <option value="">Select Business Subtype</option>
                      {businessSubtypes.map((subtype) => (
                        <option key={subtype.subtype_id} value={subtype.subtype_id}>
                          {subtype.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Service Name */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Service Name *
                    </label>
                    <input
                      type="text"
                      name="service_name"
                      value={formData.service_name}
                      onChange={handleInputChange}
                      className="w-full p-2 border border-gray-300 rounded-md"
                      required
                    />
                  </div>

                  {/* Price Range */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Price Range
                    </label>
                    <input
                      type="text"
                      name="price_range"
                      value={formData.price_range}
                      onChange={handleInputChange}
                      placeholder="e.g., ₹50,000 - ₹1,00,000"
                      className="w-full p-2 border border-gray-300 rounded-md"
                    />
                  </div>

                  {/* Location */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Location
                    </label>
                    <input
                      type="text"
                      name="location"
                      value={formData.location}
                      onChange={handleInputChange}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    />
                  </div>

                  {/* City */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      City
                    </label>
                    <input
                      type="text"
                      name="city"
                      value={formData.city}
                      onChange={handleInputChange}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    />
                  </div>

                  {/* State */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      State
                    </label>
                    <input
                      type="text"
                      name="state"
                      value={formData.state}
                      onChange={handleInputChange}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    />
                  </div>

                  {/* PIN Code */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      PIN Code
                    </label>
                    <input
                      type="text"
                      name="pin_code"
                      value={formData.pin_code}
                      onChange={handleInputChange}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    />
                  </div>

                  {/* Capacity */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Capacity
                    </label>
                    <input
                      type="number"
                      name="capacity"
                      value={formData.capacity}
                      onChange={handleInputChange}
                      placeholder="e.g., 500 for 500 people"
                      className="w-full p-2 border border-gray-300 rounded-md"
                    />
                  </div>
                </div>

                {/* Description */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={4}
                    className="w-full p-2 border border-gray-300 rounded-md"
                  />
                </div>

                {/* Amenities */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Amenities
                  </label>
                  <div className="flex">
                    <input
                      type="text"
                      value={newAmenity}
                      onChange={(e) => setNewAmenity(e.target.value)}
                      placeholder="e.g., AC, Wi-Fi, Parking"
                      className="flex-1 p-2 border border-gray-300 rounded-l-md"
                    />
                    <button
                      type="button"
                      onClick={handleAddAmenity}
                      className="px-4 py-2 bg-gray-200 text-gray-800 rounded-r-md hover:bg-gray-300"
                    >
                      Add
                    </button>
                  </div>

                  {/* Amenities List */}
                  {formData.amenities.length > 0 && (
                    <div className="mt-2 flex flex-wrap gap-2">
                      {formData.amenities.map((amenity, index) => (
                        <div key={index} className="bg-gray-100 px-3 py-1 rounded-full flex items-center">
                          <span>{amenity}</span>
                          <button
                            type="button"
                            onClick={() => handleRemoveAmenity(index)}
                            className="ml-2 text-red-600 hover:text-red-800"
                          >
                            &times;
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Images */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Images
                  </label>

                  {/* Image URL Input */}
                  <div className="flex mb-2">
                    <input
                      type="text"
                      value={newImageUrl}
                      onChange={(e) => setNewImageUrl(e.target.value)}
                      placeholder="Enter image URL"
                      className="flex-1 p-2 border border-gray-300 rounded-l-md"
                    />
                    <button
                      type="button"
                      onClick={handleAddImage}
                      className="px-4 py-2 bg-gray-200 text-gray-800 rounded-r-md hover:bg-gray-300"
                    >
                      Add URL
                    </button>
                  </div>

                  {/* File Upload */}
                  <div className="mt-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Or Upload Images
                    </label>
                    <div className="flex items-center justify-center w-full">
                      <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100">
                        <div className="flex flex-col items-center justify-center pt-5 pb-6">
                          <svg className="w-8 h-8 mb-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                            <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
                          </svg>
                          <p className="mb-2 text-sm text-gray-500"><span className="font-semibold">Click to upload</span> or drag and drop</p>
                          <p className="text-xs text-gray-500">PNG, JPG or JPEG (MAX. 2MB)</p>
                        </div>
                        <input
                          type="file"
                          className="hidden"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              // Create a URL for the file
                              const imageUrl = URL.createObjectURL(file);
                              setFormData({
                                ...formData,
                                images: [...formData.images, imageUrl]
                              });

                              // In a real implementation, you would:
                              // 1. Convert the file to base64 or
                              // 2. Use FormData to upload the file directly
                              // For this example, we'll just use the object URL
                            }
                          }}
                        />
                      </label>
                    </div>
                  </div>

                  {/* Images Preview */}
                  {formData.images.length > 0 && (
                    <div className="mt-4">
                      <h4 className="text-sm font-medium mb-2">Image Preview</h4>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                        {formData.images.map((imageUrl, index) => (
                          <div key={index} className="relative">
                            <div className="aspect-w-16 aspect-h-9 overflow-hidden rounded-md">
                              <Image
                                src={imageUrl}
                                alt={`Service image ${index + 1}`}
                                width={200}
                                height={150}
                                className="object-cover w-full h-full"
                              />
                            </div>
                            <button
                              type="button"
                              onClick={() => handleRemoveImage(index)}
                              className="absolute top-1 right-1 bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-red-800"
                            >
                              &times;
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Submit Button */}
                <div className="mt-6">
                  <button
                    type="submit"
                    className="px-6 py-2 bg-red-700 text-white rounded-md hover:bg-red-800 transition duration-200 disabled:bg-gray-400"
                    disabled={isLoading}
                  >
                    {isLoading ? 'Adding...' : 'Add Service'}
                  </button>
                </div>
              </form>
            </div>
          )}

          {/* Services List */}
          <div className="mt-6">
            {isLoading && !showAddForm ? (
              <p className="text-center py-4">Loading services...</p>
            ) : services.length === 0 ? (
              <p className="text-center py-4 text-gray-500">
                You haven't added any services yet. Click "Add New Service" to get started.
              </p>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {services.map((service) => (
                  <div key={service.service_id} className="border border-gray-200 rounded-lg overflow-hidden">
                    {/* Service Image */}
                    <div className="aspect-w-16 aspect-h-9 relative">
                      {service.images && service.images.length > 0 ? (
                        <Image
                          src={service.images[0]}
                          alt={service.service_name}
                          width={400}
                          height={225}
                          className="object-cover w-full h-full"
                        />
                      ) : (
                        <div className="bg-gray-200 w-full h-full flex items-center justify-center">
                          <span className="text-gray-500">No image available</span>
                        </div>
                      )}

                      {/* Service Type Badge */}
                      <div className="absolute top-2 left-2 bg-red-700 text-white text-xs px-2 py-1 rounded-md">
                        {service.business_subtype_name}
                      </div>
                    </div>

                    {/* Service Details */}
                    <div className="p-4">
                      <h3 className="text-lg font-semibold mb-2">{service.service_name}</h3>

                      {service.description && (
                        <p className="text-gray-600 text-sm mb-3 line-clamp-2">{service.description}</p>
                      )}

                      <div className="grid grid-cols-2 gap-2 mb-3">
                        {service.price_range && (
                          <div className="text-sm">
                            <span className="font-medium">Price:</span> {service.price_range}
                          </div>
                        )}

                        {service.location && (
                          <div className="text-sm">
                            <span className="font-medium">Location:</span> {service.location}
                          </div>
                        )}

                        {service.city && (
                          <div className="text-sm">
                            <span className="font-medium">City:</span> {service.city}
                          </div>
                        )}

                        {service.capacity && (
                          <div className="text-sm">
                            <span className="font-medium">Capacity:</span> {service.capacity}
                          </div>
                        )}
                      </div>

                      {/* Amenities */}
                      {service.amenities && service.amenities.length > 0 && (
                        <div className="mb-3">
                          <h4 className="text-sm font-medium mb-1">Amenities:</h4>
                          <div className="flex flex-wrap gap-1">
                            {service.amenities.map((amenity, index) => (
                              <span key={index} className="text-xs bg-gray-100 px-2 py-1 rounded-full">
                                {amenity}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Actions */}
                      <div className="flex justify-end mt-4">
                        <button
                          onClick={() => handleDeleteService(service.service_id)}
                          className="text-red-600 hover:text-red-800 text-sm"
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
