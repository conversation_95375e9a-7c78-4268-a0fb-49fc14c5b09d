"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"171a7b691a0c\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xca29tbWlcXE9uZURyaXZlXFxEZXNrdG9wXFxXRURaQVRfXFxmcm9udGVuZFxcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjE3MWE3YjY5MWEwY1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/upload/UploadManager.tsx":
/*!*********************************************!*\
  !*** ./components/upload/UploadManager.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _UploadTypeSelection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./UploadTypeSelection */ \"(app-pages-browser)/./components/upload/UploadTypeSelection.tsx\");\n/* harmony import */ var _VideoCategorySelection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./VideoCategorySelection */ \"(app-pages-browser)/./components/upload/VideoCategorySelection.tsx\");\n/* harmony import */ var _ThumbnailSelection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ThumbnailSelection */ \"(app-pages-browser)/./components/upload/ThumbnailSelection.tsx\");\n/* harmony import */ var _PersonalDetails__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./PersonalDetails */ \"(app-pages-browser)/./components/upload/PersonalDetails.tsx\");\n/* harmony import */ var _VendorDetails__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./VendorDetails */ \"(app-pages-browser)/./components/upload/VendorDetails.tsx\");\n/* harmony import */ var _FaceVerification__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./FaceVerification */ \"(app-pages-browser)/./components/upload/FaceVerification.tsx\");\n/* harmony import */ var _UploadProgress__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./UploadProgress */ \"(app-pages-browser)/./components/upload/UploadProgress.tsx\");\n/* harmony import */ var _contexts_UploadContexts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../contexts/UploadContexts */ \"(app-pages-browser)/./contexts/UploadContexts.tsx\");\n/* harmony import */ var _utils_uploadUtils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../utils/uploadUtils */ \"(app-pages-browser)/./utils/uploadUtils.ts\");\n/* harmony import */ var _utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../utils/alertUtils */ \"(app-pages-browser)/./utils/alertUtils.tsx\");\n/* harmony import */ var _hooks_useMedia__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../hooks/useMedia */ \"(app-pages-browser)/./hooks/useMedia.ts\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../services/api */ \"(app-pages-browser)/./services/api.ts\");\n// components/upload/UploadManager.tsx\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function to format time in minutes and seconds\nconst formatTime = (seconds)=>{\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    if (minutes === 0) {\n        return \"\".concat(remainingSeconds, \" seconds\");\n    } else if (minutes === 1 && remainingSeconds === 0) {\n        return '1 minute';\n    } else if (remainingSeconds === 0) {\n        return \"\".concat(minutes, \" minutes\");\n    } else {\n        return \"\".concat(minutes, \" minute\").concat(minutes > 1 ? 's' : '', \" and \").concat(remainingSeconds, \" second\").concat(remainingSeconds !== 1 ? 's' : '');\n    }\n};\nconst UploadManager = (param)=>{\n    let { onClose, initialType, onUploadComplete } = param;\n    _s();\n    const { state, setFile, setMediaType, setCategory, setMediaSubtype, setTitle, setDescription, setDetailField, startUpload, startUploadWithCategory, setThumbnail, setVendorDetails, setPersonalDetails, setDuration, setIsMoments, resetUpload } = (0,_contexts_UploadContexts__WEBPACK_IMPORTED_MODULE_9__.useUpload)();\n    const [phase, setPhase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('typeSelection');\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialType || '');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [previewImage, setPreviewImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [thumbnailImage, setThumbnailImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const vendorDetailsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    // Helper function to determine content type for PersonalDetails\n    const getContentType = ()=>{\n        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n            return 'moment';\n        } else if (state.mediaType === 'video') {\n            return 'video';\n        } else {\n            return 'photo';\n        }\n    };\n    // Store personal details to persist between screens\n    const [personalDetails, setLocalPersonalDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        caption: '',\n        lifePartner: '',\n        weddingStyle: '',\n        place: '',\n        eventType: '',\n        budget: ''\n    });\n    // Auto-select the type if initialType is provided\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UploadManager.useEffect\": ()=>{\n            if (initialType) {\n                console.log('Auto-selecting type from initialType:', initialType);\n                handleTypeSelected(initialType);\n            }\n        }\n    }[\"UploadManager.useEffect\"], []);\n    // Store vendor details to persist between screens\n    const [vendorDetailsData, setVendorDetailsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        venue: {\n            name: '',\n            mobileNumber: ''\n        },\n        photographer: {\n            name: '',\n            mobileNumber: ''\n        },\n        makeupArtist: {\n            name: '',\n            mobileNumber: ''\n        },\n        decorations: {\n            name: '',\n            mobileNumber: ''\n        },\n        caterer: {\n            name: '',\n            mobileNumber: ''\n        }\n    });\n    // Use the new media upload hook\n    const { mutate: uploadMedia, isPending: isUploading } = (0,_hooks_useMedia__WEBPACK_IMPORTED_MODULE_12__.useMediaUpload)();\n    // Handle media type selection\n    const handleTypeSelected = (type)=>{\n        // First, completely reset everything\n        resetUpload();\n        setPreviewImage(null);\n        setThumbnailImage(null);\n        // Then set the new type\n        setSelectedType(type);\n        console.log(\"Selected type:\", type);\n        // Set the moments flag in the upload context after reset completes\n        setTimeout(()=>{\n            if (type === 'moments') {\n                setIsMoments(true);\n                console.log(\"UPLOAD MANAGER - Setting isMoments to true (after reset)\");\n            } else {\n                setIsMoments(false);\n            }\n        }, 10); // Small delay to ensure reset completes first\n        if ([\n            'flashes',\n            'glimpses',\n            'movies',\n            'photos',\n            'moments'\n        ].includes(type)) {\n            // For explicit video types, photos, and moments, set the appropriate media type\n            if (type === 'photos') {\n                console.log('Setting media type to photo for:', type);\n                setMediaType('photo');\n                setMediaSubtype('post');\n                // Go to category selection for photos\n                setPhase('categorySelection');\n            } else if (type === 'moments') {\n                console.log('Setting media type for moments (will be determined by file type)');\n                // For moments, we'll set the media type later based on the file type (photo or video)\n                setMediaSubtype('story');\n                // For moments, skip category selection and go directly to file upload\n                console.log('Moments selected: skipping category selection, going directly to file upload');\n                handleFileUpload();\n                return; // Early return to prevent further processing\n            } else {\n                setMediaType('video');\n                setMediaSubtype(getMediaSubtypeFromSelectedType(type));\n                // Go to category selection for videos\n                setPhase('categorySelection');\n            }\n        } else if (type === 'photo') {\n            // For single photo type (if it exists)\n            console.log('Setting media type to photo for:', type);\n            setMediaType('photo');\n            setMediaSubtype('post');\n            // Use a special photo-only upload handler for photos\n            handlePhotoUpload();\n        }\n    };\n    // Helper function to get the backend media subtype from the selected UI type\n    const getMediaSubtypeFromSelectedType = (type)=>{\n        // Map UI category to backend category for media_subtype\n        switch(type){\n            // Photo types\n            case 'moments':\n                return 'story'; // Backend expects 'story' for moments\n            case 'photos':\n                return 'post'; // Backend expects 'post' for regular photos\n            // Video types\n            case 'flashes':\n                return 'flash'; // Backend expects 'flash'\n            case 'glimpses':\n                return 'glimpse'; // Backend expects 'glimpse'\n            case 'movies':\n                return 'movie'; // Backend expects 'movie'\n            // Default fallback\n            default:\n                return type === 'moments' ? 'story' : 'post'; // Default based on type\n        }\n    };\n    // Handle category selection for both videos and photos\n    const handleCategorySelected = (category)=>{\n        // First, make sure we have a clean state for the new upload\n        // but preserve the selected type and media type\n        const currentType = selectedType;\n        const currentMediaType = state.mediaType;\n        resetUpload();\n        setSelectedType(currentType);\n        setMediaType(currentMediaType);\n        setPreviewImage(null);\n        setThumbnailImage(null);\n        // Now set the new category\n        setSelectedCategory(category);\n        // Get the media subtype based on the selected type\n        let mediaSubtype;\n        if (currentType === 'photos') {\n            // For photos, always use 'post' as the media subtype\n            mediaSubtype = 'post';\n            console.log(\"UPLOAD MANAGER - Using media subtype 'post' for photos\");\n        } else {\n            // For videos, use the subtype based on the selected type\n            mediaSubtype = getMediaSubtypeFromSelectedType(selectedType);\n            console.log(\"UPLOAD MANAGER - Using media subtype \".concat(mediaSubtype, \" based on selected type \").concat(selectedType));\n            console.log(\"UPLOAD MANAGER - Backend expects: flashes→flash, glimpses→glimpse, movies→movie, moments→story\");\n        }\n        console.log(\"UPLOAD MANAGER - Selected category:\", category);\n        console.log(\"UPLOAD MANAGER - Setting media subtype to:\", mediaSubtype);\n        // Set the media subtype in the context\n        setMediaSubtype(mediaSubtype);\n        // Map the selected category to a valid backend video_category\n        let backendVideoCategory = '';\n        if (category === 'my_wedding_videos') {\n            backendVideoCategory = 'my_wedding';\n        } else if (category === 'wedding_vlog') {\n            backendVideoCategory = 'wedding_vlog';\n        }\n        // Make sure we have a valid video_category\n        if (!backendVideoCategory) {\n            console.error('Invalid video category selected:', category);\n            alert('Please select a valid video category');\n            return;\n        }\n        // Set video category in the context for the backend\n        console.log(\"UPLOAD MANAGER - Setting video_category to:\", backendVideoCategory);\n        setDetailField('video_category', backendVideoCategory);\n        // Log the final values\n        console.log(\"UPLOAD MANAGER - Selected category:\", category);\n        console.log(\"UPLOAD MANAGER - Backend video category set to:\", backendVideoCategory);\n        console.log(\"UPLOAD MANAGER - Media subtype set to:\", mediaSubtype);\n        // Proceed to file upload after setting the category\n        if (currentType === 'photos') {\n            // For photos, use the photo-specific upload handler\n            handlePhotoUpload();\n        } else {\n            // For videos, use the standard file upload handler\n            handleFileUpload();\n        }\n    };\n    // Handle thumbnail upload\n    const handleThumbnailUpload = ()=>{\n        // Create a file input element\n        const input = document.createElement('input');\n        input.type = 'file';\n        input.accept = 'image/*';\n        // Handle file selection\n        input.onchange = (e)=>{\n            const files = e.target.files;\n            if (files && files.length > 0) {\n                const file = files[0];\n                // Store the thumbnail\n                setThumbnailImage(file);\n                setThumbnail(file);\n                console.log(\"Thumbnail selected:\", file.name);\n                // Show a preview if needed\n                const reader = new FileReader();\n                reader.onload = (e)=>{\n                    var _e_target;\n                    if ((_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result) {\n                        // You could set a thumbnail preview here if needed\n                        console.log(\"Thumbnail preview ready\");\n                    }\n                };\n                reader.readAsDataURL(file);\n            }\n        };\n        // Trigger the file dialog\n        input.click();\n    };\n    // Get user-friendly display name for a category\n    const getCategoryDisplayName = (category)=>{\n        switch(category){\n            case 'flash':\n                return 'Flash';\n            case 'glimpse':\n                return 'Glimpse';\n            case 'movie':\n                return 'Movie';\n            case 'story':\n                return 'Story';\n            case 'post':\n                return 'Photo';\n            default:\n                return category.charAt(0).toUpperCase() + category.slice(1);\n        }\n    };\n    // Get appropriate category based on duration\n    const getAppropriateCategory = (duration)=>{\n        // For very short videos (1 minute or less), use flash instead of story/moments\n        if (duration <= 60) {\n            return 'flash'; // Very short videos (1 minute or less) - changed from 'story' to 'flash'\n        } else if (duration <= 90) {\n            return 'flash'; // Short videos (1.5 minutes or less)\n        } else if (duration <= 420) {\n            return 'glimpse'; // Medium videos (7 minutes or less)\n        } else {\n            return 'movie'; // Long videos (over 7 minutes)\n        }\n    };\n    // Special handler for photo uploads that strictly enforces image-only files\n    const handlePhotoUpload = ()=>{\n        console.log('handlePhotoUpload called - strict image-only upload');\n        // Create a file input element specifically for photos\n        const input = document.createElement('input');\n        input.type = 'file';\n        // Reset the input value\n        input.value = '';\n        // Only accept image files - explicitly list allowed types\n        input.accept = 'image/jpeg,image/png,image/gif,image/webp';\n        // Handle file selection with strict validation\n        input.onchange = async (e)=>{\n            const files = e.target.files;\n            if (!files || files.length === 0) return;\n            const file = files[0];\n            console.log('Photo file selected:', file.name, file.type, file.size);\n            // Strict validation - must be an image file\n            const validImageTypes = [\n                'image/jpeg',\n                'image/png',\n                'image/gif',\n                'image/webp'\n            ];\n            if (!validImageTypes.includes(file.type)) {\n                console.error('Invalid file type for photos:', file.type);\n                alert('Only JPEG, PNG, GIF, or WebP images can be uploaded as photos. Videos are not allowed.');\n                return;\n            }\n            // Additional check - reject any file that might be a video\n            if (file.type.startsWith('video/')) {\n                console.error('Attempted to upload a video file as photo');\n                alert('Videos cannot be uploaded as photos. Please select an image file.');\n                return;\n            }\n            // For photos, we need to be more careful with state management\n            // First, set the media type and subtype\n            setMediaType('photo');\n            setMediaSubtype('post');\n            // Then set the file in the state\n            setFile(file);\n            console.log('Photo file set in state:', file.name);\n            // Create a local reference to the file for use in the timeout\n            const currentFile = file;\n            // Double-check that the file is set in the state before proceeding\n            setTimeout(()=>{\n                // Check if the file is in the state\n                if (!state.file) {\n                    console.log('File not found in state after setting, trying again');\n                    // Try setting the file again\n                    setFile(currentFile);\n                    // Add another timeout to ensure the file is set\n                    setTimeout(()=>{\n                        if (!state.file) {\n                            console.log('File still not in state, setting it one more time');\n                            setFile(currentFile);\n                        } else {\n                            console.log('File confirmed in state after second attempt:', state.file.name);\n                        }\n                        // New flow logic: For moments (stories), skip personal details and go directly to face verification\n                        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                            console.log('Moments photo upload: skipping personal details, going directly to face verification');\n                            setPhase('faceVerification');\n                        } else {\n                            console.log('Moving to personalDetails phase for photo');\n                            setPhase('personalDetails');\n                        }\n                    }, 100);\n                } else {\n                    console.log('File confirmed in state:', state.file.name);\n                    // New flow logic: For moments (stories), skip personal details and go directly to face verification\n                    if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                        console.log('Moments photo upload: skipping personal details, going directly to face verification');\n                        setPhase('faceVerification');\n                    } else {\n                        console.log('Moving to personalDetails phase for photo');\n                        setPhase('personalDetails');\n                    }\n                }\n            }, 100);\n            // Handle image preview\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                var _e_target;\n                if ((_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result) {\n                    setPreviewImage(e.target.result);\n                    console.log('Preview image set for photo');\n                }\n            };\n            reader.readAsDataURL(file);\n        // Note: We don't set the phase here anymore - it's handled in the timeout above\n        };\n        // Trigger the file dialog\n        input.click();\n    };\n    // This function was previously used but is now replaced by getAppropriateCategory\n    // Keeping a comment here for reference in case it needs to be restored\n    // Handle manual upload button click\n    const handleFileUpload = async (category)=>{\n        console.log('handleFileUpload called with category:', category || 'none');\n        // Create a file input element\n        const input = document.createElement('input');\n        input.type = 'file';\n        // Reset the input value to ensure we get a new file selection event even if the same file is selected\n        input.value = '';\n        if (selectedType === 'moments') {\n            input.accept = 'image/*,video/*';\n        } else {\n            input.accept = selectedType === 'photo' || selectedType === 'photos' ? 'image/jpeg,image/png,image/gif,image/webp' // Explicitly list image types only\n             : 'video/*';\n        }\n        // Handle file selection\n        input.onchange = async (e)=>{\n            const files = e.target.files;\n            if (!files || files.length === 0) return;\n            const file = files[0];\n            console.log('File selected:', file.name, file.type, file.size);\n            console.log('UPLOAD MANAGER - selectedType before resetUpload:', selectedType);\n            // Strict validation for photo uploads - must be an image file\n            if (selectedType === 'photo' || selectedType === 'photos') {\n                const validImageTypes = [\n                    'image/jpeg',\n                    'image/png',\n                    'image/gif',\n                    'image/webp'\n                ];\n                // Check if file is a video or not a valid image type\n                if (file.type.startsWith('video/') || !validImageTypes.includes(file.type)) {\n                    console.error('Invalid file type for photos:', file.type);\n                    (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Invalid File Type', 'Only JPEG, PNG, GIF, or WebP images can be uploaded as photos. Videos are not allowed.');\n                    return;\n                }\n            }\n            // Reset the upload context before setting the new file\n            // Preserve the isMoments flag before reset\n            const preserveIsMoments = state.isMoments;\n            console.log('UPLOAD MANAGER - Preserving isMoments:', preserveIsMoments);\n            resetUpload();\n            console.log('UPLOAD MANAGER - selectedType after resetUpload:', selectedType);\n            // Restore the isMoments flag after reset\n            if (preserveIsMoments) {\n                setIsMoments(true);\n                console.log('UPLOAD MANAGER - Restored isMoments to true after reset');\n            }\n            // Set the file in the state\n            setFile(file);\n            console.log('File set in state:', file.name);\n            // If it's a video, calculate and set the duration\n            // Double-check that we're not trying to upload a video as a photo\n            if (file.type.startsWith('video/')) {\n                // Safety check - don't process videos for photo uploads\n                if (selectedType === 'photo' || selectedType === 'photos') {\n                    console.error('Attempted to process a video file for photo upload');\n                    (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Invalid File Type', 'Videos cannot be uploaded as photos. Please select an image file.');\n                    resetUpload();\n                    return;\n                }\n                try {\n                    const duration = await (0,_utils_uploadUtils__WEBPACK_IMPORTED_MODULE_10__.getVideoDuration)(file);\n                    console.log('Video duration calculated:', duration);\n                    setDuration(duration);\n                    // For moments, check if it's a video and validate the duration (max 1 minute)\n                    if (selectedType === 'moments') {\n                        console.log('Validating moments video duration...');\n                        setMediaType('video');\n                        // Check if the video is longer than 1 minute (60 seconds)\n                        if (duration > 60) {\n                            console.log(\"Moments video too long: \".concat(duration, \" seconds (max: 60 seconds)\"));\n                            // Show a more detailed error message with custom alert\n                            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showWarningAlert)('Moments Video Too Long', \"Moments videos must be 1 minute or less.\\n\\nYour video is \".concat(formatTime(duration), \" long.\\n\\nPlease select a shorter video or trim this video to 1 minute or less.\"));\n                            // Reset the upload context but preserve the selected type and category\n                            const currentType = selectedType;\n                            const currentCategory = selectedCategory;\n                            // First set the phase back to category selection\n                            setPhase('categorySelection');\n                            // Then reset the upload state\n                            setTimeout(()=>{\n                                resetUpload();\n                                setSelectedType(currentType);\n                                setSelectedCategory(currentCategory);\n                                console.log('Reset upload state after moments video duration validation failure');\n                            }, 100);\n                            // Return early to prevent further processing\n                            return;\n                        }\n                        console.log(\"Moments video duration valid: \".concat(duration, \" seconds (max: 60 seconds)\"));\n                        // For moments, we always use 'story' as the media subtype\n                        console.log('Setting media subtype for moments video to story');\n                        setMediaSubtype('story');\n                    }\n                    // If we have a category, validate the duration for that category\n                    if (selectedType && [\n                        'flashes',\n                        'glimpses',\n                        'movies'\n                    ].includes(selectedType)) {\n                        const mediaSubtype = getMediaSubtypeFromSelectedType(selectedType);\n                        const validationResult = (0,_utils_uploadUtils__WEBPACK_IMPORTED_MODULE_10__.validateVideoDuration)(duration, mediaSubtype);\n                        if (!validationResult.isValid) {\n                            // If there's a suggested category, automatically switch to it\n                            if (validationResult.suggestedCategory) {\n                                // For videos that exceed the maximum duration, automatically switch without asking\n                                console.log(\"Video exceeds maximum duration for \".concat(mediaSubtype, \". Automatically switching to \").concat(validationResult.suggestedCategory));\n                                (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showWarningAlert)('Video Duration Notice', \"Your video is too long for the \".concat(getCategoryDisplayName(mediaSubtype), \" category. It will be uploaded as a \").concat(getCategoryDisplayName(validationResult.suggestedCategory), \" instead.\"));\n                                // Switch to the suggested category\n                                console.log(\"Switching to suggested category: \".concat(validationResult.suggestedCategory));\n                                // Make sure we keep a reference to the file\n                                const currentFile = file;\n                                // Update the media subtype\n                                setMediaSubtype(validationResult.suggestedCategory);\n                                // Update the selected type to match the new category\n                                // Never suggest 'story' (moments) for other categories\n                                if (validationResult.suggestedCategory === 'flash') {\n                                    setSelectedType('flashes');\n                                } else if (validationResult.suggestedCategory === 'glimpse') {\n                                    setSelectedType('glimpses');\n                                } else if (validationResult.suggestedCategory === 'movie') {\n                                    setSelectedType('movies');\n                                }\n                                // Removed the 'story' suggestion for short videos\n                                // Make sure the file is still set in the state\n                                setTimeout(()=>{\n                                    if (!state.file) {\n                                        console.log('Re-setting file after category change:', currentFile.name);\n                                        setFile(currentFile);\n                                    }\n                                }, 50);\n                            } else {\n                                // No suggested category, just show the error\n                                (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Video Duration Error', validationResult.error || 'The video duration is not valid for this category.');\n                            }\n                        } else if (validationResult.suggestedCategory && validationResult.suggestedCategory !== mediaSubtype) {\n                            // Video is valid for current category but there's a better category\n                            // For this case, we still give the user a choice since the video is valid for the current category\n                            // Use our custom confirm dialog instead of window.confirm\n                            const confirmSwitch = await (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showAlert)({\n                                title: 'Category Suggestion',\n                                message: \"\".concat(validationResult.error, \"\\n\\nWould you like to switch to the suggested category?\"),\n                                type: 'warning',\n                                confirmText: 'Yes, Switch Category',\n                                cancelText: 'No, Keep Current',\n                                onConfirm: ()=>{}\n                            });\n                            if (confirmSwitch) {\n                                // Switch to the suggested category\n                                console.log(\"Switching to suggested category: \".concat(validationResult.suggestedCategory));\n                                // Make sure we keep a reference to the file\n                                const currentFile = file;\n                                // Update the media subtype\n                                setMediaSubtype(validationResult.suggestedCategory);\n                                // Update the selected type to match the new category\n                                // Never suggest 'story' (moments) for other categories\n                                if (validationResult.suggestedCategory === 'flash') {\n                                    setSelectedType('flashes');\n                                } else if (validationResult.suggestedCategory === 'glimpse') {\n                                    setSelectedType('glimpses');\n                                } else if (validationResult.suggestedCategory === 'movie') {\n                                    setSelectedType('movies');\n                                }\n                                // Removed the 'story' suggestion for short videos\n                                // Make sure the file is still set in the state\n                                setTimeout(()=>{\n                                    if (!state.file) {\n                                        console.log('Re-setting file after category change:', currentFile.name);\n                                        setFile(currentFile);\n                                    }\n                                }, 50);\n                            }\n                        }\n                    }\n                    // Always go to thumbnail selection for videos\n                    console.log('Moving to thumbnailSelection phase');\n                    // Keep a reference to the current file\n                    const currentFile = file;\n                    // Double-check that the file is set in the state before proceeding\n                    if (state.file) {\n                        console.log('File confirmed in state before phase change:', state.file.name);\n                        setPhase('thumbnailSelection');\n                    } else {\n                        console.log('File not found in state before phase change, setting it again');\n                        // Try setting the file again\n                        setFile(currentFile);\n                        // Add a small delay to ensure the state is updated\n                        setTimeout(()=>{\n                            // Double-check again\n                            if (!state.file) {\n                                console.log('File still not in state, setting it one more time');\n                                setFile(currentFile);\n                            }\n                            console.log('Delayed phase change to thumbnailSelection');\n                            setPhase('thumbnailSelection');\n                        }, 100);\n                    }\n                } catch (error) {\n                    console.error('Error calculating video duration:', error);\n                    // For moments videos, we need to enforce the duration check\n                    // If we can't calculate duration, we can't validate it, so we should reject the upload\n                    if (selectedType === 'moments') {\n                        (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Video Error', 'Unable to determine video duration. Please try a different video file.');\n                        resetUpload();\n                        return;\n                    }\n                    console.log('Moving to thumbnailSelection phase despite error');\n                    // Keep a reference to the current file\n                    const currentFile = file;\n                    // Double-check that the file is set in the state before proceeding\n                    if (state.file) {\n                        console.log('File confirmed in state before phase change (error case):', state.file.name);\n                        setPhase('thumbnailSelection');\n                    } else {\n                        console.log('File not found in state before phase change (error case), setting it again');\n                        // Try setting the file again\n                        setFile(currentFile);\n                        // Add a small delay to ensure the state is updated\n                        setTimeout(()=>{\n                            // Double-check again\n                            if (!state.file) {\n                                console.log('File still not in state (error case), setting it one more time');\n                                setFile(currentFile);\n                            }\n                            console.log('Delayed phase change to thumbnailSelection (error case)');\n                            setPhase('thumbnailSelection');\n                        }, 100);\n                    }\n                }\n            } else {\n                // For photos or moments images\n                console.log('UPLOAD MANAGER - selectedType before moments check:', selectedType);\n                if (selectedType === 'moments') {\n                    // For moments, we need to set the media type based on the file type\n                    if (file.type.startsWith('image/')) {\n                        console.log('Moments image detected');\n                        setMediaType('photo');\n                        // For moments images, we always use 'story' as the media subtype\n                        setMediaSubtype('story');\n                        // Create a local reference to the file for use in the timeout\n                        const currentFile = file;\n                        // Double-check that the file is set in the state before proceeding\n                        setTimeout(()=>{\n                            // Check if the file is in the state\n                            if (!state.file) {\n                                console.log('Moments photo not found in state after setting, trying again');\n                                // Try setting the file again\n                                setFile(currentFile);\n                            } else {\n                                console.log('Moments photo confirmed in state:', state.file.name);\n                            }\n                        }, 50);\n                    } else {\n                        console.log('Invalid file type for moments. Only images and videos less than 1 minute are allowed.');\n                        (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Invalid File Type', 'Invalid file type for moments. Only images and videos less than 1 minute are allowed.');\n                        // Reset the upload context but preserve the selected type and category\n                        const currentType = selectedType;\n                        const currentCategory = selectedCategory;\n                        // First set the phase back to category selection\n                        setPhase('categorySelection');\n                        // Then reset the upload state\n                        setTimeout(()=>{\n                            resetUpload();\n                            setSelectedType(currentType);\n                            setSelectedCategory(currentCategory);\n                            console.log('Reset upload state after invalid file type for moments');\n                        }, 100);\n                        return;\n                    }\n                }\n                // Handle image preview and set phase\n                const reader = new FileReader();\n                reader.onload = (e)=>{\n                    var _e_target;\n                    if ((_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result) {\n                        setPreviewImage(e.target.result);\n                        console.log('Preview image set for file:', file.name);\n                    }\n                };\n                reader.readAsDataURL(file);\n                // Create a local reference to the file for use in the timeout\n                const currentFile = file;\n                // Double-check that the file is set in the state before proceeding\n                setTimeout(()=>{\n                    // Check if the file is in the state\n                    if (!state.file) {\n                        console.log('File not found in state before moving to personalDetails, setting it again');\n                        // Try setting the file again\n                        setFile(currentFile);\n                        // Add another timeout to ensure the file is set\n                        setTimeout(()=>{\n                            if (!state.file) {\n                                console.log('File still not in state, setting it one more time');\n                                setFile(currentFile);\n                            } else {\n                                console.log('File confirmed in state after second attempt:', state.file.name);\n                            }\n                            // New flow logic: For moments (stories), skip personal details and go directly to face verification\n                            if (state.isMoments) {\n                                console.log('Moments image upload: skipping personal details, going directly to face verification');\n                                console.log('UPLOAD MANAGER - selectedType:', selectedType);\n                                console.log('UPLOAD MANAGER - state.isMoments:', state.isMoments);\n                                console.log('UPLOAD MANAGER - state.mediaSubtype:', state.mediaSubtype);\n                                setPhase('faceVerification');\n                            } else {\n                                console.log('Moving to personalDetails phase for photo/image');\n                                console.log('UPLOAD MANAGER - selectedType:', selectedType);\n                                console.log('UPLOAD MANAGER - state.isMoments:', state.isMoments);\n                                console.log('UPLOAD MANAGER - state.mediaSubtype:', state.mediaSubtype);\n                                setPhase('personalDetails');\n                            }\n                        }, 100);\n                    } else {\n                        console.log('File confirmed in state:', state.file.name);\n                        // New flow logic: For moments (stories), skip personal details and go directly to face verification\n                        if (state.isMoments) {\n                            console.log('Moments image upload: skipping personal details, going directly to face verification');\n                            console.log('UPLOAD MANAGER - selectedType:', selectedType);\n                            console.log('UPLOAD MANAGER - state.isMoments:', state.isMoments);\n                            console.log('UPLOAD MANAGER - state.mediaSubtype:', state.mediaSubtype);\n                            setPhase('faceVerification');\n                        } else {\n                            console.log('Moving to personalDetails phase for photo/image');\n                            console.log('UPLOAD MANAGER - selectedType:', selectedType);\n                            console.log('UPLOAD MANAGER - state.isMoments:', state.isMoments);\n                            console.log('UPLOAD MANAGER - state.mediaSubtype:', state.mediaSubtype);\n                            setPhase('personalDetails');\n                        }\n                    }\n                }, 100);\n            }\n        };\n        // Trigger the file dialog\n        input.click();\n    };\n    // Handle personal details completed\n    const handlePersonalDetailsCompleted = (details)=>{\n        console.log('Personal details completed:', details);\n        // Store the personal details in local state for component persistence\n        setLocalPersonalDetails(details);\n        // Validate that we have a title\n        if (!details.caption || !details.caption.trim()) {\n            console.error('Caption/title is empty, this should not happen');\n            // Go back to personal details to fix this\n            setPhase('personalDetails');\n            return;\n        }\n        // Set the title in the upload context\n        setTitle(details.caption.trim());\n        // Also store in global context for persistence (this is the upload context function)\n        setPersonalDetails(details);\n        // Check if we have a file in the state\n        if (!state.file) {\n            console.error('No file found in state after personal details');\n            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Upload Error', 'Something went wrong with your file upload. Please try again.');\n            setPhase('typeSelection');\n            return;\n        }\n        console.log('File confirmed in state after personal details:', state.file.name);\n        console.log('Personal details set successfully');\n        console.log('Title set to:', details.caption.trim());\n        console.log('Current selectedType:', selectedType);\n        console.log('Current mediaSubtype:', state.mediaSubtype);\n        // New flow logic based on backend requirements:\n        // - Moments (stories): Skip personal details, go directly to face verification\n        // - Photos: Go to face verification after personal details (no vendor details)\n        // - Videos: Go to vendor details after personal details\n        if (state.mediaType === 'photo') {\n            console.log('Photo upload: proceeding to face verification (no vendor details needed)');\n            setPhase('faceVerification');\n        } else if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n            console.log('Moments upload: proceeding to face verification (no vendor details needed)');\n            setPhase('faceVerification');\n        } else {\n            // For videos (flashes, glimpses, movies), proceed to vendor details\n            console.log('Video upload: proceeding to vendor details');\n            setPhase('vendorDetails');\n        }\n    };\n    // Handle vendor details completed\n    const handleVendorDetailsCompleted = (vendorDetails)=>{\n        // console.log('Vendor details completed:', vendorDetails);\n        // Normalize vendor details to ensure consistent field names\n        const normalizedVendorDetails = {\n            ...vendorDetails\n        };\n        // Ensure we have both frontend and backend field names for makeup artist and decorations\n        if (vendorDetails.makeupArtist) {\n            normalizedVendorDetails.makeup_artist = vendorDetails.makeupArtist;\n        } else if (vendorDetails.makeup_artist) {\n            normalizedVendorDetails.makeupArtist = vendorDetails.makeup_artist;\n        }\n        if (vendorDetails.decorations) {\n            normalizedVendorDetails.decoration = vendorDetails.decorations;\n        } else if (vendorDetails.decoration) {\n            normalizedVendorDetails.decorations = vendorDetails.decoration;\n        }\n        // Store the normalized vendor details for persistence between screens\n        setVendorDetailsData(normalizedVendorDetails);\n        // Also store in the ref for Edge browser compatibility\n        vendorDetailsRef.current = normalizedVendorDetails;\n        // Store vendor details in localStorage for persistence\n        try {\n            localStorage.setItem('wedzat_vendor_details', JSON.stringify(normalizedVendorDetails));\n            console.log('UPLOAD MANAGER - Stored vendor details in localStorage');\n        } catch (error) {\n            console.error('UPLOAD MANAGER - Failed to store vendor details in localStorage:', error);\n        }\n        // Save the current video_category before setting vendor details\n        const currentVideoCategory = state.detailFields.video_category;\n        console.log('UPLOAD MANAGER - Saving video_category before vendor details:', currentVideoCategory);\n        // Store video_category in localStorage\n        if (currentVideoCategory) {\n            try {\n                localStorage.setItem('wedzat_video_category', currentVideoCategory);\n                console.log('UPLOAD MANAGER - Stored video_category in localStorage:', currentVideoCategory);\n            } catch (error) {\n                console.error('UPLOAD MANAGER - Failed to store video_category in localStorage:', error);\n            }\n        }\n        // Store in global context for persistence\n        setVendorDetails(normalizedVendorDetails);\n        // Explicitly set each vendor detail field\n        Object.entries(normalizedVendorDetails).forEach((param)=>{\n            let [vendorType, details] = param;\n            if (details && details.name && details.mobileNumber) {\n                setDetailField(\"vendor_\".concat(vendorType, \"_name\"), details.name);\n                setDetailField(\"vendor_\".concat(vendorType, \"_contact\"), details.mobileNumber);\n            }\n        });\n        // Re-set the video_category after vendor details to ensure it's preserved\n        if (currentVideoCategory) {\n            console.log('UPLOAD MANAGER - Re-setting video_category after vendor details:', currentVideoCategory);\n            setTimeout(()=>{\n                setDetailField('video_category', currentVideoCategory);\n            }, 100);\n        }\n        // Log all detail fields after setting vendor details\n        setTimeout(()=>{\n            console.log('All detail fields after vendor details:', state.detailFields);\n            console.log('Detail fields count:', Object.keys(state.detailFields).length);\n            console.log('Normalized vendor details:', normalizedVendorDetails);\n        }, 200);\n        // Add a small delay to ensure the state is updated before proceeding\n        // This helps with cross-browser compatibility, especially in Edge\n        setTimeout(()=>{\n            // Double-check that we have at least 4 vendor details before proceeding\n            const vendorNameFields = Object.keys(state.detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_name'));\n            const vendorContactFields = Object.keys(state.detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_contact'));\n            console.log('UPLOAD MANAGER - Vendor name fields:', vendorNameFields.length);\n            console.log('UPLOAD MANAGER - Vendor contact fields:', vendorContactFields.length);\n            // Edge browser workaround - directly set vendor details in the state\n            if ( true && /Edge|Edg/.test(window.navigator.userAgent)) {\n                console.log('UPLOAD MANAGER - Edge browser detected, applying direct vendor details workaround');\n                // Create vendor detail fields directly in the state\n                // This is a workaround for Edge browser where the state update doesn't properly preserve vendor details\n                Object.entries(normalizedVendorDetails).forEach((param)=>{\n                    let [vendorType, details] = param;\n                    if (details && details.name && details.mobileNumber) {\n                        // Set the vendor details directly in the state\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                        // Also set the normalized version\n                        const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                        if (normalizedType !== vendorType) {\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                        }\n                        console.log(\"UPLOAD MANAGER - Edge workaround: Added vendor \".concat(vendorType, \" directly to state\"));\n                    }\n                });\n                // Re-set the video_category directly\n                if (currentVideoCategory) {\n                    state.detailFields.video_category = currentVideoCategory;\n                    console.log('UPLOAD MANAGER - Edge workaround: Re-set video_category directly:', currentVideoCategory);\n                }\n            }\n            // Proceed to face verification\n            setPhase('faceVerification');\n        }, 300);\n    };\n    // Handle thumbnail selection\n    const handleThumbnailSelected = (thumbnailFile)=>{\n        if (thumbnailFile) {\n            // Set the thumbnail in the context\n            setThumbnail(thumbnailFile);\n            console.log('Thumbnail selected:', thumbnailFile.name);\n        } else {\n            console.log('No thumbnail selected, using auto-generated thumbnail');\n        }\n        // New flow logic: For moments (stories), skip personal details and go directly to face verification\n        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n            console.log('Moments upload: skipping personal details, going directly to face verification');\n            setPhase('faceVerification');\n        } else {\n            // For photos and videos, go to personal details\n            console.log('Photo/Video upload: proceeding to personal details');\n            setPhase('personalDetails');\n        }\n    };\n    // Function to proceed with upload after vendor details are applied\n    const proceedWithUpload = (videoCategory)=>{\n        // For moments (stories), this function should not be called, but add safety check\n        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n            console.log('UPLOAD MANAGER - Moments detected in proceedWithUpload, calling startUpload directly');\n            startUpload();\n            return;\n        }\n        // Double-check that we have a title before changing to uploading phase\n        if (!state.title || !state.title.trim()) {\n            console.error('Title is missing before upload, setting it from personal details');\n            // Try to set the title from personal details\n            if (personalDetails.caption && personalDetails.caption.trim()) {\n                // console.log('Setting personal details from local state:', personalDetails);\n                // Use the global context function to set all personal details at once\n                setPersonalDetails(personalDetails);\n                // Explicitly set the title as well\n                setTitle(personalDetails.caption.trim());\n            } else {\n                console.error('No title in personal details either, going back to personal details');\n                setPhase('personalDetails');\n                return;\n            }\n        }\n        // Edge browser workaround - directly set vendor details in the state\n        if ( true && /Edge|Edg/.test(window.navigator.userAgent)) {\n            console.log('UPLOAD MANAGER - Edge browser detected in face verification, applying vendor details workaround');\n            // Get the vendor details from the vendor details data\n            const vendorDetailsData = vendorDetailsRef.current;\n            if (vendorDetailsData) {\n                console.log('UPLOAD MANAGER - Edge workaround: Using vendor details from ref:', vendorDetailsData);\n                // Create vendor detail fields directly in the state\n                Object.entries(vendorDetailsData).forEach((param)=>{\n                    let [vendorType, details] = param;\n                    if (details && details.name && details.mobileNumber) {\n                        // Set the vendor details directly in the state\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                        // Also set the normalized version\n                        const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                        if (normalizedType !== vendorType) {\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                        }\n                        console.log(\"UPLOAD MANAGER - Edge workaround: Added vendor \".concat(vendorType, \" directly to state\"));\n                    }\n                });\n            }\n        }\n        // For videos, check if we have a video_category\n        if (state.mediaType === 'video') {\n            console.log(\"UPLOAD MANAGER - Checking video_category before upload\");\n            console.log(\"UPLOAD MANAGER - Current video_category: \".concat(state.detailFields.video_category || 'Not set'));\n            console.log(\"UPLOAD MANAGER - Current mediaSubtype: \".concat(state.mediaSubtype));\n            console.log(\"UPLOAD MANAGER - Selected category: \".concat(selectedCategory || 'Not set'));\n            // Special handling for glimpses\n            if (state.mediaSubtype === 'glimpse') {\n                console.log(\"UPLOAD MANAGER - Special handling for glimpses\");\n                // If we don't have a video_category yet, try to set it from selectedCategory\n                if (!state.detailFields.video_category && selectedCategory) {\n                    // Map the UI category to the backend video_category\n                    let videoCategory = '';\n                    if (selectedCategory === 'my_wedding_videos') {\n                        videoCategory = 'my_wedding';\n                    } else if (selectedCategory === 'wedding_vlog') {\n                        videoCategory = 'wedding_vlog';\n                    } else if (selectedCategory === 'friends_family_videos') {\n                        videoCategory = 'friends_family_video';\n                    }\n                    if (videoCategory) {\n                        console.log(\"UPLOAD MANAGER - Setting video_category for glimpse: \".concat(videoCategory));\n                        setDetailField('video_category', videoCategory);\n                    }\n                } else {\n                    console.log(\"UPLOAD MANAGER - Glimpse already has video_category: \".concat(state.detailFields.video_category));\n                }\n            }\n            // If we still don't have a video_category, use a default based on selectedCategory\n            if (!state.detailFields.video_category && selectedCategory) {\n                console.log(\"UPLOAD MANAGER - No video_category set, using selectedCategory: \".concat(selectedCategory));\n                // Map the UI category to the backend video_category\n                let videoCategory = '';\n                if (selectedCategory === 'my_wedding_videos') {\n                    videoCategory = 'my_wedding';\n                } else if (selectedCategory === 'wedding_vlog') {\n                    videoCategory = 'wedding_vlog';\n                } else if (selectedCategory === 'friends_family_videos') {\n                    videoCategory = 'friends_family_video';\n                }\n                if (videoCategory) {\n                    console.log(\"UPLOAD MANAGER - Setting video_category from selectedCategory: \".concat(videoCategory));\n                    setDetailField('video_category', videoCategory);\n                }\n            }\n            // Final check - if we still don't have a video_category, use a default\n            if (!state.detailFields.video_category) {\n                console.log('No video_category found, using a default one');\n                // Use 'my_wedding' as a default category instead of asking the user again\n                setDetailField('video_category', 'my_wedding');\n                console.log('Set default video_category to my_wedding');\n            }\n        }\n        // Edge browser workaround - directly set vendor details in the state before upload\n        if ( true && /Edge|Edg/.test(window.navigator.userAgent)) {\n            console.log('UPLOAD MANAGER - Edge browser detected before upload, applying vendor details workaround');\n            // Get the vendor details from the vendor details data\n            const vendorDetailsData = vendorDetailsRef.current;\n            if (vendorDetailsData && Object.keys(vendorDetailsData).length > 0) {\n                console.log('UPLOAD MANAGER - Edge workaround: Using vendor details from ref before upload:', vendorDetailsData);\n                // Create vendor detail fields directly in the state\n                Object.entries(vendorDetailsData).forEach((param)=>{\n                    let [vendorType, details] = param;\n                    if (details && details.name && details.mobileNumber) {\n                        // Set the vendor details directly in the state\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                        // Also set the normalized version\n                        const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                        if (normalizedType !== vendorType) {\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                        }\n                        console.log(\"UPLOAD MANAGER - Edge workaround: Added vendor \".concat(vendorType, \" directly to state before upload\"));\n                    }\n                });\n            }\n        }\n        // Check if we have a file before proceeding\n        if (!state.file) {\n            console.error('No file found in state before upload');\n            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Upload Error', 'No file selected. Please select a file to upload.');\n            // Go back to type selection to start over\n            setPhase('typeSelection');\n            return;\n        }\n        // Now we can proceed to uploading phase\n        setPhase('uploading');\n        // Log the current state before starting upload\n        console.log('Current state before upload:', {\n            file: state.file ? state.file.name : 'No file',\n            mediaType: state.mediaType,\n            mediaSubtype: state.mediaSubtype,\n            title: state.title,\n            description: state.description,\n            detailFields: state.detailFields,\n            detailFieldsCount: Object.keys(state.detailFields).length\n        });\n        // Double-check that we're using the correct category\n        console.log(\"UPLOAD MANAGER - Final check - Selected type: \".concat(selectedType));\n        console.log(\"UPLOAD MANAGER - Final check - MediaSubtype in state: \".concat(state.mediaSubtype));\n        // If the mediaSubtype doesn't match what we expect based on the selected type, fix it\n        if (selectedType && state.mediaSubtype !== getMediaSubtypeFromSelectedType(selectedType)) {\n            console.log(\"UPLOAD MANAGER - WARNING: MediaSubtype mismatch detected!\");\n            console.log(\"UPLOAD MANAGER - Expected mediaSubtype based on selected type: \".concat(getMediaSubtypeFromSelectedType(selectedType)));\n            console.log(\"UPLOAD MANAGER - Actual mediaSubtype in state: \".concat(state.mediaSubtype));\n            console.log(\"UPLOAD MANAGER - Correcting category before upload...\");\n            // Get the corrected category\n            const correctedCategory = getMediaSubtypeFromSelectedType(selectedType);\n            console.log(\"UPLOAD MANAGER - Category corrected to: \".concat(correctedCategory));\n            // Get the video_category from the original selection\n            // We need to map it to the correct backend value\n            let videoCategory = '';\n            if (selectedCategory === 'my_wedding_videos') {\n                videoCategory = 'my_wedding';\n            } else if (selectedCategory === 'wedding_vlog') {\n                videoCategory = 'wedding_vlog';\n            } else if (selectedCategory === 'friends_family_videos') {\n                videoCategory = 'friends_family_video';\n            }\n            console.log(\"UPLOAD MANAGER - Original selected category: \".concat(selectedCategory));\n            console.log(\"UPLOAD MANAGER - Mapped to backend video_category: \".concat(videoCategory));\n            // Start the upload process with the corrected category and video_category\n            startUploadWithCategory(correctedCategory, videoCategory);\n        } else {\n            // Get the video_category from the state\n            const finalVideoCategory = videoCategory || state.detailFields.video_category || 'my_wedding';\n            console.log(\"UPLOAD MANAGER - Using video_category for upload: \".concat(finalVideoCategory));\n            // Start the upload process with the current category and video_category\n            startUploadWithCategory(state.mediaSubtype, finalVideoCategory).then(()=>{\n                // Upload completed successfully\n                console.log('Upload completed successfully');\n            }).catch((error)=>{\n                console.error('Upload failed:', error);\n            });\n        }\n    };\n    // Handle moments upload with dedicated flow\n    const handleMomentsUpload = async ()=>{\n        console.log('UPLOAD MANAGER - Starting dedicated moments upload flow');\n        if (!state.file) {\n            console.error('No file found for moments upload');\n            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Upload Error', 'No file selected for upload.');\n            return;\n        }\n        // Set uploading state\n        setPhase('uploading');\n        try {\n            console.log('UPLOAD MANAGER - Uploading moments file:', state.file.name);\n            // For moments, always use 'story' subtype - backend should set is_story = true\n            const momentsSubtype = 'story';\n            console.log('UPLOAD MANAGER - Using subtype for moments:', momentsSubtype);\n            console.log('UPLOAD MANAGER - Media type:', state.mediaType);\n            console.log('UPLOAD MANAGER - Backend should set is_story = true and handle database constraints properly');\n            // Use upload service directly with minimal data for moments\n            const result = await _services_api__WEBPACK_IMPORTED_MODULE_13__.uploadService.handleUpload(state.file, state.mediaType, momentsSubtype, state.title || state.file.name.replace(/\\.[^/.]+$/, \"\"), '', [], {}, state.duration, state.thumbnail, (progress)=>{\n                console.log(\"Moments upload progress: \".concat(progress, \"%\"));\n            // You can add progress updates here if needed\n            });\n            console.log('UPLOAD MANAGER - Moments upload completed successfully:', result);\n            // Show success and reset\n            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showSuccessAlert)('Upload Successful', 'Your moment has been uploaded successfully!');\n            resetUpload();\n            setPhase('typeSelection');\n        } catch (error) {\n            console.error('UPLOAD MANAGER - Moments upload failed:', error);\n            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Upload Failed', error instanceof Error ? error.message : 'Failed to upload moment. Please try again.');\n            setPhase('faceVerification'); // Go back to face verification\n        }\n    };\n    // Handle face verification completed and start upload\n    const handleFaceVerificationCompleted = ()=>{\n        console.log('Face verification completed, starting upload process');\n        // Check if we have a file in the state\n        if (!state.file) {\n            console.error('No file found in state after face verification');\n            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Upload Error', 'Something went wrong with your file upload. Please try again.');\n            setPhase('typeSelection');\n            return;\n        }\n        console.log('File confirmed in state after face verification:', state.file.name);\n        // For moments (stories), use completely separate upload flow\n        if (selectedType === 'moments') {\n            console.log('UPLOAD MANAGER - Moments detected after face verification, using dedicated moments upload flow');\n            // Set a default title if not already set (using filename without extension)\n            if (!state.title || !state.title.trim()) {\n                const defaultTitle = state.file.name.replace(/\\.[^/.]+$/, \"\"); // Remove file extension\n                setTitle(defaultTitle);\n                console.log('UPLOAD MANAGER - Set default title for moments:', defaultTitle);\n            }\n            // Call dedicated moments upload function\n            setTimeout(()=>{\n                handleMomentsUpload();\n            }, 100);\n            return;\n        }\n        // Try to get vendor details from localStorage first\n        let vendorDetailsData = vendorDetailsRef.current;\n        // If not in ref, try localStorage\n        if (!vendorDetailsData || Object.keys(vendorDetailsData).length === 0) {\n            try {\n                const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n                if (storedVendorDetails) {\n                    vendorDetailsData = JSON.parse(storedVendorDetails);\n                    console.log('UPLOAD MANAGER - Retrieved vendor details from localStorage:', storedVendorDetails);\n                    // Update the ref with the localStorage data\n                    vendorDetailsRef.current = vendorDetailsData;\n                    // Log the vendor details we found\n                    console.log(\"UPLOAD MANAGER - Found \".concat(Object.keys(vendorDetailsData).length, \" vendor details in localStorage\"));\n                    Object.entries(vendorDetailsData).forEach((param)=>{\n                        let [vendorType, details] = param;\n                        if (details && details.name && details.mobileNumber) {\n                            console.log(\"UPLOAD MANAGER - Vendor \".concat(vendorType, \": \").concat(details.name, \" (\").concat(details.mobileNumber, \")\"));\n                        }\n                    });\n                } else {\n                    console.log('UPLOAD MANAGER - No vendor details found in localStorage');\n                }\n            } catch (error) {\n                console.error('UPLOAD MANAGER - Failed to retrieve vendor details from localStorage:', error);\n            }\n        } else {\n            console.log(\"UPLOAD MANAGER - Using \".concat(Object.keys(vendorDetailsData).length, \" vendor details from ref\"));\n        }\n        // Try to get video_category from localStorage\n        let videoCategory = state.detailFields.video_category;\n        if (!videoCategory) {\n            try {\n                const storedVideoCategory = localStorage.getItem('wedzat_video_category');\n                if (storedVideoCategory) {\n                    videoCategory = storedVideoCategory;\n                    console.log('UPLOAD MANAGER - Retrieved video_category from localStorage:', videoCategory);\n                    // Set it in the state\n                    setDetailField('video_category', videoCategory);\n                }\n            } catch (error) {\n                console.error('UPLOAD MANAGER - Failed to retrieve video_category from localStorage:', error);\n            }\n        }\n        // Ensure vendor details are present\n        if (vendorDetailsData) {\n            console.log('UPLOAD MANAGER - Applying vendor details to state');\n            // Create a batch of all detail fields to update at once\n            const detailFieldUpdates = {};\n            let completeVendorCount = 0;\n            // Re-apply vendor details to ensure they're in the state\n            Object.entries(vendorDetailsData).forEach((param)=>{\n                let [vendorType, details] = param;\n                if (details && details.name && details.mobileNumber) {\n                    // Add to the batch\n                    detailFieldUpdates[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                    detailFieldUpdates[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                    completeVendorCount++;\n                    // Also set normalized versions\n                    const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                    if (normalizedType !== vendorType) {\n                        detailFieldUpdates[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                        detailFieldUpdates[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                    }\n                }\n            });\n            // Apply all updates at once\n            console.log(\"UPLOAD MANAGER - Applying \".concat(completeVendorCount, \" complete vendor details to state\"));\n            console.log('UPLOAD MANAGER - Detail field updates:', JSON.stringify(detailFieldUpdates));\n            // Apply each update individually to ensure they're all set\n            Object.entries(detailFieldUpdates).forEach((param)=>{\n                let [field, value] = param;\n                setDetailField(field, value);\n            });\n            // Add a delay before proceeding to ensure state updates are applied\n            setTimeout(()=>{\n                console.log('UPLOAD MANAGER - Vendor details applied to state, proceeding with upload');\n                proceedWithUpload(videoCategory);\n            }, 500);\n        } else {\n            console.log('UPLOAD MANAGER - No vendor details found, proceeding with upload');\n            proceedWithUpload(videoCategory);\n        }\n    // This code has been moved to the proceedWithUpload function\n    };\n    // Handle going back to personal details from upload error\n    const handleBackToPersonalDetails = ()=>{\n        // console.log('Going back to personal details with stored data:', personalDetails);\n        // Make sure the personal details are set in the context\n        if (personalDetails.caption && personalDetails.caption.trim()) {\n            // Use the global context function to set all personal details at once\n            setPersonalDetails(personalDetails);\n        }\n        setPhase('personalDetails');\n    };\n    // Handle close modal\n    const handleClose = ()=>{\n        // Check if upload was successful and call onUploadComplete\n        if (state.step === 'complete' && onUploadComplete) {\n            console.log('Upload completed successfully, calling onUploadComplete callback');\n            onUploadComplete();\n        }\n        // Reset the phase first\n        setPhase('closed');\n        // Call the onClose callback if provided\n        if (onClose) {\n            onClose();\n        }\n        // Reset the upload state after a short delay to ensure the modal is closed first\n        setTimeout(()=>{\n            resetUpload();\n            console.log('Upload state reset after modal close');\n        }, 100);\n    };\n    // Render selected phase component\n    if (phase === 'closed') {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            phase === 'typeSelection' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UploadTypeSelection__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                onNext: handleTypeSelected,\n                onClose: handleClose\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1463,\n                columnNumber: 9\n            }, undefined),\n            phase === 'categorySelection' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VideoCategorySelection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onNext: handleCategorySelected,\n                onBack: ()=>setPhase('typeSelection'),\n                onUpload: handleCategorySelected,\n                onThumbnailUpload: handleThumbnailUpload,\n                onClose: handleClose,\n                mediaType: state.mediaType,\n                selectedType: selectedType\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1470,\n                columnNumber: 9\n            }, undefined),\n            phase === 'thumbnailSelection' && state.file && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ThumbnailSelection__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                videoFile: state.file,\n                onNext: handleThumbnailSelected,\n                onBack: ()=>{\n                    // Go back to category selection instead of triggering file upload again\n                    if ([\n                        'flashes',\n                        'glimpses',\n                        'movies'\n                    ].includes(selectedType)) {\n                        setPhase('categorySelection');\n                    } else {\n                        // For moments, go back to type selection\n                        setPhase('typeSelection');\n                    }\n                },\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1482,\n                columnNumber: 9\n            }, undefined),\n            phase === 'personalDetails' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PersonalDetails__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onNext: handlePersonalDetailsCompleted,\n                onBack: ()=>{\n                    // Go back to thumbnail selection for videos\n                    if (state.mediaType === 'video' && state.file) {\n                        setPhase('thumbnailSelection');\n                    } else {\n                        // For photos, go back to type selection\n                        setPhase('typeSelection');\n                    }\n                },\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                },\n                previewImage: previewImage,\n                videoFile: state.mediaType === 'video' ? state.file : null,\n                mediaType: state.mediaType,\n                contentType: getContentType(),\n                initialDetails: personalDetails\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1503,\n                columnNumber: 9\n            }, undefined),\n            phase === 'vendorDetails' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VendorDetails__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                onNext: handleVendorDetailsCompleted,\n                onBack: ()=>setPhase('personalDetails'),\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                },\n                initialVendorDetails: vendorDetailsData,\n                videoCategory: (()=>{\n                    const category = state.detailFields.video_category || 'my_wedding';\n                    console.log('UPLOAD MANAGER - Passing video category to VendorDetails:', category);\n                    return category;\n                })()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1528,\n                columnNumber: 9\n            }, undefined),\n            phase === 'faceVerification' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FaceVerification__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                onUpload: handleFaceVerificationCompleted,\n                onBack: ()=>{\n                    // New flow logic for back navigation:\n                    // - Moments: Go back to thumbnail selection (or type selection for images)\n                    // - Photos: Go back to personal details\n                    // - Videos: Go back to vendor details\n                    if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                        // For moments, go back to thumbnail selection for videos, or type selection for images\n                        if (state.mediaType === 'video') {\n                            setPhase('thumbnailSelection');\n                        } else {\n                            setPhase('typeSelection');\n                        }\n                    } else if (state.mediaType === 'photo') {\n                        setPhase('personalDetails');\n                    } else {\n                        setPhase('vendorDetails');\n                    }\n                },\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1546,\n                columnNumber: 9\n            }, undefined),\n            (phase === 'uploading' || phase === 'complete') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UploadProgress__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                },\n                onGoBack: handleBackToPersonalDetails\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1575,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(UploadManager, \"idPi3SGLJtrz87d+SNzKuUjYbcw=\", false, function() {\n    return [\n        _contexts_UploadContexts__WEBPACK_IMPORTED_MODULE_9__.useUpload,\n        _hooks_useMedia__WEBPACK_IMPORTED_MODULE_12__.useMediaUpload\n    ];\n});\n_c = UploadManager;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UploadManager);\nvar _c;\n$RefreshReg$(_c, \"UploadManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/upload/UploadManager.tsx\n"));

/***/ })

});