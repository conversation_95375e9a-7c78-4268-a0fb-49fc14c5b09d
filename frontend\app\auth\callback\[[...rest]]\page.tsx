// app/auth/callback/[[...rest]]/page.tsx
"use client";
import { useEffect, useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import { useUser } from "@clerk/nextjs";
import Image from "next/image";
import axios from "axios";
// import { authService } from "../../../../services/api";

// Declare global Clerk type
declare global {
  interface Window {
    Clerk: {
      session: {
        getToken: (options?: { template?: string }) => Promise<string>;
      };
    };
  }
}

const setCookie = (name: string, value: string) => {
  document.cookie = `${name}=${value}; path=/; max-age=86400; SameSite=Lax`;
};

export default function AuthCallback() {
  const router = useRouter();
  const { isSignedIn, user, isLoaded } = useUser();
  const [status, setStatus] = useState("Authenticating...");

  // Define handleClerkAuth with useCallback
  const handleClerkAuth = useCallback(async () => {
    if (!isLoaded || !isSignedIn || !user) {

      setStatus("Please Create An Account. Redirecting...");
      setTimeout(() => {
        router.push("/");
      }, 2000);
      return;
    }

    try {
      setStatus("Processing authentication...");
      let token: string;

      try {
        token = await window.Clerk.session.getToken();
      } catch (tokenError) {
        console.error("Error getting token:", tokenError);
        throw new Error("Could not retrieve authentication token");
      }

      if (!token) {
        throw new Error("No token received from Clerk");
      }

      // Extract user info
      const userEmail = user?.primaryEmailAddress?.emailAddress || "";
      const userName = user?.fullName || "";
      const userId = user?.id || "";

      try {
        const apiUrl =
          "https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub";
        const response = await axios.post(`${apiUrl}/auth-clerk`, {
          clerk_token: token,
          user_type: "normal",
          user_email: userEmail,
          user_name: userName,
          user_id: userId,
        });

        // Store token consistently across all storage keys
        if (response.data?.token) {
          const backendToken = response.data.token;
          // Set in localStorage
          localStorage.setItem("jwt_token", backendToken);
          localStorage.setItem("token", backendToken);
          localStorage.setItem("wedzat_token", backendToken);
          // Also set in cookies
          setCookie("jwt_token", backendToken);
          setCookie("token", backendToken);
          setCookie("wedzat_token", backendToken);
          // Remove any old Clerk session token if it exists
          localStorage.removeItem("clerk_token");
        } else {
          throw new Error("No token received from backend");
        }

        setStatus("Authentication successful. Redirecting...");
        router.push("/home");
      } catch (apiError) {
        console.error("Error calling backend API:", apiError);
        setStatus("Authentication failed. Please try again.");
        router.push("/");
      }
    } catch (error) {
      console.error("Authentication error:", error);
      setStatus("Authentication failed. Please try again.");
      router.push("/");
    }
  }, [isLoaded, isSignedIn, user, router]);

  useEffect(() => {
    // First check for JWT token (our custom auth)
    const jwtToken = localStorage.getItem("wedzat_token");

    if (jwtToken) {
      // If we already have a JWT token, go straight to home
      router.push("/home");
      return;
    }

    // If using Clerk authentication
    if (isLoaded) {
      handleClerkAuth();
    }
  }, [isLoaded, handleClerkAuth, router]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-white">
      <div className="mb-6">
        <Image
          src="/pics/logo.png"
          alt="Wedzat logo"
          width={80}
          height={80}
          className="object-cover"
        />
      </div>
      <h1 className="text-2xl font-bold mb-4">{status}</h1>
      <div className="w-16 h-16 border-t-4 border-red-700 border-solid rounded-full animate-spin"></div>
      <p className="mt-4 text-gray-600">
        Please wait while we complete the process.
      </p>
    </div>
  );
}
