"use client";
import React, { useEffect, useState } from "react";
import { getMyWeddingContent } from "../../services/myWeddingService";
import userService from "../../services/api/userService";
import { TopNavigation, SideNavigation, MobileNavigation } from "../../components/HomeDashboard/Navigation";
import Image from "next/image";

// Types for the API response items
interface VideoItem {
  video_id: string;
  video_name: string;
  video_url: string;
  video_thumbnail?: string;
  video_description?: string;
  video_category?: string;
  video_duration?: number;
  created_at: string;
  user_name?: string;
  user_id?: string;
  is_own_content?: boolean;
  video_views?: number;
  video_likes?: number;
  video_comments?: number;
}

interface PhotoItem {
  photo_id: string;
  photo_name: string;
  photo_url: string;
  photo_description?: string;
  photo_tags?: string[];
  photo_category?: string;
  created_at: string;
  user_name?: string;
  user_id?: string;
  is_own_content?: boolean;
  photo_views?: number;
  photo_likes?: number;
  photo_comments?: number;
}

interface ApiResponse {
  data: (VideoItem | PhotoItem)[];
  pagination: {
    current_page: number;
    per_page: number;
    total_items: number;
    total_pages: number;
    has_next: boolean;
    has_previous: boolean;
  };
}

function getUserIdFromToken(): string | null {
  const token =
    (typeof window !== "undefined" &&
      (localStorage.getItem("token") ||
        localStorage.getItem("jwt_token") ||
        localStorage.getItem("wedzat_token"))) ||
    null;
  if (!token) return null;
  try {
    const payload = JSON.parse(atob(token.split(".")[1]));
    return payload.user_id || payload.sub || null;
  } catch (e) {
    return null;
  }
}

// Helper functions for thumbnails and navigation
function getFlashThumbnail(item: any) {
  return item.video_thumbnail || "/pics/placeholder.svg";
}
function getGlimpseThumbnail(item: any) {
  if (item.video_thumbnail) return item.video_thumbnail;
  if (item.video_url && item.video_url.includes("youtube")) {
    const videoId = getYoutubeId(item.video_url);
    if (videoId) return `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
  }
  return "/pics/placeholder.svg";
}
function getMovieThumbnail(item: any) {
  return item.video_thumbnail || "/pics/placeholder.svg";
}
function getPhotoThumbnail(item: any) {
  return item.photo_url || "/pics/placeholder.svg";
}
function getYoutubeId(url: string): string {
  if (!url) return '';
  const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
  const match = url.match(regExp);
  return (match && match[2].length === 11) ? match[2] : '';
}
function goToFlash(index: number) {
  window.location.href = `/home/<USER>/shorts?index=${index}`;
}
function goToGlimpse(video_id: string) {
  window.location.href = `/home/<USER>/${video_id}`;
}
function goToMovie(video_id: string) {
  window.location.href = `/home/<USER>/${video_id}`;
}
function goToPhoto(photo_id: string) {
  window.location.href = `/home/<USER>/${photo_id}`;
}

const MyWeddingsPage = () => {
  const [content, setContent] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sidebarExpanded, setSidebarExpanded] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    async function fetchContent() {
      let user_id = getUserIdFromToken();
      if (!user_id) {
        // Fallback: fetch user details from API
        try {
          const user = await userService.getUserDetails();
          user_id = getUserIdFromToken();// Using user_id instead of id
        } catch (err) {
          setError("User not logged in");
          setLoading(false);
          return;
        }
      }
      setLoading(true);
      setError(null);
      getMyWeddingContent({ user_id: user_id!, content_type: "", page: 1, limit: 50 })
        .then((res) => {
          setContent(res.data.data || []);
          setLoading(false);
        })
        .catch(() => {
          setError("Failed to load your saved wedding content.");
          setLoading(false);
        });
    }
    fetchContent();
  }, []);

  // Categorize by media_type
  const flashes = content.filter((item) => item.media_type === "flash");
  const glimpses = content.filter((item) => item.media_type === "glimpses");
  const movies = content.filter((item) => item.media_type === "movies");
  const photos = content.filter((item) => item.media_type === "photo");

  if (!mounted) {
    return (
      <div className="p-8 text-center">
        Loading...
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-white w-full">
      <TopNavigation />
      <div className="flex flex-1 bg-white">
        <SideNavigation
          expanded={sidebarExpanded}
          onExpand={() => setSidebarExpanded(true)}
          onCollapse={() => setSidebarExpanded(false)}
        />
        <main
          className={`flex-1 py-4 pr-4 pl-0 bg-white ${sidebarExpanded ? "md:ml-48" : "md:ml-20"}`}
          style={{
            marginTop: "80px",
            transition: "all 300ms ease-in-out",
            minHeight: "calc(100vh - 80px)",
            paddingBottom: "40px",
            overflowY: "auto",
            overflowX: "hidden",
            paddingRight: "20px",
            paddingLeft: "0",
          }}
        >
          <div className="flex flex-col gap-8 max-w-[1100px] w-full pl-2">
            <h1 className="text-2xl font-bold mb-6">My Weddings</h1>

            {/* Flashes Section */}
            <section className="mb-8">
              <h2 className="text-xl font-semibold mb-4 font-inter text-black">Flashes</h2>
              {loading ? (
                <div className="py-4 text-center">
                  <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                  <span className="text-gray-600">Loading...</span>
                </div>
              ) : error ? (
                <div className="py-4 text-center text-red-500">{error}</div>
              ) : flashes.length === 0 ? (
                <p className="text-gray-500">No flashes saved.</p>
              ) : (
                <div className="overflow-x-auto scrollbar-hide">
                  <div className="flex gap-3 pb-4 flex-nowrap">
                    {flashes.map((item, index) => (
                      <div
                        key={item.video_id}
                        onClick={() => window.location.href = `/home/<USER>/shorts?index=${index}`}
                        className="min-w-[40vw] w-[40vw] sm:min-w-[30vw] sm:w-[30vw] md:min-w-[22vw] md:w-[22vw] lg:min-w-[18vw] lg:w-[18vw] h-[220px] sm:h-[280px] md:h-[308px] max-w-[200px] flex-shrink-0 inline-block relative rounded-[10px] border border-[#B31B1E] p-[6px] sm:p-[10px] cursor-pointer overflow-hidden transition-transform duration-200 hover:scale-105"
                        style={{
                          background: "linear-gradient(180deg, rgba(113, 113, 113, 0) 71.27%, #393939 100%)",
                        }}
                      >
                        <div className="relative w-full h-full">
                          <Image
                            src={item.video_thumbnail || "/pics/placeholder.svg"}
                            alt={item.video_name || "Flash"}
                            fill
                            className="object-cover rounded-[6px]"
                            onError={(e) => {
                              const img = e.target as HTMLImageElement;
                              if (img) img.src = "/pics/placeholder.svg";
                            }}
                          />
                          <div className="absolute inset-0 flex items-center justify-center">
                            <div className="w-12 h-12 rounded-full bg-red-500 bg-opacity-80 flex items-center justify-center">
                              <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="white"><path d="M8 5v14l11-7z" /></svg>
                            </div>
                          </div>
                          <div className="absolute bottom-2 left-2 right-2 text-white">
                            <div className="font-medium text-sm">{item.video_name}</div>
                            <div className="text-xs opacity-80">
                              {item.video_views ? `${(item.video_views / 1000).toFixed(1)}K views` : ''}
                              {item.video_likes ? ` • ${(item.video_likes / 1000).toFixed(1)}K likes` : ''}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </section>

            {/* Glimpses Section */}
            <section className="mb-8">
              <h2 className="text-xl font-semibold mb-4 font-inter text-black">Glimpses</h2>
              {loading ? (
                <div className="py-4 text-center">
                  <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                  <span className="text-gray-600">Loading...</span>
                </div>
              ) : error ? (
                <div className="py-4 text-center text-red-500">{error}</div>
              ) : glimpses.length === 0 ? (
                <p className="text-gray-500">No glimpses saved.</p>
              ) : (
                <div className="overflow-x-auto scrollbar-hide">
                  <div className="flex gap-3 pb-4 flex-nowrap">
                    {glimpses.map((item) => (
                      <div
                        key={item.video_id}
                        onClick={() => window.location.href = `/home/<USER>/${item.video_id}`}
                        className="min-w-[40vw] w-[40vw] sm:min-w-[30vw] sm:w-[30vw] md:min-w-[22vw] md:w-[22vw] lg:min-w-[18vw] lg:w-[18vw] h-[220px] sm:h-[280px] md:h-[308px] max-w-[200px] flex-shrink-0 inline-block relative rounded-[10px] border border-[#B31B1E] p-[6px] sm:p-[10px] cursor-pointer overflow-hidden transition-transform duration-200 hover:scale-105"
                        style={{
                          background: "linear-gradient(180deg, rgba(113, 113, 113, 0) 71.27%, #393939 100%)",
                        }}
                      >
                        <div className="relative w-full h-full">
                          <Image
                            src={item.video_thumbnail || "/pics/placeholder.svg"}
                            alt={item.video_name || "Glimpse"}
                            fill
                            className="object-cover rounded-[6px]"
                            onError={(e) => {
                              const img = e.target as HTMLImageElement;
                              if (img) img.src = "/pics/placeholder.svg";
                            }}
                          />
                          <div className="absolute inset-0 flex items-center justify-center">
                            <div className="w-12 h-12 rounded-full bg-red-500 bg-opacity-80 flex items-center justify-center">
                              <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="white"><path d="M8 5v14l11-7z" /></svg>
                            </div>
                          </div>
                          <div className="absolute bottom-2 left-2 right-2 text-white">
                            <div className="font-medium text-sm">{item.video_name}</div>
                            <div className="text-xs opacity-80">
                              {item.video_views ? `${(item.video_views / 1000).toFixed(1)}K views` : ''}
                              {item.video_likes ? ` • ${(item.video_likes / 1000).toFixed(1)}K likes` : ''}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </section>

            {/* Movies Section */}
            <section className="mb-8">
              <h2 className="text-xl font-semibold mb-4 font-inter text-black">Movies</h2>
              {loading ? (
                <div className="py-4 text-center">
                  <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                  <span className="text-gray-600">Loading...</span>
                </div>
              ) : error ? (
                <div className="py-4 text-center text-red-500">{error}</div>
              ) : movies.length === 0 ? (
                <p className="text-gray-500">No movies saved.</p>
              ) : (
                <div className="overflow-x-auto scrollbar-hide">
                  <div className="flex gap-3 pb-4 flex-nowrap">
                    {movies.map((item) => (
                      <div
                        key={item.video_id}
                        onClick={() => window.location.href = `/home/<USER>/${item.video_id}`}
                        className="min-w-[40vw] w-[40vw] sm:min-w-[30vw] sm:w-[30vw] md:min-w-[22vw] md:w-[22vw] lg:min-w-[18vw] lg:w-[18vw] h-[220px] sm:h-[280px] md:h-[308px] max-w-[200px] flex-shrink-0 inline-block relative rounded-[10px] border border-[#B31B1E] p-[6px] sm:p-[10px] cursor-pointer overflow-hidden transition-transform duration-200 hover:scale-105"
                        style={{
                          background: "linear-gradient(180deg, rgba(113, 113, 113, 0) 71.27%, #393939 100%)",
                        }}
                      >
                        <div className="relative w-full h-full">
                          <Image
                            src={item.video_thumbnail || "/pics/placeholder.svg"}
                            alt={item.video_name || "Movie"}
                            fill
                            className="object-cover rounded-[6px]"
                            onError={(e) => {
                              const img = e.target as HTMLImageElement;
                              if (img) img.src = "/pics/placeholder.svg";
                            }}
                          />
                          <div className="absolute inset-0 flex items-center justify-center">
                            <div className="w-12 h-12 rounded-full bg-red-500 bg-opacity-80 flex items-center justify-center">
                              <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="white"><path d="M8 5v14l11-7z" /></svg>
                            </div>
                          </div>
                          <div className="absolute bottom-2 left-2 right-2 text-white">
                            <div className="font-medium text-sm">{item.video_name}</div>
                            <div className="text-xs opacity-80">
                              {item.video_views ? `${(item.video_views / 1000).toFixed(1)}K views` : ''}
                              {item.video_likes ? ` • ${(item.video_likes / 1000).toFixed(1)}K likes` : ''}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </section>

            {/* Photos Section */}
            <section className="mb-8">
              <h2 className="text-xl font-semibold mb-4 font-inter text-black">Photos</h2>
              {loading ? (
                <div className="py-4 text-center">
                  <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                  <span className="text-gray-600">Loading...</span>
                </div>
              ) : error ? (
                <div className="py-4 text-center text-red-500">{error}</div>
              ) : photos.length === 0 ? (
                <p className="text-gray-500">No photos saved.</p>
              ) : (
                <div className="overflow-x-auto scrollbar-hide">
                  <div className="flex gap-3 pb-4 flex-nowrap">
                    {photos.map((item) => (
                      <div
                        key={item.photo_id}
                        onClick={() => window.location.href = `/home/<USER>/${item.photo_id}`}
                        className="min-w-[40vw] w-[40vw] sm:min-w-[30vw] sm:w-[30vw] md:min-w-[22vw] md:w-[22vw] lg:min-w-[18vw] lg:w-[18vw] h-[220px] sm:h-[280px] md:h-[308px] max-w-[200px] flex-shrink-0 inline-block relative rounded-[10px] border border-[#B31B1E] p-[6px] sm:p-[10px] cursor-pointer overflow-hidden transition-transform duration-200 hover:scale-105"
                      >
                        <div className="relative w-full h-full">
                          <Image
                            src={item.photo_url || "/pics/placeholder.svg"}
                            alt={item.photo_name || "Photo"}
                            fill
                            className="object-cover rounded-[6px]"
                            onError={(e) => {
                              const img = e.target as HTMLImageElement;
                              if (img) img.src = "/pics/placeholder.svg";
                            }}
                          />
                          <div className="absolute bottom-2 left-2 right-2 text-white">
                            <div className="font-medium text-sm">{item.photo_name}</div>
                            <div className="text-xs opacity-80">
                              {item.photo_views ? `${(item.photo_views / 1000).toFixed(1)}K views` : ''}
                              {item.photo_likes ? ` • ${(item.photo_likes / 1000).toFixed(1)}K likes` : ''}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </section>
          </div>
        </main>
      </div>
      <MobileNavigation />
    </div>
  );
};

export default MyWeddingsPage; 