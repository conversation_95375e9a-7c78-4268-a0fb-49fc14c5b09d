"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"91a68e9b660f\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xca29tbWlcXE9uZURyaXZlXFxEZXNrdG9wXFxXRURaQVRfXFxmcm9udGVuZFxcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjkxYTY4ZTliNjYwZlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/upload/UploadManager.tsx":
/*!*********************************************!*\
  !*** ./components/upload/UploadManager.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _UploadTypeSelection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./UploadTypeSelection */ \"(app-pages-browser)/./components/upload/UploadTypeSelection.tsx\");\n/* harmony import */ var _VideoCategorySelection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./VideoCategorySelection */ \"(app-pages-browser)/./components/upload/VideoCategorySelection.tsx\");\n/* harmony import */ var _ThumbnailSelection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ThumbnailSelection */ \"(app-pages-browser)/./components/upload/ThumbnailSelection.tsx\");\n/* harmony import */ var _PersonalDetails__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./PersonalDetails */ \"(app-pages-browser)/./components/upload/PersonalDetails.tsx\");\n/* harmony import */ var _VendorDetails__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./VendorDetails */ \"(app-pages-browser)/./components/upload/VendorDetails.tsx\");\n/* harmony import */ var _FaceVerification__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./FaceVerification */ \"(app-pages-browser)/./components/upload/FaceVerification.tsx\");\n/* harmony import */ var _UploadProgress__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./UploadProgress */ \"(app-pages-browser)/./components/upload/UploadProgress.tsx\");\n/* harmony import */ var _contexts_UploadContexts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../contexts/UploadContexts */ \"(app-pages-browser)/./contexts/UploadContexts.tsx\");\n/* harmony import */ var _utils_uploadUtils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../utils/uploadUtils */ \"(app-pages-browser)/./utils/uploadUtils.ts\");\n/* harmony import */ var _utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../utils/alertUtils */ \"(app-pages-browser)/./utils/alertUtils.tsx\");\n/* harmony import */ var _hooks_useMedia__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../hooks/useMedia */ \"(app-pages-browser)/./hooks/useMedia.ts\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../services/api */ \"(app-pages-browser)/./services/api.ts\");\n// components/upload/UploadManager.tsx\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function to format time in minutes and seconds\nconst formatTime = (seconds)=>{\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    if (minutes === 0) {\n        return \"\".concat(remainingSeconds, \" seconds\");\n    } else if (minutes === 1 && remainingSeconds === 0) {\n        return '1 minute';\n    } else if (remainingSeconds === 0) {\n        return \"\".concat(minutes, \" minutes\");\n    } else {\n        return \"\".concat(minutes, \" minute\").concat(minutes > 1 ? 's' : '', \" and \").concat(remainingSeconds, \" second\").concat(remainingSeconds !== 1 ? 's' : '');\n    }\n};\nconst UploadManager = (param)=>{\n    let { onClose, initialType, onUploadComplete } = param;\n    _s();\n    const { state, setFile, setMediaType, setCategory, setMediaSubtype, setTitle, setDescription, setDetailField, startUpload, startUploadWithCategory, setThumbnail, setVendorDetails, setPersonalDetails, setDuration, resetUpload } = (0,_contexts_UploadContexts__WEBPACK_IMPORTED_MODULE_9__.useUpload)();\n    const [phase, setPhase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('typeSelection');\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialType || '');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [previewImage, setPreviewImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [thumbnailImage, setThumbnailImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const vendorDetailsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    // Helper function to determine content type for PersonalDetails\n    const getContentType = ()=>{\n        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n            return 'moment';\n        } else if (state.mediaType === 'video') {\n            return 'video';\n        } else {\n            return 'photo';\n        }\n    };\n    // Store personal details to persist between screens\n    const [personalDetails, setLocalPersonalDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        caption: '',\n        lifePartner: '',\n        weddingStyle: '',\n        place: '',\n        eventType: '',\n        budget: ''\n    });\n    // Auto-select the type if initialType is provided\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UploadManager.useEffect\": ()=>{\n            if (initialType) {\n                console.log('Auto-selecting type from initialType:', initialType);\n                handleTypeSelected(initialType);\n            }\n        }\n    }[\"UploadManager.useEffect\"], []);\n    // Store vendor details to persist between screens\n    const [vendorDetailsData, setVendorDetailsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        venue: {\n            name: '',\n            mobileNumber: ''\n        },\n        photographer: {\n            name: '',\n            mobileNumber: ''\n        },\n        makeupArtist: {\n            name: '',\n            mobileNumber: ''\n        },\n        decorations: {\n            name: '',\n            mobileNumber: ''\n        },\n        caterer: {\n            name: '',\n            mobileNumber: ''\n        }\n    });\n    // Use the new media upload hook\n    const { mutate: uploadMedia, isPending: isUploading } = (0,_hooks_useMedia__WEBPACK_IMPORTED_MODULE_12__.useMediaUpload)();\n    // Handle media type selection\n    const handleTypeSelected = (type)=>{\n        // First, completely reset everything\n        resetUpload();\n        setPreviewImage(null);\n        setThumbnailImage(null);\n        // Then set the new type\n        setSelectedType(type);\n        console.log(\"Selected type:\", type);\n        if ([\n            'flashes',\n            'glimpses',\n            'movies',\n            'photos',\n            'moments'\n        ].includes(type)) {\n            // For explicit video types, photos, and moments, set the appropriate media type\n            if (type === 'photos') {\n                console.log('Setting media type to photo for:', type);\n                setMediaType('photo');\n                setMediaSubtype('post');\n                // Go to category selection for photos\n                setPhase('categorySelection');\n            } else if (type === 'moments') {\n                console.log('Setting media type for moments (will be determined by file type)');\n                // For moments, we'll set the media type later based on the file type (photo or video)\n                setMediaSubtype('story');\n                // For moments, skip category selection and go directly to file upload\n                console.log('Moments selected: skipping category selection, going directly to file upload');\n                handleFileUpload();\n                return; // Early return to prevent further processing\n            } else {\n                setMediaType('video');\n                setMediaSubtype(getMediaSubtypeFromSelectedType(type));\n                // Go to category selection for videos\n                setPhase('categorySelection');\n            }\n        } else if (type === 'photo') {\n            // For single photo type (if it exists)\n            console.log('Setting media type to photo for:', type);\n            setMediaType('photo');\n            setMediaSubtype('post');\n            // Use a special photo-only upload handler for photos\n            handlePhotoUpload();\n        }\n    };\n    // Helper function to get the backend media subtype from the selected UI type\n    const getMediaSubtypeFromSelectedType = (type)=>{\n        // Map UI category to backend category for media_subtype\n        switch(type){\n            // Photo types\n            case 'moments':\n                return 'story'; // Backend expects 'story' for moments\n            case 'photos':\n                return 'post'; // Backend expects 'post' for regular photos\n            // Video types\n            case 'flashes':\n                return 'flash'; // Backend expects 'flash'\n            case 'glimpses':\n                return 'glimpse'; // Backend expects 'glimpse'\n            case 'movies':\n                return 'movie'; // Backend expects 'movie'\n            // Default fallback\n            default:\n                return type === 'moments' ? 'story' : 'post'; // Default based on type\n        }\n    };\n    // Handle category selection for both videos and photos\n    const handleCategorySelected = (category)=>{\n        // First, make sure we have a clean state for the new upload\n        // but preserve the selected type and media type\n        const currentType = selectedType;\n        const currentMediaType = state.mediaType;\n        resetUpload();\n        setSelectedType(currentType);\n        setMediaType(currentMediaType);\n        setPreviewImage(null);\n        setThumbnailImage(null);\n        // Now set the new category\n        setSelectedCategory(category);\n        // Get the media subtype based on the selected type\n        let mediaSubtype;\n        if (currentType === 'photos') {\n            // For photos, always use 'post' as the media subtype\n            mediaSubtype = 'post';\n            console.log(\"UPLOAD MANAGER - Using media subtype 'post' for photos\");\n        } else {\n            // For videos, use the subtype based on the selected type\n            mediaSubtype = getMediaSubtypeFromSelectedType(selectedType);\n            console.log(\"UPLOAD MANAGER - Using media subtype \".concat(mediaSubtype, \" based on selected type \").concat(selectedType));\n            console.log(\"UPLOAD MANAGER - Backend expects: flashes→flash, glimpses→glimpse, movies→movie, moments→story\");\n        }\n        console.log(\"UPLOAD MANAGER - Selected category:\", category);\n        console.log(\"UPLOAD MANAGER - Setting media subtype to:\", mediaSubtype);\n        // Set the media subtype in the context\n        setMediaSubtype(mediaSubtype);\n        // Map the selected category to a valid backend video_category\n        let backendVideoCategory = '';\n        if (category === 'my_wedding_videos') {\n            backendVideoCategory = 'my_wedding';\n        } else if (category === 'wedding_vlog') {\n            backendVideoCategory = 'wedding_vlog';\n        }\n        // Make sure we have a valid video_category\n        if (!backendVideoCategory) {\n            console.error('Invalid video category selected:', category);\n            alert('Please select a valid video category');\n            return;\n        }\n        // Set video category in the context for the backend\n        console.log(\"UPLOAD MANAGER - Setting video_category to:\", backendVideoCategory);\n        setDetailField('video_category', backendVideoCategory);\n        // Log the final values\n        console.log(\"UPLOAD MANAGER - Selected category:\", category);\n        console.log(\"UPLOAD MANAGER - Backend video category set to:\", backendVideoCategory);\n        console.log(\"UPLOAD MANAGER - Media subtype set to:\", mediaSubtype);\n        // Proceed to file upload after setting the category\n        if (currentType === 'photos') {\n            // For photos, use the photo-specific upload handler\n            handlePhotoUpload();\n        } else {\n            // For videos, use the standard file upload handler\n            handleFileUpload();\n        }\n    };\n    // Handle thumbnail upload\n    const handleThumbnailUpload = ()=>{\n        // Create a file input element\n        const input = document.createElement('input');\n        input.type = 'file';\n        input.accept = 'image/*';\n        // Handle file selection\n        input.onchange = (e)=>{\n            const files = e.target.files;\n            if (files && files.length > 0) {\n                const file = files[0];\n                // Store the thumbnail\n                setThumbnailImage(file);\n                setThumbnail(file);\n                console.log(\"Thumbnail selected:\", file.name);\n                // Show a preview if needed\n                const reader = new FileReader();\n                reader.onload = (e)=>{\n                    var _e_target;\n                    if ((_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result) {\n                        // You could set a thumbnail preview here if needed\n                        console.log(\"Thumbnail preview ready\");\n                    }\n                };\n                reader.readAsDataURL(file);\n            }\n        };\n        // Trigger the file dialog\n        input.click();\n    };\n    // Get user-friendly display name for a category\n    const getCategoryDisplayName = (category)=>{\n        switch(category){\n            case 'flash':\n                return 'Flash';\n            case 'glimpse':\n                return 'Glimpse';\n            case 'movie':\n                return 'Movie';\n            case 'story':\n                return 'Story';\n            case 'post':\n                return 'Photo';\n            default:\n                return category.charAt(0).toUpperCase() + category.slice(1);\n        }\n    };\n    // Get appropriate category based on duration\n    const getAppropriateCategory = (duration)=>{\n        // For very short videos (1 minute or less), use flash instead of story/moments\n        if (duration <= 60) {\n            return 'flash'; // Very short videos (1 minute or less) - changed from 'story' to 'flash'\n        } else if (duration <= 90) {\n            return 'flash'; // Short videos (1.5 minutes or less)\n        } else if (duration <= 420) {\n            return 'glimpse'; // Medium videos (7 minutes or less)\n        } else {\n            return 'movie'; // Long videos (over 7 minutes)\n        }\n    };\n    // Special handler for photo uploads that strictly enforces image-only files\n    const handlePhotoUpload = ()=>{\n        console.log('handlePhotoUpload called - strict image-only upload');\n        // Create a file input element specifically for photos\n        const input = document.createElement('input');\n        input.type = 'file';\n        // Reset the input value\n        input.value = '';\n        // Only accept image files - explicitly list allowed types\n        input.accept = 'image/jpeg,image/png,image/gif,image/webp';\n        // Handle file selection with strict validation\n        input.onchange = async (e)=>{\n            const files = e.target.files;\n            if (!files || files.length === 0) return;\n            const file = files[0];\n            console.log('Photo file selected:', file.name, file.type, file.size);\n            // Strict validation - must be an image file\n            const validImageTypes = [\n                'image/jpeg',\n                'image/png',\n                'image/gif',\n                'image/webp'\n            ];\n            if (!validImageTypes.includes(file.type)) {\n                console.error('Invalid file type for photos:', file.type);\n                alert('Only JPEG, PNG, GIF, or WebP images can be uploaded as photos. Videos are not allowed.');\n                return;\n            }\n            // Additional check - reject any file that might be a video\n            if (file.type.startsWith('video/')) {\n                console.error('Attempted to upload a video file as photo');\n                alert('Videos cannot be uploaded as photos. Please select an image file.');\n                return;\n            }\n            // For photos, we need to be more careful with state management\n            // First, set the media type and subtype\n            setMediaType('photo');\n            setMediaSubtype('post');\n            // Then set the file in the state\n            setFile(file);\n            console.log('Photo file set in state:', file.name);\n            // Create a local reference to the file for use in the timeout\n            const currentFile = file;\n            // Double-check that the file is set in the state before proceeding\n            setTimeout(()=>{\n                // Check if the file is in the state\n                if (!state.file) {\n                    console.log('File not found in state after setting, trying again');\n                    // Try setting the file again\n                    setFile(currentFile);\n                    // Add another timeout to ensure the file is set\n                    setTimeout(()=>{\n                        if (!state.file) {\n                            console.log('File still not in state, setting it one more time');\n                            setFile(currentFile);\n                        } else {\n                            console.log('File confirmed in state after second attempt:', state.file.name);\n                        }\n                        // New flow logic: For moments (stories), skip personal details and go directly to face verification\n                        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                            console.log('Moments photo upload: skipping personal details, going directly to face verification');\n                            setPhase('faceVerification');\n                        } else {\n                            console.log('Moving to personalDetails phase for photo');\n                            setPhase('personalDetails');\n                        }\n                    }, 100);\n                } else {\n                    console.log('File confirmed in state:', state.file.name);\n                    // New flow logic: For moments (stories), skip personal details and go directly to face verification\n                    if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                        console.log('Moments photo upload: skipping personal details, going directly to face verification');\n                        setPhase('faceVerification');\n                    } else {\n                        console.log('Moving to personalDetails phase for photo');\n                        setPhase('personalDetails');\n                    }\n                }\n            }, 100);\n            // Handle image preview\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                var _e_target;\n                if ((_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result) {\n                    setPreviewImage(e.target.result);\n                    console.log('Preview image set for photo');\n                }\n            };\n            reader.readAsDataURL(file);\n        // Note: We don't set the phase here anymore - it's handled in the timeout above\n        };\n        // Trigger the file dialog\n        input.click();\n    };\n    // This function was previously used but is now replaced by getAppropriateCategory\n    // Keeping a comment here for reference in case it needs to be restored\n    // Handle manual upload button click\n    const handleFileUpload = async (category)=>{\n        console.log('handleFileUpload called with category:', category || 'none');\n        // Create a file input element\n        const input = document.createElement('input');\n        input.type = 'file';\n        // Reset the input value to ensure we get a new file selection event even if the same file is selected\n        input.value = '';\n        if (selectedType === 'moments') {\n            input.accept = 'image/*,video/*';\n        } else {\n            input.accept = selectedType === 'photo' || selectedType === 'photos' ? 'image/jpeg,image/png,image/gif,image/webp' // Explicitly list image types only\n             : 'video/*';\n        }\n        // Handle file selection\n        input.onchange = async (e)=>{\n            const files = e.target.files;\n            if (!files || files.length === 0) return;\n            const file = files[0];\n            console.log('File selected:', file.name, file.type, file.size);\n            // Strict validation for photo uploads - must be an image file\n            if (selectedType === 'photo' || selectedType === 'photos') {\n                const validImageTypes = [\n                    'image/jpeg',\n                    'image/png',\n                    'image/gif',\n                    'image/webp'\n                ];\n                // Check if file is a video or not a valid image type\n                if (file.type.startsWith('video/') || !validImageTypes.includes(file.type)) {\n                    console.error('Invalid file type for photos:', file.type);\n                    (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Invalid File Type', 'Only JPEG, PNG, GIF, or WebP images can be uploaded as photos. Videos are not allowed.');\n                    return;\n                }\n            }\n            // Reset the upload context before setting the new file\n            resetUpload();\n            // Set the file in the state\n            setFile(file);\n            console.log('File set in state:', file.name);\n            // If it's a video, calculate and set the duration\n            // Double-check that we're not trying to upload a video as a photo\n            if (file.type.startsWith('video/')) {\n                // Safety check - don't process videos for photo uploads\n                if (selectedType === 'photo' || selectedType === 'photos') {\n                    console.error('Attempted to process a video file for photo upload');\n                    (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Invalid File Type', 'Videos cannot be uploaded as photos. Please select an image file.');\n                    resetUpload();\n                    return;\n                }\n                try {\n                    const duration = await (0,_utils_uploadUtils__WEBPACK_IMPORTED_MODULE_10__.getVideoDuration)(file);\n                    console.log('Video duration calculated:', duration);\n                    setDuration(duration);\n                    // For moments, check if it's a video and validate the duration (max 1 minute)\n                    if (selectedType === 'moments') {\n                        console.log('Validating moments video duration...');\n                        setMediaType('video');\n                        // Check if the video is longer than 1 minute (60 seconds)\n                        if (duration > 60) {\n                            console.log(\"Moments video too long: \".concat(duration, \" seconds (max: 60 seconds)\"));\n                            // Show a more detailed error message with custom alert\n                            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showWarningAlert)('Moments Video Too Long', \"Moments videos must be 1 minute or less.\\n\\nYour video is \".concat(formatTime(duration), \" long.\\n\\nPlease select a shorter video or trim this video to 1 minute or less.\"));\n                            // Reset the upload context but preserve the selected type and category\n                            const currentType = selectedType;\n                            const currentCategory = selectedCategory;\n                            // First set the phase back to category selection\n                            setPhase('categorySelection');\n                            // Then reset the upload state\n                            setTimeout(()=>{\n                                resetUpload();\n                                setSelectedType(currentType);\n                                setSelectedCategory(currentCategory);\n                                console.log('Reset upload state after moments video duration validation failure');\n                            }, 100);\n                            // Return early to prevent further processing\n                            return;\n                        }\n                        console.log(\"Moments video duration valid: \".concat(duration, \" seconds (max: 60 seconds)\"));\n                        // For moments, we always use 'story' as the media subtype\n                        console.log('Setting media subtype for moments video to story');\n                        setMediaSubtype('story');\n                    }\n                    // If we have a category, validate the duration for that category\n                    if (selectedType && [\n                        'flashes',\n                        'glimpses',\n                        'movies'\n                    ].includes(selectedType)) {\n                        const mediaSubtype = getMediaSubtypeFromSelectedType(selectedType);\n                        const validationResult = (0,_utils_uploadUtils__WEBPACK_IMPORTED_MODULE_10__.validateVideoDuration)(duration, mediaSubtype);\n                        if (!validationResult.isValid) {\n                            // If there's a suggested category, automatically switch to it\n                            if (validationResult.suggestedCategory) {\n                                // For videos that exceed the maximum duration, automatically switch without asking\n                                console.log(\"Video exceeds maximum duration for \".concat(mediaSubtype, \". Automatically switching to \").concat(validationResult.suggestedCategory));\n                                (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showWarningAlert)('Video Duration Notice', \"Your video is too long for the \".concat(getCategoryDisplayName(mediaSubtype), \" category. It will be uploaded as a \").concat(getCategoryDisplayName(validationResult.suggestedCategory), \" instead.\"));\n                                // Switch to the suggested category\n                                console.log(\"Switching to suggested category: \".concat(validationResult.suggestedCategory));\n                                // Make sure we keep a reference to the file\n                                const currentFile = file;\n                                // Update the media subtype\n                                setMediaSubtype(validationResult.suggestedCategory);\n                                // Update the selected type to match the new category\n                                // Never suggest 'story' (moments) for other categories\n                                if (validationResult.suggestedCategory === 'flash') {\n                                    setSelectedType('flashes');\n                                } else if (validationResult.suggestedCategory === 'glimpse') {\n                                    setSelectedType('glimpses');\n                                } else if (validationResult.suggestedCategory === 'movie') {\n                                    setSelectedType('movies');\n                                }\n                                // Removed the 'story' suggestion for short videos\n                                // Make sure the file is still set in the state\n                                setTimeout(()=>{\n                                    if (!state.file) {\n                                        console.log('Re-setting file after category change:', currentFile.name);\n                                        setFile(currentFile);\n                                    }\n                                }, 50);\n                            } else {\n                                // No suggested category, just show the error\n                                (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Video Duration Error', validationResult.error || 'The video duration is not valid for this category.');\n                            }\n                        } else if (validationResult.suggestedCategory && validationResult.suggestedCategory !== mediaSubtype) {\n                            // Video is valid for current category but there's a better category\n                            // For this case, we still give the user a choice since the video is valid for the current category\n                            // Use our custom confirm dialog instead of window.confirm\n                            const confirmSwitch = await (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showAlert)({\n                                title: 'Category Suggestion',\n                                message: \"\".concat(validationResult.error, \"\\n\\nWould you like to switch to the suggested category?\"),\n                                type: 'warning',\n                                confirmText: 'Yes, Switch Category',\n                                cancelText: 'No, Keep Current',\n                                onConfirm: ()=>{}\n                            });\n                            if (confirmSwitch) {\n                                // Switch to the suggested category\n                                console.log(\"Switching to suggested category: \".concat(validationResult.suggestedCategory));\n                                // Make sure we keep a reference to the file\n                                const currentFile = file;\n                                // Update the media subtype\n                                setMediaSubtype(validationResult.suggestedCategory);\n                                // Update the selected type to match the new category\n                                // Never suggest 'story' (moments) for other categories\n                                if (validationResult.suggestedCategory === 'flash') {\n                                    setSelectedType('flashes');\n                                } else if (validationResult.suggestedCategory === 'glimpse') {\n                                    setSelectedType('glimpses');\n                                } else if (validationResult.suggestedCategory === 'movie') {\n                                    setSelectedType('movies');\n                                }\n                                // Removed the 'story' suggestion for short videos\n                                // Make sure the file is still set in the state\n                                setTimeout(()=>{\n                                    if (!state.file) {\n                                        console.log('Re-setting file after category change:', currentFile.name);\n                                        setFile(currentFile);\n                                    }\n                                }, 50);\n                            }\n                        }\n                    }\n                    // Always go to thumbnail selection for videos\n                    console.log('Moving to thumbnailSelection phase');\n                    // Keep a reference to the current file\n                    const currentFile = file;\n                    // Double-check that the file is set in the state before proceeding\n                    if (state.file) {\n                        console.log('File confirmed in state before phase change:', state.file.name);\n                        setPhase('thumbnailSelection');\n                    } else {\n                        console.log('File not found in state before phase change, setting it again');\n                        // Try setting the file again\n                        setFile(currentFile);\n                        // Add a small delay to ensure the state is updated\n                        setTimeout(()=>{\n                            // Double-check again\n                            if (!state.file) {\n                                console.log('File still not in state, setting it one more time');\n                                setFile(currentFile);\n                            }\n                            console.log('Delayed phase change to thumbnailSelection');\n                            setPhase('thumbnailSelection');\n                        }, 100);\n                    }\n                } catch (error) {\n                    console.error('Error calculating video duration:', error);\n                    // For moments videos, we need to enforce the duration check\n                    // If we can't calculate duration, we can't validate it, so we should reject the upload\n                    if (selectedType === 'moments') {\n                        (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Video Error', 'Unable to determine video duration. Please try a different video file.');\n                        resetUpload();\n                        return;\n                    }\n                    console.log('Moving to thumbnailSelection phase despite error');\n                    // Keep a reference to the current file\n                    const currentFile = file;\n                    // Double-check that the file is set in the state before proceeding\n                    if (state.file) {\n                        console.log('File confirmed in state before phase change (error case):', state.file.name);\n                        setPhase('thumbnailSelection');\n                    } else {\n                        console.log('File not found in state before phase change (error case), setting it again');\n                        // Try setting the file again\n                        setFile(currentFile);\n                        // Add a small delay to ensure the state is updated\n                        setTimeout(()=>{\n                            // Double-check again\n                            if (!state.file) {\n                                console.log('File still not in state (error case), setting it one more time');\n                                setFile(currentFile);\n                            }\n                            console.log('Delayed phase change to thumbnailSelection (error case)');\n                            setPhase('thumbnailSelection');\n                        }, 100);\n                    }\n                }\n            } else {\n                // For photos or moments images\n                if (selectedType === 'moments') {\n                    // For moments, we need to set the media type based on the file type\n                    if (file.type.startsWith('image/')) {\n                        console.log('Moments image detected');\n                        setMediaType('photo');\n                        // For moments images, we always use 'story' as the media subtype\n                        setMediaSubtype('story');\n                        // Create a local reference to the file for use in the timeout\n                        const currentFile = file;\n                        // Double-check that the file is set in the state before proceeding\n                        setTimeout(()=>{\n                            // Check if the file is in the state\n                            if (!state.file) {\n                                console.log('Moments photo not found in state after setting, trying again');\n                                // Try setting the file again\n                                setFile(currentFile);\n                            } else {\n                                console.log('Moments photo confirmed in state:', state.file.name);\n                            }\n                        }, 50);\n                    } else {\n                        console.log('Invalid file type for moments. Only images and videos less than 1 minute are allowed.');\n                        (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Invalid File Type', 'Invalid file type for moments. Only images and videos less than 1 minute are allowed.');\n                        // Reset the upload context but preserve the selected type and category\n                        const currentType = selectedType;\n                        const currentCategory = selectedCategory;\n                        // First set the phase back to category selection\n                        setPhase('categorySelection');\n                        // Then reset the upload state\n                        setTimeout(()=>{\n                            resetUpload();\n                            setSelectedType(currentType);\n                            setSelectedCategory(currentCategory);\n                            console.log('Reset upload state after invalid file type for moments');\n                        }, 100);\n                        return;\n                    }\n                }\n                // Handle image preview and set phase\n                const reader = new FileReader();\n                reader.onload = (e)=>{\n                    var _e_target;\n                    if ((_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result) {\n                        setPreviewImage(e.target.result);\n                        console.log('Preview image set for file:', file.name);\n                    }\n                };\n                reader.readAsDataURL(file);\n                // Create a local reference to the file for use in the timeout\n                const currentFile = file;\n                // Double-check that the file is set in the state before proceeding\n                setTimeout(()=>{\n                    // Check if the file is in the state\n                    if (!state.file) {\n                        console.log('File not found in state before moving to personalDetails, setting it again');\n                        // Try setting the file again\n                        setFile(currentFile);\n                        // Add another timeout to ensure the file is set\n                        setTimeout(()=>{\n                            if (!state.file) {\n                                console.log('File still not in state, setting it one more time');\n                                setFile(currentFile);\n                            } else {\n                                console.log('File confirmed in state after second attempt:', state.file.name);\n                            }\n                            // New flow logic: For moments (stories), skip personal details and go directly to face verification\n                            if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                                console.log('Moments image upload: skipping personal details, going directly to face verification');\n                                setPhase('faceVerification');\n                            } else {\n                                console.log('Moving to personalDetails phase for photo/image');\n                                setPhase('personalDetails');\n                            }\n                        }, 100);\n                    } else {\n                        console.log('File confirmed in state:', state.file.name);\n                        // New flow logic: For moments (stories), skip personal details and go directly to face verification\n                        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                            console.log('Moments image upload: skipping personal details, going directly to face verification');\n                            setPhase('faceVerification');\n                        } else {\n                            console.log('Moving to personalDetails phase for photo/image');\n                            setPhase('personalDetails');\n                        }\n                    }\n                }, 100);\n            }\n        };\n        // Trigger the file dialog\n        input.click();\n    };\n    // Handle personal details completed\n    const handlePersonalDetailsCompleted = (details)=>{\n        console.log('Personal details completed:', details);\n        // Store the personal details in local state for component persistence\n        setLocalPersonalDetails(details);\n        // Validate that we have a title\n        if (!details.caption || !details.caption.trim()) {\n            console.error('Caption/title is empty, this should not happen');\n            // Go back to personal details to fix this\n            setPhase('personalDetails');\n            return;\n        }\n        // Set the title in the upload context\n        setTitle(details.caption.trim());\n        // Also store in global context for persistence (this is the upload context function)\n        setPersonalDetails(details);\n        // Check if we have a file in the state\n        if (!state.file) {\n            console.error('No file found in state after personal details');\n            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Upload Error', 'Something went wrong with your file upload. Please try again.');\n            setPhase('typeSelection');\n            return;\n        }\n        console.log('File confirmed in state after personal details:', state.file.name);\n        console.log('Personal details set successfully');\n        console.log('Title set to:', details.caption.trim());\n        console.log('Current selectedType:', selectedType);\n        console.log('Current mediaSubtype:', state.mediaSubtype);\n        // New flow logic based on backend requirements:\n        // - Moments (stories): Skip personal details, go directly to face verification\n        // - Photos: Go to face verification after personal details (no vendor details)\n        // - Videos: Go to vendor details after personal details\n        if (state.mediaType === 'photo') {\n            console.log('Photo upload: proceeding to face verification (no vendor details needed)');\n            setPhase('faceVerification');\n        } else if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n            console.log('Moments upload: proceeding to face verification (no vendor details needed)');\n            setPhase('faceVerification');\n        } else {\n            // For videos (flashes, glimpses, movies), proceed to vendor details\n            console.log('Video upload: proceeding to vendor details');\n            setPhase('vendorDetails');\n        }\n    };\n    // Handle vendor details completed\n    const handleVendorDetailsCompleted = (vendorDetails)=>{\n        // console.log('Vendor details completed:', vendorDetails);\n        // Normalize vendor details to ensure consistent field names\n        const normalizedVendorDetails = {\n            ...vendorDetails\n        };\n        // Ensure we have both frontend and backend field names for makeup artist and decorations\n        if (vendorDetails.makeupArtist) {\n            normalizedVendorDetails.makeup_artist = vendorDetails.makeupArtist;\n        } else if (vendorDetails.makeup_artist) {\n            normalizedVendorDetails.makeupArtist = vendorDetails.makeup_artist;\n        }\n        if (vendorDetails.decorations) {\n            normalizedVendorDetails.decoration = vendorDetails.decorations;\n        } else if (vendorDetails.decoration) {\n            normalizedVendorDetails.decorations = vendorDetails.decoration;\n        }\n        // Store the normalized vendor details for persistence between screens\n        setVendorDetailsData(normalizedVendorDetails);\n        // Also store in the ref for Edge browser compatibility\n        vendorDetailsRef.current = normalizedVendorDetails;\n        // Store vendor details in localStorage for persistence\n        try {\n            localStorage.setItem('wedzat_vendor_details', JSON.stringify(normalizedVendorDetails));\n            console.log('UPLOAD MANAGER - Stored vendor details in localStorage');\n        } catch (error) {\n            console.error('UPLOAD MANAGER - Failed to store vendor details in localStorage:', error);\n        }\n        // Save the current video_category before setting vendor details\n        const currentVideoCategory = state.detailFields.video_category;\n        console.log('UPLOAD MANAGER - Saving video_category before vendor details:', currentVideoCategory);\n        // Store video_category in localStorage\n        if (currentVideoCategory) {\n            try {\n                localStorage.setItem('wedzat_video_category', currentVideoCategory);\n                console.log('UPLOAD MANAGER - Stored video_category in localStorage:', currentVideoCategory);\n            } catch (error) {\n                console.error('UPLOAD MANAGER - Failed to store video_category in localStorage:', error);\n            }\n        }\n        // Store in global context for persistence\n        setVendorDetails(normalizedVendorDetails);\n        // Explicitly set each vendor detail field\n        Object.entries(normalizedVendorDetails).forEach((param)=>{\n            let [vendorType, details] = param;\n            if (details && details.name && details.mobileNumber) {\n                setDetailField(\"vendor_\".concat(vendorType, \"_name\"), details.name);\n                setDetailField(\"vendor_\".concat(vendorType, \"_contact\"), details.mobileNumber);\n            }\n        });\n        // Re-set the video_category after vendor details to ensure it's preserved\n        if (currentVideoCategory) {\n            console.log('UPLOAD MANAGER - Re-setting video_category after vendor details:', currentVideoCategory);\n            setTimeout(()=>{\n                setDetailField('video_category', currentVideoCategory);\n            }, 100);\n        }\n        // Log all detail fields after setting vendor details\n        setTimeout(()=>{\n            console.log('All detail fields after vendor details:', state.detailFields);\n            console.log('Detail fields count:', Object.keys(state.detailFields).length);\n            console.log('Normalized vendor details:', normalizedVendorDetails);\n        }, 200);\n        // Add a small delay to ensure the state is updated before proceeding\n        // This helps with cross-browser compatibility, especially in Edge\n        setTimeout(()=>{\n            // Double-check that we have at least 4 vendor details before proceeding\n            const vendorNameFields = Object.keys(state.detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_name'));\n            const vendorContactFields = Object.keys(state.detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_contact'));\n            console.log('UPLOAD MANAGER - Vendor name fields:', vendorNameFields.length);\n            console.log('UPLOAD MANAGER - Vendor contact fields:', vendorContactFields.length);\n            // Edge browser workaround - directly set vendor details in the state\n            if ( true && /Edge|Edg/.test(window.navigator.userAgent)) {\n                console.log('UPLOAD MANAGER - Edge browser detected, applying direct vendor details workaround');\n                // Create vendor detail fields directly in the state\n                // This is a workaround for Edge browser where the state update doesn't properly preserve vendor details\n                Object.entries(normalizedVendorDetails).forEach((param)=>{\n                    let [vendorType, details] = param;\n                    if (details && details.name && details.mobileNumber) {\n                        // Set the vendor details directly in the state\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                        // Also set the normalized version\n                        const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                        if (normalizedType !== vendorType) {\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                        }\n                        console.log(\"UPLOAD MANAGER - Edge workaround: Added vendor \".concat(vendorType, \" directly to state\"));\n                    }\n                });\n                // Re-set the video_category directly\n                if (currentVideoCategory) {\n                    state.detailFields.video_category = currentVideoCategory;\n                    console.log('UPLOAD MANAGER - Edge workaround: Re-set video_category directly:', currentVideoCategory);\n                }\n            }\n            // Proceed to face verification\n            setPhase('faceVerification');\n        }, 300);\n    };\n    // Handle thumbnail selection\n    const handleThumbnailSelected = (thumbnailFile)=>{\n        if (thumbnailFile) {\n            // Set the thumbnail in the context\n            setThumbnail(thumbnailFile);\n            console.log('Thumbnail selected:', thumbnailFile.name);\n        } else {\n            console.log('No thumbnail selected, using auto-generated thumbnail');\n        }\n        // New flow logic: For moments (stories), skip personal details and go directly to face verification\n        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n            console.log('Moments upload: skipping personal details, going directly to face verification');\n            setPhase('faceVerification');\n        } else {\n            // For photos and videos, go to personal details\n            console.log('Photo/Video upload: proceeding to personal details');\n            setPhase('personalDetails');\n        }\n    };\n    // Function to proceed with upload after vendor details are applied\n    const proceedWithUpload = (videoCategory)=>{\n        // For moments (stories), this function should not be called, but add safety check\n        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n            console.log('UPLOAD MANAGER - Moments detected in proceedWithUpload, calling startUpload directly');\n            startUpload();\n            return;\n        }\n        // Double-check that we have a title before changing to uploading phase\n        if (!state.title || !state.title.trim()) {\n            console.error('Title is missing before upload, setting it from personal details');\n            // Try to set the title from personal details\n            if (personalDetails.caption && personalDetails.caption.trim()) {\n                // console.log('Setting personal details from local state:', personalDetails);\n                // Use the global context function to set all personal details at once\n                setPersonalDetails(personalDetails);\n                // Explicitly set the title as well\n                setTitle(personalDetails.caption.trim());\n            } else {\n                console.error('No title in personal details either, going back to personal details');\n                setPhase('personalDetails');\n                return;\n            }\n        }\n        // Edge browser workaround - directly set vendor details in the state\n        if ( true && /Edge|Edg/.test(window.navigator.userAgent)) {\n            console.log('UPLOAD MANAGER - Edge browser detected in face verification, applying vendor details workaround');\n            // Get the vendor details from the vendor details data\n            const vendorDetailsData = vendorDetailsRef.current;\n            if (vendorDetailsData) {\n                console.log('UPLOAD MANAGER - Edge workaround: Using vendor details from ref:', vendorDetailsData);\n                // Create vendor detail fields directly in the state\n                Object.entries(vendorDetailsData).forEach((param)=>{\n                    let [vendorType, details] = param;\n                    if (details && details.name && details.mobileNumber) {\n                        // Set the vendor details directly in the state\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                        // Also set the normalized version\n                        const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                        if (normalizedType !== vendorType) {\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                        }\n                        console.log(\"UPLOAD MANAGER - Edge workaround: Added vendor \".concat(vendorType, \" directly to state\"));\n                    }\n                });\n            }\n        }\n        // For videos, check if we have a video_category\n        if (state.mediaType === 'video') {\n            console.log(\"UPLOAD MANAGER - Checking video_category before upload\");\n            console.log(\"UPLOAD MANAGER - Current video_category: \".concat(state.detailFields.video_category || 'Not set'));\n            console.log(\"UPLOAD MANAGER - Current mediaSubtype: \".concat(state.mediaSubtype));\n            console.log(\"UPLOAD MANAGER - Selected category: \".concat(selectedCategory || 'Not set'));\n            // Special handling for glimpses\n            if (state.mediaSubtype === 'glimpse') {\n                console.log(\"UPLOAD MANAGER - Special handling for glimpses\");\n                // If we don't have a video_category yet, try to set it from selectedCategory\n                if (!state.detailFields.video_category && selectedCategory) {\n                    // Map the UI category to the backend video_category\n                    let videoCategory = '';\n                    if (selectedCategory === 'my_wedding_videos') {\n                        videoCategory = 'my_wedding';\n                    } else if (selectedCategory === 'wedding_vlog') {\n                        videoCategory = 'wedding_vlog';\n                    } else if (selectedCategory === 'friends_family_videos') {\n                        videoCategory = 'friends_family_video';\n                    }\n                    if (videoCategory) {\n                        console.log(\"UPLOAD MANAGER - Setting video_category for glimpse: \".concat(videoCategory));\n                        setDetailField('video_category', videoCategory);\n                    }\n                } else {\n                    console.log(\"UPLOAD MANAGER - Glimpse already has video_category: \".concat(state.detailFields.video_category));\n                }\n            }\n            // If we still don't have a video_category, use a default based on selectedCategory\n            if (!state.detailFields.video_category && selectedCategory) {\n                console.log(\"UPLOAD MANAGER - No video_category set, using selectedCategory: \".concat(selectedCategory));\n                // Map the UI category to the backend video_category\n                let videoCategory = '';\n                if (selectedCategory === 'my_wedding_videos') {\n                    videoCategory = 'my_wedding';\n                } else if (selectedCategory === 'wedding_vlog') {\n                    videoCategory = 'wedding_vlog';\n                } else if (selectedCategory === 'friends_family_videos') {\n                    videoCategory = 'friends_family_video';\n                }\n                if (videoCategory) {\n                    console.log(\"UPLOAD MANAGER - Setting video_category from selectedCategory: \".concat(videoCategory));\n                    setDetailField('video_category', videoCategory);\n                }\n            }\n            // Final check - if we still don't have a video_category, use a default\n            if (!state.detailFields.video_category) {\n                console.log('No video_category found, using a default one');\n                // Use 'my_wedding' as a default category instead of asking the user again\n                setDetailField('video_category', 'my_wedding');\n                console.log('Set default video_category to my_wedding');\n            }\n        }\n        // Edge browser workaround - directly set vendor details in the state before upload\n        if ( true && /Edge|Edg/.test(window.navigator.userAgent)) {\n            console.log('UPLOAD MANAGER - Edge browser detected before upload, applying vendor details workaround');\n            // Get the vendor details from the vendor details data\n            const vendorDetailsData = vendorDetailsRef.current;\n            if (vendorDetailsData && Object.keys(vendorDetailsData).length > 0) {\n                console.log('UPLOAD MANAGER - Edge workaround: Using vendor details from ref before upload:', vendorDetailsData);\n                // Create vendor detail fields directly in the state\n                Object.entries(vendorDetailsData).forEach((param)=>{\n                    let [vendorType, details] = param;\n                    if (details && details.name && details.mobileNumber) {\n                        // Set the vendor details directly in the state\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                        // Also set the normalized version\n                        const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                        if (normalizedType !== vendorType) {\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                        }\n                        console.log(\"UPLOAD MANAGER - Edge workaround: Added vendor \".concat(vendorType, \" directly to state before upload\"));\n                    }\n                });\n            }\n        }\n        // Check if we have a file before proceeding\n        if (!state.file) {\n            console.error('No file found in state before upload');\n            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Upload Error', 'No file selected. Please select a file to upload.');\n            // Go back to type selection to start over\n            setPhase('typeSelection');\n            return;\n        }\n        // Now we can proceed to uploading phase\n        setPhase('uploading');\n        // Log the current state before starting upload\n        console.log('Current state before upload:', {\n            file: state.file ? state.file.name : 'No file',\n            mediaType: state.mediaType,\n            mediaSubtype: state.mediaSubtype,\n            title: state.title,\n            description: state.description,\n            detailFields: state.detailFields,\n            detailFieldsCount: Object.keys(state.detailFields).length\n        });\n        // Double-check that we're using the correct category\n        console.log(\"UPLOAD MANAGER - Final check - Selected type: \".concat(selectedType));\n        console.log(\"UPLOAD MANAGER - Final check - MediaSubtype in state: \".concat(state.mediaSubtype));\n        // If the mediaSubtype doesn't match what we expect based on the selected type, fix it\n        if (selectedType && state.mediaSubtype !== getMediaSubtypeFromSelectedType(selectedType)) {\n            console.log(\"UPLOAD MANAGER - WARNING: MediaSubtype mismatch detected!\");\n            console.log(\"UPLOAD MANAGER - Expected mediaSubtype based on selected type: \".concat(getMediaSubtypeFromSelectedType(selectedType)));\n            console.log(\"UPLOAD MANAGER - Actual mediaSubtype in state: \".concat(state.mediaSubtype));\n            console.log(\"UPLOAD MANAGER - Correcting category before upload...\");\n            // Get the corrected category\n            const correctedCategory = getMediaSubtypeFromSelectedType(selectedType);\n            console.log(\"UPLOAD MANAGER - Category corrected to: \".concat(correctedCategory));\n            // Get the video_category from the original selection\n            // We need to map it to the correct backend value\n            let videoCategory = '';\n            if (selectedCategory === 'my_wedding_videos') {\n                videoCategory = 'my_wedding';\n            } else if (selectedCategory === 'wedding_vlog') {\n                videoCategory = 'wedding_vlog';\n            } else if (selectedCategory === 'friends_family_videos') {\n                videoCategory = 'friends_family_video';\n            }\n            console.log(\"UPLOAD MANAGER - Original selected category: \".concat(selectedCategory));\n            console.log(\"UPLOAD MANAGER - Mapped to backend video_category: \".concat(videoCategory));\n            // Start the upload process with the corrected category and video_category\n            startUploadWithCategory(correctedCategory, videoCategory);\n        } else {\n            // Get the video_category from the state\n            const finalVideoCategory = videoCategory || state.detailFields.video_category || 'my_wedding';\n            console.log(\"UPLOAD MANAGER - Using video_category for upload: \".concat(finalVideoCategory));\n            // Start the upload process with the current category and video_category\n            startUploadWithCategory(state.mediaSubtype, finalVideoCategory).then(()=>{\n                // Upload completed successfully\n                console.log('Upload completed successfully');\n            }).catch((error)=>{\n                console.error('Upload failed:', error);\n            });\n        }\n    };\n    // Handle moments upload with dedicated flow\n    const handleMomentsUpload = async ()=>{\n        console.log('UPLOAD MANAGER - Starting dedicated moments upload flow');\n        if (!state.file) {\n            console.error('No file found for moments upload');\n            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Upload Error', 'No file selected for upload.');\n            return;\n        }\n        // Set uploading state\n        setPhase('uploading');\n        try {\n            console.log('UPLOAD MANAGER - Uploading moments file:', state.file.name);\n            // For moments, determine the correct subtype based on media type\n            let momentsSubtype;\n            if (state.mediaType === 'photo') {\n                momentsSubtype = 'story'; // Photos can use 'story' subtype\n            } else {\n                // For videos, use 'flash' as the subtype but backend will handle it as story\n                momentsSubtype = 'story'; // Backend expects 'story' for moments regardless of media type\n            }\n            console.log('UPLOAD MANAGER - Using subtype for moments:', momentsSubtype);\n            // Use upload service directly with minimal data for moments\n            const result = await _services_api__WEBPACK_IMPORTED_MODULE_13__.uploadService.handleUpload(state.file, state.mediaType, momentsSubtype, state.title || state.file.name.replace(/\\.[^/.]+$/, \"\"), '', [], {}, state.duration, state.thumbnail, (progress)=>{\n                console.log(\"Moments upload progress: \".concat(progress, \"%\"));\n            // You can add progress updates here if needed\n            });\n            console.log('UPLOAD MANAGER - Moments upload completed successfully:', result);\n            // Show success and reset\n            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showSuccessAlert)('Upload Successful', 'Your moment has been uploaded successfully!');\n            resetUpload();\n            setPhase('typeSelection');\n        } catch (error) {\n            console.error('UPLOAD MANAGER - Moments upload failed:', error);\n            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Upload Failed', error instanceof Error ? error.message : 'Failed to upload moment. Please try again.');\n            setPhase('faceVerification'); // Go back to face verification\n        }\n    };\n    // Handle face verification completed and start upload\n    const handleFaceVerificationCompleted = ()=>{\n        console.log('Face verification completed, starting upload process');\n        // Check if we have a file in the state\n        if (!state.file) {\n            console.error('No file found in state after face verification');\n            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Upload Error', 'Something went wrong with your file upload. Please try again.');\n            setPhase('typeSelection');\n            return;\n        }\n        console.log('File confirmed in state after face verification:', state.file.name);\n        // For moments (stories), use completely separate upload flow\n        if (selectedType === 'moments') {\n            console.log('UPLOAD MANAGER - Moments detected after face verification, using dedicated moments upload flow');\n            // Set a default title if not already set (using filename without extension)\n            if (!state.title || !state.title.trim()) {\n                const defaultTitle = state.file.name.replace(/\\.[^/.]+$/, \"\"); // Remove file extension\n                setTitle(defaultTitle);\n                console.log('UPLOAD MANAGER - Set default title for moments:', defaultTitle);\n            }\n            // Call dedicated moments upload function\n            setTimeout(()=>{\n                handleMomentsUpload();\n            }, 100);\n            return;\n        }\n        // Try to get vendor details from localStorage first\n        let vendorDetailsData = vendorDetailsRef.current;\n        // If not in ref, try localStorage\n        if (!vendorDetailsData || Object.keys(vendorDetailsData).length === 0) {\n            try {\n                const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n                if (storedVendorDetails) {\n                    vendorDetailsData = JSON.parse(storedVendorDetails);\n                    console.log('UPLOAD MANAGER - Retrieved vendor details from localStorage:', storedVendorDetails);\n                    // Update the ref with the localStorage data\n                    vendorDetailsRef.current = vendorDetailsData;\n                    // Log the vendor details we found\n                    console.log(\"UPLOAD MANAGER - Found \".concat(Object.keys(vendorDetailsData).length, \" vendor details in localStorage\"));\n                    Object.entries(vendorDetailsData).forEach((param)=>{\n                        let [vendorType, details] = param;\n                        if (details && details.name && details.mobileNumber) {\n                            console.log(\"UPLOAD MANAGER - Vendor \".concat(vendorType, \": \").concat(details.name, \" (\").concat(details.mobileNumber, \")\"));\n                        }\n                    });\n                } else {\n                    console.log('UPLOAD MANAGER - No vendor details found in localStorage');\n                }\n            } catch (error) {\n                console.error('UPLOAD MANAGER - Failed to retrieve vendor details from localStorage:', error);\n            }\n        } else {\n            console.log(\"UPLOAD MANAGER - Using \".concat(Object.keys(vendorDetailsData).length, \" vendor details from ref\"));\n        }\n        // Try to get video_category from localStorage\n        let videoCategory = state.detailFields.video_category;\n        if (!videoCategory) {\n            try {\n                const storedVideoCategory = localStorage.getItem('wedzat_video_category');\n                if (storedVideoCategory) {\n                    videoCategory = storedVideoCategory;\n                    console.log('UPLOAD MANAGER - Retrieved video_category from localStorage:', videoCategory);\n                    // Set it in the state\n                    setDetailField('video_category', videoCategory);\n                }\n            } catch (error) {\n                console.error('UPLOAD MANAGER - Failed to retrieve video_category from localStorage:', error);\n            }\n        }\n        // Ensure vendor details are present\n        if (vendorDetailsData) {\n            console.log('UPLOAD MANAGER - Applying vendor details to state');\n            // Create a batch of all detail fields to update at once\n            const detailFieldUpdates = {};\n            let completeVendorCount = 0;\n            // Re-apply vendor details to ensure they're in the state\n            Object.entries(vendorDetailsData).forEach((param)=>{\n                let [vendorType, details] = param;\n                if (details && details.name && details.mobileNumber) {\n                    // Add to the batch\n                    detailFieldUpdates[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                    detailFieldUpdates[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                    completeVendorCount++;\n                    // Also set normalized versions\n                    const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                    if (normalizedType !== vendorType) {\n                        detailFieldUpdates[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                        detailFieldUpdates[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                    }\n                }\n            });\n            // Apply all updates at once\n            console.log(\"UPLOAD MANAGER - Applying \".concat(completeVendorCount, \" complete vendor details to state\"));\n            console.log('UPLOAD MANAGER - Detail field updates:', JSON.stringify(detailFieldUpdates));\n            // Apply each update individually to ensure they're all set\n            Object.entries(detailFieldUpdates).forEach((param)=>{\n                let [field, value] = param;\n                setDetailField(field, value);\n            });\n            // Add a delay before proceeding to ensure state updates are applied\n            setTimeout(()=>{\n                console.log('UPLOAD MANAGER - Vendor details applied to state, proceeding with upload');\n                proceedWithUpload(videoCategory);\n            }, 500);\n        } else {\n            console.log('UPLOAD MANAGER - No vendor details found, proceeding with upload');\n            proceedWithUpload(videoCategory);\n        }\n    // This code has been moved to the proceedWithUpload function\n    };\n    // Handle going back to personal details from upload error\n    const handleBackToPersonalDetails = ()=>{\n        // console.log('Going back to personal details with stored data:', personalDetails);\n        // Make sure the personal details are set in the context\n        if (personalDetails.caption && personalDetails.caption.trim()) {\n            // Use the global context function to set all personal details at once\n            setPersonalDetails(personalDetails);\n        }\n        setPhase('personalDetails');\n    };\n    // Handle close modal\n    const handleClose = ()=>{\n        // Check if upload was successful and call onUploadComplete\n        if (state.step === 'complete' && onUploadComplete) {\n            console.log('Upload completed successfully, calling onUploadComplete callback');\n            onUploadComplete();\n        }\n        // Reset the phase first\n        setPhase('closed');\n        // Call the onClose callback if provided\n        if (onClose) {\n            onClose();\n        }\n        // Reset the upload state after a short delay to ensure the modal is closed first\n        setTimeout(()=>{\n            resetUpload();\n            console.log('Upload state reset after modal close');\n        }, 100);\n    };\n    // Render selected phase component\n    if (phase === 'closed') {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            phase === 'typeSelection' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UploadTypeSelection__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                onNext: handleTypeSelected,\n                onClose: handleClose\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1432,\n                columnNumber: 9\n            }, undefined),\n            phase === 'categorySelection' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VideoCategorySelection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onNext: handleCategorySelected,\n                onBack: ()=>setPhase('typeSelection'),\n                onUpload: handleCategorySelected,\n                onThumbnailUpload: handleThumbnailUpload,\n                onClose: handleClose,\n                mediaType: state.mediaType,\n                selectedType: selectedType\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1439,\n                columnNumber: 9\n            }, undefined),\n            phase === 'thumbnailSelection' && state.file && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ThumbnailSelection__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                videoFile: state.file,\n                onNext: handleThumbnailSelected,\n                onBack: ()=>{\n                    // Go back to category selection instead of triggering file upload again\n                    if ([\n                        'flashes',\n                        'glimpses',\n                        'movies'\n                    ].includes(selectedType)) {\n                        setPhase('categorySelection');\n                    } else {\n                        // For moments, go back to type selection\n                        setPhase('typeSelection');\n                    }\n                },\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1451,\n                columnNumber: 9\n            }, undefined),\n            phase === 'personalDetails' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PersonalDetails__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onNext: handlePersonalDetailsCompleted,\n                onBack: ()=>{\n                    // Go back to thumbnail selection for videos\n                    if (state.mediaType === 'video' && state.file) {\n                        setPhase('thumbnailSelection');\n                    } else {\n                        // For photos, go back to type selection\n                        setPhase('typeSelection');\n                    }\n                },\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                },\n                previewImage: previewImage,\n                videoFile: state.mediaType === 'video' ? state.file : null,\n                mediaType: state.mediaType,\n                contentType: getContentType(),\n                initialDetails: personalDetails\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1472,\n                columnNumber: 9\n            }, undefined),\n            phase === 'vendorDetails' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VendorDetails__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                onNext: handleVendorDetailsCompleted,\n                onBack: ()=>setPhase('personalDetails'),\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                },\n                initialVendorDetails: vendorDetailsData,\n                videoCategory: (()=>{\n                    const category = state.detailFields.video_category || 'my_wedding';\n                    console.log('UPLOAD MANAGER - Passing video category to VendorDetails:', category);\n                    return category;\n                })()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1497,\n                columnNumber: 9\n            }, undefined),\n            phase === 'faceVerification' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FaceVerification__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                onUpload: handleFaceVerificationCompleted,\n                onBack: ()=>{\n                    // New flow logic for back navigation:\n                    // - Moments: Go back to thumbnail selection (or type selection for images)\n                    // - Photos: Go back to personal details\n                    // - Videos: Go back to vendor details\n                    if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                        // For moments, go back to thumbnail selection for videos, or type selection for images\n                        if (state.mediaType === 'video') {\n                            setPhase('thumbnailSelection');\n                        } else {\n                            setPhase('typeSelection');\n                        }\n                    } else if (state.mediaType === 'photo') {\n                        setPhase('personalDetails');\n                    } else {\n                        setPhase('vendorDetails');\n                    }\n                },\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1515,\n                columnNumber: 9\n            }, undefined),\n            (phase === 'uploading' || phase === 'complete') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UploadProgress__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                },\n                onGoBack: handleBackToPersonalDetails\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1544,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(UploadManager, \"LQsMODnAAL1BJd6LrYNCqh7ZCEM=\", false, function() {\n    return [\n        _contexts_UploadContexts__WEBPACK_IMPORTED_MODULE_9__.useUpload,\n        _hooks_useMedia__WEBPACK_IMPORTED_MODULE_12__.useMediaUpload\n    ];\n});\n_c = UploadManager;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UploadManager);\nvar _c;\n$RefreshReg$(_c, \"UploadManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/upload/UploadManager.tsx\n"));

/***/ })

});