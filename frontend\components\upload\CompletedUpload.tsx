// components/upload/CompletedUpload.tsx
'use client';

import React from 'react';
import { CheckCircle } from 'lucide-react';
import { useUpload } from '../../contexts/UploadContexts';
import { useRouter } from 'next/navigation';

const CompletedUpload: React.FC = () => {
  const { resetUpload, state } = useUpload();
  const router = useRouter();
  
  const handleGoToHome = () => {
    router.push('/home');
  };
  
  return (
    <div className="text-center py-8">
      <div className="mx-auto w-24 h-24 mb-4 flex items-center justify-center rounded-full bg-green-50">
        <CheckCircle className="h-12 w-12 text-green-500" />
      </div>
      <h3 className="text-lg font-medium text-gray-900">
        Upload Complete!
      </h3>
      <p className="mt-1 text-sm text-gray-500">
        Your {state.mediaType} has been successfully uploaded and is now available.
      </p>
      
      <div className="mt-6 flex justify-center space-x-4">
        <button
          type="button"
          onClick={resetUpload}
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
        >
          Upload Another
        </button>
        <button
          type="button"
          onClick={handleGoToHome}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
        >
          Go to Home
        </button>
      </div>
    </div>
  );
};

export default CompletedUpload;