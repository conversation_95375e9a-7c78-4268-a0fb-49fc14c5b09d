"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./components/upload/UploadManager.tsx":
/*!*********************************************!*\
  !*** ./components/upload/UploadManager.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _UploadTypeSelection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./UploadTypeSelection */ \"(app-pages-browser)/./components/upload/UploadTypeSelection.tsx\");\n/* harmony import */ var _VideoCategorySelection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./VideoCategorySelection */ \"(app-pages-browser)/./components/upload/VideoCategorySelection.tsx\");\n/* harmony import */ var _ThumbnailSelection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ThumbnailSelection */ \"(app-pages-browser)/./components/upload/ThumbnailSelection.tsx\");\n/* harmony import */ var _PersonalDetails__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./PersonalDetails */ \"(app-pages-browser)/./components/upload/PersonalDetails.tsx\");\n/* harmony import */ var _VendorDetails__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./VendorDetails */ \"(app-pages-browser)/./components/upload/VendorDetails.tsx\");\n/* harmony import */ var _FaceVerification__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./FaceVerification */ \"(app-pages-browser)/./components/upload/FaceVerification.tsx\");\n/* harmony import */ var _UploadProgress__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./UploadProgress */ \"(app-pages-browser)/./components/upload/UploadProgress.tsx\");\n/* harmony import */ var _contexts_UploadContexts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../contexts/UploadContexts */ \"(app-pages-browser)/./contexts/UploadContexts.tsx\");\n/* harmony import */ var _utils_uploadUtils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../utils/uploadUtils */ \"(app-pages-browser)/./utils/uploadUtils.ts\");\n/* harmony import */ var _utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../utils/alertUtils */ \"(app-pages-browser)/./utils/alertUtils.tsx\");\n/* harmony import */ var _hooks_useMedia__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../hooks/useMedia */ \"(app-pages-browser)/./hooks/useMedia.ts\");\n// components/upload/UploadManager.tsx\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function to format time in minutes and seconds\nconst formatTime = (seconds)=>{\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    if (minutes === 0) {\n        return \"\".concat(remainingSeconds, \" seconds\");\n    } else if (minutes === 1 && remainingSeconds === 0) {\n        return '1 minute';\n    } else if (remainingSeconds === 0) {\n        return \"\".concat(minutes, \" minutes\");\n    } else {\n        return \"\".concat(minutes, \" minute\").concat(minutes > 1 ? 's' : '', \" and \").concat(remainingSeconds, \" second\").concat(remainingSeconds !== 1 ? 's' : '');\n    }\n};\nconst UploadManager = (param)=>{\n    let { onClose, initialType, onUploadComplete } = param;\n    _s();\n    const { state, setFile, setMediaType, setCategory, setMediaSubtype, setTitle, setDescription, setDetailField, startUpload, startUploadWithCategory, setThumbnail, setVendorDetails, setPersonalDetails, setDuration, resetUpload } = (0,_contexts_UploadContexts__WEBPACK_IMPORTED_MODULE_9__.useUpload)();\n    const [phase, setPhase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('typeSelection');\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialType || '');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [previewImage, setPreviewImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [thumbnailImage, setThumbnailImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const vendorDetailsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    // Helper function to determine content type for PersonalDetails\n    const getContentType = ()=>{\n        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n            return 'moment';\n        } else if (state.mediaType === 'video') {\n            return 'video';\n        } else {\n            return 'photo';\n        }\n    };\n    // Store personal details to persist between screens\n    const [personalDetails, setLocalPersonalDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        caption: '',\n        lifePartner: '',\n        weddingStyle: '',\n        place: '',\n        eventType: '',\n        budget: ''\n    });\n    // Auto-select the type if initialType is provided\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UploadManager.useEffect\": ()=>{\n            if (initialType) {\n                console.log('Auto-selecting type from initialType:', initialType);\n                handleTypeSelected(initialType);\n            }\n        }\n    }[\"UploadManager.useEffect\"], []);\n    // Store vendor details to persist between screens\n    const [vendorDetailsData, setVendorDetailsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        venue: {\n            name: '',\n            mobileNumber: ''\n        },\n        photographer: {\n            name: '',\n            mobileNumber: ''\n        },\n        makeupArtist: {\n            name: '',\n            mobileNumber: ''\n        },\n        decorations: {\n            name: '',\n            mobileNumber: ''\n        },\n        caterer: {\n            name: '',\n            mobileNumber: ''\n        }\n    });\n    // Use the new media upload hook\n    const { mutate: uploadMedia, isPending: isUploading } = (0,_hooks_useMedia__WEBPACK_IMPORTED_MODULE_12__.useMediaUpload)();\n    // Handle media type selection\n    const handleTypeSelected = (type)=>{\n        // First, completely reset everything\n        resetUpload();\n        setPreviewImage(null);\n        setThumbnailImage(null);\n        // Then set the new type\n        setSelectedType(type);\n        console.log(\"Selected type:\", type);\n        if ([\n            'flashes',\n            'glimpses',\n            'movies',\n            'photos',\n            'moments'\n        ].includes(type)) {\n            // For explicit video types, photos, and moments, set the appropriate media type\n            if (type === 'photos') {\n                console.log('Setting media type to photo for:', type);\n                setMediaType('photo');\n                setMediaSubtype('post');\n                // Go to category selection for photos\n                setPhase('categorySelection');\n            } else if (type === 'moments') {\n                console.log('Setting media type for moments (will be determined by file type)');\n                // For moments, we'll set the media type later based on the file type (photo or video)\n                setMediaSubtype('story');\n                // For moments, skip category selection and go directly to file upload\n                console.log('Moments selected: skipping category selection, going directly to file upload');\n                handleFileUpload();\n                return; // Early return to prevent further processing\n            } else {\n                setMediaType('video');\n                setMediaSubtype(getMediaSubtypeFromSelectedType(type));\n                // Go to category selection for videos\n                setPhase('categorySelection');\n            }\n        } else if (type === 'photo') {\n            // For single photo type (if it exists)\n            console.log('Setting media type to photo for:', type);\n            setMediaType('photo');\n            setMediaSubtype('post');\n            // Use a special photo-only upload handler for photos\n            handlePhotoUpload();\n        }\n    };\n    // Helper function to get the backend media subtype from the selected UI type\n    const getMediaSubtypeFromSelectedType = (type)=>{\n        // Map UI category to backend category for media_subtype\n        switch(type){\n            // Photo types\n            case 'moments':\n                return 'story'; // Backend expects 'story' for moments\n            case 'photos':\n                return 'post'; // Backend expects 'post' for regular photos\n            // Video types\n            case 'flashes':\n                return 'flash'; // Backend expects 'flash'\n            case 'glimpses':\n                return 'glimpse'; // Backend expects 'glimpse'\n            case 'movies':\n                return 'movie'; // Backend expects 'movie'\n            // Default fallback\n            default:\n                return type === 'moments' ? 'story' : 'post'; // Default based on type\n        }\n    };\n    // Handle category selection for both videos and photos\n    const handleCategorySelected = (category)=>{\n        // First, make sure we have a clean state for the new upload\n        // but preserve the selected type and media type\n        const currentType = selectedType;\n        const currentMediaType = state.mediaType;\n        resetUpload();\n        setSelectedType(currentType);\n        setMediaType(currentMediaType);\n        setPreviewImage(null);\n        setThumbnailImage(null);\n        // Now set the new category\n        setSelectedCategory(category);\n        // Get the media subtype based on the selected type\n        let mediaSubtype;\n        if (currentType === 'photos') {\n            // For photos, always use 'post' as the media subtype\n            mediaSubtype = 'post';\n            console.log(\"UPLOAD MANAGER - Using media subtype 'post' for photos\");\n        } else {\n            // For videos, use the subtype based on the selected type\n            mediaSubtype = getMediaSubtypeFromSelectedType(selectedType);\n            console.log(\"UPLOAD MANAGER - Using media subtype \".concat(mediaSubtype, \" based on selected type \").concat(selectedType));\n            console.log(\"UPLOAD MANAGER - Backend expects: flashes→flash, glimpses→glimpse, movies→movie, moments→story\");\n        }\n        console.log(\"UPLOAD MANAGER - Selected category:\", category);\n        console.log(\"UPLOAD MANAGER - Setting media subtype to:\", mediaSubtype);\n        // Set the media subtype in the context\n        setMediaSubtype(mediaSubtype);\n        // Map the selected category to a valid backend video_category\n        let backendVideoCategory = '';\n        if (category === 'my_wedding_videos') {\n            backendVideoCategory = 'my_wedding';\n        } else if (category === 'wedding_vlog') {\n            backendVideoCategory = 'wedding_vlog';\n        }\n        // Make sure we have a valid video_category\n        if (!backendVideoCategory) {\n            console.error('Invalid video category selected:', category);\n            alert('Please select a valid video category');\n            return;\n        }\n        // Set video category in the context for the backend\n        console.log(\"UPLOAD MANAGER - Setting video_category to:\", backendVideoCategory);\n        setDetailField('video_category', backendVideoCategory);\n        // Log the final values\n        console.log(\"UPLOAD MANAGER - Selected category:\", category);\n        console.log(\"UPLOAD MANAGER - Backend video category set to:\", backendVideoCategory);\n        console.log(\"UPLOAD MANAGER - Media subtype set to:\", mediaSubtype);\n        // Proceed to file upload after setting the category\n        if (currentType === 'photos') {\n            // For photos, use the photo-specific upload handler\n            handlePhotoUpload();\n        } else {\n            // For videos, use the standard file upload handler\n            handleFileUpload();\n        }\n    };\n    // Handle thumbnail upload\n    const handleThumbnailUpload = ()=>{\n        // Create a file input element\n        const input = document.createElement('input');\n        input.type = 'file';\n        input.accept = 'image/*';\n        // Handle file selection\n        input.onchange = (e)=>{\n            const files = e.target.files;\n            if (files && files.length > 0) {\n                const file = files[0];\n                // Store the thumbnail\n                setThumbnailImage(file);\n                setThumbnail(file);\n                console.log(\"Thumbnail selected:\", file.name);\n                // Show a preview if needed\n                const reader = new FileReader();\n                reader.onload = (e)=>{\n                    var _e_target;\n                    if ((_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result) {\n                        // You could set a thumbnail preview here if needed\n                        console.log(\"Thumbnail preview ready\");\n                    }\n                };\n                reader.readAsDataURL(file);\n            }\n        };\n        // Trigger the file dialog\n        input.click();\n    };\n    // Get user-friendly display name for a category\n    const getCategoryDisplayName = (category)=>{\n        switch(category){\n            case 'flash':\n                return 'Flash';\n            case 'glimpse':\n                return 'Glimpse';\n            case 'movie':\n                return 'Movie';\n            case 'story':\n                return 'Story';\n            case 'post':\n                return 'Photo';\n            default:\n                return category.charAt(0).toUpperCase() + category.slice(1);\n        }\n    };\n    // Get appropriate category based on duration\n    const getAppropriateCategory = (duration)=>{\n        // For very short videos (1 minute or less), use flash instead of story/moments\n        if (duration <= 60) {\n            return 'flash'; // Very short videos (1 minute or less) - changed from 'story' to 'flash'\n        } else if (duration <= 90) {\n            return 'flash'; // Short videos (1.5 minutes or less)\n        } else if (duration <= 420) {\n            return 'glimpse'; // Medium videos (7 minutes or less)\n        } else {\n            return 'movie'; // Long videos (over 7 minutes)\n        }\n    };\n    // Special handler for photo uploads that strictly enforces image-only files\n    const handlePhotoUpload = ()=>{\n        console.log('handlePhotoUpload called - strict image-only upload');\n        // Create a file input element specifically for photos\n        const input = document.createElement('input');\n        input.type = 'file';\n        // Reset the input value\n        input.value = '';\n        // Only accept image files - explicitly list allowed types\n        input.accept = 'image/jpeg,image/png,image/gif,image/webp';\n        // Handle file selection with strict validation\n        input.onchange = async (e)=>{\n            const files = e.target.files;\n            if (!files || files.length === 0) return;\n            const file = files[0];\n            console.log('Photo file selected:', file.name, file.type, file.size);\n            // Strict validation - must be an image file\n            const validImageTypes = [\n                'image/jpeg',\n                'image/png',\n                'image/gif',\n                'image/webp'\n            ];\n            if (!validImageTypes.includes(file.type)) {\n                console.error('Invalid file type for photos:', file.type);\n                alert('Only JPEG, PNG, GIF, or WebP images can be uploaded as photos. Videos are not allowed.');\n                return;\n            }\n            // Additional check - reject any file that might be a video\n            if (file.type.startsWith('video/')) {\n                console.error('Attempted to upload a video file as photo');\n                alert('Videos cannot be uploaded as photos. Please select an image file.');\n                return;\n            }\n            // For photos, we need to be more careful with state management\n            // First, set the media type and subtype\n            setMediaType('photo');\n            setMediaSubtype('post');\n            // Then set the file in the state\n            setFile(file);\n            console.log('Photo file set in state:', file.name);\n            // Create a local reference to the file for use in the timeout\n            const currentFile = file;\n            // Double-check that the file is set in the state before proceeding\n            setTimeout(()=>{\n                // Check if the file is in the state\n                if (!state.file) {\n                    console.log('File not found in state after setting, trying again');\n                    // Try setting the file again\n                    setFile(currentFile);\n                    // Add another timeout to ensure the file is set\n                    setTimeout(()=>{\n                        if (!state.file) {\n                            console.log('File still not in state, setting it one more time');\n                            setFile(currentFile);\n                        } else {\n                            console.log('File confirmed in state after second attempt:', state.file.name);\n                        }\n                        // New flow logic: For moments (stories), skip personal details and go directly to face verification\n                        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                            console.log('Moments photo upload: skipping personal details, going directly to face verification');\n                            setPhase('faceVerification');\n                        } else {\n                            console.log('Moving to personalDetails phase for photo');\n                            setPhase('personalDetails');\n                        }\n                    }, 100);\n                } else {\n                    console.log('File confirmed in state:', state.file.name);\n                    // New flow logic: For moments (stories), skip personal details and go directly to face verification\n                    if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                        console.log('Moments photo upload: skipping personal details, going directly to face verification');\n                        setPhase('faceVerification');\n                    } else {\n                        console.log('Moving to personalDetails phase for photo');\n                        setPhase('personalDetails');\n                    }\n                }\n            }, 100);\n            // Handle image preview\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                var _e_target;\n                if ((_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result) {\n                    setPreviewImage(e.target.result);\n                    console.log('Preview image set for photo');\n                }\n            };\n            reader.readAsDataURL(file);\n        // Note: We don't set the phase here anymore - it's handled in the timeout above\n        };\n        // Trigger the file dialog\n        input.click();\n    };\n    // This function was previously used but is now replaced by getAppropriateCategory\n    // Keeping a comment here for reference in case it needs to be restored\n    // Handle manual upload button click\n    const handleFileUpload = async (category)=>{\n        console.log('handleFileUpload called with category:', category || 'none');\n        // Create a file input element\n        const input = document.createElement('input');\n        input.type = 'file';\n        // Reset the input value to ensure we get a new file selection event even if the same file is selected\n        input.value = '';\n        if (selectedType === 'moments') {\n            input.accept = 'image/*,video/*';\n        } else {\n            input.accept = selectedType === 'photo' || selectedType === 'photos' ? 'image/jpeg,image/png,image/gif,image/webp' // Explicitly list image types only\n             : 'video/*';\n        }\n        // Handle file selection\n        input.onchange = async (e)=>{\n            const files = e.target.files;\n            if (!files || files.length === 0) return;\n            const file = files[0];\n            console.log('File selected:', file.name, file.type, file.size);\n            // Strict validation for photo uploads - must be an image file\n            if (selectedType === 'photo' || selectedType === 'photos') {\n                const validImageTypes = [\n                    'image/jpeg',\n                    'image/png',\n                    'image/gif',\n                    'image/webp'\n                ];\n                // Check if file is a video or not a valid image type\n                if (file.type.startsWith('video/') || !validImageTypes.includes(file.type)) {\n                    console.error('Invalid file type for photos:', file.type);\n                    (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Invalid File Type', 'Only JPEG, PNG, GIF, or WebP images can be uploaded as photos. Videos are not allowed.');\n                    return;\n                }\n            }\n            // Reset the upload context before setting the new file\n            resetUpload();\n            // Set the file in the state\n            setFile(file);\n            console.log('File set in state:', file.name);\n            // If it's a video, calculate and set the duration\n            // Double-check that we're not trying to upload a video as a photo\n            if (file.type.startsWith('video/')) {\n                // Safety check - don't process videos for photo uploads\n                if (selectedType === 'photo' || selectedType === 'photos') {\n                    console.error('Attempted to process a video file for photo upload');\n                    (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Invalid File Type', 'Videos cannot be uploaded as photos. Please select an image file.');\n                    resetUpload();\n                    return;\n                }\n                try {\n                    const duration = await (0,_utils_uploadUtils__WEBPACK_IMPORTED_MODULE_10__.getVideoDuration)(file);\n                    console.log('Video duration calculated:', duration);\n                    setDuration(duration);\n                    // For moments, check if it's a video and validate the duration (max 1 minute)\n                    if (selectedType === 'moments') {\n                        console.log('Validating moments video duration...');\n                        setMediaType('video');\n                        // Check if the video is longer than 1 minute (60 seconds)\n                        if (duration > 60) {\n                            console.log(\"Moments video too long: \".concat(duration, \" seconds (max: 60 seconds)\"));\n                            // Show a more detailed error message with custom alert\n                            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showWarningAlert)('Moments Video Too Long', \"Moments videos must be 1 minute or less.\\n\\nYour video is \".concat(formatTime(duration), \" long.\\n\\nPlease select a shorter video or trim this video to 1 minute or less.\"));\n                            // Reset the upload context but preserve the selected type and category\n                            const currentType = selectedType;\n                            const currentCategory = selectedCategory;\n                            // First set the phase back to category selection\n                            setPhase('categorySelection');\n                            // Then reset the upload state\n                            setTimeout(()=>{\n                                resetUpload();\n                                setSelectedType(currentType);\n                                setSelectedCategory(currentCategory);\n                                console.log('Reset upload state after moments video duration validation failure');\n                            }, 100);\n                            // Return early to prevent further processing\n                            return;\n                        }\n                        console.log(\"Moments video duration valid: \".concat(duration, \" seconds (max: 60 seconds)\"));\n                        // For moments, we always use 'story' as the media subtype\n                        console.log('Setting media subtype for moments video to story');\n                        setMediaSubtype('story');\n                    }\n                    // If we have a category, validate the duration for that category\n                    if (selectedType && [\n                        'flashes',\n                        'glimpses',\n                        'movies'\n                    ].includes(selectedType)) {\n                        const mediaSubtype = getMediaSubtypeFromSelectedType(selectedType);\n                        const validationResult = (0,_utils_uploadUtils__WEBPACK_IMPORTED_MODULE_10__.validateVideoDuration)(duration, mediaSubtype);\n                        if (!validationResult.isValid) {\n                            // If there's a suggested category, automatically switch to it\n                            if (validationResult.suggestedCategory) {\n                                // For videos that exceed the maximum duration, automatically switch without asking\n                                console.log(\"Video exceeds maximum duration for \".concat(mediaSubtype, \". Automatically switching to \").concat(validationResult.suggestedCategory));\n                                (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showWarningAlert)('Video Duration Notice', \"Your video is too long for the \".concat(getCategoryDisplayName(mediaSubtype), \" category. It will be uploaded as a \").concat(getCategoryDisplayName(validationResult.suggestedCategory), \" instead.\"));\n                                // Switch to the suggested category\n                                console.log(\"Switching to suggested category: \".concat(validationResult.suggestedCategory));\n                                // Make sure we keep a reference to the file\n                                const currentFile = file;\n                                // Update the media subtype\n                                setMediaSubtype(validationResult.suggestedCategory);\n                                // Update the selected type to match the new category\n                                // Never suggest 'story' (moments) for other categories\n                                if (validationResult.suggestedCategory === 'flash') {\n                                    setSelectedType('flashes');\n                                } else if (validationResult.suggestedCategory === 'glimpse') {\n                                    setSelectedType('glimpses');\n                                } else if (validationResult.suggestedCategory === 'movie') {\n                                    setSelectedType('movies');\n                                }\n                                // Removed the 'story' suggestion for short videos\n                                // Make sure the file is still set in the state\n                                setTimeout(()=>{\n                                    if (!state.file) {\n                                        console.log('Re-setting file after category change:', currentFile.name);\n                                        setFile(currentFile);\n                                    }\n                                }, 50);\n                            } else {\n                                // No suggested category, just show the error\n                                (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Video Duration Error', validationResult.error || 'The video duration is not valid for this category.');\n                            }\n                        } else if (validationResult.suggestedCategory && validationResult.suggestedCategory !== mediaSubtype) {\n                            // Video is valid for current category but there's a better category\n                            // For this case, we still give the user a choice since the video is valid for the current category\n                            // Use our custom confirm dialog instead of window.confirm\n                            const confirmSwitch = await (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showAlert)({\n                                title: 'Category Suggestion',\n                                message: \"\".concat(validationResult.error, \"\\n\\nWould you like to switch to the suggested category?\"),\n                                type: 'warning',\n                                confirmText: 'Yes, Switch Category',\n                                cancelText: 'No, Keep Current',\n                                onConfirm: ()=>{}\n                            });\n                            if (confirmSwitch) {\n                                // Switch to the suggested category\n                                console.log(\"Switching to suggested category: \".concat(validationResult.suggestedCategory));\n                                // Make sure we keep a reference to the file\n                                const currentFile = file;\n                                // Update the media subtype\n                                setMediaSubtype(validationResult.suggestedCategory);\n                                // Update the selected type to match the new category\n                                // Never suggest 'story' (moments) for other categories\n                                if (validationResult.suggestedCategory === 'flash') {\n                                    setSelectedType('flashes');\n                                } else if (validationResult.suggestedCategory === 'glimpse') {\n                                    setSelectedType('glimpses');\n                                } else if (validationResult.suggestedCategory === 'movie') {\n                                    setSelectedType('movies');\n                                }\n                                // Removed the 'story' suggestion for short videos\n                                // Make sure the file is still set in the state\n                                setTimeout(()=>{\n                                    if (!state.file) {\n                                        console.log('Re-setting file after category change:', currentFile.name);\n                                        setFile(currentFile);\n                                    }\n                                }, 50);\n                            }\n                        }\n                    }\n                    // Always go to thumbnail selection for videos\n                    console.log('Moving to thumbnailSelection phase');\n                    // Keep a reference to the current file\n                    const currentFile = file;\n                    // Double-check that the file is set in the state before proceeding\n                    if (state.file) {\n                        console.log('File confirmed in state before phase change:', state.file.name);\n                        setPhase('thumbnailSelection');\n                    } else {\n                        console.log('File not found in state before phase change, setting it again');\n                        // Try setting the file again\n                        setFile(currentFile);\n                        // Add a small delay to ensure the state is updated\n                        setTimeout(()=>{\n                            // Double-check again\n                            if (!state.file) {\n                                console.log('File still not in state, setting it one more time');\n                                setFile(currentFile);\n                            }\n                            console.log('Delayed phase change to thumbnailSelection');\n                            setPhase('thumbnailSelection');\n                        }, 100);\n                    }\n                } catch (error) {\n                    console.error('Error calculating video duration:', error);\n                    // For moments videos, we need to enforce the duration check\n                    // If we can't calculate duration, we can't validate it, so we should reject the upload\n                    if (selectedType === 'moments') {\n                        (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Video Error', 'Unable to determine video duration. Please try a different video file.');\n                        resetUpload();\n                        return;\n                    }\n                    console.log('Moving to thumbnailSelection phase despite error');\n                    // Keep a reference to the current file\n                    const currentFile = file;\n                    // Double-check that the file is set in the state before proceeding\n                    if (state.file) {\n                        console.log('File confirmed in state before phase change (error case):', state.file.name);\n                        setPhase('thumbnailSelection');\n                    } else {\n                        console.log('File not found in state before phase change (error case), setting it again');\n                        // Try setting the file again\n                        setFile(currentFile);\n                        // Add a small delay to ensure the state is updated\n                        setTimeout(()=>{\n                            // Double-check again\n                            if (!state.file) {\n                                console.log('File still not in state (error case), setting it one more time');\n                                setFile(currentFile);\n                            }\n                            console.log('Delayed phase change to thumbnailSelection (error case)');\n                            setPhase('thumbnailSelection');\n                        }, 100);\n                    }\n                }\n            } else {\n                // For photos or moments images\n                if (selectedType === 'moments') {\n                    // For moments, we need to set the media type based on the file type\n                    if (file.type.startsWith('image/')) {\n                        console.log('Moments image detected');\n                        setMediaType('photo');\n                        // For moments images, we always use 'story' as the media subtype\n                        setMediaSubtype('story');\n                        // Create a local reference to the file for use in the timeout\n                        const currentFile = file;\n                        // Double-check that the file is set in the state before proceeding\n                        setTimeout(()=>{\n                            // Check if the file is in the state\n                            if (!state.file) {\n                                console.log('Moments photo not found in state after setting, trying again');\n                                // Try setting the file again\n                                setFile(currentFile);\n                            } else {\n                                console.log('Moments photo confirmed in state:', state.file.name);\n                            }\n                        }, 50);\n                    } else {\n                        console.log('Invalid file type for moments. Only images and videos less than 1 minute are allowed.');\n                        (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Invalid File Type', 'Invalid file type for moments. Only images and videos less than 1 minute are allowed.');\n                        // Reset the upload context but preserve the selected type and category\n                        const currentType = selectedType;\n                        const currentCategory = selectedCategory;\n                        // First set the phase back to category selection\n                        setPhase('categorySelection');\n                        // Then reset the upload state\n                        setTimeout(()=>{\n                            resetUpload();\n                            setSelectedType(currentType);\n                            setSelectedCategory(currentCategory);\n                            console.log('Reset upload state after invalid file type for moments');\n                        }, 100);\n                        return;\n                    }\n                }\n                // Handle image preview and set phase\n                const reader = new FileReader();\n                reader.onload = (e)=>{\n                    var _e_target;\n                    if ((_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result) {\n                        setPreviewImage(e.target.result);\n                        console.log('Preview image set for file:', file.name);\n                    }\n                };\n                reader.readAsDataURL(file);\n                // Create a local reference to the file for use in the timeout\n                const currentFile = file;\n                // Double-check that the file is set in the state before proceeding\n                setTimeout(()=>{\n                    // Check if the file is in the state\n                    if (!state.file) {\n                        console.log('File not found in state before moving to personalDetails, setting it again');\n                        // Try setting the file again\n                        setFile(currentFile);\n                        // Add another timeout to ensure the file is set\n                        setTimeout(()=>{\n                            if (!state.file) {\n                                console.log('File still not in state, setting it one more time');\n                                setFile(currentFile);\n                            } else {\n                                console.log('File confirmed in state after second attempt:', state.file.name);\n                            }\n                            // New flow logic: For moments (stories), skip personal details and go directly to face verification\n                            if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                                console.log('Moments image upload: skipping personal details, going directly to face verification');\n                                setPhase('faceVerification');\n                            } else {\n                                console.log('Moving to personalDetails phase for photo/image');\n                                setPhase('personalDetails');\n                            }\n                        }, 100);\n                    } else {\n                        console.log('File confirmed in state:', state.file.name);\n                        // New flow logic: For moments (stories), skip personal details and go directly to face verification\n                        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                            console.log('Moments image upload: skipping personal details, going directly to face verification');\n                            setPhase('faceVerification');\n                        } else {\n                            console.log('Moving to personalDetails phase for photo/image');\n                            setPhase('personalDetails');\n                        }\n                    }\n                }, 100);\n            }\n        };\n        // Trigger the file dialog\n        input.click();\n    };\n    // Handle personal details completed\n    const handlePersonalDetailsCompleted = (details)=>{\n        console.log('Personal details completed:', details);\n        // Store the personal details in local state for component persistence\n        setLocalPersonalDetails(details);\n        // Validate that we have a title\n        if (!details.caption || !details.caption.trim()) {\n            console.error('Caption/title is empty, this should not happen');\n            // Go back to personal details to fix this\n            setPhase('personalDetails');\n            return;\n        }\n        // Set the title in the upload context\n        setTitle(details.caption.trim());\n        // Also store in global context for persistence (this is the upload context function)\n        setPersonalDetails(details);\n        // Check if we have a file in the state\n        if (!state.file) {\n            console.error('No file found in state after personal details');\n            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Upload Error', 'Something went wrong with your file upload. Please try again.');\n            setPhase('typeSelection');\n            return;\n        }\n        console.log('File confirmed in state after personal details:', state.file.name);\n        console.log('Personal details set successfully');\n        console.log('Title set to:', details.caption.trim());\n        console.log('Current selectedType:', selectedType);\n        console.log('Current mediaSubtype:', state.mediaSubtype);\n        // New flow logic based on backend requirements:\n        // - Moments (stories): Skip personal details, go directly to face verification\n        // - Photos: Go to face verification after personal details (no vendor details)\n        // - Videos: Go to vendor details after personal details\n        if (state.mediaType === 'photo') {\n            console.log('Photo upload: proceeding to face verification (no vendor details needed)');\n            setPhase('faceVerification');\n        } else if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n            console.log('Moments upload: proceeding to face verification (no vendor details needed)');\n            setPhase('faceVerification');\n        } else {\n            // For videos (flashes, glimpses, movies), proceed to vendor details\n            console.log('Video upload: proceeding to vendor details');\n            setPhase('vendorDetails');\n        }\n    };\n    // Handle vendor details completed\n    const handleVendorDetailsCompleted = (vendorDetails)=>{\n        // console.log('Vendor details completed:', vendorDetails);\n        // Normalize vendor details to ensure consistent field names\n        const normalizedVendorDetails = {\n            ...vendorDetails\n        };\n        // Ensure we have both frontend and backend field names for makeup artist and decorations\n        if (vendorDetails.makeupArtist) {\n            normalizedVendorDetails.makeup_artist = vendorDetails.makeupArtist;\n        } else if (vendorDetails.makeup_artist) {\n            normalizedVendorDetails.makeupArtist = vendorDetails.makeup_artist;\n        }\n        if (vendorDetails.decorations) {\n            normalizedVendorDetails.decoration = vendorDetails.decorations;\n        } else if (vendorDetails.decoration) {\n            normalizedVendorDetails.decorations = vendorDetails.decoration;\n        }\n        // Store the normalized vendor details for persistence between screens\n        setVendorDetailsData(normalizedVendorDetails);\n        // Also store in the ref for Edge browser compatibility\n        vendorDetailsRef.current = normalizedVendorDetails;\n        // Store vendor details in localStorage for persistence\n        try {\n            localStorage.setItem('wedzat_vendor_details', JSON.stringify(normalizedVendorDetails));\n            console.log('UPLOAD MANAGER - Stored vendor details in localStorage');\n        } catch (error) {\n            console.error('UPLOAD MANAGER - Failed to store vendor details in localStorage:', error);\n        }\n        // Save the current video_category before setting vendor details\n        const currentVideoCategory = state.detailFields.video_category;\n        console.log('UPLOAD MANAGER - Saving video_category before vendor details:', currentVideoCategory);\n        // Store video_category in localStorage\n        if (currentVideoCategory) {\n            try {\n                localStorage.setItem('wedzat_video_category', currentVideoCategory);\n                console.log('UPLOAD MANAGER - Stored video_category in localStorage:', currentVideoCategory);\n            } catch (error) {\n                console.error('UPLOAD MANAGER - Failed to store video_category in localStorage:', error);\n            }\n        }\n        // Store in global context for persistence\n        setVendorDetails(normalizedVendorDetails);\n        // Explicitly set each vendor detail field\n        Object.entries(normalizedVendorDetails).forEach((param)=>{\n            let [vendorType, details] = param;\n            if (details && details.name && details.mobileNumber) {\n                setDetailField(\"vendor_\".concat(vendorType, \"_name\"), details.name);\n                setDetailField(\"vendor_\".concat(vendorType, \"_contact\"), details.mobileNumber);\n            }\n        });\n        // Re-set the video_category after vendor details to ensure it's preserved\n        if (currentVideoCategory) {\n            console.log('UPLOAD MANAGER - Re-setting video_category after vendor details:', currentVideoCategory);\n            setTimeout(()=>{\n                setDetailField('video_category', currentVideoCategory);\n            }, 100);\n        }\n        // Log all detail fields after setting vendor details\n        setTimeout(()=>{\n            console.log('All detail fields after vendor details:', state.detailFields);\n            console.log('Detail fields count:', Object.keys(state.detailFields).length);\n            console.log('Normalized vendor details:', normalizedVendorDetails);\n        }, 200);\n        // Add a small delay to ensure the state is updated before proceeding\n        // This helps with cross-browser compatibility, especially in Edge\n        setTimeout(()=>{\n            // Double-check that we have at least 4 vendor details before proceeding\n            const vendorNameFields = Object.keys(state.detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_name'));\n            const vendorContactFields = Object.keys(state.detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_contact'));\n            console.log('UPLOAD MANAGER - Vendor name fields:', vendorNameFields.length);\n            console.log('UPLOAD MANAGER - Vendor contact fields:', vendorContactFields.length);\n            // Edge browser workaround - directly set vendor details in the state\n            if ( true && /Edge|Edg/.test(window.navigator.userAgent)) {\n                console.log('UPLOAD MANAGER - Edge browser detected, applying direct vendor details workaround');\n                // Create vendor detail fields directly in the state\n                // This is a workaround for Edge browser where the state update doesn't properly preserve vendor details\n                Object.entries(normalizedVendorDetails).forEach((param)=>{\n                    let [vendorType, details] = param;\n                    if (details && details.name && details.mobileNumber) {\n                        // Set the vendor details directly in the state\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                        // Also set the normalized version\n                        const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                        if (normalizedType !== vendorType) {\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                        }\n                        console.log(\"UPLOAD MANAGER - Edge workaround: Added vendor \".concat(vendorType, \" directly to state\"));\n                    }\n                });\n                // Re-set the video_category directly\n                if (currentVideoCategory) {\n                    state.detailFields.video_category = currentVideoCategory;\n                    console.log('UPLOAD MANAGER - Edge workaround: Re-set video_category directly:', currentVideoCategory);\n                }\n            }\n            // Proceed to face verification\n            setPhase('faceVerification');\n        }, 300);\n    };\n    // Handle thumbnail selection\n    const handleThumbnailSelected = (thumbnailFile)=>{\n        if (thumbnailFile) {\n            // Set the thumbnail in the context\n            setThumbnail(thumbnailFile);\n            console.log('Thumbnail selected:', thumbnailFile.name);\n        } else {\n            console.log('No thumbnail selected, using auto-generated thumbnail');\n        }\n        // New flow logic: For moments (stories), skip personal details and go directly to face verification\n        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n            console.log('Moments upload: skipping personal details, going directly to face verification');\n            setPhase('faceVerification');\n        } else {\n            // For photos and videos, go to personal details\n            console.log('Photo/Video upload: proceeding to personal details');\n            setPhase('personalDetails');\n        }\n    };\n    // Function to proceed with upload after vendor details are applied\n    const proceedWithUpload = (videoCategory)=>{\n        // For moments (stories), this function should not be called, but add safety check\n        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n            console.log('UPLOAD MANAGER - Moments detected in proceedWithUpload, calling startUpload directly');\n            startUpload();\n            return;\n        }\n        // Double-check that we have a title before changing to uploading phase\n        if (!state.title || !state.title.trim()) {\n            console.error('Title is missing before upload, setting it from personal details');\n            // Try to set the title from personal details\n            if (personalDetails.caption && personalDetails.caption.trim()) {\n                // console.log('Setting personal details from local state:', personalDetails);\n                // Use the global context function to set all personal details at once\n                setPersonalDetails(personalDetails);\n                // Explicitly set the title as well\n                setTitle(personalDetails.caption.trim());\n            } else {\n                console.error('No title in personal details either, going back to personal details');\n                setPhase('personalDetails');\n                return;\n            }\n        }\n        // Edge browser workaround - directly set vendor details in the state\n        if ( true && /Edge|Edg/.test(window.navigator.userAgent)) {\n            console.log('UPLOAD MANAGER - Edge browser detected in face verification, applying vendor details workaround');\n            // Get the vendor details from the vendor details data\n            const vendorDetailsData = vendorDetailsRef.current;\n            if (vendorDetailsData) {\n                console.log('UPLOAD MANAGER - Edge workaround: Using vendor details from ref:', vendorDetailsData);\n                // Create vendor detail fields directly in the state\n                Object.entries(vendorDetailsData).forEach((param)=>{\n                    let [vendorType, details] = param;\n                    if (details && details.name && details.mobileNumber) {\n                        // Set the vendor details directly in the state\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                        // Also set the normalized version\n                        const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                        if (normalizedType !== vendorType) {\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                        }\n                        console.log(\"UPLOAD MANAGER - Edge workaround: Added vendor \".concat(vendorType, \" directly to state\"));\n                    }\n                });\n            }\n        }\n        // For videos, check if we have a video_category\n        if (state.mediaType === 'video') {\n            console.log(\"UPLOAD MANAGER - Checking video_category before upload\");\n            console.log(\"UPLOAD MANAGER - Current video_category: \".concat(state.detailFields.video_category || 'Not set'));\n            console.log(\"UPLOAD MANAGER - Current mediaSubtype: \".concat(state.mediaSubtype));\n            console.log(\"UPLOAD MANAGER - Selected category: \".concat(selectedCategory || 'Not set'));\n            // Special handling for glimpses\n            if (state.mediaSubtype === 'glimpse') {\n                console.log(\"UPLOAD MANAGER - Special handling for glimpses\");\n                // If we don't have a video_category yet, try to set it from selectedCategory\n                if (!state.detailFields.video_category && selectedCategory) {\n                    // Map the UI category to the backend video_category\n                    let videoCategory = '';\n                    if (selectedCategory === 'my_wedding_videos') {\n                        videoCategory = 'my_wedding';\n                    } else if (selectedCategory === 'wedding_vlog') {\n                        videoCategory = 'wedding_vlog';\n                    } else if (selectedCategory === 'friends_family_videos') {\n                        videoCategory = 'friends_family_video';\n                    }\n                    if (videoCategory) {\n                        console.log(\"UPLOAD MANAGER - Setting video_category for glimpse: \".concat(videoCategory));\n                        setDetailField('video_category', videoCategory);\n                    }\n                } else {\n                    console.log(\"UPLOAD MANAGER - Glimpse already has video_category: \".concat(state.detailFields.video_category));\n                }\n            }\n            // If we still don't have a video_category, use a default based on selectedCategory\n            if (!state.detailFields.video_category && selectedCategory) {\n                console.log(\"UPLOAD MANAGER - No video_category set, using selectedCategory: \".concat(selectedCategory));\n                // Map the UI category to the backend video_category\n                let videoCategory = '';\n                if (selectedCategory === 'my_wedding_videos') {\n                    videoCategory = 'my_wedding';\n                } else if (selectedCategory === 'wedding_vlog') {\n                    videoCategory = 'wedding_vlog';\n                } else if (selectedCategory === 'friends_family_videos') {\n                    videoCategory = 'friends_family_video';\n                }\n                if (videoCategory) {\n                    console.log(\"UPLOAD MANAGER - Setting video_category from selectedCategory: \".concat(videoCategory));\n                    setDetailField('video_category', videoCategory);\n                }\n            }\n            // Final check - if we still don't have a video_category, use a default\n            if (!state.detailFields.video_category) {\n                console.log('No video_category found, using a default one');\n                // Use 'my_wedding' as a default category instead of asking the user again\n                setDetailField('video_category', 'my_wedding');\n                console.log('Set default video_category to my_wedding');\n            }\n        }\n        // Edge browser workaround - directly set vendor details in the state before upload\n        if ( true && /Edge|Edg/.test(window.navigator.userAgent)) {\n            console.log('UPLOAD MANAGER - Edge browser detected before upload, applying vendor details workaround');\n            // Get the vendor details from the vendor details data\n            const vendorDetailsData = vendorDetailsRef.current;\n            if (vendorDetailsData && Object.keys(vendorDetailsData).length > 0) {\n                console.log('UPLOAD MANAGER - Edge workaround: Using vendor details from ref before upload:', vendorDetailsData);\n                // Create vendor detail fields directly in the state\n                Object.entries(vendorDetailsData).forEach((param)=>{\n                    let [vendorType, details] = param;\n                    if (details && details.name && details.mobileNumber) {\n                        // Set the vendor details directly in the state\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                        // Also set the normalized version\n                        const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                        if (normalizedType !== vendorType) {\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                        }\n                        console.log(\"UPLOAD MANAGER - Edge workaround: Added vendor \".concat(vendorType, \" directly to state before upload\"));\n                    }\n                });\n            }\n        }\n        // Check if we have a file before proceeding\n        if (!state.file) {\n            console.error('No file found in state before upload');\n            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Upload Error', 'No file selected. Please select a file to upload.');\n            // Go back to type selection to start over\n            setPhase('typeSelection');\n            return;\n        }\n        // Now we can proceed to uploading phase\n        setPhase('uploading');\n        // Log the current state before starting upload\n        console.log('Current state before upload:', {\n            file: state.file ? state.file.name : 'No file',\n            mediaType: state.mediaType,\n            mediaSubtype: state.mediaSubtype,\n            title: state.title,\n            description: state.description,\n            detailFields: state.detailFields,\n            detailFieldsCount: Object.keys(state.detailFields).length\n        });\n        // Double-check that we're using the correct category\n        console.log(\"UPLOAD MANAGER - Final check - Selected type: \".concat(selectedType));\n        console.log(\"UPLOAD MANAGER - Final check - MediaSubtype in state: \".concat(state.mediaSubtype));\n        // If the mediaSubtype doesn't match what we expect based on the selected type, fix it\n        if (selectedType && state.mediaSubtype !== getMediaSubtypeFromSelectedType(selectedType)) {\n            console.log(\"UPLOAD MANAGER - WARNING: MediaSubtype mismatch detected!\");\n            console.log(\"UPLOAD MANAGER - Expected mediaSubtype based on selected type: \".concat(getMediaSubtypeFromSelectedType(selectedType)));\n            console.log(\"UPLOAD MANAGER - Actual mediaSubtype in state: \".concat(state.mediaSubtype));\n            console.log(\"UPLOAD MANAGER - Correcting category before upload...\");\n            // Get the corrected category\n            const correctedCategory = getMediaSubtypeFromSelectedType(selectedType);\n            console.log(\"UPLOAD MANAGER - Category corrected to: \".concat(correctedCategory));\n            // Get the video_category from the original selection\n            // We need to map it to the correct backend value\n            let videoCategory = '';\n            if (selectedCategory === 'my_wedding_videos') {\n                videoCategory = 'my_wedding';\n            } else if (selectedCategory === 'wedding_vlog') {\n                videoCategory = 'wedding_vlog';\n            } else if (selectedCategory === 'friends_family_videos') {\n                videoCategory = 'friends_family_video';\n            }\n            console.log(\"UPLOAD MANAGER - Original selected category: \".concat(selectedCategory));\n            console.log(\"UPLOAD MANAGER - Mapped to backend video_category: \".concat(videoCategory));\n            // Start the upload process with the corrected category and video_category\n            startUploadWithCategory(correctedCategory, videoCategory);\n        } else {\n            // Get the video_category from the state\n            const finalVideoCategory = videoCategory || state.detailFields.video_category || 'my_wedding';\n            console.log(\"UPLOAD MANAGER - Using video_category for upload: \".concat(finalVideoCategory));\n            // Start the upload process with the current category and video_category\n            startUploadWithCategory(state.mediaSubtype, finalVideoCategory).then(()=>{\n                // Upload completed successfully\n                console.log('Upload completed successfully');\n            }).catch((error)=>{\n                console.error('Upload failed:', error);\n            });\n        }\n    };\n    // Handle face verification completed and start upload\n    const handleFaceVerificationCompleted = ()=>{\n        console.log('Face verification completed, starting upload process');\n        // Check if we have a file in the state\n        if (!state.file) {\n            console.error('No file found in state after face verification');\n            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Upload Error', 'Something went wrong with your file upload. Please try again.');\n            setPhase('typeSelection');\n            return;\n        }\n        console.log('File confirmed in state after face verification:', state.file.name);\n        // For moments (stories), use completely separate upload flow\n        if (selectedType === 'moments') {\n            console.log('UPLOAD MANAGER - Moments detected after face verification, using dedicated moments upload flow');\n            // Set a default title if not already set (using filename without extension)\n            if (!state.title || !state.title.trim()) {\n                const defaultTitle = state.file.name.replace(/\\.[^/.]+$/, \"\"); // Remove file extension\n                setTitle(defaultTitle);\n                console.log('UPLOAD MANAGER - Set default title for moments:', defaultTitle);\n            }\n            // Call dedicated moments upload function\n            setTimeout(()=>{\n                handleMomentsUpload();\n            }, 100);\n            return;\n        }\n        // Try to get vendor details from localStorage first\n        let vendorDetailsData = vendorDetailsRef.current;\n        // If not in ref, try localStorage\n        if (!vendorDetailsData || Object.keys(vendorDetailsData).length === 0) {\n            try {\n                const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n                if (storedVendorDetails) {\n                    vendorDetailsData = JSON.parse(storedVendorDetails);\n                    console.log('UPLOAD MANAGER - Retrieved vendor details from localStorage:', storedVendorDetails);\n                    // Update the ref with the localStorage data\n                    vendorDetailsRef.current = vendorDetailsData;\n                    // Log the vendor details we found\n                    console.log(\"UPLOAD MANAGER - Found \".concat(Object.keys(vendorDetailsData).length, \" vendor details in localStorage\"));\n                    Object.entries(vendorDetailsData).forEach((param)=>{\n                        let [vendorType, details] = param;\n                        if (details && details.name && details.mobileNumber) {\n                            console.log(\"UPLOAD MANAGER - Vendor \".concat(vendorType, \": \").concat(details.name, \" (\").concat(details.mobileNumber, \")\"));\n                        }\n                    });\n                } else {\n                    console.log('UPLOAD MANAGER - No vendor details found in localStorage');\n                }\n            } catch (error) {\n                console.error('UPLOAD MANAGER - Failed to retrieve vendor details from localStorage:', error);\n            }\n        } else {\n            console.log(\"UPLOAD MANAGER - Using \".concat(Object.keys(vendorDetailsData).length, \" vendor details from ref\"));\n        }\n        // Try to get video_category from localStorage\n        let videoCategory = state.detailFields.video_category;\n        if (!videoCategory) {\n            try {\n                const storedVideoCategory = localStorage.getItem('wedzat_video_category');\n                if (storedVideoCategory) {\n                    videoCategory = storedVideoCategory;\n                    console.log('UPLOAD MANAGER - Retrieved video_category from localStorage:', videoCategory);\n                    // Set it in the state\n                    setDetailField('video_category', videoCategory);\n                }\n            } catch (error) {\n                console.error('UPLOAD MANAGER - Failed to retrieve video_category from localStorage:', error);\n            }\n        }\n        // Ensure vendor details are present\n        if (vendorDetailsData) {\n            console.log('UPLOAD MANAGER - Applying vendor details to state');\n            // Create a batch of all detail fields to update at once\n            const detailFieldUpdates = {};\n            let completeVendorCount = 0;\n            // Re-apply vendor details to ensure they're in the state\n            Object.entries(vendorDetailsData).forEach((param)=>{\n                let [vendorType, details] = param;\n                if (details && details.name && details.mobileNumber) {\n                    // Add to the batch\n                    detailFieldUpdates[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                    detailFieldUpdates[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                    completeVendorCount++;\n                    // Also set normalized versions\n                    const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                    if (normalizedType !== vendorType) {\n                        detailFieldUpdates[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                        detailFieldUpdates[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                    }\n                }\n            });\n            // Apply all updates at once\n            console.log(\"UPLOAD MANAGER - Applying \".concat(completeVendorCount, \" complete vendor details to state\"));\n            console.log('UPLOAD MANAGER - Detail field updates:', JSON.stringify(detailFieldUpdates));\n            // Apply each update individually to ensure they're all set\n            Object.entries(detailFieldUpdates).forEach((param)=>{\n                let [field, value] = param;\n                setDetailField(field, value);\n            });\n            // Add a delay before proceeding to ensure state updates are applied\n            setTimeout(()=>{\n                console.log('UPLOAD MANAGER - Vendor details applied to state, proceeding with upload');\n                proceedWithUpload(videoCategory);\n            }, 500);\n        } else {\n            console.log('UPLOAD MANAGER - No vendor details found, proceeding with upload');\n            proceedWithUpload(videoCategory);\n        }\n    // This code has been moved to the proceedWithUpload function\n    };\n    // Handle going back to personal details from upload error\n    const handleBackToPersonalDetails = ()=>{\n        // console.log('Going back to personal details with stored data:', personalDetails);\n        // Make sure the personal details are set in the context\n        if (personalDetails.caption && personalDetails.caption.trim()) {\n            // Use the global context function to set all personal details at once\n            setPersonalDetails(personalDetails);\n        }\n        setPhase('personalDetails');\n    };\n    // Handle close modal\n    const handleClose = ()=>{\n        // Check if upload was successful and call onUploadComplete\n        if (state.step === 'complete' && onUploadComplete) {\n            console.log('Upload completed successfully, calling onUploadComplete callback');\n            onUploadComplete();\n        }\n        // Reset the phase first\n        setPhase('closed');\n        // Call the onClose callback if provided\n        if (onClose) {\n            onClose();\n        }\n        // Reset the upload state after a short delay to ensure the modal is closed first\n        setTimeout(()=>{\n            resetUpload();\n            console.log('Upload state reset after modal close');\n        }, 100);\n    };\n    // Render selected phase component\n    if (phase === 'closed') {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            phase === 'typeSelection' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UploadTypeSelection__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                onNext: handleTypeSelected,\n                onClose: handleClose\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1373,\n                columnNumber: 9\n            }, undefined),\n            phase === 'categorySelection' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VideoCategorySelection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onNext: handleCategorySelected,\n                onBack: ()=>setPhase('typeSelection'),\n                onUpload: handleCategorySelected,\n                onThumbnailUpload: handleThumbnailUpload,\n                onClose: handleClose,\n                mediaType: state.mediaType,\n                selectedType: selectedType\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1380,\n                columnNumber: 9\n            }, undefined),\n            phase === 'thumbnailSelection' && state.file && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ThumbnailSelection__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                videoFile: state.file,\n                onNext: handleThumbnailSelected,\n                onBack: ()=>{\n                    // Go back to category selection instead of triggering file upload again\n                    if ([\n                        'flashes',\n                        'glimpses',\n                        'movies'\n                    ].includes(selectedType)) {\n                        setPhase('categorySelection');\n                    } else {\n                        // For moments, go back to type selection\n                        setPhase('typeSelection');\n                    }\n                },\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1392,\n                columnNumber: 9\n            }, undefined),\n            phase === 'personalDetails' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PersonalDetails__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onNext: handlePersonalDetailsCompleted,\n                onBack: ()=>{\n                    // Go back to thumbnail selection for videos\n                    if (state.mediaType === 'video' && state.file) {\n                        setPhase('thumbnailSelection');\n                    } else {\n                        // For photos, go back to type selection\n                        setPhase('typeSelection');\n                    }\n                },\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                },\n                previewImage: previewImage,\n                videoFile: state.mediaType === 'video' ? state.file : null,\n                mediaType: state.mediaType,\n                contentType: getContentType(),\n                initialDetails: personalDetails\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1413,\n                columnNumber: 9\n            }, undefined),\n            phase === 'vendorDetails' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VendorDetails__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                onNext: handleVendorDetailsCompleted,\n                onBack: ()=>setPhase('personalDetails'),\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                },\n                initialVendorDetails: vendorDetailsData,\n                videoCategory: (()=>{\n                    const category = state.detailFields.video_category || 'my_wedding';\n                    console.log('UPLOAD MANAGER - Passing video category to VendorDetails:', category);\n                    return category;\n                })()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1438,\n                columnNumber: 9\n            }, undefined),\n            phase === 'faceVerification' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FaceVerification__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                onUpload: handleFaceVerificationCompleted,\n                onBack: ()=>{\n                    // New flow logic for back navigation:\n                    // - Moments: Go back to thumbnail selection (or type selection for images)\n                    // - Photos: Go back to personal details\n                    // - Videos: Go back to vendor details\n                    if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                        // For moments, go back to thumbnail selection for videos, or type selection for images\n                        if (state.mediaType === 'video') {\n                            setPhase('thumbnailSelection');\n                        } else {\n                            setPhase('typeSelection');\n                        }\n                    } else if (state.mediaType === 'photo') {\n                        setPhase('personalDetails');\n                    } else {\n                        setPhase('vendorDetails');\n                    }\n                },\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1456,\n                columnNumber: 9\n            }, undefined),\n            (phase === 'uploading' || phase === 'complete') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UploadProgress__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                },\n                onGoBack: handleBackToPersonalDetails\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1485,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(UploadManager, \"LQsMODnAAL1BJd6LrYNCqh7ZCEM=\", false, function() {\n    return [\n        _contexts_UploadContexts__WEBPACK_IMPORTED_MODULE_9__.useUpload,\n        _hooks_useMedia__WEBPACK_IMPORTED_MODULE_12__.useMediaUpload\n    ];\n});\n_c = UploadManager;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UploadManager);\nvar _c;\n$RefreshReg$(_c, \"UploadManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvdXBsb2FkL1VwbG9hZE1hbmFnZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLHNDQUFzQzs7O0FBR3FCO0FBQ0g7QUFDTTtBQUNSO0FBQ047QUFDSjtBQUNNO0FBQ0o7QUFDWTtBQUN3QjtBQUNHO0FBQ2Q7QUFrQ3ZFLHdEQUF3RDtBQUN4RCxNQUFNa0IsYUFBYSxDQUFDQztJQUNsQixNQUFNQyxVQUFVQyxLQUFLQyxLQUFLLENBQUNILFVBQVU7SUFDckMsTUFBTUksbUJBQW1CRixLQUFLQyxLQUFLLENBQUNILFVBQVU7SUFFOUMsSUFBSUMsWUFBWSxHQUFHO1FBQ2pCLE9BQU8sR0FBb0IsT0FBakJHLGtCQUFpQjtJQUM3QixPQUFPLElBQUlILFlBQVksS0FBS0cscUJBQXFCLEdBQUc7UUFDbEQsT0FBTztJQUNULE9BQU8sSUFBSUEscUJBQXFCLEdBQUc7UUFDakMsT0FBTyxHQUFXLE9BQVJILFNBQVE7SUFDcEIsT0FBTztRQUNMLE9BQU8sR0FBb0JBLE9BQWpCQSxTQUFRLFdBQXVDRyxPQUE5QkgsVUFBVSxJQUFJLE1BQU0sSUFBRyxTQUFpQ0csT0FBMUJBLGtCQUFpQixXQUEyQyxPQUFsQ0EscUJBQXFCLElBQUksTUFBTTtJQUNwSDtBQUNGO0FBRUEsTUFBTUMsZ0JBQThDO1FBQUMsRUFBRUMsT0FBTyxFQUFFQyxXQUFXLEVBQUVDLGdCQUFnQixFQUFFOztJQUM3RixNQUFNLEVBQUVDLEtBQUssRUFBRUMsT0FBTyxFQUFFQyxZQUFZLEVBQUVDLFdBQVcsRUFBRUMsZUFBZSxFQUFFQyxRQUFRLEVBQUVDLGNBQWMsRUFBRUMsY0FBYyxFQUFFQyxXQUFXLEVBQUVDLHVCQUF1QixFQUFFQyxZQUFZLEVBQUVDLGdCQUFnQixFQUFFQyxrQkFBa0IsRUFBRUMsV0FBVyxFQUFFQyxXQUFXLEVBQUUsR0FBRy9CLG1FQUFTQTtJQUM5TyxNQUFNLENBQUNnQyxPQUFPQyxTQUFTLEdBQUczQywrQ0FBUUEsQ0FBYztJQUNoRCxNQUFNLENBQUM0QyxjQUFjQyxnQkFBZ0IsR0FBRzdDLCtDQUFRQSxDQUFTeUIsZUFBZTtJQUN4RSxNQUFNLENBQUNxQixrQkFBa0JDLG9CQUFvQixHQUFHL0MsK0NBQVFBLENBQVM7SUFDakUsTUFBTSxDQUFDZ0QsY0FBY0MsZ0JBQWdCLEdBQUdqRCwrQ0FBUUEsQ0FBZ0I7SUFDaEUsTUFBTSxDQUFDa0QsZ0JBQWdCQyxrQkFBa0IsR0FBR25ELCtDQUFRQSxDQUFjO0lBQ2xFLE1BQU1vRCxlQUFlbkQsNkNBQU1BLENBQW1CO0lBQzlDLE1BQU1vRCxtQkFBbUJwRCw2Q0FBTUEsQ0FBbUMsQ0FBQztJQUVuRSxnRUFBZ0U7SUFDaEUsTUFBTXFELGlCQUFpQjtRQUNyQixJQUFJVixpQkFBaUIsYUFBYWpCLE1BQU00QixZQUFZLEtBQUssU0FBUztZQUNoRSxPQUFPO1FBQ1QsT0FBTyxJQUFJNUIsTUFBTTZCLFNBQVMsS0FBSyxTQUFTO1lBQ3RDLE9BQU87UUFDVCxPQUFPO1lBQ0wsT0FBTztRQUNUO0lBQ0Y7SUFFQSxvREFBb0Q7SUFDcEQsTUFBTSxDQUFDQyxpQkFBaUJDLHdCQUF3QixHQUFHMUQsK0NBQVFBLENBQXNCO1FBQy9FMkQsU0FBUztRQUNUQyxhQUFhO1FBQ2JDLGNBQWM7UUFDZEMsT0FBTztRQUNQQyxXQUFXO1FBQ1hDLFFBQVE7SUFDVjtJQUVBLGtEQUFrRDtJQUNsRDlELGdEQUFTQTttQ0FBQztZQUNSLElBQUl1QixhQUFhO2dCQUNmd0MsUUFBUUMsR0FBRyxDQUFDLHlDQUF5Q3pDO2dCQUNyRDBDLG1CQUFtQjFDO1lBQ3JCO1FBQ0Y7a0NBQUcsRUFBRTtJQUVMLGtEQUFrRDtJQUNsRCxNQUFNLENBQUMyQyxtQkFBbUJDLHFCQUFxQixHQUFHckUsK0NBQVFBLENBQW1DO1FBQzNGc0UsT0FBTztZQUFFQyxNQUFNO1lBQUlDLGNBQWM7UUFBRztRQUNwQ0MsY0FBYztZQUFFRixNQUFNO1lBQUlDLGNBQWM7UUFBRztRQUMzQ0UsY0FBYztZQUFFSCxNQUFNO1lBQUlDLGNBQWM7UUFBRztRQUMzQ0csYUFBYTtZQUFFSixNQUFNO1lBQUlDLGNBQWM7UUFBRztRQUMxQ0ksU0FBUztZQUFFTCxNQUFNO1lBQUlDLGNBQWM7UUFBRztJQUN4QztJQUVBLGdDQUFnQztJQUNoQyxNQUFNLEVBQUVLLFFBQVFDLFdBQVcsRUFBRUMsV0FBV0MsV0FBVyxFQUFFLEdBQUdoRSxnRUFBY0E7SUFFdEUsOEJBQThCO0lBQzlCLE1BQU1tRCxxQkFBcUIsQ0FBQ2M7UUFDMUIscUNBQXFDO1FBQ3JDeEM7UUFDQVEsZ0JBQWdCO1FBQ2hCRSxrQkFBa0I7UUFFbEIsd0JBQXdCO1FBQ3hCTixnQkFBZ0JvQztRQUNoQmhCLFFBQVFDLEdBQUcsQ0FBQyxrQkFBa0JlO1FBRTlCLElBQUk7WUFBQztZQUFXO1lBQVk7WUFBVTtZQUFVO1NBQVUsQ0FBQ0MsUUFBUSxDQUFDRCxPQUFPO1lBQ3pFLGdGQUFnRjtZQUNoRixJQUFJQSxTQUFTLFVBQVU7Z0JBQ3JCaEIsUUFBUUMsR0FBRyxDQUFDLG9DQUFvQ2U7Z0JBQ2hEcEQsYUFBYTtnQkFDYkUsZ0JBQWdCO2dCQUNoQixzQ0FBc0M7Z0JBQ3RDWSxTQUFTO1lBQ1gsT0FBTyxJQUFJc0MsU0FBUyxXQUFXO2dCQUM3QmhCLFFBQVFDLEdBQUcsQ0FBQztnQkFDWixzRkFBc0Y7Z0JBQ3RGbkMsZ0JBQWdCO2dCQUNoQixzRUFBc0U7Z0JBQ3RFa0MsUUFBUUMsR0FBRyxDQUFDO2dCQUNaaUI7Z0JBQ0EsUUFBUSw2Q0FBNkM7WUFDdkQsT0FBTztnQkFDTHRELGFBQWE7Z0JBQ2JFLGdCQUFnQnFELGdDQUFnQ0g7Z0JBQ2hELHNDQUFzQztnQkFDdEN0QyxTQUFTO1lBQ1g7UUFDRixPQUFPLElBQUlzQyxTQUFTLFNBQVM7WUFDM0IsdUNBQXVDO1lBQ3ZDaEIsUUFBUUMsR0FBRyxDQUFDLG9DQUFvQ2U7WUFDaERwRCxhQUFhO1lBQ2JFLGdCQUFnQjtZQUNoQixxREFBcUQ7WUFDckRzRDtRQUNGO0lBQ0Y7SUFFQSw2RUFBNkU7SUFDN0UsTUFBTUQsa0NBQWtDLENBQUNIO1FBQ3ZDLHdEQUF3RDtRQUN4RCxPQUFRQTtZQUNOLGNBQWM7WUFDZCxLQUFLO2dCQUNILE9BQU8sU0FBVSxzQ0FBc0M7WUFDekQsS0FBSztnQkFDSCxPQUFPLFFBQVUsNENBQTRDO1lBRS9ELGNBQWM7WUFDZCxLQUFLO2dCQUNILE9BQU8sU0FBVSwwQkFBMEI7WUFDN0MsS0FBSztnQkFDSCxPQUFPLFdBQVksNEJBQTRCO1lBQ2pELEtBQUs7Z0JBQ0gsT0FBTyxTQUFVLDBCQUEwQjtZQUU3QyxtQkFBbUI7WUFDbkI7Z0JBQ0UsT0FBT0EsU0FBUyxZQUFZLFVBQVUsUUFBUyx3QkFBd0I7UUFDM0U7SUFDRjtJQUVBLHVEQUF1RDtJQUN2RCxNQUFNSyx5QkFBeUIsQ0FBQ0M7UUFDOUIsNERBQTREO1FBQzVELGdEQUFnRDtRQUNoRCxNQUFNQyxjQUFjNUM7UUFDcEIsTUFBTTZDLG1CQUFtQjlELE1BQU02QixTQUFTO1FBQ3hDZjtRQUNBSSxnQkFBZ0IyQztRQUNoQjNELGFBQWE0RDtRQUNieEMsZ0JBQWdCO1FBQ2hCRSxrQkFBa0I7UUFFbEIsMkJBQTJCO1FBQzNCSixvQkFBb0J3QztRQUVwQixtREFBbUQ7UUFDbkQsSUFBSWhDO1FBQ0osSUFBSWlDLGdCQUFnQixVQUFVO1lBQzVCLHFEQUFxRDtZQUNyRGpDLGVBQWU7WUFDZlUsUUFBUUMsR0FBRyxDQUFFO1FBQ2YsT0FBTztZQUNMLHlEQUF5RDtZQUN6RFgsZUFBZTZCLGdDQUFnQ3hDO1lBQy9DcUIsUUFBUUMsR0FBRyxDQUFDLHdDQUErRXRCLE9BQXZDVyxjQUFhLDRCQUF1QyxPQUFiWDtZQUMzRnFCLFFBQVFDLEdBQUcsQ0FBRTtRQUNmO1FBRUFELFFBQVFDLEdBQUcsQ0FBQyx1Q0FBdUNxQjtRQUNuRHRCLFFBQVFDLEdBQUcsQ0FBQyw4Q0FBOENYO1FBRTFELHVDQUF1QztRQUN2Q3hCLGdCQUFnQndCO1FBRWhCLDhEQUE4RDtRQUM5RCxJQUFJbUMsdUJBQXVCO1FBRTNCLElBQUlILGFBQWEscUJBQXFCO1lBQ3BDRyx1QkFBdUI7UUFDekIsT0FBTyxJQUFJSCxhQUFhLGdCQUFnQjtZQUN0Q0csdUJBQXVCO1FBQ3pCO1FBRUEsMkNBQTJDO1FBQzNDLElBQUksQ0FBQ0Esc0JBQXNCO1lBQ3pCekIsUUFBUTBCLEtBQUssQ0FBQyxvQ0FBb0NKO1lBQ2xESyxNQUFNO1lBQ047UUFDRjtRQUVBLG9EQUFvRDtRQUNwRDNCLFFBQVFDLEdBQUcsQ0FBQywrQ0FBK0N3QjtRQUMzRHhELGVBQWUsa0JBQWtCd0Q7UUFFakMsdUJBQXVCO1FBQ3ZCekIsUUFBUUMsR0FBRyxDQUFDLHVDQUF1Q3FCO1FBQ25EdEIsUUFBUUMsR0FBRyxDQUFDLG1EQUFtRHdCO1FBQy9EekIsUUFBUUMsR0FBRyxDQUFDLDBDQUEwQ1g7UUFFdEQsb0RBQW9EO1FBQ3BELElBQUlpQyxnQkFBZ0IsVUFBVTtZQUM1QixvREFBb0Q7WUFDcERIO1FBQ0YsT0FBTztZQUNMLG1EQUFtRDtZQUNuREY7UUFDRjtJQUNGO0lBRUEsMEJBQTBCO0lBQzFCLE1BQU1VLHdCQUF3QjtRQUM1Qiw4QkFBOEI7UUFDOUIsTUFBTUMsUUFBUUMsU0FBU0MsYUFBYSxDQUFDO1FBQ3JDRixNQUFNYixJQUFJLEdBQUc7UUFDYmEsTUFBTUcsTUFBTSxHQUFHO1FBRWYsd0JBQXdCO1FBQ3hCSCxNQUFNSSxRQUFRLEdBQUcsQ0FBQ0M7WUFDaEIsTUFBTUMsUUFBUSxFQUFHQyxNQUFNLENBQXNCRCxLQUFLO1lBQ2xELElBQUlBLFNBQVNBLE1BQU1FLE1BQU0sR0FBRyxHQUFHO2dCQUM3QixNQUFNQyxPQUFPSCxLQUFLLENBQUMsRUFBRTtnQkFFckIsc0JBQXNCO2dCQUN0QmpELGtCQUFrQm9EO2dCQUNsQmxFLGFBQWFrRTtnQkFFYnRDLFFBQVFDLEdBQUcsQ0FBQyx1QkFBdUJxQyxLQUFLaEMsSUFBSTtnQkFFNUMsMkJBQTJCO2dCQUMzQixNQUFNaUMsU0FBUyxJQUFJQztnQkFDbkJELE9BQU9FLE1BQU0sR0FBRyxDQUFDUDt3QkFDWEE7b0JBQUosS0FBSUEsWUFBQUEsRUFBRUUsTUFBTSxjQUFSRixnQ0FBQUEsVUFBVVEsTUFBTSxFQUFFO3dCQUNwQixtREFBbUQ7d0JBQ25EMUMsUUFBUUMsR0FBRyxDQUFDO29CQUNkO2dCQUNGO2dCQUNBc0MsT0FBT0ksYUFBYSxDQUFDTDtZQUN2QjtRQUNGO1FBRUEsMEJBQTBCO1FBQzFCVCxNQUFNZSxLQUFLO0lBQ2I7SUFFQSxnREFBZ0Q7SUFDaEQsTUFBTUMseUJBQXlCLENBQUN2QjtRQUM5QixPQUFRQTtZQUNOLEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNUO2dCQUNFLE9BQU9BLFNBQVN3QixNQUFNLENBQUMsR0FBR0MsV0FBVyxLQUFLekIsU0FBUzBCLEtBQUssQ0FBQztRQUM3RDtJQUNGO0lBRUEsNkNBQTZDO0lBQzdDLE1BQU1DLHlCQUF5QixDQUFDQztRQUM5QiwrRUFBK0U7UUFDL0UsSUFBSUEsWUFBWSxJQUFJO1lBQ2xCLE9BQU8sU0FBUyx5RUFBeUU7UUFDM0YsT0FBTyxJQUFJQSxZQUFZLElBQUk7WUFDekIsT0FBTyxTQUFTLHFDQUFxQztRQUN2RCxPQUFPLElBQUlBLFlBQVksS0FBSztZQUMxQixPQUFPLFdBQVcsb0NBQW9DO1FBQ3hELE9BQU87WUFDTCxPQUFPLFNBQVMsK0JBQStCO1FBQ2pEO0lBQ0Y7SUFFQSw0RUFBNEU7SUFDNUUsTUFBTTlCLG9CQUFvQjtRQUN4QnBCLFFBQVFDLEdBQUcsQ0FBQztRQUVaLHNEQUFzRDtRQUN0RCxNQUFNNEIsUUFBUUMsU0FBU0MsYUFBYSxDQUFDO1FBQ3JDRixNQUFNYixJQUFJLEdBQUc7UUFFYix3QkFBd0I7UUFDeEJhLE1BQU1zQixLQUFLLEdBQUc7UUFFZCwwREFBMEQ7UUFDMUR0QixNQUFNRyxNQUFNLEdBQUc7UUFFZiwrQ0FBK0M7UUFDL0NILE1BQU1JLFFBQVEsR0FBRyxPQUFPQztZQUN0QixNQUFNQyxRQUFRLEVBQUdDLE1BQU0sQ0FBc0JELEtBQUs7WUFDbEQsSUFBSSxDQUFDQSxTQUFTQSxNQUFNRSxNQUFNLEtBQUssR0FBRztZQUVsQyxNQUFNQyxPQUFPSCxLQUFLLENBQUMsRUFBRTtZQUNyQm5DLFFBQVFDLEdBQUcsQ0FBQyx3QkFBd0JxQyxLQUFLaEMsSUFBSSxFQUFFZ0MsS0FBS3RCLElBQUksRUFBRXNCLEtBQUtjLElBQUk7WUFFbkUsNENBQTRDO1lBQzVDLE1BQU1DLGtCQUFrQjtnQkFBQztnQkFBYztnQkFBYTtnQkFBYTthQUFhO1lBRTlFLElBQUksQ0FBQ0EsZ0JBQWdCcEMsUUFBUSxDQUFDcUIsS0FBS3RCLElBQUksR0FBRztnQkFDeENoQixRQUFRMEIsS0FBSyxDQUFDLGlDQUFpQ1ksS0FBS3RCLElBQUk7Z0JBQ3hEVyxNQUFNO2dCQUNOO1lBQ0Y7WUFFQSwyREFBMkQ7WUFDM0QsSUFBSVcsS0FBS3RCLElBQUksQ0FBQ3NDLFVBQVUsQ0FBQyxXQUFXO2dCQUNsQ3RELFFBQVEwQixLQUFLLENBQUM7Z0JBQ2RDLE1BQU07Z0JBQ047WUFDRjtZQUVBLCtEQUErRDtZQUMvRCx3Q0FBd0M7WUFDeEMvRCxhQUFhO1lBQ2JFLGdCQUFnQjtZQUVoQixpQ0FBaUM7WUFDakNILFFBQVEyRTtZQUNSdEMsUUFBUUMsR0FBRyxDQUFDLDRCQUE0QnFDLEtBQUtoQyxJQUFJO1lBRWpELDhEQUE4RDtZQUM5RCxNQUFNaUQsY0FBY2pCO1lBRXBCLG1FQUFtRTtZQUNuRWtCLFdBQVc7Z0JBQ1Qsb0NBQW9DO2dCQUNwQyxJQUFJLENBQUM5RixNQUFNNEUsSUFBSSxFQUFFO29CQUNmdEMsUUFBUUMsR0FBRyxDQUFDO29CQUNaLDZCQUE2QjtvQkFDN0J0QyxRQUFRNEY7b0JBRVIsZ0RBQWdEO29CQUNoREMsV0FBVzt3QkFDVCxJQUFJLENBQUM5RixNQUFNNEUsSUFBSSxFQUFFOzRCQUNmdEMsUUFBUUMsR0FBRyxDQUFDOzRCQUNadEMsUUFBUTRGO3dCQUNWLE9BQU87NEJBQ0x2RCxRQUFRQyxHQUFHLENBQUMsaURBQWlEdkMsTUFBTTRFLElBQUksQ0FBQ2hDLElBQUk7d0JBQzlFO3dCQUNBLG9HQUFvRzt3QkFDcEcsSUFBSTNCLGlCQUFpQixhQUFhakIsTUFBTTRCLFlBQVksS0FBSyxTQUFTOzRCQUNoRVUsUUFBUUMsR0FBRyxDQUFDOzRCQUNadkIsU0FBUzt3QkFDWCxPQUFPOzRCQUNMc0IsUUFBUUMsR0FBRyxDQUFDOzRCQUNadkIsU0FBUzt3QkFDWDtvQkFDRixHQUFHO2dCQUNMLE9BQU87b0JBQ0xzQixRQUFRQyxHQUFHLENBQUMsNEJBQTRCdkMsTUFBTTRFLElBQUksQ0FBQ2hDLElBQUk7b0JBQ3ZELG9HQUFvRztvQkFDcEcsSUFBSTNCLGlCQUFpQixhQUFhakIsTUFBTTRCLFlBQVksS0FBSyxTQUFTO3dCQUNoRVUsUUFBUUMsR0FBRyxDQUFDO3dCQUNadkIsU0FBUztvQkFDWCxPQUFPO3dCQUNMc0IsUUFBUUMsR0FBRyxDQUFDO3dCQUNadkIsU0FBUztvQkFDWDtnQkFDRjtZQUNGLEdBQUc7WUFFSCx1QkFBdUI7WUFDdkIsTUFBTTZELFNBQVMsSUFBSUM7WUFDbkJELE9BQU9FLE1BQU0sR0FBRyxDQUFDUDtvQkFDWEE7Z0JBQUosS0FBSUEsWUFBQUEsRUFBRUUsTUFBTSxjQUFSRixnQ0FBQUEsVUFBVVEsTUFBTSxFQUFFO29CQUNwQjFELGdCQUFnQmtELEVBQUVFLE1BQU0sQ0FBQ00sTUFBTTtvQkFDL0IxQyxRQUFRQyxHQUFHLENBQUM7Z0JBQ2Q7WUFDRjtZQUNBc0MsT0FBT0ksYUFBYSxDQUFDTDtRQUVyQixnRkFBZ0Y7UUFDbEY7UUFFQSwwQkFBMEI7UUFDMUJULE1BQU1lLEtBQUs7SUFDYjtJQUVBLGtGQUFrRjtJQUNsRix1RUFBdUU7SUFFdkUsb0NBQW9DO0lBQ3BDLE1BQU0xQixtQkFBbUIsT0FBT0k7UUFDOUJ0QixRQUFRQyxHQUFHLENBQUMsMENBQTBDcUIsWUFBWTtRQUVsRSw4QkFBOEI7UUFDOUIsTUFBTU8sUUFBUUMsU0FBU0MsYUFBYSxDQUFDO1FBQ3JDRixNQUFNYixJQUFJLEdBQUc7UUFFYixzR0FBc0c7UUFDdEdhLE1BQU1zQixLQUFLLEdBQUc7UUFFZCxJQUFJeEUsaUJBQWlCLFdBQVc7WUFDOUJrRCxNQUFNRyxNQUFNLEdBQUc7UUFDakIsT0FBTztZQUNMSCxNQUFNRyxNQUFNLEdBQUdyRCxpQkFBaUIsV0FBV0EsaUJBQWlCLFdBQ3hELDRDQUE0QyxtQ0FBbUM7ZUFDL0U7UUFDTjtRQUVBLHdCQUF3QjtRQUN4QmtELE1BQU1JLFFBQVEsR0FBRyxPQUFPQztZQUN0QixNQUFNQyxRQUFRLEVBQUdDLE1BQU0sQ0FBc0JELEtBQUs7WUFDbEQsSUFBSSxDQUFDQSxTQUFTQSxNQUFNRSxNQUFNLEtBQUssR0FBRztZQUVsQyxNQUFNQyxPQUFPSCxLQUFLLENBQUMsRUFBRTtZQUNyQm5DLFFBQVFDLEdBQUcsQ0FBQyxrQkFBa0JxQyxLQUFLaEMsSUFBSSxFQUFFZ0MsS0FBS3RCLElBQUksRUFBRXNCLEtBQUtjLElBQUk7WUFFN0QsOERBQThEO1lBQzlELElBQUl6RSxpQkFBaUIsV0FBV0EsaUJBQWlCLFVBQVU7Z0JBQ3pELE1BQU0wRSxrQkFBa0I7b0JBQUM7b0JBQWM7b0JBQWE7b0JBQWE7aUJBQWE7Z0JBRTlFLHFEQUFxRDtnQkFDckQsSUFBSWYsS0FBS3RCLElBQUksQ0FBQ3NDLFVBQVUsQ0FBQyxhQUFhLENBQUNELGdCQUFnQnBDLFFBQVEsQ0FBQ3FCLEtBQUt0QixJQUFJLEdBQUc7b0JBQzFFaEIsUUFBUTBCLEtBQUssQ0FBQyxpQ0FBaUNZLEtBQUt0QixJQUFJO29CQUN4RG5FLGtFQUFjQSxDQUFDLHFCQUFxQjtvQkFDcEM7Z0JBQ0Y7WUFDRjtZQUVBLHVEQUF1RDtZQUN2RDJCO1lBRUEsNEJBQTRCO1lBQzVCYixRQUFRMkU7WUFDUnRDLFFBQVFDLEdBQUcsQ0FBQyxzQkFBc0JxQyxLQUFLaEMsSUFBSTtZQUUzQyxrREFBa0Q7WUFDbEQsa0VBQWtFO1lBQ2xFLElBQUlnQyxLQUFLdEIsSUFBSSxDQUFDc0MsVUFBVSxDQUFDLFdBQVc7Z0JBQ2xDLHdEQUF3RDtnQkFDeEQsSUFBSTNFLGlCQUFpQixXQUFXQSxpQkFBaUIsVUFBVTtvQkFDekRxQixRQUFRMEIsS0FBSyxDQUFDO29CQUNkN0Usa0VBQWNBLENBQUMscUJBQXFCO29CQUNwQzJCO29CQUNBO2dCQUNGO2dCQUNBLElBQUk7b0JBQ0YsTUFBTTBFLFdBQVcsTUFBTXhHLHFFQUFnQkEsQ0FBQzRGO29CQUN4Q3RDLFFBQVFDLEdBQUcsQ0FBQyw4QkFBOEJpRDtvQkFDMUMzRSxZQUFZMkU7b0JBRVosOEVBQThFO29CQUM5RSxJQUFJdkUsaUJBQWlCLFdBQVc7d0JBQzlCcUIsUUFBUUMsR0FBRyxDQUFDO3dCQUNackMsYUFBYTt3QkFFYiwwREFBMEQ7d0JBQzFELElBQUlzRixXQUFXLElBQUk7NEJBQ2pCbEQsUUFBUUMsR0FBRyxDQUFDLDJCQUFvQyxPQUFUaUQsVUFBUzs0QkFFaEQsdURBQXVEOzRCQUN2RHRHLG9FQUFnQkEsQ0FDZCwwQkFDQSw2REFBa0YsT0FBckJJLFdBQVdrRyxXQUFVOzRCQUdwRix1RUFBdUU7NEJBQ3ZFLE1BQU0zQixjQUFjNUM7NEJBQ3BCLE1BQU04RSxrQkFBa0I1RTs0QkFFeEIsaURBQWlEOzRCQUNqREgsU0FBUzs0QkFFVCw4QkFBOEI7NEJBQzlCOEUsV0FBVztnQ0FDVGhGO2dDQUNBSSxnQkFBZ0IyQztnQ0FDaEJ6QyxvQkFBb0IyRTtnQ0FDcEJ6RCxRQUFRQyxHQUFHLENBQUM7NEJBQ2QsR0FBRzs0QkFFSCw2Q0FBNkM7NEJBQzdDO3dCQUNGO3dCQUVBRCxRQUFRQyxHQUFHLENBQUMsaUNBQTBDLE9BQVRpRCxVQUFTO3dCQUN0RCwwREFBMEQ7d0JBQzFEbEQsUUFBUUMsR0FBRyxDQUFDO3dCQUNabkMsZ0JBQWdCO29CQUNsQjtvQkFFQSxpRUFBaUU7b0JBQ2pFLElBQUlhLGdCQUFnQjt3QkFBQzt3QkFBVzt3QkFBWTtxQkFBUyxDQUFDc0MsUUFBUSxDQUFDdEMsZUFBZTt3QkFDNUUsTUFBTVcsZUFBZTZCLGdDQUFnQ3hDO3dCQUNyRCxNQUFNK0UsbUJBQW1CL0csMEVBQXFCQSxDQUFDdUcsVUFBVTVEO3dCQUV6RCxJQUFJLENBQUNvRSxpQkFBaUJDLE9BQU8sRUFBRTs0QkFDN0IsOERBQThEOzRCQUM5RCxJQUFJRCxpQkFBaUJFLGlCQUFpQixFQUFFO2dDQUN0QyxtRkFBbUY7Z0NBQ25GNUQsUUFBUUMsR0FBRyxDQUFDLHNDQUFrRnlELE9BQTVDcEUsY0FBYSxpQ0FBa0UsT0FBbkNvRSxpQkFBaUJFLGlCQUFpQjtnQ0FDaEloSCxvRUFBZ0JBLENBQ2QseUJBQ0Esa0NBQTZHaUcsT0FBM0VBLHVCQUF1QnZELGVBQWMsd0NBQWlHLE9BQTNEdUQsdUJBQXVCYSxpQkFBaUJFLGlCQUFpQixHQUFFO2dDQUcxSyxtQ0FBbUM7Z0NBQ25DNUQsUUFBUUMsR0FBRyxDQUFDLG9DQUF1RSxPQUFuQ3lELGlCQUFpQkUsaUJBQWlCO2dDQUVsRiw0Q0FBNEM7Z0NBQzVDLE1BQU1MLGNBQWNqQjtnQ0FFcEIsMkJBQTJCO2dDQUMzQnhFLGdCQUFnQjRGLGlCQUFpQkUsaUJBQWlCO2dDQUVsRCxxREFBcUQ7Z0NBQ3JELHVEQUF1RDtnQ0FDdkQsSUFBSUYsaUJBQWlCRSxpQkFBaUIsS0FBSyxTQUFTO29DQUNsRGhGLGdCQUFnQjtnQ0FDbEIsT0FBTyxJQUFJOEUsaUJBQWlCRSxpQkFBaUIsS0FBSyxXQUFXO29DQUMzRGhGLGdCQUFnQjtnQ0FDbEIsT0FBTyxJQUFJOEUsaUJBQWlCRSxpQkFBaUIsS0FBSyxTQUFTO29DQUN6RGhGLGdCQUFnQjtnQ0FDbEI7Z0NBQ0Esa0RBQWtEO2dDQUVsRCwrQ0FBK0M7Z0NBQy9DNEUsV0FBVztvQ0FDVCxJQUFJLENBQUM5RixNQUFNNEUsSUFBSSxFQUFFO3dDQUNmdEMsUUFBUUMsR0FBRyxDQUFDLDBDQUEwQ3NELFlBQVlqRCxJQUFJO3dDQUN0RTNDLFFBQVE0RjtvQ0FDVjtnQ0FDRixHQUFHOzRCQUNMLE9BQU87Z0NBQ0wsNkNBQTZDO2dDQUM3QzFHLGtFQUFjQSxDQUFDLHdCQUF3QjZHLGlCQUFpQmhDLEtBQUssSUFBSTs0QkFDbkU7d0JBQ0YsT0FBTyxJQUFJZ0MsaUJBQWlCRSxpQkFBaUIsSUFBSUYsaUJBQWlCRSxpQkFBaUIsS0FBS3RFLGNBQWM7NEJBQ3BHLG9FQUFvRTs0QkFDcEUsbUdBQW1HOzRCQUNuRywwREFBMEQ7NEJBQzFELE1BQU11RSxnQkFBZ0IsTUFBTS9HLDZEQUFTQSxDQUFDO2dDQUNwQ2dILE9BQU87Z0NBQ1BDLFNBQVMsR0FBMEIsT0FBdkJMLGlCQUFpQmhDLEtBQUssRUFBQztnQ0FDbkNWLE1BQU07Z0NBQ05nRCxhQUFhO2dDQUNiQyxZQUFZO2dDQUNaQyxXQUFXLEtBQVE7NEJBQ3JCOzRCQUVBLElBQUlMLGVBQWU7Z0NBQ2pCLG1DQUFtQztnQ0FDbkM3RCxRQUFRQyxHQUFHLENBQUMsb0NBQXVFLE9BQW5DeUQsaUJBQWlCRSxpQkFBaUI7Z0NBRWxGLDRDQUE0QztnQ0FDNUMsTUFBTUwsY0FBY2pCO2dDQUVwQiwyQkFBMkI7Z0NBQzNCeEUsZ0JBQWdCNEYsaUJBQWlCRSxpQkFBaUI7Z0NBRWxELHFEQUFxRDtnQ0FDckQsdURBQXVEO2dDQUN2RCxJQUFJRixpQkFBaUJFLGlCQUFpQixLQUFLLFNBQVM7b0NBQ2xEaEYsZ0JBQWdCO2dDQUNsQixPQUFPLElBQUk4RSxpQkFBaUJFLGlCQUFpQixLQUFLLFdBQVc7b0NBQzNEaEYsZ0JBQWdCO2dDQUNsQixPQUFPLElBQUk4RSxpQkFBaUJFLGlCQUFpQixLQUFLLFNBQVM7b0NBQ3pEaEYsZ0JBQWdCO2dDQUNsQjtnQ0FDQSxrREFBa0Q7Z0NBRWxELCtDQUErQztnQ0FDL0M0RSxXQUFXO29DQUNULElBQUksQ0FBQzlGLE1BQU00RSxJQUFJLEVBQUU7d0NBQ2Z0QyxRQUFRQyxHQUFHLENBQUMsMENBQTBDc0QsWUFBWWpELElBQUk7d0NBQ3RFM0MsUUFBUTRGO29DQUNWO2dDQUNGLEdBQUc7NEJBQ0w7d0JBQ0Y7b0JBQ0Y7b0JBRUEsOENBQThDO29CQUM5Q3ZELFFBQVFDLEdBQUcsQ0FBQztvQkFFWix1Q0FBdUM7b0JBQ3ZDLE1BQU1zRCxjQUFjakI7b0JBRXBCLG1FQUFtRTtvQkFDbkUsSUFBSTVFLE1BQU00RSxJQUFJLEVBQUU7d0JBQ2R0QyxRQUFRQyxHQUFHLENBQUMsZ0RBQWdEdkMsTUFBTTRFLElBQUksQ0FBQ2hDLElBQUk7d0JBQzNFNUIsU0FBUztvQkFDWCxPQUFPO3dCQUNMc0IsUUFBUUMsR0FBRyxDQUFDO3dCQUNaLDZCQUE2Qjt3QkFDN0J0QyxRQUFRNEY7d0JBQ1IsbURBQW1EO3dCQUNuREMsV0FBVzs0QkFDVCxxQkFBcUI7NEJBQ3JCLElBQUksQ0FBQzlGLE1BQU00RSxJQUFJLEVBQUU7Z0NBQ2Z0QyxRQUFRQyxHQUFHLENBQUM7Z0NBQ1p0QyxRQUFRNEY7NEJBQ1Y7NEJBQ0F2RCxRQUFRQyxHQUFHLENBQUM7NEJBQ1p2QixTQUFTO3dCQUNYLEdBQUc7b0JBQ0w7Z0JBQ0YsRUFBRSxPQUFPZ0QsT0FBTztvQkFDZDFCLFFBQVEwQixLQUFLLENBQUMscUNBQXFDQTtvQkFFbkQsNERBQTREO29CQUM1RCx1RkFBdUY7b0JBQ3ZGLElBQUkvQyxpQkFBaUIsV0FBVzt3QkFDOUI5QixrRUFBY0EsQ0FBQyxlQUFlO3dCQUM5QjJCO3dCQUNBO29CQUNGO29CQUNBd0IsUUFBUUMsR0FBRyxDQUFDO29CQUVaLHVDQUF1QztvQkFDdkMsTUFBTXNELGNBQWNqQjtvQkFFcEIsbUVBQW1FO29CQUNuRSxJQUFJNUUsTUFBTTRFLElBQUksRUFBRTt3QkFDZHRDLFFBQVFDLEdBQUcsQ0FBQyw2REFBNkR2QyxNQUFNNEUsSUFBSSxDQUFDaEMsSUFBSTt3QkFDeEY1QixTQUFTO29CQUNYLE9BQU87d0JBQ0xzQixRQUFRQyxHQUFHLENBQUM7d0JBQ1osNkJBQTZCO3dCQUM3QnRDLFFBQVE0Rjt3QkFDUixtREFBbUQ7d0JBQ25EQyxXQUFXOzRCQUNULHFCQUFxQjs0QkFDckIsSUFBSSxDQUFDOUYsTUFBTTRFLElBQUksRUFBRTtnQ0FDZnRDLFFBQVFDLEdBQUcsQ0FBQztnQ0FDWnRDLFFBQVE0Rjs0QkFDVjs0QkFDQXZELFFBQVFDLEdBQUcsQ0FBQzs0QkFDWnZCLFNBQVM7d0JBQ1gsR0FBRztvQkFDTDtnQkFDRjtZQUNGLE9BQU87Z0JBQ0wsK0JBQStCO2dCQUMvQixJQUFJQyxpQkFBaUIsV0FBVztvQkFDOUIsb0VBQW9FO29CQUNwRSxJQUFJMkQsS0FBS3RCLElBQUksQ0FBQ3NDLFVBQVUsQ0FBQyxXQUFXO3dCQUNsQ3RELFFBQVFDLEdBQUcsQ0FBQzt3QkFDWnJDLGFBQWE7d0JBQ2IsaUVBQWlFO3dCQUNqRUUsZ0JBQWdCO3dCQUVoQiw4REFBOEQ7d0JBQzlELE1BQU15RixjQUFjakI7d0JBRXBCLG1FQUFtRTt3QkFDbkVrQixXQUFXOzRCQUNULG9DQUFvQzs0QkFDcEMsSUFBSSxDQUFDOUYsTUFBTTRFLElBQUksRUFBRTtnQ0FDZnRDLFFBQVFDLEdBQUcsQ0FBQztnQ0FDWiw2QkFBNkI7Z0NBQzdCdEMsUUFBUTRGOzRCQUNWLE9BQU87Z0NBQ0x2RCxRQUFRQyxHQUFHLENBQUMscUNBQXFDdkMsTUFBTTRFLElBQUksQ0FBQ2hDLElBQUk7NEJBQ2xFO3dCQUNGLEdBQUc7b0JBQ0wsT0FBTzt3QkFDTE4sUUFBUUMsR0FBRyxDQUFDO3dCQUNacEQsa0VBQWNBLENBQUMscUJBQXFCO3dCQUVwQyx1RUFBdUU7d0JBQ3ZFLE1BQU0wRSxjQUFjNUM7d0JBQ3BCLE1BQU04RSxrQkFBa0I1RTt3QkFFeEIsaURBQWlEO3dCQUNqREgsU0FBUzt3QkFFVCw4QkFBOEI7d0JBQzlCOEUsV0FBVzs0QkFDVGhGOzRCQUNBSSxnQkFBZ0IyQzs0QkFDaEJ6QyxvQkFBb0IyRTs0QkFDcEJ6RCxRQUFRQyxHQUFHLENBQUM7d0JBQ2QsR0FBRzt3QkFDSDtvQkFDRjtnQkFDRjtnQkFFQSxxQ0FBcUM7Z0JBQ3JDLE1BQU1zQyxTQUFTLElBQUlDO2dCQUNuQkQsT0FBT0UsTUFBTSxHQUFHLENBQUNQO3dCQUNYQTtvQkFBSixLQUFJQSxZQUFBQSxFQUFFRSxNQUFNLGNBQVJGLGdDQUFBQSxVQUFVUSxNQUFNLEVBQUU7d0JBQ3BCMUQsZ0JBQWdCa0QsRUFBRUUsTUFBTSxDQUFDTSxNQUFNO3dCQUMvQjFDLFFBQVFDLEdBQUcsQ0FBQywrQkFBK0JxQyxLQUFLaEMsSUFBSTtvQkFDdEQ7Z0JBQ0Y7Z0JBQ0FpQyxPQUFPSSxhQUFhLENBQUNMO2dCQUVyQiw4REFBOEQ7Z0JBQzlELE1BQU1pQixjQUFjakI7Z0JBRXBCLG1FQUFtRTtnQkFDbkVrQixXQUFXO29CQUNULG9DQUFvQztvQkFDcEMsSUFBSSxDQUFDOUYsTUFBTTRFLElBQUksRUFBRTt3QkFDZnRDLFFBQVFDLEdBQUcsQ0FBQzt3QkFDWiw2QkFBNkI7d0JBQzdCdEMsUUFBUTRGO3dCQUVSLGdEQUFnRDt3QkFDaERDLFdBQVc7NEJBQ1QsSUFBSSxDQUFDOUYsTUFBTTRFLElBQUksRUFBRTtnQ0FDZnRDLFFBQVFDLEdBQUcsQ0FBQztnQ0FDWnRDLFFBQVE0Rjs0QkFDVixPQUFPO2dDQUNMdkQsUUFBUUMsR0FBRyxDQUFDLGlEQUFpRHZDLE1BQU00RSxJQUFJLENBQUNoQyxJQUFJOzRCQUM5RTs0QkFDQSxvR0FBb0c7NEJBQ3BHLElBQUkzQixpQkFBaUIsYUFBYWpCLE1BQU00QixZQUFZLEtBQUssU0FBUztnQ0FDaEVVLFFBQVFDLEdBQUcsQ0FBQztnQ0FDWnZCLFNBQVM7NEJBQ1gsT0FBTztnQ0FDTHNCLFFBQVFDLEdBQUcsQ0FBQztnQ0FDWnZCLFNBQVM7NEJBQ1g7d0JBQ0YsR0FBRztvQkFDTCxPQUFPO3dCQUNMc0IsUUFBUUMsR0FBRyxDQUFDLDRCQUE0QnZDLE1BQU00RSxJQUFJLENBQUNoQyxJQUFJO3dCQUN2RCxvR0FBb0c7d0JBQ3BHLElBQUkzQixpQkFBaUIsYUFBYWpCLE1BQU00QixZQUFZLEtBQUssU0FBUzs0QkFDaEVVLFFBQVFDLEdBQUcsQ0FBQzs0QkFDWnZCLFNBQVM7d0JBQ1gsT0FBTzs0QkFDTHNCLFFBQVFDLEdBQUcsQ0FBQzs0QkFDWnZCLFNBQVM7d0JBQ1g7b0JBQ0Y7Z0JBQ0YsR0FBRztZQUNMO1FBQ0Y7UUFFQSwwQkFBMEI7UUFDMUJtRCxNQUFNZSxLQUFLO0lBQ2I7SUFFQSxvQ0FBb0M7SUFDcEMsTUFBTXVCLGlDQUFpQyxDQUFDQztRQUN0Q3BFLFFBQVFDLEdBQUcsQ0FBQywrQkFBK0JtRTtRQUUzQyxzRUFBc0U7UUFDdEUzRSx3QkFBd0IyRTtRQUV4QixnQ0FBZ0M7UUFDaEMsSUFBSSxDQUFDQSxRQUFRMUUsT0FBTyxJQUFJLENBQUMwRSxRQUFRMUUsT0FBTyxDQUFDMkUsSUFBSSxJQUFJO1lBQy9DckUsUUFBUTBCLEtBQUssQ0FBQztZQUNkLDBDQUEwQztZQUMxQ2hELFNBQVM7WUFDVDtRQUNGO1FBRUEsc0NBQXNDO1FBQ3RDWCxTQUFTcUcsUUFBUTFFLE9BQU8sQ0FBQzJFLElBQUk7UUFFN0IscUZBQXFGO1FBQ3JGL0YsbUJBQW1COEY7UUFFbkIsdUNBQXVDO1FBQ3ZDLElBQUksQ0FBQzFHLE1BQU00RSxJQUFJLEVBQUU7WUFDZnRDLFFBQVEwQixLQUFLLENBQUM7WUFDZDdFLGtFQUFjQSxDQUFDLGdCQUFnQjtZQUMvQjZCLFNBQVM7WUFDVDtRQUNGO1FBRUFzQixRQUFRQyxHQUFHLENBQUMsbURBQW1EdkMsTUFBTTRFLElBQUksQ0FBQ2hDLElBQUk7UUFDOUVOLFFBQVFDLEdBQUcsQ0FBQztRQUNaRCxRQUFRQyxHQUFHLENBQUMsaUJBQWlCbUUsUUFBUTFFLE9BQU8sQ0FBQzJFLElBQUk7UUFDakRyRSxRQUFRQyxHQUFHLENBQUMseUJBQXlCdEI7UUFDckNxQixRQUFRQyxHQUFHLENBQUMseUJBQXlCdkMsTUFBTTRCLFlBQVk7UUFFdkQsZ0RBQWdEO1FBQ2hELCtFQUErRTtRQUMvRSwrRUFBK0U7UUFDL0Usd0RBQXdEO1FBRXhELElBQUk1QixNQUFNNkIsU0FBUyxLQUFLLFNBQVM7WUFDL0JTLFFBQVFDLEdBQUcsQ0FBQztZQUNadkIsU0FBUztRQUNYLE9BQU8sSUFBSUMsaUJBQWlCLGFBQWFqQixNQUFNNEIsWUFBWSxLQUFLLFNBQVM7WUFDdkVVLFFBQVFDLEdBQUcsQ0FBQztZQUNadkIsU0FBUztRQUNYLE9BQU87WUFDTCxvRUFBb0U7WUFDcEVzQixRQUFRQyxHQUFHLENBQUM7WUFDWnZCLFNBQVM7UUFDWDtJQUNGO0lBRUEsa0NBQWtDO0lBQ2xDLE1BQU00RiwrQkFBK0IsQ0FBQ0M7UUFDcEMsMkRBQTJEO1FBRTNELDREQUE0RDtRQUM1RCxNQUFNQywwQkFBMEI7WUFBRSxHQUFHRCxhQUFhO1FBQUM7UUFFbkQseUZBQXlGO1FBQ3pGLElBQUlBLGNBQWM5RCxZQUFZLEVBQUU7WUFDOUIrRCx3QkFBd0JDLGFBQWEsR0FBR0YsY0FBYzlELFlBQVk7UUFDcEUsT0FBTyxJQUFJOEQsY0FBY0UsYUFBYSxFQUFFO1lBQ3RDRCx3QkFBd0IvRCxZQUFZLEdBQUc4RCxjQUFjRSxhQUFhO1FBQ3BFO1FBRUEsSUFBSUYsY0FBYzdELFdBQVcsRUFBRTtZQUM3QjhELHdCQUF3QkUsVUFBVSxHQUFHSCxjQUFjN0QsV0FBVztRQUNoRSxPQUFPLElBQUk2RCxjQUFjRyxVQUFVLEVBQUU7WUFDbkNGLHdCQUF3QjlELFdBQVcsR0FBRzZELGNBQWNHLFVBQVU7UUFDaEU7UUFFQSxzRUFBc0U7UUFDdEV0RSxxQkFBcUJvRTtRQUVyQix1REFBdUQ7UUFDdkRwRixpQkFBaUJ1RixPQUFPLEdBQUdIO1FBRTNCLHVEQUF1RDtRQUN2RCxJQUFJO1lBQ0ZJLGFBQWFDLE9BQU8sQ0FBQyx5QkFBeUJDLEtBQUtDLFNBQVMsQ0FBQ1A7WUFDN0R4RSxRQUFRQyxHQUFHLENBQUM7UUFDZCxFQUFFLE9BQU95QixPQUFPO1lBQ2QxQixRQUFRMEIsS0FBSyxDQUFDLG9FQUFvRUE7UUFDcEY7UUFFQSxnRUFBZ0U7UUFDaEUsTUFBTXNELHVCQUF1QnRILE1BQU11SCxZQUFZLENBQUNDLGNBQWM7UUFDOURsRixRQUFRQyxHQUFHLENBQUMsaUVBQWlFK0U7UUFFN0UsdUNBQXVDO1FBQ3ZDLElBQUlBLHNCQUFzQjtZQUN4QixJQUFJO2dCQUNGSixhQUFhQyxPQUFPLENBQUMseUJBQXlCRztnQkFDOUNoRixRQUFRQyxHQUFHLENBQUMsMkRBQTJEK0U7WUFDekUsRUFBRSxPQUFPdEQsT0FBTztnQkFDZDFCLFFBQVEwQixLQUFLLENBQUMsb0VBQW9FQTtZQUNwRjtRQUNGO1FBRUEsMENBQTBDO1FBQzFDckQsaUJBQWlCbUc7UUFFakIsMENBQTBDO1FBQzFDVyxPQUFPQyxPQUFPLENBQUNaLHlCQUF5QmEsT0FBTyxDQUFDO2dCQUFDLENBQUNDLFlBQVlsQixRQUFRO1lBQ3BFLElBQUlBLFdBQVdBLFFBQVE5RCxJQUFJLElBQUk4RCxRQUFRN0QsWUFBWSxFQUFFO2dCQUNuRHRDLGVBQWUsVUFBcUIsT0FBWHFILFlBQVcsVUFBUWxCLFFBQVE5RCxJQUFJO2dCQUN4RHJDLGVBQWUsVUFBcUIsT0FBWHFILFlBQVcsYUFBV2xCLFFBQVE3RCxZQUFZO1lBQ3JFO1FBQ0Y7UUFFQSwwRUFBMEU7UUFDMUUsSUFBSXlFLHNCQUFzQjtZQUN4QmhGLFFBQVFDLEdBQUcsQ0FBQyxvRUFBb0UrRTtZQUNoRnhCLFdBQVc7Z0JBQ1R2RixlQUFlLGtCQUFrQitHO1lBQ25DLEdBQUc7UUFDTDtRQUVBLHFEQUFxRDtRQUNyRHhCLFdBQVc7WUFDVHhELFFBQVFDLEdBQUcsQ0FBQywyQ0FBMkN2QyxNQUFNdUgsWUFBWTtZQUN6RWpGLFFBQVFDLEdBQUcsQ0FBQyx3QkFBd0JrRixPQUFPSSxJQUFJLENBQUM3SCxNQUFNdUgsWUFBWSxFQUFFNUMsTUFBTTtZQUMxRXJDLFFBQVFDLEdBQUcsQ0FBQyw4QkFBOEJ1RTtRQUM1QyxHQUFHO1FBRUgscUVBQXFFO1FBQ3JFLGtFQUFrRTtRQUNsRWhCLFdBQVc7WUFDVCx3RUFBd0U7WUFDeEUsTUFBTWdDLG1CQUFtQkwsT0FBT0ksSUFBSSxDQUFDN0gsTUFBTXVILFlBQVksRUFBRVEsTUFBTSxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJcEMsVUFBVSxDQUFDLGNBQWNvQyxJQUFJQyxRQUFRLENBQUM7WUFDakgsTUFBTUMsc0JBQXNCVCxPQUFPSSxJQUFJLENBQUM3SCxNQUFNdUgsWUFBWSxFQUFFUSxNQUFNLENBQUNDLENBQUFBLE1BQU9BLElBQUlwQyxVQUFVLENBQUMsY0FBY29DLElBQUlDLFFBQVEsQ0FBQztZQUVwSDNGLFFBQVFDLEdBQUcsQ0FBQyx3Q0FBd0N1RixpQkFBaUJuRCxNQUFNO1lBQzNFckMsUUFBUUMsR0FBRyxDQUFDLDJDQUEyQzJGLG9CQUFvQnZELE1BQU07WUFFakYscUVBQXFFO1lBQ3JFLElBQUksS0FBNkIsSUFBSSxXQUFXd0QsSUFBSSxDQUFDQyxPQUFPQyxTQUFTLENBQUNDLFNBQVMsR0FBRztnQkFDaEZoRyxRQUFRQyxHQUFHLENBQUM7Z0JBRVosb0RBQW9EO2dCQUNwRCx3R0FBd0c7Z0JBQ3hHa0YsT0FBT0MsT0FBTyxDQUFDWix5QkFBeUJhLE9BQU8sQ0FBQzt3QkFBQyxDQUFDQyxZQUFZbEIsUUFBUTtvQkFDcEUsSUFBSUEsV0FBV0EsUUFBUTlELElBQUksSUFBSThELFFBQVE3RCxZQUFZLEVBQUU7d0JBQ25ELCtDQUErQzt3QkFDL0M3QyxNQUFNdUgsWUFBWSxDQUFDLFVBQXFCLE9BQVhLLFlBQVcsU0FBTyxHQUFHbEIsUUFBUTlELElBQUk7d0JBQzlENUMsTUFBTXVILFlBQVksQ0FBQyxVQUFxQixPQUFYSyxZQUFXLFlBQVUsR0FBR2xCLFFBQVE3RCxZQUFZO3dCQUV6RSxrQ0FBa0M7d0JBQ2xDLE1BQU0wRixpQkFBaUJYLGVBQWUsaUJBQWlCLGtCQUNyREEsZUFBZSxnQkFBZ0IsZUFBZUE7d0JBRWhELElBQUlXLG1CQUFtQlgsWUFBWTs0QkFDakM1SCxNQUFNdUgsWUFBWSxDQUFDLFVBQXlCLE9BQWZnQixnQkFBZSxTQUFPLEdBQUc3QixRQUFROUQsSUFBSTs0QkFDbEU1QyxNQUFNdUgsWUFBWSxDQUFDLFVBQXlCLE9BQWZnQixnQkFBZSxZQUFVLEdBQUc3QixRQUFRN0QsWUFBWTt3QkFDL0U7d0JBRUFQLFFBQVFDLEdBQUcsQ0FBQyxrREFBNkQsT0FBWHFGLFlBQVc7b0JBQzNFO2dCQUNGO2dCQUVBLHFDQUFxQztnQkFDckMsSUFBSU4sc0JBQXNCO29CQUN4QnRILE1BQU11SCxZQUFZLENBQUNDLGNBQWMsR0FBR0Y7b0JBQ3BDaEYsUUFBUUMsR0FBRyxDQUFDLHFFQUFxRStFO2dCQUNuRjtZQUNGO1lBRUEsK0JBQStCO1lBQy9CdEcsU0FBUztRQUNYLEdBQUc7SUFDTDtJQUVBLDZCQUE2QjtJQUM3QixNQUFNd0gsMEJBQTBCLENBQUNDO1FBQy9CLElBQUlBLGVBQWU7WUFDakIsbUNBQW1DO1lBQ25DL0gsYUFBYStIO1lBQ2JuRyxRQUFRQyxHQUFHLENBQUMsdUJBQXVCa0csY0FBYzdGLElBQUk7UUFDdkQsT0FBTztZQUNMTixRQUFRQyxHQUFHLENBQUM7UUFDZDtRQUVBLG9HQUFvRztRQUNwRyxJQUFJdEIsaUJBQWlCLGFBQWFqQixNQUFNNEIsWUFBWSxLQUFLLFNBQVM7WUFDaEVVLFFBQVFDLEdBQUcsQ0FBQztZQUNadkIsU0FBUztRQUNYLE9BQU87WUFDTCxnREFBZ0Q7WUFDaERzQixRQUFRQyxHQUFHLENBQUM7WUFDWnZCLFNBQVM7UUFDWDtJQUNGO0lBRUEsbUVBQW1FO0lBQ25FLE1BQU0wSCxvQkFBb0IsQ0FBQ0M7UUFDekIsa0ZBQWtGO1FBQ2xGLElBQUkxSCxpQkFBaUIsYUFBYWpCLE1BQU00QixZQUFZLEtBQUssU0FBUztZQUNoRVUsUUFBUUMsR0FBRyxDQUFDO1lBQ1ovQjtZQUNBO1FBQ0Y7UUFFQSx1RUFBdUU7UUFDdkUsSUFBSSxDQUFDUixNQUFNb0csS0FBSyxJQUFJLENBQUNwRyxNQUFNb0csS0FBSyxDQUFDTyxJQUFJLElBQUk7WUFDdkNyRSxRQUFRMEIsS0FBSyxDQUFDO1lBRWQsNkNBQTZDO1lBQzdDLElBQUlsQyxnQkFBZ0JFLE9BQU8sSUFBSUYsZ0JBQWdCRSxPQUFPLENBQUMyRSxJQUFJLElBQUk7Z0JBQzdELDhFQUE4RTtnQkFDOUUsc0VBQXNFO2dCQUN0RS9GLG1CQUFtQmtCO2dCQUNuQixtQ0FBbUM7Z0JBQ25DekIsU0FBU3lCLGdCQUFnQkUsT0FBTyxDQUFDMkUsSUFBSTtZQUN2QyxPQUFPO2dCQUNMckUsUUFBUTBCLEtBQUssQ0FBQztnQkFDZGhELFNBQVM7Z0JBQ1Q7WUFDRjtRQUNGO1FBRUEscUVBQXFFO1FBQ3JFLElBQUksS0FBNkIsSUFBSSxXQUFXbUgsSUFBSSxDQUFDQyxPQUFPQyxTQUFTLENBQUNDLFNBQVMsR0FBRztZQUNoRmhHLFFBQVFDLEdBQUcsQ0FBQztZQUVaLHNEQUFzRDtZQUN0RCxNQUFNRSxvQkFBb0JmLGlCQUFpQnVGLE9BQU87WUFDbEQsSUFBSXhFLG1CQUFtQjtnQkFDckJILFFBQVFDLEdBQUcsQ0FBQyxvRUFBb0VFO2dCQUVoRixvREFBb0Q7Z0JBQ3BEZ0YsT0FBT0MsT0FBTyxDQUFDakYsbUJBQW1Ca0YsT0FBTyxDQUFDO3dCQUFDLENBQUNDLFlBQVlsQixRQUFRO29CQUM5RCxJQUFJQSxXQUFXQSxRQUFROUQsSUFBSSxJQUFJOEQsUUFBUTdELFlBQVksRUFBRTt3QkFDbkQsK0NBQStDO3dCQUMvQzdDLE1BQU11SCxZQUFZLENBQUMsVUFBcUIsT0FBWEssWUFBVyxTQUFPLEdBQUdsQixRQUFROUQsSUFBSTt3QkFDOUQ1QyxNQUFNdUgsWUFBWSxDQUFDLFVBQXFCLE9BQVhLLFlBQVcsWUFBVSxHQUFHbEIsUUFBUTdELFlBQVk7d0JBRXpFLGtDQUFrQzt3QkFDbEMsTUFBTTBGLGlCQUFpQlgsZUFBZSxpQkFBaUIsa0JBQ3JEQSxlQUFlLGdCQUFnQixlQUFlQTt3QkFFaEQsSUFBSVcsbUJBQW1CWCxZQUFZOzRCQUNqQzVILE1BQU11SCxZQUFZLENBQUMsVUFBeUIsT0FBZmdCLGdCQUFlLFNBQU8sR0FBRzdCLFFBQVE5RCxJQUFJOzRCQUNsRTVDLE1BQU11SCxZQUFZLENBQUMsVUFBeUIsT0FBZmdCLGdCQUFlLFlBQVUsR0FBRzdCLFFBQVE3RCxZQUFZO3dCQUMvRTt3QkFFQVAsUUFBUUMsR0FBRyxDQUFDLGtEQUE2RCxPQUFYcUYsWUFBVztvQkFDM0U7Z0JBQ0Y7WUFDRjtRQUNGO1FBRUEsZ0RBQWdEO1FBQ2hELElBQUk1SCxNQUFNNkIsU0FBUyxLQUFLLFNBQVM7WUFDL0JTLFFBQVFDLEdBQUcsQ0FBRTtZQUNiRCxRQUFRQyxHQUFHLENBQUMsNENBQTJGLE9BQS9DdkMsTUFBTXVILFlBQVksQ0FBQ0MsY0FBYyxJQUFJO1lBQzdGbEYsUUFBUUMsR0FBRyxDQUFDLDBDQUE2RCxPQUFuQnZDLE1BQU00QixZQUFZO1lBQ3hFVSxRQUFRQyxHQUFHLENBQUMsdUNBQXFFLE9BQTlCcEIsb0JBQW9CO1lBRXZFLGdDQUFnQztZQUNoQyxJQUFJbkIsTUFBTTRCLFlBQVksS0FBSyxXQUFXO2dCQUNwQ1UsUUFBUUMsR0FBRyxDQUFFO2dCQUViLDZFQUE2RTtnQkFDN0UsSUFBSSxDQUFDdkMsTUFBTXVILFlBQVksQ0FBQ0MsY0FBYyxJQUFJckcsa0JBQWtCO29CQUMxRCxvREFBb0Q7b0JBQ3BELElBQUl3SCxnQkFBZ0I7b0JBRXBCLElBQUl4SCxxQkFBcUIscUJBQXFCO3dCQUM1Q3dILGdCQUFnQjtvQkFDbEIsT0FBTyxJQUFJeEgscUJBQXFCLGdCQUFnQjt3QkFDOUN3SCxnQkFBZ0I7b0JBQ2xCLE9BQU8sSUFBSXhILHFCQUFxQix5QkFBeUI7d0JBQ3ZEd0gsZ0JBQWdCO29CQUNsQjtvQkFFQSxJQUFJQSxlQUFlO3dCQUNqQnJHLFFBQVFDLEdBQUcsQ0FBQyx3REFBc0UsT0FBZG9HO3dCQUNwRXBJLGVBQWUsa0JBQWtCb0k7b0JBQ25DO2dCQUNGLE9BQU87b0JBQ0xyRyxRQUFRQyxHQUFHLENBQUMsd0RBQTBGLE9BQWxDdkMsTUFBTXVILFlBQVksQ0FBQ0MsY0FBYztnQkFDdkc7WUFDRjtZQUVBLG1GQUFtRjtZQUNuRixJQUFJLENBQUN4SCxNQUFNdUgsWUFBWSxDQUFDQyxjQUFjLElBQUlyRyxrQkFBa0I7Z0JBQzFEbUIsUUFBUUMsR0FBRyxDQUFDLG1FQUFvRixPQUFqQnBCO2dCQUUvRSxvREFBb0Q7Z0JBQ3BELElBQUl3SCxnQkFBZ0I7Z0JBRXBCLElBQUl4SCxxQkFBcUIscUJBQXFCO29CQUM1Q3dILGdCQUFnQjtnQkFDbEIsT0FBTyxJQUFJeEgscUJBQXFCLGdCQUFnQjtvQkFDOUN3SCxnQkFBZ0I7Z0JBQ2xCLE9BQU8sSUFBSXhILHFCQUFxQix5QkFBeUI7b0JBQ3ZEd0gsZ0JBQWdCO2dCQUNsQjtnQkFFQSxJQUFJQSxlQUFlO29CQUNqQnJHLFFBQVFDLEdBQUcsQ0FBQyxrRUFBZ0YsT0FBZG9HO29CQUM5RXBJLGVBQWUsa0JBQWtCb0k7Z0JBQ25DO1lBQ0Y7WUFFQSx1RUFBdUU7WUFDdkUsSUFBSSxDQUFDM0ksTUFBTXVILFlBQVksQ0FBQ0MsY0FBYyxFQUFFO2dCQUN0Q2xGLFFBQVFDLEdBQUcsQ0FBQztnQkFDWiwwRUFBMEU7Z0JBQzFFaEMsZUFBZSxrQkFBa0I7Z0JBQ2pDK0IsUUFBUUMsR0FBRyxDQUFDO1lBQ2Q7UUFDRjtRQUVBLG1GQUFtRjtRQUNuRixJQUFJLEtBQTZCLElBQUksV0FBVzRGLElBQUksQ0FBQ0MsT0FBT0MsU0FBUyxDQUFDQyxTQUFTLEdBQUc7WUFDaEZoRyxRQUFRQyxHQUFHLENBQUM7WUFFWixzREFBc0Q7WUFDdEQsTUFBTUUsb0JBQW9CZixpQkFBaUJ1RixPQUFPO1lBQ2xELElBQUl4RSxxQkFBcUJnRixPQUFPSSxJQUFJLENBQUNwRixtQkFBbUJrQyxNQUFNLEdBQUcsR0FBRztnQkFDbEVyQyxRQUFRQyxHQUFHLENBQUMsa0ZBQWtGRTtnQkFFOUYsb0RBQW9EO2dCQUNwRGdGLE9BQU9DLE9BQU8sQ0FBQ2pGLG1CQUFtQmtGLE9BQU8sQ0FBQzt3QkFBQyxDQUFDQyxZQUFZbEIsUUFBUTtvQkFDOUQsSUFBSUEsV0FBV0EsUUFBUTlELElBQUksSUFBSThELFFBQVE3RCxZQUFZLEVBQUU7d0JBQ25ELCtDQUErQzt3QkFDL0M3QyxNQUFNdUgsWUFBWSxDQUFDLFVBQXFCLE9BQVhLLFlBQVcsU0FBTyxHQUFHbEIsUUFBUTlELElBQUk7d0JBQzlENUMsTUFBTXVILFlBQVksQ0FBQyxVQUFxQixPQUFYSyxZQUFXLFlBQVUsR0FBR2xCLFFBQVE3RCxZQUFZO3dCQUV6RSxrQ0FBa0M7d0JBQ2xDLE1BQU0wRixpQkFBaUJYLGVBQWUsaUJBQWlCLGtCQUNyREEsZUFBZSxnQkFBZ0IsZUFBZUE7d0JBRWhELElBQUlXLG1CQUFtQlgsWUFBWTs0QkFDakM1SCxNQUFNdUgsWUFBWSxDQUFDLFVBQXlCLE9BQWZnQixnQkFBZSxTQUFPLEdBQUc3QixRQUFROUQsSUFBSTs0QkFDbEU1QyxNQUFNdUgsWUFBWSxDQUFDLFVBQXlCLE9BQWZnQixnQkFBZSxZQUFVLEdBQUc3QixRQUFRN0QsWUFBWTt3QkFDL0U7d0JBRUFQLFFBQVFDLEdBQUcsQ0FBQyxrREFBNkQsT0FBWHFGLFlBQVc7b0JBQzNFO2dCQUNGO1lBQ0Y7UUFDRjtRQUVBLDRDQUE0QztRQUM1QyxJQUFJLENBQUM1SCxNQUFNNEUsSUFBSSxFQUFFO1lBQ2Z0QyxRQUFRMEIsS0FBSyxDQUFDO1lBQ2Q3RSxrRUFBY0EsQ0FBQyxnQkFBZ0I7WUFFL0IsMENBQTBDO1lBQzFDNkIsU0FBUztZQUNUO1FBQ0Y7UUFFQSx3Q0FBd0M7UUFDeENBLFNBQVM7UUFFVCwrQ0FBK0M7UUFDL0NzQixRQUFRQyxHQUFHLENBQUMsZ0NBQWdDO1lBQzFDcUMsTUFBTTVFLE1BQU00RSxJQUFJLEdBQUc1RSxNQUFNNEUsSUFBSSxDQUFDaEMsSUFBSSxHQUFHO1lBQ3JDZixXQUFXN0IsTUFBTTZCLFNBQVM7WUFDMUJELGNBQWM1QixNQUFNNEIsWUFBWTtZQUNoQ3dFLE9BQU9wRyxNQUFNb0csS0FBSztZQUNsQndDLGFBQWE1SSxNQUFNNEksV0FBVztZQUM5QnJCLGNBQWN2SCxNQUFNdUgsWUFBWTtZQUNoQ3NCLG1CQUFtQnBCLE9BQU9JLElBQUksQ0FBQzdILE1BQU11SCxZQUFZLEVBQUU1QyxNQUFNO1FBQzNEO1FBRUEscURBQXFEO1FBQ3JEckMsUUFBUUMsR0FBRyxDQUFDLGlEQUE4RCxPQUFidEI7UUFDN0RxQixRQUFRQyxHQUFHLENBQUMseURBQTRFLE9BQW5CdkMsTUFBTTRCLFlBQVk7UUFFdkYsc0ZBQXNGO1FBQ3RGLElBQUlYLGdCQUFnQmpCLE1BQU00QixZQUFZLEtBQUs2QixnQ0FBZ0N4QyxlQUFlO1lBQ3hGcUIsUUFBUUMsR0FBRyxDQUFFO1lBQ2JELFFBQVFDLEdBQUcsQ0FBQyxrRUFBZ0gsT0FBOUNrQixnQ0FBZ0N4QztZQUM5R3FCLFFBQVFDLEdBQUcsQ0FBQyxrREFBcUUsT0FBbkJ2QyxNQUFNNEIsWUFBWTtZQUNoRlUsUUFBUUMsR0FBRyxDQUFFO1lBRWIsNkJBQTZCO1lBQzdCLE1BQU11RyxvQkFBb0JyRixnQ0FBZ0N4QztZQUMxRHFCLFFBQVFDLEdBQUcsQ0FBQywyQ0FBNkQsT0FBbEJ1RztZQUV2RCxxREFBcUQ7WUFDckQsaURBQWlEO1lBQ2pELElBQUlILGdCQUFnQjtZQUVwQixJQUFJeEgscUJBQXFCLHFCQUFxQjtnQkFDNUN3SCxnQkFBZ0I7WUFDbEIsT0FBTyxJQUFJeEgscUJBQXFCLGdCQUFnQjtnQkFDOUN3SCxnQkFBZ0I7WUFDbEIsT0FBTyxJQUFJeEgscUJBQXFCLHlCQUF5QjtnQkFDdkR3SCxnQkFBZ0I7WUFDbEI7WUFFQXJHLFFBQVFDLEdBQUcsQ0FBQyxnREFBaUUsT0FBakJwQjtZQUM1RG1CLFFBQVFDLEdBQUcsQ0FBQyxzREFBb0UsT0FBZG9HO1lBRWxFLDBFQUEwRTtZQUMxRWxJLHdCQUF3QnFJLG1CQUFtQkg7UUFDN0MsT0FBTztZQUNMLHdDQUF3QztZQUN4QyxNQUFNSSxxQkFBcUJKLGlCQUFpQjNJLE1BQU11SCxZQUFZLENBQUNDLGNBQWMsSUFBSTtZQUNqRmxGLFFBQVFDLEdBQUcsQ0FBQyxxREFBd0UsT0FBbkJ3RztZQUVqRSx3RUFBd0U7WUFDeEV0SSx3QkFBd0JULE1BQU00QixZQUFZLEVBQUVtSCxvQkFBb0JDLElBQUksQ0FBQztnQkFDbkUsZ0NBQWdDO2dCQUNoQzFHLFFBQVFDLEdBQUcsQ0FBQztZQUNkLEdBQUcwRyxLQUFLLENBQUMsQ0FBQ2pGO2dCQUNSMUIsUUFBUTBCLEtBQUssQ0FBQyxrQkFBa0JBO1lBQ2xDO1FBQ0Y7SUFDRjtJQUVBLHNEQUFzRDtJQUN0RCxNQUFNa0Ysa0NBQWtDO1FBQ3RDNUcsUUFBUUMsR0FBRyxDQUFDO1FBRVosdUNBQXVDO1FBQ3ZDLElBQUksQ0FBQ3ZDLE1BQU00RSxJQUFJLEVBQUU7WUFDZnRDLFFBQVEwQixLQUFLLENBQUM7WUFDZDdFLGtFQUFjQSxDQUFDLGdCQUFnQjtZQUMvQjZCLFNBQVM7WUFDVDtRQUNGO1FBRUFzQixRQUFRQyxHQUFHLENBQUMsb0RBQW9EdkMsTUFBTTRFLElBQUksQ0FBQ2hDLElBQUk7UUFFL0UsNkRBQTZEO1FBQzdELElBQUkzQixpQkFBaUIsV0FBVztZQUM5QnFCLFFBQVFDLEdBQUcsQ0FBQztZQUVaLDRFQUE0RTtZQUM1RSxJQUFJLENBQUN2QyxNQUFNb0csS0FBSyxJQUFJLENBQUNwRyxNQUFNb0csS0FBSyxDQUFDTyxJQUFJLElBQUk7Z0JBQ3ZDLE1BQU13QyxlQUFlbkosTUFBTTRFLElBQUksQ0FBQ2hDLElBQUksQ0FBQ3dHLE9BQU8sQ0FBQyxhQUFhLEtBQUssd0JBQXdCO2dCQUN2Ri9JLFNBQVM4STtnQkFDVDdHLFFBQVFDLEdBQUcsQ0FBQyxtREFBbUQ0RztZQUNqRTtZQUVBLHlDQUF5QztZQUN6Q3JELFdBQVc7Z0JBQ1R1RDtZQUNGLEdBQUc7WUFDSDtRQUNGO1FBRUEsb0RBQW9EO1FBQ3BELElBQUk1RyxvQkFBb0JmLGlCQUFpQnVGLE9BQU87UUFFaEQsa0NBQWtDO1FBQ2xDLElBQUksQ0FBQ3hFLHFCQUFxQmdGLE9BQU9JLElBQUksQ0FBQ3BGLG1CQUFtQmtDLE1BQU0sS0FBSyxHQUFHO1lBQ3JFLElBQUk7Z0JBQ0YsTUFBTTJFLHNCQUFzQnBDLGFBQWFxQyxPQUFPLENBQUM7Z0JBQ2pELElBQUlELHFCQUFxQjtvQkFDdkI3RyxvQkFBb0IyRSxLQUFLb0MsS0FBSyxDQUFDRjtvQkFDL0JoSCxRQUFRQyxHQUFHLENBQUMsZ0VBQWdFK0c7b0JBRTVFLDRDQUE0QztvQkFDNUM1SCxpQkFBaUJ1RixPQUFPLEdBQUd4RTtvQkFFM0Isa0NBQWtDO29CQUNsQ0gsUUFBUUMsR0FBRyxDQUFDLDBCQUFnRSxPQUF0Q2tGLE9BQU9JLElBQUksQ0FBQ3BGLG1CQUFtQmtDLE1BQU0sRUFBQztvQkFDNUU4QyxPQUFPQyxPQUFPLENBQUNqRixtQkFBbUJrRixPQUFPLENBQUM7NEJBQUMsQ0FBQ0MsWUFBWWxCLFFBQXVCO3dCQUM3RSxJQUFJQSxXQUFXQSxRQUFROUQsSUFBSSxJQUFJOEQsUUFBUTdELFlBQVksRUFBRTs0QkFDbkRQLFFBQVFDLEdBQUcsQ0FBQywyQkFBMENtRSxPQUFma0IsWUFBVyxNQUFxQmxCLE9BQWpCQSxRQUFROUQsSUFBSSxFQUFDLE1BQXlCLE9BQXJCOEQsUUFBUTdELFlBQVksRUFBQzt3QkFDOUY7b0JBQ0Y7Z0JBQ0YsT0FBTztvQkFDTFAsUUFBUUMsR0FBRyxDQUFDO2dCQUNkO1lBQ0YsRUFBRSxPQUFPeUIsT0FBTztnQkFDZDFCLFFBQVEwQixLQUFLLENBQUMseUVBQXlFQTtZQUN6RjtRQUNGLE9BQU87WUFDTDFCLFFBQVFDLEdBQUcsQ0FBQywwQkFBZ0UsT0FBdENrRixPQUFPSSxJQUFJLENBQUNwRixtQkFBbUJrQyxNQUFNLEVBQUM7UUFDOUU7UUFFQSw4Q0FBOEM7UUFDOUMsSUFBSWdFLGdCQUFnQjNJLE1BQU11SCxZQUFZLENBQUNDLGNBQWM7UUFDckQsSUFBSSxDQUFDbUIsZUFBZTtZQUNsQixJQUFJO2dCQUNGLE1BQU1jLHNCQUFzQnZDLGFBQWFxQyxPQUFPLENBQUM7Z0JBQ2pELElBQUlFLHFCQUFxQjtvQkFDdkJkLGdCQUFnQmM7b0JBQ2hCbkgsUUFBUUMsR0FBRyxDQUFDLGdFQUFnRW9HO29CQUU1RSxzQkFBc0I7b0JBQ3RCcEksZUFBZSxrQkFBa0JvSTtnQkFDbkM7WUFDRixFQUFFLE9BQU8zRSxPQUFPO2dCQUNkMUIsUUFBUTBCLEtBQUssQ0FBQyx5RUFBeUVBO1lBQ3pGO1FBQ0Y7UUFFQSxvQ0FBb0M7UUFDcEMsSUFBSXZCLG1CQUFtQjtZQUNyQkgsUUFBUUMsR0FBRyxDQUFDO1lBRVosd0RBQXdEO1lBQ3hELE1BQU1tSCxxQkFBNkMsQ0FBQztZQUNwRCxJQUFJQyxzQkFBc0I7WUFFMUIseURBQXlEO1lBQ3pEbEMsT0FBT0MsT0FBTyxDQUFDakYsbUJBQW1Ca0YsT0FBTyxDQUFDO29CQUFDLENBQUNDLFlBQVlsQixRQUFRO2dCQUM5RCxJQUFJQSxXQUFXQSxRQUFROUQsSUFBSSxJQUFJOEQsUUFBUTdELFlBQVksRUFBRTtvQkFDbkQsbUJBQW1CO29CQUNuQjZHLGtCQUFrQixDQUFDLFVBQXFCLE9BQVg5QixZQUFXLFNBQU8sR0FBR2xCLFFBQVE5RCxJQUFJO29CQUM5RDhHLGtCQUFrQixDQUFDLFVBQXFCLE9BQVg5QixZQUFXLFlBQVUsR0FBR2xCLFFBQVE3RCxZQUFZO29CQUN6RThHO29CQUVBLCtCQUErQjtvQkFDL0IsTUFBTXBCLGlCQUFpQlgsZUFBZSxpQkFBaUIsa0JBQ3JEQSxlQUFlLGdCQUFnQixlQUFlQTtvQkFFaEQsSUFBSVcsbUJBQW1CWCxZQUFZO3dCQUNqQzhCLGtCQUFrQixDQUFDLFVBQXlCLE9BQWZuQixnQkFBZSxTQUFPLEdBQUc3QixRQUFROUQsSUFBSTt3QkFDbEU4RyxrQkFBa0IsQ0FBQyxVQUF5QixPQUFmbkIsZ0JBQWUsWUFBVSxHQUFHN0IsUUFBUTdELFlBQVk7b0JBQy9FO2dCQUNGO1lBQ0Y7WUFFQSw0QkFBNEI7WUFDNUJQLFFBQVFDLEdBQUcsQ0FBQyw2QkFBaUQsT0FBcEJvSCxxQkFBb0I7WUFDN0RySCxRQUFRQyxHQUFHLENBQUMsMENBQTBDNkUsS0FBS0MsU0FBUyxDQUFDcUM7WUFFckUsMkRBQTJEO1lBQzNEakMsT0FBT0MsT0FBTyxDQUFDZ0Msb0JBQW9CL0IsT0FBTyxDQUFDO29CQUFDLENBQUNpQyxPQUFPbkUsTUFBTTtnQkFDeERsRixlQUFlcUosT0FBT25FO1lBQ3hCO1lBRUEsb0VBQW9FO1lBQ3BFSyxXQUFXO2dCQUNUeEQsUUFBUUMsR0FBRyxDQUFDO2dCQUNabUcsa0JBQWtCQztZQUNwQixHQUFHO1FBQ0wsT0FBTztZQUNMckcsUUFBUUMsR0FBRyxDQUFDO1lBQ1ptRyxrQkFBa0JDO1FBQ3BCO0lBRUEsNkRBQTZEO0lBQy9EO0lBRUEsMERBQTBEO0lBQzFELE1BQU1rQiw4QkFBOEI7UUFDbEMsb0ZBQW9GO1FBRXBGLHdEQUF3RDtRQUN4RCxJQUFJL0gsZ0JBQWdCRSxPQUFPLElBQUlGLGdCQUFnQkUsT0FBTyxDQUFDMkUsSUFBSSxJQUFJO1lBQzdELHNFQUFzRTtZQUN0RS9GLG1CQUFtQmtCO1FBQ3JCO1FBRUFkLFNBQVM7SUFDWDtJQUVBLHFCQUFxQjtJQUNyQixNQUFNOEksY0FBYztRQUNsQiwyREFBMkQ7UUFDM0QsSUFBSTlKLE1BQU0rSixJQUFJLEtBQUssY0FBY2hLLGtCQUFrQjtZQUNqRHVDLFFBQVFDLEdBQUcsQ0FBQztZQUNaeEM7UUFDRjtRQUVBLHdCQUF3QjtRQUN4QmlCLFNBQVM7UUFFVCx3Q0FBd0M7UUFDeEMsSUFBSW5CLFNBQVM7WUFDWEE7UUFDRjtRQUVBLGlGQUFpRjtRQUNqRmlHLFdBQVc7WUFDVGhGO1lBQ0F3QixRQUFRQyxHQUFHLENBQUM7UUFDZCxHQUFHO0lBQ0w7SUFFQSxrQ0FBa0M7SUFDbEMsSUFBSXhCLFVBQVUsVUFBVTtRQUN0QixPQUFPO0lBQ1Q7SUFFQSxxQkFDRTs7WUFDR0EsVUFBVSxpQ0FDVCw4REFBQ3ZDLDREQUFtQkE7Z0JBQ2xCd0wsUUFBUXhIO2dCQUNSM0MsU0FBU2lLOzs7Ozs7WUFJWi9JLFVBQVUscUNBQ1QsOERBQUN0QywrREFBc0JBO2dCQUNyQnVMLFFBQVFyRztnQkFDUnNHLFFBQVEsSUFBTWpKLFNBQVM7Z0JBQ3ZCa0osVUFBVXZHO2dCQUNWd0csbUJBQW1Cakc7Z0JBQ25CckUsU0FBU2lLO2dCQUNUakksV0FBVzdCLE1BQU02QixTQUFTO2dCQUMxQlosY0FBY0E7Ozs7OztZQUlqQkYsVUFBVSx3QkFBd0JmLE1BQU00RSxJQUFJLGtCQUMzQyw4REFBQ2xHLDJEQUFrQkE7Z0JBQ2pCMEwsV0FBV3BLLE1BQU00RSxJQUFJO2dCQUNyQm9GLFFBQVF4QjtnQkFDUnlCLFFBQVE7b0JBQ04sd0VBQXdFO29CQUN4RSxJQUFJO3dCQUFDO3dCQUFXO3dCQUFZO3FCQUFTLENBQUMxRyxRQUFRLENBQUN0QyxlQUFlO3dCQUM1REQsU0FBUztvQkFDWCxPQUFPO3dCQUNMLHlDQUF5Qzt3QkFDekNBLFNBQVM7b0JBQ1g7Z0JBQ0Y7Z0JBQ0FuQixTQUFTO29CQUNQLDRDQUE0QztvQkFDNUNpQjtvQkFDQWdKO2dCQUNGOzs7Ozs7WUFJSC9JLFVBQVUsbUNBQ1QsOERBQUNwQyx3REFBZUE7Z0JBQ2RxTCxRQUFRdkQ7Z0JBQ1J3RCxRQUFRO29CQUNOLDRDQUE0QztvQkFDNUMsSUFBSWpLLE1BQU02QixTQUFTLEtBQUssV0FBVzdCLE1BQU00RSxJQUFJLEVBQUU7d0JBQzdDNUQsU0FBUztvQkFDWCxPQUFPO3dCQUNMLHdDQUF3Qzt3QkFDeENBLFNBQVM7b0JBQ1g7Z0JBQ0Y7Z0JBQ0FuQixTQUFTO29CQUNQLDRDQUE0QztvQkFDNUNpQjtvQkFDQWdKO2dCQUNGO2dCQUNBekksY0FBY0E7Z0JBQ2QrSSxXQUFXcEssTUFBTTZCLFNBQVMsS0FBSyxVQUFVN0IsTUFBTTRFLElBQUksR0FBRztnQkFDdEQvQyxXQUFXN0IsTUFBTTZCLFNBQVM7Z0JBQzFCd0ksYUFBYTFJO2dCQUNiMkksZ0JBQWdCeEk7Ozs7OztZQUluQmYsVUFBVSxpQ0FDVCw4REFBQ25DLHNEQUFhQTtnQkFDWm9MLFFBQVFwRDtnQkFDUnFELFFBQVEsSUFBTWpKLFNBQVM7Z0JBQ3ZCbkIsU0FBUztvQkFDUCw0Q0FBNEM7b0JBQzVDaUI7b0JBQ0FnSjtnQkFDRjtnQkFDQVMsc0JBQXNCOUg7Z0JBQ3RCa0csZUFBZSxDQUFDO29CQUNkLE1BQU0vRSxXQUFXNUQsTUFBTXVILFlBQVksQ0FBQ0MsY0FBYyxJQUFJO29CQUN0RGxGLFFBQVFDLEdBQUcsQ0FBQyw2REFBNkRxQjtvQkFDekUsT0FBT0E7Z0JBQ1Q7Ozs7OztZQUlIN0MsVUFBVSxvQ0FDVCw4REFBQ2xDLHlEQUFnQkE7Z0JBQ2ZxTCxVQUFVaEI7Z0JBQ1ZlLFFBQVE7b0JBQ04sc0NBQXNDO29CQUN0QywyRUFBMkU7b0JBQzNFLHdDQUF3QztvQkFDeEMsc0NBQXNDO29CQUN0QyxJQUFJaEosaUJBQWlCLGFBQWFqQixNQUFNNEIsWUFBWSxLQUFLLFNBQVM7d0JBQ2hFLHVGQUF1Rjt3QkFDdkYsSUFBSTVCLE1BQU02QixTQUFTLEtBQUssU0FBUzs0QkFDL0JiLFNBQVM7d0JBQ1gsT0FBTzs0QkFDTEEsU0FBUzt3QkFDWDtvQkFDRixPQUFPLElBQUloQixNQUFNNkIsU0FBUyxLQUFLLFNBQVM7d0JBQ3RDYixTQUFTO29CQUNYLE9BQU87d0JBQ0xBLFNBQVM7b0JBQ1g7Z0JBQ0Y7Z0JBQ0FuQixTQUFTO29CQUNQLDRDQUE0QztvQkFDNUNpQjtvQkFDQWdKO2dCQUNGOzs7Ozs7WUFJRi9JLENBQUFBLFVBQVUsZUFBZUEsVUFBVSxVQUFTLG1CQUM1Qyw4REFBQ2pDLHVEQUFjQTtnQkFDYmUsU0FBUztvQkFDUCw0Q0FBNEM7b0JBQzVDaUI7b0JBQ0FnSjtnQkFDRjtnQkFDQVUsVUFBVVg7Ozs7Ozs7O0FBS3BCO0dBdjVDTWpLOztRQUNpT2IsK0RBQVNBO1FBZ0R0TE0sNERBQWNBOzs7S0FqRGxFTztBQXk1Q04saUVBQWVBLGFBQWFBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xca29tbWlcXE9uZURyaXZlXFxEZXNrdG9wXFxXRURaQVRfXFxmcm9udGVuZFxcY29tcG9uZW50c1xcdXBsb2FkXFxVcGxvYWRNYW5hZ2VyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBjb21wb25lbnRzL3VwbG9hZC9VcGxvYWRNYW5hZ2VyLnRzeFxyXG4ndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZVJlZiwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgVXBsb2FkVHlwZVNlbGVjdGlvbiBmcm9tICcuL1VwbG9hZFR5cGVTZWxlY3Rpb24nO1xyXG5pbXBvcnQgVmlkZW9DYXRlZ29yeVNlbGVjdGlvbiBmcm9tICcuL1ZpZGVvQ2F0ZWdvcnlTZWxlY3Rpb24nO1xyXG5pbXBvcnQgVGh1bWJuYWlsU2VsZWN0aW9uIGZyb20gJy4vVGh1bWJuYWlsU2VsZWN0aW9uJztcclxuaW1wb3J0IFBlcnNvbmFsRGV0YWlscyBmcm9tICcuL1BlcnNvbmFsRGV0YWlscyc7XHJcbmltcG9ydCBWZW5kb3JEZXRhaWxzIGZyb20gJy4vVmVuZG9yRGV0YWlscyc7XHJcbmltcG9ydCBGYWNlVmVyaWZpY2F0aW9uIGZyb20gJy4vRmFjZVZlcmlmaWNhdGlvbic7XHJcbmltcG9ydCBVcGxvYWRQcm9ncmVzcyBmcm9tICcuL1VwbG9hZFByb2dyZXNzJztcclxuaW1wb3J0IHsgdXNlVXBsb2FkIH0gZnJvbSAnLi4vLi4vY29udGV4dHMvVXBsb2FkQ29udGV4dHMnO1xyXG5pbXBvcnQgeyBnZXRWaWRlb0R1cmF0aW9uLCB2YWxpZGF0ZVZpZGVvRHVyYXRpb24gfSBmcm9tICcuLi8uLi91dGlscy91cGxvYWRVdGlscyc7XHJcbmltcG9ydCB7IHNob3dXYXJuaW5nQWxlcnQsIHNob3dFcnJvckFsZXJ0LCBzaG93QWxlcnQgfSBmcm9tICcuLi8uLi91dGlscy9hbGVydFV0aWxzJztcclxuaW1wb3J0IHsgdXNlTWVkaWFVcGxvYWQsIHVzZVByZWxvYWRNZWRpYSB9IGZyb20gJy4uLy4uL2hvb2tzL3VzZU1lZGlhJztcclxuXHJcbmV4cG9ydCB0eXBlIFVwbG9hZFBoYXNlID1cclxuICB8ICd0eXBlU2VsZWN0aW9uJ1xyXG4gIHwgJ2NhdGVnb3J5U2VsZWN0aW9uJ1xyXG4gIHwgJ2ZpbGVVcGxvYWQnXHJcbiAgfCAndGh1bWJuYWlsU2VsZWN0aW9uJ1xyXG4gIHwgJ3BlcnNvbmFsRGV0YWlscydcclxuICB8ICd2ZW5kb3JEZXRhaWxzJ1xyXG4gIHwgJ2ZhY2VWZXJpZmljYXRpb24nXHJcbiAgfCAndXBsb2FkaW5nJ1xyXG4gIHwgJ2NvbXBsZXRlJ1xyXG4gIHwgJ2Nsb3NlZCc7XHJcblxyXG5pbnRlcmZhY2UgUGVyc29uYWxEZXRhaWxzRGF0YSB7XHJcbiAgY2FwdGlvbjogc3RyaW5nO1xyXG4gIGxpZmVQYXJ0bmVyOiBzdHJpbmc7XHJcbiAgd2VkZGluZ1N0eWxlOiBzdHJpbmc7XHJcbiAgcGxhY2U6IHN0cmluZztcclxuICBldmVudFR5cGU/OiBzdHJpbmc7XHJcbiAgYnVkZ2V0Pzogc3RyaW5nO1xyXG59XHJcblxyXG5pbnRlcmZhY2UgVmVuZG9yRGV0YWlsSXRlbSB7XHJcbiAgbmFtZTogc3RyaW5nO1xyXG4gIG1vYmlsZU51bWJlcjogc3RyaW5nO1xyXG59XHJcblxyXG5pbnRlcmZhY2UgVXBsb2FkTWFuYWdlclByb3BzIHtcclxuICBvbkNsb3NlPzogKCkgPT4gdm9pZDtcclxuICBpbml0aWFsVHlwZT86IHN0cmluZztcclxuICBvblVwbG9hZENvbXBsZXRlPzogKCkgPT4gdm9pZDtcclxufVxyXG5cclxuLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGZvcm1hdCB0aW1lIGluIG1pbnV0ZXMgYW5kIHNlY29uZHNcclxuY29uc3QgZm9ybWF0VGltZSA9IChzZWNvbmRzOiBudW1iZXIpOiBzdHJpbmcgPT4ge1xyXG4gIGNvbnN0IG1pbnV0ZXMgPSBNYXRoLmZsb29yKHNlY29uZHMgLyA2MCk7XHJcbiAgY29uc3QgcmVtYWluaW5nU2Vjb25kcyA9IE1hdGguZmxvb3Ioc2Vjb25kcyAlIDYwKTtcclxuXHJcbiAgaWYgKG1pbnV0ZXMgPT09IDApIHtcclxuICAgIHJldHVybiBgJHtyZW1haW5pbmdTZWNvbmRzfSBzZWNvbmRzYDtcclxuICB9IGVsc2UgaWYgKG1pbnV0ZXMgPT09IDEgJiYgcmVtYWluaW5nU2Vjb25kcyA9PT0gMCkge1xyXG4gICAgcmV0dXJuICcxIG1pbnV0ZSc7XHJcbiAgfSBlbHNlIGlmIChyZW1haW5pbmdTZWNvbmRzID09PSAwKSB7XHJcbiAgICByZXR1cm4gYCR7bWludXRlc30gbWludXRlc2A7XHJcbiAgfSBlbHNlIHtcclxuICAgIHJldHVybiBgJHttaW51dGVzfSBtaW51dGUke21pbnV0ZXMgPiAxID8gJ3MnIDogJyd9IGFuZCAke3JlbWFpbmluZ1NlY29uZHN9IHNlY29uZCR7cmVtYWluaW5nU2Vjb25kcyAhPT0gMSA/ICdzJyA6ICcnfWA7XHJcbiAgfVxyXG59O1xyXG5cclxuY29uc3QgVXBsb2FkTWFuYWdlcjogUmVhY3QuRkM8VXBsb2FkTWFuYWdlclByb3BzPiA9ICh7IG9uQ2xvc2UsIGluaXRpYWxUeXBlLCBvblVwbG9hZENvbXBsZXRlIH0pID0+IHtcclxuICBjb25zdCB7IHN0YXRlLCBzZXRGaWxlLCBzZXRNZWRpYVR5cGUsIHNldENhdGVnb3J5LCBzZXRNZWRpYVN1YnR5cGUsIHNldFRpdGxlLCBzZXREZXNjcmlwdGlvbiwgc2V0RGV0YWlsRmllbGQsIHN0YXJ0VXBsb2FkLCBzdGFydFVwbG9hZFdpdGhDYXRlZ29yeSwgc2V0VGh1bWJuYWlsLCBzZXRWZW5kb3JEZXRhaWxzLCBzZXRQZXJzb25hbERldGFpbHMsIHNldER1cmF0aW9uLCByZXNldFVwbG9hZCB9ID0gdXNlVXBsb2FkKCk7XHJcbiAgY29uc3QgW3BoYXNlLCBzZXRQaGFzZV0gPSB1c2VTdGF0ZTxVcGxvYWRQaGFzZT4oJ3R5cGVTZWxlY3Rpb24nKTtcclxuICBjb25zdCBbc2VsZWN0ZWRUeXBlLCBzZXRTZWxlY3RlZFR5cGVdID0gdXNlU3RhdGU8c3RyaW5nPihpbml0aWFsVHlwZSB8fCAnJyk7XHJcbiAgY29uc3QgW3NlbGVjdGVkQ2F0ZWdvcnksIHNldFNlbGVjdGVkQ2F0ZWdvcnldID0gdXNlU3RhdGU8c3RyaW5nPignJyk7XHJcbiAgY29uc3QgW3ByZXZpZXdJbWFnZSwgc2V0UHJldmlld0ltYWdlXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IFt0aHVtYm5haWxJbWFnZSwgc2V0VGh1bWJuYWlsSW1hZ2VdID0gdXNlU3RhdGU8RmlsZSB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IGZpbGVJbnB1dFJlZiA9IHVzZVJlZjxIVE1MSW5wdXRFbGVtZW50PihudWxsKTtcclxuICBjb25zdCB2ZW5kb3JEZXRhaWxzUmVmID0gdXNlUmVmPFJlY29yZDxzdHJpbmcsIFZlbmRvckRldGFpbEl0ZW0+Pih7fSk7XHJcblxyXG4gIC8vIEhlbHBlciBmdW5jdGlvbiB0byBkZXRlcm1pbmUgY29udGVudCB0eXBlIGZvciBQZXJzb25hbERldGFpbHNcclxuICBjb25zdCBnZXRDb250ZW50VHlwZSA9ICgpOiAncGhvdG8nIHwgJ3ZpZGVvJyB8ICdtb21lbnQnID0+IHtcclxuICAgIGlmIChzZWxlY3RlZFR5cGUgPT09ICdtb21lbnRzJyB8fCBzdGF0ZS5tZWRpYVN1YnR5cGUgPT09ICdzdG9yeScpIHtcclxuICAgICAgcmV0dXJuICdtb21lbnQnO1xyXG4gICAgfSBlbHNlIGlmIChzdGF0ZS5tZWRpYVR5cGUgPT09ICd2aWRlbycpIHtcclxuICAgICAgcmV0dXJuICd2aWRlbyc7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICByZXR1cm4gJ3Bob3RvJztcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyBTdG9yZSBwZXJzb25hbCBkZXRhaWxzIHRvIHBlcnNpc3QgYmV0d2VlbiBzY3JlZW5zXHJcbiAgY29uc3QgW3BlcnNvbmFsRGV0YWlscywgc2V0TG9jYWxQZXJzb25hbERldGFpbHNdID0gdXNlU3RhdGU8UGVyc29uYWxEZXRhaWxzRGF0YT4oe1xyXG4gICAgY2FwdGlvbjogJycsXHJcbiAgICBsaWZlUGFydG5lcjogJycsXHJcbiAgICB3ZWRkaW5nU3R5bGU6ICcnLFxyXG4gICAgcGxhY2U6ICcnLFxyXG4gICAgZXZlbnRUeXBlOiAnJyxcclxuICAgIGJ1ZGdldDogJydcclxuICB9KTtcclxuXHJcbiAgLy8gQXV0by1zZWxlY3QgdGhlIHR5cGUgaWYgaW5pdGlhbFR5cGUgaXMgcHJvdmlkZWRcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKGluaXRpYWxUeXBlKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdBdXRvLXNlbGVjdGluZyB0eXBlIGZyb20gaW5pdGlhbFR5cGU6JywgaW5pdGlhbFR5cGUpO1xyXG4gICAgICBoYW5kbGVUeXBlU2VsZWN0ZWQoaW5pdGlhbFR5cGUpO1xyXG4gICAgfVxyXG4gIH0sIFtdKTtcclxuXHJcbiAgLy8gU3RvcmUgdmVuZG9yIGRldGFpbHMgdG8gcGVyc2lzdCBiZXR3ZWVuIHNjcmVlbnNcclxuICBjb25zdCBbdmVuZG9yRGV0YWlsc0RhdGEsIHNldFZlbmRvckRldGFpbHNEYXRhXSA9IHVzZVN0YXRlPFJlY29yZDxzdHJpbmcsIFZlbmRvckRldGFpbEl0ZW0+Pih7XHJcbiAgICB2ZW51ZTogeyBuYW1lOiAnJywgbW9iaWxlTnVtYmVyOiAnJyB9LFxyXG4gICAgcGhvdG9ncmFwaGVyOiB7IG5hbWU6ICcnLCBtb2JpbGVOdW1iZXI6ICcnIH0sXHJcbiAgICBtYWtldXBBcnRpc3Q6IHsgbmFtZTogJycsIG1vYmlsZU51bWJlcjogJycgfSxcclxuICAgIGRlY29yYXRpb25zOiB7IG5hbWU6ICcnLCBtb2JpbGVOdW1iZXI6ICcnIH0sXHJcbiAgICBjYXRlcmVyOiB7IG5hbWU6ICcnLCBtb2JpbGVOdW1iZXI6ICcnIH1cclxuICB9KTtcclxuXHJcbiAgLy8gVXNlIHRoZSBuZXcgbWVkaWEgdXBsb2FkIGhvb2tcclxuICBjb25zdCB7IG11dGF0ZTogdXBsb2FkTWVkaWEsIGlzUGVuZGluZzogaXNVcGxvYWRpbmcgfSA9IHVzZU1lZGlhVXBsb2FkKCk7XHJcblxyXG4gIC8vIEhhbmRsZSBtZWRpYSB0eXBlIHNlbGVjdGlvblxyXG4gIGNvbnN0IGhhbmRsZVR5cGVTZWxlY3RlZCA9ICh0eXBlOiBzdHJpbmcpID0+IHtcclxuICAgIC8vIEZpcnN0LCBjb21wbGV0ZWx5IHJlc2V0IGV2ZXJ5dGhpbmdcclxuICAgIHJlc2V0VXBsb2FkKCk7XHJcbiAgICBzZXRQcmV2aWV3SW1hZ2UobnVsbCk7XHJcbiAgICBzZXRUaHVtYm5haWxJbWFnZShudWxsKTtcclxuXHJcbiAgICAvLyBUaGVuIHNldCB0aGUgbmV3IHR5cGVcclxuICAgIHNldFNlbGVjdGVkVHlwZSh0eXBlKTtcclxuICAgIGNvbnNvbGUubG9nKFwiU2VsZWN0ZWQgdHlwZTpcIiwgdHlwZSk7XHJcblxyXG4gICAgaWYgKFsnZmxhc2hlcycsICdnbGltcHNlcycsICdtb3ZpZXMnLCAncGhvdG9zJywgJ21vbWVudHMnXS5pbmNsdWRlcyh0eXBlKSkge1xyXG4gICAgICAvLyBGb3IgZXhwbGljaXQgdmlkZW8gdHlwZXMsIHBob3RvcywgYW5kIG1vbWVudHMsIHNldCB0aGUgYXBwcm9wcmlhdGUgbWVkaWEgdHlwZVxyXG4gICAgICBpZiAodHlwZSA9PT0gJ3Bob3RvcycpIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnU2V0dGluZyBtZWRpYSB0eXBlIHRvIHBob3RvIGZvcjonLCB0eXBlKTtcclxuICAgICAgICBzZXRNZWRpYVR5cGUoJ3Bob3RvJyk7XHJcbiAgICAgICAgc2V0TWVkaWFTdWJ0eXBlKCdwb3N0Jyk7XHJcbiAgICAgICAgLy8gR28gdG8gY2F0ZWdvcnkgc2VsZWN0aW9uIGZvciBwaG90b3NcclxuICAgICAgICBzZXRQaGFzZSgnY2F0ZWdvcnlTZWxlY3Rpb24nKTtcclxuICAgICAgfSBlbHNlIGlmICh0eXBlID09PSAnbW9tZW50cycpIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnU2V0dGluZyBtZWRpYSB0eXBlIGZvciBtb21lbnRzICh3aWxsIGJlIGRldGVybWluZWQgYnkgZmlsZSB0eXBlKScpO1xyXG4gICAgICAgIC8vIEZvciBtb21lbnRzLCB3ZSdsbCBzZXQgdGhlIG1lZGlhIHR5cGUgbGF0ZXIgYmFzZWQgb24gdGhlIGZpbGUgdHlwZSAocGhvdG8gb3IgdmlkZW8pXHJcbiAgICAgICAgc2V0TWVkaWFTdWJ0eXBlKCdzdG9yeScpO1xyXG4gICAgICAgIC8vIEZvciBtb21lbnRzLCBza2lwIGNhdGVnb3J5IHNlbGVjdGlvbiBhbmQgZ28gZGlyZWN0bHkgdG8gZmlsZSB1cGxvYWRcclxuICAgICAgICBjb25zb2xlLmxvZygnTW9tZW50cyBzZWxlY3RlZDogc2tpcHBpbmcgY2F0ZWdvcnkgc2VsZWN0aW9uLCBnb2luZyBkaXJlY3RseSB0byBmaWxlIHVwbG9hZCcpO1xyXG4gICAgICAgIGhhbmRsZUZpbGVVcGxvYWQoKTtcclxuICAgICAgICByZXR1cm47IC8vIEVhcmx5IHJldHVybiB0byBwcmV2ZW50IGZ1cnRoZXIgcHJvY2Vzc2luZ1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHNldE1lZGlhVHlwZSgndmlkZW8nKTtcclxuICAgICAgICBzZXRNZWRpYVN1YnR5cGUoZ2V0TWVkaWFTdWJ0eXBlRnJvbVNlbGVjdGVkVHlwZSh0eXBlKSk7XHJcbiAgICAgICAgLy8gR28gdG8gY2F0ZWdvcnkgc2VsZWN0aW9uIGZvciB2aWRlb3NcclxuICAgICAgICBzZXRQaGFzZSgnY2F0ZWdvcnlTZWxlY3Rpb24nKTtcclxuICAgICAgfVxyXG4gICAgfSBlbHNlIGlmICh0eXBlID09PSAncGhvdG8nKSB7XHJcbiAgICAgIC8vIEZvciBzaW5nbGUgcGhvdG8gdHlwZSAoaWYgaXQgZXhpc3RzKVxyXG4gICAgICBjb25zb2xlLmxvZygnU2V0dGluZyBtZWRpYSB0eXBlIHRvIHBob3RvIGZvcjonLCB0eXBlKTtcclxuICAgICAgc2V0TWVkaWFUeXBlKCdwaG90bycpO1xyXG4gICAgICBzZXRNZWRpYVN1YnR5cGUoJ3Bvc3QnKTtcclxuICAgICAgLy8gVXNlIGEgc3BlY2lhbCBwaG90by1vbmx5IHVwbG9hZCBoYW5kbGVyIGZvciBwaG90b3NcclxuICAgICAgaGFuZGxlUGhvdG9VcGxvYWQoKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyBIZWxwZXIgZnVuY3Rpb24gdG8gZ2V0IHRoZSBiYWNrZW5kIG1lZGlhIHN1YnR5cGUgZnJvbSB0aGUgc2VsZWN0ZWQgVUkgdHlwZVxyXG4gIGNvbnN0IGdldE1lZGlhU3VidHlwZUZyb21TZWxlY3RlZFR5cGUgPSAodHlwZTogc3RyaW5nKTogc3RyaW5nID0+IHtcclxuICAgIC8vIE1hcCBVSSBjYXRlZ29yeSB0byBiYWNrZW5kIGNhdGVnb3J5IGZvciBtZWRpYV9zdWJ0eXBlXHJcbiAgICBzd2l0Y2ggKHR5cGUpIHtcclxuICAgICAgLy8gUGhvdG8gdHlwZXNcclxuICAgICAgY2FzZSAnbW9tZW50cyc6XHJcbiAgICAgICAgcmV0dXJuICdzdG9yeSc7ICAvLyBCYWNrZW5kIGV4cGVjdHMgJ3N0b3J5JyBmb3IgbW9tZW50c1xyXG4gICAgICBjYXNlICdwaG90b3MnOlxyXG4gICAgICAgIHJldHVybiAncG9zdCc7ICAgLy8gQmFja2VuZCBleHBlY3RzICdwb3N0JyBmb3IgcmVndWxhciBwaG90b3NcclxuXHJcbiAgICAgIC8vIFZpZGVvIHR5cGVzXHJcbiAgICAgIGNhc2UgJ2ZsYXNoZXMnOlxyXG4gICAgICAgIHJldHVybiAnZmxhc2gnOyAgLy8gQmFja2VuZCBleHBlY3RzICdmbGFzaCdcclxuICAgICAgY2FzZSAnZ2xpbXBzZXMnOlxyXG4gICAgICAgIHJldHVybiAnZ2xpbXBzZSc7ICAvLyBCYWNrZW5kIGV4cGVjdHMgJ2dsaW1wc2UnXHJcbiAgICAgIGNhc2UgJ21vdmllcyc6XHJcbiAgICAgICAgcmV0dXJuICdtb3ZpZSc7ICAvLyBCYWNrZW5kIGV4cGVjdHMgJ21vdmllJ1xyXG5cclxuICAgICAgLy8gRGVmYXVsdCBmYWxsYmFja1xyXG4gICAgICBkZWZhdWx0OlxyXG4gICAgICAgIHJldHVybiB0eXBlID09PSAnbW9tZW50cycgPyAnc3RvcnknIDogJ3Bvc3QnOyAgLy8gRGVmYXVsdCBiYXNlZCBvbiB0eXBlXHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgLy8gSGFuZGxlIGNhdGVnb3J5IHNlbGVjdGlvbiBmb3IgYm90aCB2aWRlb3MgYW5kIHBob3Rvc1xyXG4gIGNvbnN0IGhhbmRsZUNhdGVnb3J5U2VsZWN0ZWQgPSAoY2F0ZWdvcnk6IHN0cmluZykgPT4ge1xyXG4gICAgLy8gRmlyc3QsIG1ha2Ugc3VyZSB3ZSBoYXZlIGEgY2xlYW4gc3RhdGUgZm9yIHRoZSBuZXcgdXBsb2FkXHJcbiAgICAvLyBidXQgcHJlc2VydmUgdGhlIHNlbGVjdGVkIHR5cGUgYW5kIG1lZGlhIHR5cGVcclxuICAgIGNvbnN0IGN1cnJlbnRUeXBlID0gc2VsZWN0ZWRUeXBlO1xyXG4gICAgY29uc3QgY3VycmVudE1lZGlhVHlwZSA9IHN0YXRlLm1lZGlhVHlwZTtcclxuICAgIHJlc2V0VXBsb2FkKCk7XHJcbiAgICBzZXRTZWxlY3RlZFR5cGUoY3VycmVudFR5cGUpO1xyXG4gICAgc2V0TWVkaWFUeXBlKGN1cnJlbnRNZWRpYVR5cGUpO1xyXG4gICAgc2V0UHJldmlld0ltYWdlKG51bGwpO1xyXG4gICAgc2V0VGh1bWJuYWlsSW1hZ2UobnVsbCk7XHJcblxyXG4gICAgLy8gTm93IHNldCB0aGUgbmV3IGNhdGVnb3J5XHJcbiAgICBzZXRTZWxlY3RlZENhdGVnb3J5KGNhdGVnb3J5KTtcclxuXHJcbiAgICAvLyBHZXQgdGhlIG1lZGlhIHN1YnR5cGUgYmFzZWQgb24gdGhlIHNlbGVjdGVkIHR5cGVcclxuICAgIGxldCBtZWRpYVN1YnR5cGU7XHJcbiAgICBpZiAoY3VycmVudFR5cGUgPT09ICdwaG90b3MnKSB7XHJcbiAgICAgIC8vIEZvciBwaG90b3MsIGFsd2F5cyB1c2UgJ3Bvc3QnIGFzIHRoZSBtZWRpYSBzdWJ0eXBlXHJcbiAgICAgIG1lZGlhU3VidHlwZSA9ICdwb3N0JztcclxuICAgICAgY29uc29sZS5sb2coYFVQTE9BRCBNQU5BR0VSIC0gVXNpbmcgbWVkaWEgc3VidHlwZSAncG9zdCcgZm9yIHBob3Rvc2ApO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgLy8gRm9yIHZpZGVvcywgdXNlIHRoZSBzdWJ0eXBlIGJhc2VkIG9uIHRoZSBzZWxlY3RlZCB0eXBlXHJcbiAgICAgIG1lZGlhU3VidHlwZSA9IGdldE1lZGlhU3VidHlwZUZyb21TZWxlY3RlZFR5cGUoc2VsZWN0ZWRUeXBlKTtcclxuICAgICAgY29uc29sZS5sb2coYFVQTE9BRCBNQU5BR0VSIC0gVXNpbmcgbWVkaWEgc3VidHlwZSAke21lZGlhU3VidHlwZX0gYmFzZWQgb24gc2VsZWN0ZWQgdHlwZSAke3NlbGVjdGVkVHlwZX1gKTtcclxuICAgICAgY29uc29sZS5sb2coYFVQTE9BRCBNQU5BR0VSIC0gQmFja2VuZCBleHBlY3RzOiBmbGFzaGVz4oaSZmxhc2gsIGdsaW1wc2Vz4oaSZ2xpbXBzZSwgbW92aWVz4oaSbW92aWUsIG1vbWVudHPihpJzdG9yeWApO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnNvbGUubG9nKFwiVVBMT0FEIE1BTkFHRVIgLSBTZWxlY3RlZCBjYXRlZ29yeTpcIiwgY2F0ZWdvcnkpO1xyXG4gICAgY29uc29sZS5sb2coXCJVUExPQUQgTUFOQUdFUiAtIFNldHRpbmcgbWVkaWEgc3VidHlwZSB0bzpcIiwgbWVkaWFTdWJ0eXBlKTtcclxuXHJcbiAgICAvLyBTZXQgdGhlIG1lZGlhIHN1YnR5cGUgaW4gdGhlIGNvbnRleHRcclxuICAgIHNldE1lZGlhU3VidHlwZShtZWRpYVN1YnR5cGUpO1xyXG5cclxuICAgIC8vIE1hcCB0aGUgc2VsZWN0ZWQgY2F0ZWdvcnkgdG8gYSB2YWxpZCBiYWNrZW5kIHZpZGVvX2NhdGVnb3J5XHJcbiAgICBsZXQgYmFja2VuZFZpZGVvQ2F0ZWdvcnkgPSAnJztcclxuXHJcbiAgICBpZiAoY2F0ZWdvcnkgPT09ICdteV93ZWRkaW5nX3ZpZGVvcycpIHtcclxuICAgICAgYmFja2VuZFZpZGVvQ2F0ZWdvcnkgPSAnbXlfd2VkZGluZyc7XHJcbiAgICB9IGVsc2UgaWYgKGNhdGVnb3J5ID09PSAnd2VkZGluZ192bG9nJykge1xyXG4gICAgICBiYWNrZW5kVmlkZW9DYXRlZ29yeSA9ICd3ZWRkaW5nX3Zsb2cnO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIE1ha2Ugc3VyZSB3ZSBoYXZlIGEgdmFsaWQgdmlkZW9fY2F0ZWdvcnlcclxuICAgIGlmICghYmFja2VuZFZpZGVvQ2F0ZWdvcnkpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignSW52YWxpZCB2aWRlbyBjYXRlZ29yeSBzZWxlY3RlZDonLCBjYXRlZ29yeSk7XHJcbiAgICAgIGFsZXJ0KCdQbGVhc2Ugc2VsZWN0IGEgdmFsaWQgdmlkZW8gY2F0ZWdvcnknKTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIFNldCB2aWRlbyBjYXRlZ29yeSBpbiB0aGUgY29udGV4dCBmb3IgdGhlIGJhY2tlbmRcclxuICAgIGNvbnNvbGUubG9nKFwiVVBMT0FEIE1BTkFHRVIgLSBTZXR0aW5nIHZpZGVvX2NhdGVnb3J5IHRvOlwiLCBiYWNrZW5kVmlkZW9DYXRlZ29yeSk7XHJcbiAgICBzZXREZXRhaWxGaWVsZCgndmlkZW9fY2F0ZWdvcnknLCBiYWNrZW5kVmlkZW9DYXRlZ29yeSk7XHJcblxyXG4gICAgLy8gTG9nIHRoZSBmaW5hbCB2YWx1ZXNcclxuICAgIGNvbnNvbGUubG9nKFwiVVBMT0FEIE1BTkFHRVIgLSBTZWxlY3RlZCBjYXRlZ29yeTpcIiwgY2F0ZWdvcnkpO1xyXG4gICAgY29uc29sZS5sb2coXCJVUExPQUQgTUFOQUdFUiAtIEJhY2tlbmQgdmlkZW8gY2F0ZWdvcnkgc2V0IHRvOlwiLCBiYWNrZW5kVmlkZW9DYXRlZ29yeSk7XHJcbiAgICBjb25zb2xlLmxvZyhcIlVQTE9BRCBNQU5BR0VSIC0gTWVkaWEgc3VidHlwZSBzZXQgdG86XCIsIG1lZGlhU3VidHlwZSk7XHJcblxyXG4gICAgLy8gUHJvY2VlZCB0byBmaWxlIHVwbG9hZCBhZnRlciBzZXR0aW5nIHRoZSBjYXRlZ29yeVxyXG4gICAgaWYgKGN1cnJlbnRUeXBlID09PSAncGhvdG9zJykge1xyXG4gICAgICAvLyBGb3IgcGhvdG9zLCB1c2UgdGhlIHBob3RvLXNwZWNpZmljIHVwbG9hZCBoYW5kbGVyXHJcbiAgICAgIGhhbmRsZVBob3RvVXBsb2FkKCk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICAvLyBGb3IgdmlkZW9zLCB1c2UgdGhlIHN0YW5kYXJkIGZpbGUgdXBsb2FkIGhhbmRsZXJcclxuICAgICAgaGFuZGxlRmlsZVVwbG9hZCgpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIEhhbmRsZSB0aHVtYm5haWwgdXBsb2FkXHJcbiAgY29uc3QgaGFuZGxlVGh1bWJuYWlsVXBsb2FkID0gKCkgPT4ge1xyXG4gICAgLy8gQ3JlYXRlIGEgZmlsZSBpbnB1dCBlbGVtZW50XHJcbiAgICBjb25zdCBpbnB1dCA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2lucHV0Jyk7XHJcbiAgICBpbnB1dC50eXBlID0gJ2ZpbGUnO1xyXG4gICAgaW5wdXQuYWNjZXB0ID0gJ2ltYWdlLyonO1xyXG5cclxuICAgIC8vIEhhbmRsZSBmaWxlIHNlbGVjdGlvblxyXG4gICAgaW5wdXQub25jaGFuZ2UgPSAoZSkgPT4ge1xyXG4gICAgICBjb25zdCBmaWxlcyA9IChlLnRhcmdldCBhcyBIVE1MSW5wdXRFbGVtZW50KS5maWxlcztcclxuICAgICAgaWYgKGZpbGVzICYmIGZpbGVzLmxlbmd0aCA+IDApIHtcclxuICAgICAgICBjb25zdCBmaWxlID0gZmlsZXNbMF07XHJcblxyXG4gICAgICAgIC8vIFN0b3JlIHRoZSB0aHVtYm5haWxcclxuICAgICAgICBzZXRUaHVtYm5haWxJbWFnZShmaWxlKTtcclxuICAgICAgICBzZXRUaHVtYm5haWwoZmlsZSk7XHJcblxyXG4gICAgICAgIGNvbnNvbGUubG9nKFwiVGh1bWJuYWlsIHNlbGVjdGVkOlwiLCBmaWxlLm5hbWUpO1xyXG5cclxuICAgICAgICAvLyBTaG93IGEgcHJldmlldyBpZiBuZWVkZWRcclxuICAgICAgICBjb25zdCByZWFkZXIgPSBuZXcgRmlsZVJlYWRlcigpO1xyXG4gICAgICAgIHJlYWRlci5vbmxvYWQgPSAoZSkgPT4ge1xyXG4gICAgICAgICAgaWYgKGUudGFyZ2V0Py5yZXN1bHQpIHtcclxuICAgICAgICAgICAgLy8gWW91IGNvdWxkIHNldCBhIHRodW1ibmFpbCBwcmV2aWV3IGhlcmUgaWYgbmVlZGVkXHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKFwiVGh1bWJuYWlsIHByZXZpZXcgcmVhZHlcIik7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfTtcclxuICAgICAgICByZWFkZXIucmVhZEFzRGF0YVVSTChmaWxlKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICAvLyBUcmlnZ2VyIHRoZSBmaWxlIGRpYWxvZ1xyXG4gICAgaW5wdXQuY2xpY2soKTtcclxuICB9O1xyXG5cclxuICAvLyBHZXQgdXNlci1mcmllbmRseSBkaXNwbGF5IG5hbWUgZm9yIGEgY2F0ZWdvcnlcclxuICBjb25zdCBnZXRDYXRlZ29yeURpc3BsYXlOYW1lID0gKGNhdGVnb3J5OiBzdHJpbmcpOiBzdHJpbmcgPT4ge1xyXG4gICAgc3dpdGNoIChjYXRlZ29yeSkge1xyXG4gICAgICBjYXNlICdmbGFzaCc6XHJcbiAgICAgICAgcmV0dXJuICdGbGFzaCc7XHJcbiAgICAgIGNhc2UgJ2dsaW1wc2UnOlxyXG4gICAgICAgIHJldHVybiAnR2xpbXBzZSc7XHJcbiAgICAgIGNhc2UgJ21vdmllJzpcclxuICAgICAgICByZXR1cm4gJ01vdmllJztcclxuICAgICAgY2FzZSAnc3RvcnknOlxyXG4gICAgICAgIHJldHVybiAnU3RvcnknO1xyXG4gICAgICBjYXNlICdwb3N0JzpcclxuICAgICAgICByZXR1cm4gJ1Bob3RvJztcclxuICAgICAgZGVmYXVsdDpcclxuICAgICAgICByZXR1cm4gY2F0ZWdvcnkuY2hhckF0KDApLnRvVXBwZXJDYXNlKCkgKyBjYXRlZ29yeS5zbGljZSgxKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyBHZXQgYXBwcm9wcmlhdGUgY2F0ZWdvcnkgYmFzZWQgb24gZHVyYXRpb25cclxuICBjb25zdCBnZXRBcHByb3ByaWF0ZUNhdGVnb3J5ID0gKGR1cmF0aW9uOiBudW1iZXIpOiBzdHJpbmcgPT4ge1xyXG4gICAgLy8gRm9yIHZlcnkgc2hvcnQgdmlkZW9zICgxIG1pbnV0ZSBvciBsZXNzKSwgdXNlIGZsYXNoIGluc3RlYWQgb2Ygc3RvcnkvbW9tZW50c1xyXG4gICAgaWYgKGR1cmF0aW9uIDw9IDYwKSB7XHJcbiAgICAgIHJldHVybiAnZmxhc2gnOyAvLyBWZXJ5IHNob3J0IHZpZGVvcyAoMSBtaW51dGUgb3IgbGVzcykgLSBjaGFuZ2VkIGZyb20gJ3N0b3J5JyB0byAnZmxhc2gnXHJcbiAgICB9IGVsc2UgaWYgKGR1cmF0aW9uIDw9IDkwKSB7XHJcbiAgICAgIHJldHVybiAnZmxhc2gnOyAvLyBTaG9ydCB2aWRlb3MgKDEuNSBtaW51dGVzIG9yIGxlc3MpXHJcbiAgICB9IGVsc2UgaWYgKGR1cmF0aW9uIDw9IDQyMCkge1xyXG4gICAgICByZXR1cm4gJ2dsaW1wc2UnOyAvLyBNZWRpdW0gdmlkZW9zICg3IG1pbnV0ZXMgb3IgbGVzcylcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHJldHVybiAnbW92aWUnOyAvLyBMb25nIHZpZGVvcyAob3ZlciA3IG1pbnV0ZXMpXHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgLy8gU3BlY2lhbCBoYW5kbGVyIGZvciBwaG90byB1cGxvYWRzIHRoYXQgc3RyaWN0bHkgZW5mb3JjZXMgaW1hZ2Utb25seSBmaWxlc1xyXG4gIGNvbnN0IGhhbmRsZVBob3RvVXBsb2FkID0gKCkgPT4ge1xyXG4gICAgY29uc29sZS5sb2coJ2hhbmRsZVBob3RvVXBsb2FkIGNhbGxlZCAtIHN0cmljdCBpbWFnZS1vbmx5IHVwbG9hZCcpO1xyXG5cclxuICAgIC8vIENyZWF0ZSBhIGZpbGUgaW5wdXQgZWxlbWVudCBzcGVjaWZpY2FsbHkgZm9yIHBob3Rvc1xyXG4gICAgY29uc3QgaW5wdXQgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdpbnB1dCcpO1xyXG4gICAgaW5wdXQudHlwZSA9ICdmaWxlJztcclxuXHJcbiAgICAvLyBSZXNldCB0aGUgaW5wdXQgdmFsdWVcclxuICAgIGlucHV0LnZhbHVlID0gJyc7XHJcblxyXG4gICAgLy8gT25seSBhY2NlcHQgaW1hZ2UgZmlsZXMgLSBleHBsaWNpdGx5IGxpc3QgYWxsb3dlZCB0eXBlc1xyXG4gICAgaW5wdXQuYWNjZXB0ID0gJ2ltYWdlL2pwZWcsaW1hZ2UvcG5nLGltYWdlL2dpZixpbWFnZS93ZWJwJztcclxuXHJcbiAgICAvLyBIYW5kbGUgZmlsZSBzZWxlY3Rpb24gd2l0aCBzdHJpY3QgdmFsaWRhdGlvblxyXG4gICAgaW5wdXQub25jaGFuZ2UgPSBhc3luYyAoZSkgPT4ge1xyXG4gICAgICBjb25zdCBmaWxlcyA9IChlLnRhcmdldCBhcyBIVE1MSW5wdXRFbGVtZW50KS5maWxlcztcclxuICAgICAgaWYgKCFmaWxlcyB8fCBmaWxlcy5sZW5ndGggPT09IDApIHJldHVybjtcclxuXHJcbiAgICAgIGNvbnN0IGZpbGUgPSBmaWxlc1swXTtcclxuICAgICAgY29uc29sZS5sb2coJ1Bob3RvIGZpbGUgc2VsZWN0ZWQ6JywgZmlsZS5uYW1lLCBmaWxlLnR5cGUsIGZpbGUuc2l6ZSk7XHJcblxyXG4gICAgICAvLyBTdHJpY3QgdmFsaWRhdGlvbiAtIG11c3QgYmUgYW4gaW1hZ2UgZmlsZVxyXG4gICAgICBjb25zdCB2YWxpZEltYWdlVHlwZXMgPSBbJ2ltYWdlL2pwZWcnLCAnaW1hZ2UvcG5nJywgJ2ltYWdlL2dpZicsICdpbWFnZS93ZWJwJ107XHJcblxyXG4gICAgICBpZiAoIXZhbGlkSW1hZ2VUeXBlcy5pbmNsdWRlcyhmaWxlLnR5cGUpKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignSW52YWxpZCBmaWxlIHR5cGUgZm9yIHBob3RvczonLCBmaWxlLnR5cGUpO1xyXG4gICAgICAgIGFsZXJ0KCdPbmx5IEpQRUcsIFBORywgR0lGLCBvciBXZWJQIGltYWdlcyBjYW4gYmUgdXBsb2FkZWQgYXMgcGhvdG9zLiBWaWRlb3MgYXJlIG5vdCBhbGxvd2VkLicpO1xyXG4gICAgICAgIHJldHVybjtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gQWRkaXRpb25hbCBjaGVjayAtIHJlamVjdCBhbnkgZmlsZSB0aGF0IG1pZ2h0IGJlIGEgdmlkZW9cclxuICAgICAgaWYgKGZpbGUudHlwZS5zdGFydHNXaXRoKCd2aWRlby8nKSkge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0F0dGVtcHRlZCB0byB1cGxvYWQgYSB2aWRlbyBmaWxlIGFzIHBob3RvJyk7XHJcbiAgICAgICAgYWxlcnQoJ1ZpZGVvcyBjYW5ub3QgYmUgdXBsb2FkZWQgYXMgcGhvdG9zLiBQbGVhc2Ugc2VsZWN0IGFuIGltYWdlIGZpbGUuJyk7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBGb3IgcGhvdG9zLCB3ZSBuZWVkIHRvIGJlIG1vcmUgY2FyZWZ1bCB3aXRoIHN0YXRlIG1hbmFnZW1lbnRcclxuICAgICAgLy8gRmlyc3QsIHNldCB0aGUgbWVkaWEgdHlwZSBhbmQgc3VidHlwZVxyXG4gICAgICBzZXRNZWRpYVR5cGUoJ3Bob3RvJyk7XHJcbiAgICAgIHNldE1lZGlhU3VidHlwZSgncG9zdCcpO1xyXG5cclxuICAgICAgLy8gVGhlbiBzZXQgdGhlIGZpbGUgaW4gdGhlIHN0YXRlXHJcbiAgICAgIHNldEZpbGUoZmlsZSk7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdQaG90byBmaWxlIHNldCBpbiBzdGF0ZTonLCBmaWxlLm5hbWUpO1xyXG5cclxuICAgICAgLy8gQ3JlYXRlIGEgbG9jYWwgcmVmZXJlbmNlIHRvIHRoZSBmaWxlIGZvciB1c2UgaW4gdGhlIHRpbWVvdXRcclxuICAgICAgY29uc3QgY3VycmVudEZpbGUgPSBmaWxlO1xyXG5cclxuICAgICAgLy8gRG91YmxlLWNoZWNrIHRoYXQgdGhlIGZpbGUgaXMgc2V0IGluIHRoZSBzdGF0ZSBiZWZvcmUgcHJvY2VlZGluZ1xyXG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICAvLyBDaGVjayBpZiB0aGUgZmlsZSBpcyBpbiB0aGUgc3RhdGVcclxuICAgICAgICBpZiAoIXN0YXRlLmZpbGUpIHtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdGaWxlIG5vdCBmb3VuZCBpbiBzdGF0ZSBhZnRlciBzZXR0aW5nLCB0cnlpbmcgYWdhaW4nKTtcclxuICAgICAgICAgIC8vIFRyeSBzZXR0aW5nIHRoZSBmaWxlIGFnYWluXHJcbiAgICAgICAgICBzZXRGaWxlKGN1cnJlbnRGaWxlKTtcclxuXHJcbiAgICAgICAgICAvLyBBZGQgYW5vdGhlciB0aW1lb3V0IHRvIGVuc3VyZSB0aGUgZmlsZSBpcyBzZXRcclxuICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgICBpZiAoIXN0YXRlLmZpbGUpIHtcclxuICAgICAgICAgICAgICBjb25zb2xlLmxvZygnRmlsZSBzdGlsbCBub3QgaW4gc3RhdGUsIHNldHRpbmcgaXQgb25lIG1vcmUgdGltZScpO1xyXG4gICAgICAgICAgICAgIHNldEZpbGUoY3VycmVudEZpbGUpO1xyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdGaWxlIGNvbmZpcm1lZCBpbiBzdGF0ZSBhZnRlciBzZWNvbmQgYXR0ZW1wdDonLCBzdGF0ZS5maWxlLm5hbWUpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIC8vIE5ldyBmbG93IGxvZ2ljOiBGb3IgbW9tZW50cyAoc3RvcmllcyksIHNraXAgcGVyc29uYWwgZGV0YWlscyBhbmQgZ28gZGlyZWN0bHkgdG8gZmFjZSB2ZXJpZmljYXRpb25cclxuICAgICAgICAgICAgaWYgKHNlbGVjdGVkVHlwZSA9PT0gJ21vbWVudHMnIHx8IHN0YXRlLm1lZGlhU3VidHlwZSA9PT0gJ3N0b3J5Jykge1xyXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdNb21lbnRzIHBob3RvIHVwbG9hZDogc2tpcHBpbmcgcGVyc29uYWwgZGV0YWlscywgZ29pbmcgZGlyZWN0bHkgdG8gZmFjZSB2ZXJpZmljYXRpb24nKTtcclxuICAgICAgICAgICAgICBzZXRQaGFzZSgnZmFjZVZlcmlmaWNhdGlvbicpO1xyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdNb3ZpbmcgdG8gcGVyc29uYWxEZXRhaWxzIHBoYXNlIGZvciBwaG90bycpO1xyXG4gICAgICAgICAgICAgIHNldFBoYXNlKCdwZXJzb25hbERldGFpbHMnKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfSwgMTAwKTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ0ZpbGUgY29uZmlybWVkIGluIHN0YXRlOicsIHN0YXRlLmZpbGUubmFtZSk7XHJcbiAgICAgICAgICAvLyBOZXcgZmxvdyBsb2dpYzogRm9yIG1vbWVudHMgKHN0b3JpZXMpLCBza2lwIHBlcnNvbmFsIGRldGFpbHMgYW5kIGdvIGRpcmVjdGx5IHRvIGZhY2UgdmVyaWZpY2F0aW9uXHJcbiAgICAgICAgICBpZiAoc2VsZWN0ZWRUeXBlID09PSAnbW9tZW50cycgfHwgc3RhdGUubWVkaWFTdWJ0eXBlID09PSAnc3RvcnknKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdNb21lbnRzIHBob3RvIHVwbG9hZDogc2tpcHBpbmcgcGVyc29uYWwgZGV0YWlscywgZ29pbmcgZGlyZWN0bHkgdG8gZmFjZSB2ZXJpZmljYXRpb24nKTtcclxuICAgICAgICAgICAgc2V0UGhhc2UoJ2ZhY2VWZXJpZmljYXRpb24nKTtcclxuICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdNb3ZpbmcgdG8gcGVyc29uYWxEZXRhaWxzIHBoYXNlIGZvciBwaG90bycpO1xyXG4gICAgICAgICAgICBzZXRQaGFzZSgncGVyc29uYWxEZXRhaWxzJyk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9LCAxMDApO1xyXG5cclxuICAgICAgLy8gSGFuZGxlIGltYWdlIHByZXZpZXdcclxuICAgICAgY29uc3QgcmVhZGVyID0gbmV3IEZpbGVSZWFkZXIoKTtcclxuICAgICAgcmVhZGVyLm9ubG9hZCA9IChlKSA9PiB7XHJcbiAgICAgICAgaWYgKGUudGFyZ2V0Py5yZXN1bHQpIHtcclxuICAgICAgICAgIHNldFByZXZpZXdJbWFnZShlLnRhcmdldC5yZXN1bHQgYXMgc3RyaW5nKTtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdQcmV2aWV3IGltYWdlIHNldCBmb3IgcGhvdG8nKTtcclxuICAgICAgICB9XHJcbiAgICAgIH07XHJcbiAgICAgIHJlYWRlci5yZWFkQXNEYXRhVVJMKGZpbGUpO1xyXG5cclxuICAgICAgLy8gTm90ZTogV2UgZG9uJ3Qgc2V0IHRoZSBwaGFzZSBoZXJlIGFueW1vcmUgLSBpdCdzIGhhbmRsZWQgaW4gdGhlIHRpbWVvdXQgYWJvdmVcclxuICAgIH07XHJcblxyXG4gICAgLy8gVHJpZ2dlciB0aGUgZmlsZSBkaWFsb2dcclxuICAgIGlucHV0LmNsaWNrKCk7XHJcbiAgfTtcclxuXHJcbiAgLy8gVGhpcyBmdW5jdGlvbiB3YXMgcHJldmlvdXNseSB1c2VkIGJ1dCBpcyBub3cgcmVwbGFjZWQgYnkgZ2V0QXBwcm9wcmlhdGVDYXRlZ29yeVxyXG4gIC8vIEtlZXBpbmcgYSBjb21tZW50IGhlcmUgZm9yIHJlZmVyZW5jZSBpbiBjYXNlIGl0IG5lZWRzIHRvIGJlIHJlc3RvcmVkXHJcblxyXG4gIC8vIEhhbmRsZSBtYW51YWwgdXBsb2FkIGJ1dHRvbiBjbGlja1xyXG4gIGNvbnN0IGhhbmRsZUZpbGVVcGxvYWQgPSBhc3luYyAoY2F0ZWdvcnk/OiBzdHJpbmcpID0+IHtcclxuICAgIGNvbnNvbGUubG9nKCdoYW5kbGVGaWxlVXBsb2FkIGNhbGxlZCB3aXRoIGNhdGVnb3J5OicsIGNhdGVnb3J5IHx8ICdub25lJyk7XHJcblxyXG4gICAgLy8gQ3JlYXRlIGEgZmlsZSBpbnB1dCBlbGVtZW50XHJcbiAgICBjb25zdCBpbnB1dCA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2lucHV0Jyk7XHJcbiAgICBpbnB1dC50eXBlID0gJ2ZpbGUnO1xyXG5cclxuICAgIC8vIFJlc2V0IHRoZSBpbnB1dCB2YWx1ZSB0byBlbnN1cmUgd2UgZ2V0IGEgbmV3IGZpbGUgc2VsZWN0aW9uIGV2ZW50IGV2ZW4gaWYgdGhlIHNhbWUgZmlsZSBpcyBzZWxlY3RlZFxyXG4gICAgaW5wdXQudmFsdWUgPSAnJztcclxuXHJcbiAgICBpZiAoc2VsZWN0ZWRUeXBlID09PSAnbW9tZW50cycpIHtcclxuICAgICAgaW5wdXQuYWNjZXB0ID0gJ2ltYWdlLyosdmlkZW8vKic7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBpbnB1dC5hY2NlcHQgPSBzZWxlY3RlZFR5cGUgPT09ICdwaG90bycgfHwgc2VsZWN0ZWRUeXBlID09PSAncGhvdG9zJ1xyXG4gICAgICAgID8gJ2ltYWdlL2pwZWcsaW1hZ2UvcG5nLGltYWdlL2dpZixpbWFnZS93ZWJwJyAvLyBFeHBsaWNpdGx5IGxpc3QgaW1hZ2UgdHlwZXMgb25seVxyXG4gICAgICAgIDogJ3ZpZGVvLyonO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIEhhbmRsZSBmaWxlIHNlbGVjdGlvblxyXG4gICAgaW5wdXQub25jaGFuZ2UgPSBhc3luYyAoZSkgPT4ge1xyXG4gICAgICBjb25zdCBmaWxlcyA9IChlLnRhcmdldCBhcyBIVE1MSW5wdXRFbGVtZW50KS5maWxlcztcclxuICAgICAgaWYgKCFmaWxlcyB8fCBmaWxlcy5sZW5ndGggPT09IDApIHJldHVybjtcclxuXHJcbiAgICAgIGNvbnN0IGZpbGUgPSBmaWxlc1swXTtcclxuICAgICAgY29uc29sZS5sb2coJ0ZpbGUgc2VsZWN0ZWQ6JywgZmlsZS5uYW1lLCBmaWxlLnR5cGUsIGZpbGUuc2l6ZSk7XHJcblxyXG4gICAgICAvLyBTdHJpY3QgdmFsaWRhdGlvbiBmb3IgcGhvdG8gdXBsb2FkcyAtIG11c3QgYmUgYW4gaW1hZ2UgZmlsZVxyXG4gICAgICBpZiAoc2VsZWN0ZWRUeXBlID09PSAncGhvdG8nIHx8IHNlbGVjdGVkVHlwZSA9PT0gJ3Bob3RvcycpIHtcclxuICAgICAgICBjb25zdCB2YWxpZEltYWdlVHlwZXMgPSBbJ2ltYWdlL2pwZWcnLCAnaW1hZ2UvcG5nJywgJ2ltYWdlL2dpZicsICdpbWFnZS93ZWJwJ107XHJcblxyXG4gICAgICAgIC8vIENoZWNrIGlmIGZpbGUgaXMgYSB2aWRlbyBvciBub3QgYSB2YWxpZCBpbWFnZSB0eXBlXHJcbiAgICAgICAgaWYgKGZpbGUudHlwZS5zdGFydHNXaXRoKCd2aWRlby8nKSB8fCAhdmFsaWRJbWFnZVR5cGVzLmluY2x1ZGVzKGZpbGUudHlwZSkpIHtcclxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ludmFsaWQgZmlsZSB0eXBlIGZvciBwaG90b3M6JywgZmlsZS50eXBlKTtcclxuICAgICAgICAgIHNob3dFcnJvckFsZXJ0KCdJbnZhbGlkIEZpbGUgVHlwZScsICdPbmx5IEpQRUcsIFBORywgR0lGLCBvciBXZWJQIGltYWdlcyBjYW4gYmUgdXBsb2FkZWQgYXMgcGhvdG9zLiBWaWRlb3MgYXJlIG5vdCBhbGxvd2VkLicpO1xyXG4gICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gUmVzZXQgdGhlIHVwbG9hZCBjb250ZXh0IGJlZm9yZSBzZXR0aW5nIHRoZSBuZXcgZmlsZVxyXG4gICAgICByZXNldFVwbG9hZCgpO1xyXG5cclxuICAgICAgLy8gU2V0IHRoZSBmaWxlIGluIHRoZSBzdGF0ZVxyXG4gICAgICBzZXRGaWxlKGZpbGUpO1xyXG4gICAgICBjb25zb2xlLmxvZygnRmlsZSBzZXQgaW4gc3RhdGU6JywgZmlsZS5uYW1lKTtcclxuXHJcbiAgICAgIC8vIElmIGl0J3MgYSB2aWRlbywgY2FsY3VsYXRlIGFuZCBzZXQgdGhlIGR1cmF0aW9uXHJcbiAgICAgIC8vIERvdWJsZS1jaGVjayB0aGF0IHdlJ3JlIG5vdCB0cnlpbmcgdG8gdXBsb2FkIGEgdmlkZW8gYXMgYSBwaG90b1xyXG4gICAgICBpZiAoZmlsZS50eXBlLnN0YXJ0c1dpdGgoJ3ZpZGVvLycpKSB7XHJcbiAgICAgICAgLy8gU2FmZXR5IGNoZWNrIC0gZG9uJ3QgcHJvY2VzcyB2aWRlb3MgZm9yIHBob3RvIHVwbG9hZHNcclxuICAgICAgICBpZiAoc2VsZWN0ZWRUeXBlID09PSAncGhvdG8nIHx8IHNlbGVjdGVkVHlwZSA9PT0gJ3Bob3RvcycpIHtcclxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0F0dGVtcHRlZCB0byBwcm9jZXNzIGEgdmlkZW8gZmlsZSBmb3IgcGhvdG8gdXBsb2FkJyk7XHJcbiAgICAgICAgICBzaG93RXJyb3JBbGVydCgnSW52YWxpZCBGaWxlIFR5cGUnLCAnVmlkZW9zIGNhbm5vdCBiZSB1cGxvYWRlZCBhcyBwaG90b3MuIFBsZWFzZSBzZWxlY3QgYW4gaW1hZ2UgZmlsZS4nKTtcclxuICAgICAgICAgIHJlc2V0VXBsb2FkKCk7XHJcbiAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICBjb25zdCBkdXJhdGlvbiA9IGF3YWl0IGdldFZpZGVvRHVyYXRpb24oZmlsZSk7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnVmlkZW8gZHVyYXRpb24gY2FsY3VsYXRlZDonLCBkdXJhdGlvbik7XHJcbiAgICAgICAgICBzZXREdXJhdGlvbihkdXJhdGlvbik7XHJcblxyXG4gICAgICAgICAgLy8gRm9yIG1vbWVudHMsIGNoZWNrIGlmIGl0J3MgYSB2aWRlbyBhbmQgdmFsaWRhdGUgdGhlIGR1cmF0aW9uIChtYXggMSBtaW51dGUpXHJcbiAgICAgICAgICBpZiAoc2VsZWN0ZWRUeXBlID09PSAnbW9tZW50cycpIHtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coJ1ZhbGlkYXRpbmcgbW9tZW50cyB2aWRlbyBkdXJhdGlvbi4uLicpO1xyXG4gICAgICAgICAgICBzZXRNZWRpYVR5cGUoJ3ZpZGVvJyk7XHJcblxyXG4gICAgICAgICAgICAvLyBDaGVjayBpZiB0aGUgdmlkZW8gaXMgbG9uZ2VyIHRoYW4gMSBtaW51dGUgKDYwIHNlY29uZHMpXHJcbiAgICAgICAgICAgIGlmIChkdXJhdGlvbiA+IDYwKSB7XHJcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coYE1vbWVudHMgdmlkZW8gdG9vIGxvbmc6ICR7ZHVyYXRpb259IHNlY29uZHMgKG1heDogNjAgc2Vjb25kcylgKTtcclxuXHJcbiAgICAgICAgICAgICAgLy8gU2hvdyBhIG1vcmUgZGV0YWlsZWQgZXJyb3IgbWVzc2FnZSB3aXRoIGN1c3RvbSBhbGVydFxyXG4gICAgICAgICAgICAgIHNob3dXYXJuaW5nQWxlcnQoXHJcbiAgICAgICAgICAgICAgICAnTW9tZW50cyBWaWRlbyBUb28gTG9uZycsXHJcbiAgICAgICAgICAgICAgICBgTW9tZW50cyB2aWRlb3MgbXVzdCBiZSAxIG1pbnV0ZSBvciBsZXNzLlxcblxcbllvdXIgdmlkZW8gaXMgJHtmb3JtYXRUaW1lKGR1cmF0aW9uKX0gbG9uZy5cXG5cXG5QbGVhc2Ugc2VsZWN0IGEgc2hvcnRlciB2aWRlbyBvciB0cmltIHRoaXMgdmlkZW8gdG8gMSBtaW51dGUgb3IgbGVzcy5gXHJcbiAgICAgICAgICAgICAgKTtcclxuXHJcbiAgICAgICAgICAgICAgLy8gUmVzZXQgdGhlIHVwbG9hZCBjb250ZXh0IGJ1dCBwcmVzZXJ2ZSB0aGUgc2VsZWN0ZWQgdHlwZSBhbmQgY2F0ZWdvcnlcclxuICAgICAgICAgICAgICBjb25zdCBjdXJyZW50VHlwZSA9IHNlbGVjdGVkVHlwZTtcclxuICAgICAgICAgICAgICBjb25zdCBjdXJyZW50Q2F0ZWdvcnkgPSBzZWxlY3RlZENhdGVnb3J5O1xyXG5cclxuICAgICAgICAgICAgICAvLyBGaXJzdCBzZXQgdGhlIHBoYXNlIGJhY2sgdG8gY2F0ZWdvcnkgc2VsZWN0aW9uXHJcbiAgICAgICAgICAgICAgc2V0UGhhc2UoJ2NhdGVnb3J5U2VsZWN0aW9uJyk7XHJcblxyXG4gICAgICAgICAgICAgIC8vIFRoZW4gcmVzZXQgdGhlIHVwbG9hZCBzdGF0ZVxyXG4gICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgcmVzZXRVcGxvYWQoKTtcclxuICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkVHlwZShjdXJyZW50VHlwZSk7XHJcbiAgICAgICAgICAgICAgICBzZXRTZWxlY3RlZENhdGVnb3J5KGN1cnJlbnRDYXRlZ29yeSk7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnUmVzZXQgdXBsb2FkIHN0YXRlIGFmdGVyIG1vbWVudHMgdmlkZW8gZHVyYXRpb24gdmFsaWRhdGlvbiBmYWlsdXJlJyk7XHJcbiAgICAgICAgICAgICAgfSwgMTAwKTtcclxuXHJcbiAgICAgICAgICAgICAgLy8gUmV0dXJuIGVhcmx5IHRvIHByZXZlbnQgZnVydGhlciBwcm9jZXNzaW5nXHJcbiAgICAgICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhgTW9tZW50cyB2aWRlbyBkdXJhdGlvbiB2YWxpZDogJHtkdXJhdGlvbn0gc2Vjb25kcyAobWF4OiA2MCBzZWNvbmRzKWApO1xyXG4gICAgICAgICAgICAvLyBGb3IgbW9tZW50cywgd2UgYWx3YXlzIHVzZSAnc3RvcnknIGFzIHRoZSBtZWRpYSBzdWJ0eXBlXHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdTZXR0aW5nIG1lZGlhIHN1YnR5cGUgZm9yIG1vbWVudHMgdmlkZW8gdG8gc3RvcnknKTtcclxuICAgICAgICAgICAgc2V0TWVkaWFTdWJ0eXBlKCdzdG9yeScpO1xyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIC8vIElmIHdlIGhhdmUgYSBjYXRlZ29yeSwgdmFsaWRhdGUgdGhlIGR1cmF0aW9uIGZvciB0aGF0IGNhdGVnb3J5XHJcbiAgICAgICAgICBpZiAoc2VsZWN0ZWRUeXBlICYmIFsnZmxhc2hlcycsICdnbGltcHNlcycsICdtb3ZpZXMnXS5pbmNsdWRlcyhzZWxlY3RlZFR5cGUpKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IG1lZGlhU3VidHlwZSA9IGdldE1lZGlhU3VidHlwZUZyb21TZWxlY3RlZFR5cGUoc2VsZWN0ZWRUeXBlKTtcclxuICAgICAgICAgICAgY29uc3QgdmFsaWRhdGlvblJlc3VsdCA9IHZhbGlkYXRlVmlkZW9EdXJhdGlvbihkdXJhdGlvbiwgbWVkaWFTdWJ0eXBlKTtcclxuXHJcbiAgICAgICAgICAgIGlmICghdmFsaWRhdGlvblJlc3VsdC5pc1ZhbGlkKSB7XHJcbiAgICAgICAgICAgICAgLy8gSWYgdGhlcmUncyBhIHN1Z2dlc3RlZCBjYXRlZ29yeSwgYXV0b21hdGljYWxseSBzd2l0Y2ggdG8gaXRcclxuICAgICAgICAgICAgICBpZiAodmFsaWRhdGlvblJlc3VsdC5zdWdnZXN0ZWRDYXRlZ29yeSkge1xyXG4gICAgICAgICAgICAgICAgLy8gRm9yIHZpZGVvcyB0aGF0IGV4Y2VlZCB0aGUgbWF4aW11bSBkdXJhdGlvbiwgYXV0b21hdGljYWxseSBzd2l0Y2ggd2l0aG91dCBhc2tpbmdcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGBWaWRlbyBleGNlZWRzIG1heGltdW0gZHVyYXRpb24gZm9yICR7bWVkaWFTdWJ0eXBlfS4gQXV0b21hdGljYWxseSBzd2l0Y2hpbmcgdG8gJHt2YWxpZGF0aW9uUmVzdWx0LnN1Z2dlc3RlZENhdGVnb3J5fWApO1xyXG4gICAgICAgICAgICAgICAgc2hvd1dhcm5pbmdBbGVydChcclxuICAgICAgICAgICAgICAgICAgJ1ZpZGVvIER1cmF0aW9uIE5vdGljZScsXHJcbiAgICAgICAgICAgICAgICAgIGBZb3VyIHZpZGVvIGlzIHRvbyBsb25nIGZvciB0aGUgJHtnZXRDYXRlZ29yeURpc3BsYXlOYW1lKG1lZGlhU3VidHlwZSl9IGNhdGVnb3J5LiBJdCB3aWxsIGJlIHVwbG9hZGVkIGFzIGEgJHtnZXRDYXRlZ29yeURpc3BsYXlOYW1lKHZhbGlkYXRpb25SZXN1bHQuc3VnZ2VzdGVkQ2F0ZWdvcnkpfSBpbnN0ZWFkLmBcclxuICAgICAgICAgICAgICAgICk7XHJcblxyXG4gICAgICAgICAgICAgICAgLy8gU3dpdGNoIHRvIHRoZSBzdWdnZXN0ZWQgY2F0ZWdvcnlcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGBTd2l0Y2hpbmcgdG8gc3VnZ2VzdGVkIGNhdGVnb3J5OiAke3ZhbGlkYXRpb25SZXN1bHQuc3VnZ2VzdGVkQ2F0ZWdvcnl9YCk7XHJcblxyXG4gICAgICAgICAgICAgICAgLy8gTWFrZSBzdXJlIHdlIGtlZXAgYSByZWZlcmVuY2UgdG8gdGhlIGZpbGVcclxuICAgICAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRGaWxlID0gZmlsZTtcclxuXHJcbiAgICAgICAgICAgICAgICAvLyBVcGRhdGUgdGhlIG1lZGlhIHN1YnR5cGVcclxuICAgICAgICAgICAgICAgIHNldE1lZGlhU3VidHlwZSh2YWxpZGF0aW9uUmVzdWx0LnN1Z2dlc3RlZENhdGVnb3J5KTtcclxuXHJcbiAgICAgICAgICAgICAgICAvLyBVcGRhdGUgdGhlIHNlbGVjdGVkIHR5cGUgdG8gbWF0Y2ggdGhlIG5ldyBjYXRlZ29yeVxyXG4gICAgICAgICAgICAgICAgLy8gTmV2ZXIgc3VnZ2VzdCAnc3RvcnknIChtb21lbnRzKSBmb3Igb3RoZXIgY2F0ZWdvcmllc1xyXG4gICAgICAgICAgICAgICAgaWYgKHZhbGlkYXRpb25SZXN1bHQuc3VnZ2VzdGVkQ2F0ZWdvcnkgPT09ICdmbGFzaCcpIHtcclxuICAgICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRUeXBlKCdmbGFzaGVzJyk7XHJcbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHZhbGlkYXRpb25SZXN1bHQuc3VnZ2VzdGVkQ2F0ZWdvcnkgPT09ICdnbGltcHNlJykge1xyXG4gICAgICAgICAgICAgICAgICBzZXRTZWxlY3RlZFR5cGUoJ2dsaW1wc2VzJyk7XHJcbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHZhbGlkYXRpb25SZXN1bHQuc3VnZ2VzdGVkQ2F0ZWdvcnkgPT09ICdtb3ZpZScpIHtcclxuICAgICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRUeXBlKCdtb3ZpZXMnKTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIC8vIFJlbW92ZWQgdGhlICdzdG9yeScgc3VnZ2VzdGlvbiBmb3Igc2hvcnQgdmlkZW9zXHJcblxyXG4gICAgICAgICAgICAgICAgLy8gTWFrZSBzdXJlIHRoZSBmaWxlIGlzIHN0aWxsIHNldCBpbiB0aGUgc3RhdGVcclxuICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICBpZiAoIXN0YXRlLmZpbGUpIHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnUmUtc2V0dGluZyBmaWxlIGFmdGVyIGNhdGVnb3J5IGNoYW5nZTonLCBjdXJyZW50RmlsZS5uYW1lKTtcclxuICAgICAgICAgICAgICAgICAgICBzZXRGaWxlKGN1cnJlbnRGaWxlKTtcclxuICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfSwgNTApO1xyXG4gICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAvLyBObyBzdWdnZXN0ZWQgY2F0ZWdvcnksIGp1c3Qgc2hvdyB0aGUgZXJyb3JcclxuICAgICAgICAgICAgICAgIHNob3dFcnJvckFsZXJ0KCdWaWRlbyBEdXJhdGlvbiBFcnJvcicsIHZhbGlkYXRpb25SZXN1bHQuZXJyb3IgfHwgJ1RoZSB2aWRlbyBkdXJhdGlvbiBpcyBub3QgdmFsaWQgZm9yIHRoaXMgY2F0ZWdvcnkuJyk7XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9IGVsc2UgaWYgKHZhbGlkYXRpb25SZXN1bHQuc3VnZ2VzdGVkQ2F0ZWdvcnkgJiYgdmFsaWRhdGlvblJlc3VsdC5zdWdnZXN0ZWRDYXRlZ29yeSAhPT0gbWVkaWFTdWJ0eXBlKSB7XHJcbiAgICAgICAgICAgICAgLy8gVmlkZW8gaXMgdmFsaWQgZm9yIGN1cnJlbnQgY2F0ZWdvcnkgYnV0IHRoZXJlJ3MgYSBiZXR0ZXIgY2F0ZWdvcnlcclxuICAgICAgICAgICAgICAvLyBGb3IgdGhpcyBjYXNlLCB3ZSBzdGlsbCBnaXZlIHRoZSB1c2VyIGEgY2hvaWNlIHNpbmNlIHRoZSB2aWRlbyBpcyB2YWxpZCBmb3IgdGhlIGN1cnJlbnQgY2F0ZWdvcnlcclxuICAgICAgICAgICAgICAvLyBVc2Ugb3VyIGN1c3RvbSBjb25maXJtIGRpYWxvZyBpbnN0ZWFkIG9mIHdpbmRvdy5jb25maXJtXHJcbiAgICAgICAgICAgICAgY29uc3QgY29uZmlybVN3aXRjaCA9IGF3YWl0IHNob3dBbGVydCh7XHJcbiAgICAgICAgICAgICAgICB0aXRsZTogJ0NhdGVnb3J5IFN1Z2dlc3Rpb24nLFxyXG4gICAgICAgICAgICAgICAgbWVzc2FnZTogYCR7dmFsaWRhdGlvblJlc3VsdC5lcnJvcn1cXG5cXG5Xb3VsZCB5b3UgbGlrZSB0byBzd2l0Y2ggdG8gdGhlIHN1Z2dlc3RlZCBjYXRlZ29yeT9gLFxyXG4gICAgICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnLFxyXG4gICAgICAgICAgICAgICAgY29uZmlybVRleHQ6ICdZZXMsIFN3aXRjaCBDYXRlZ29yeScsXHJcbiAgICAgICAgICAgICAgICBjYW5jZWxUZXh0OiAnTm8sIEtlZXAgQ3VycmVudCcsXHJcbiAgICAgICAgICAgICAgICBvbkNvbmZpcm06ICgpID0+IHsgfVxyXG4gICAgICAgICAgICAgIH0pO1xyXG5cclxuICAgICAgICAgICAgICBpZiAoY29uZmlybVN3aXRjaCkge1xyXG4gICAgICAgICAgICAgICAgLy8gU3dpdGNoIHRvIHRoZSBzdWdnZXN0ZWQgY2F0ZWdvcnlcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGBTd2l0Y2hpbmcgdG8gc3VnZ2VzdGVkIGNhdGVnb3J5OiAke3ZhbGlkYXRpb25SZXN1bHQuc3VnZ2VzdGVkQ2F0ZWdvcnl9YCk7XHJcblxyXG4gICAgICAgICAgICAgICAgLy8gTWFrZSBzdXJlIHdlIGtlZXAgYSByZWZlcmVuY2UgdG8gdGhlIGZpbGVcclxuICAgICAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRGaWxlID0gZmlsZTtcclxuXHJcbiAgICAgICAgICAgICAgICAvLyBVcGRhdGUgdGhlIG1lZGlhIHN1YnR5cGVcclxuICAgICAgICAgICAgICAgIHNldE1lZGlhU3VidHlwZSh2YWxpZGF0aW9uUmVzdWx0LnN1Z2dlc3RlZENhdGVnb3J5KTtcclxuXHJcbiAgICAgICAgICAgICAgICAvLyBVcGRhdGUgdGhlIHNlbGVjdGVkIHR5cGUgdG8gbWF0Y2ggdGhlIG5ldyBjYXRlZ29yeVxyXG4gICAgICAgICAgICAgICAgLy8gTmV2ZXIgc3VnZ2VzdCAnc3RvcnknIChtb21lbnRzKSBmb3Igb3RoZXIgY2F0ZWdvcmllc1xyXG4gICAgICAgICAgICAgICAgaWYgKHZhbGlkYXRpb25SZXN1bHQuc3VnZ2VzdGVkQ2F0ZWdvcnkgPT09ICdmbGFzaCcpIHtcclxuICAgICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRUeXBlKCdmbGFzaGVzJyk7XHJcbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHZhbGlkYXRpb25SZXN1bHQuc3VnZ2VzdGVkQ2F0ZWdvcnkgPT09ICdnbGltcHNlJykge1xyXG4gICAgICAgICAgICAgICAgICBzZXRTZWxlY3RlZFR5cGUoJ2dsaW1wc2VzJyk7XHJcbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHZhbGlkYXRpb25SZXN1bHQuc3VnZ2VzdGVkQ2F0ZWdvcnkgPT09ICdtb3ZpZScpIHtcclxuICAgICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRUeXBlKCdtb3ZpZXMnKTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIC8vIFJlbW92ZWQgdGhlICdzdG9yeScgc3VnZ2VzdGlvbiBmb3Igc2hvcnQgdmlkZW9zXHJcblxyXG4gICAgICAgICAgICAgICAgLy8gTWFrZSBzdXJlIHRoZSBmaWxlIGlzIHN0aWxsIHNldCBpbiB0aGUgc3RhdGVcclxuICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICBpZiAoIXN0YXRlLmZpbGUpIHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnUmUtc2V0dGluZyBmaWxlIGFmdGVyIGNhdGVnb3J5IGNoYW5nZTonLCBjdXJyZW50RmlsZS5uYW1lKTtcclxuICAgICAgICAgICAgICAgICAgICBzZXRGaWxlKGN1cnJlbnRGaWxlKTtcclxuICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfSwgNTApO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIC8vIEFsd2F5cyBnbyB0byB0aHVtYm5haWwgc2VsZWN0aW9uIGZvciB2aWRlb3NcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdNb3ZpbmcgdG8gdGh1bWJuYWlsU2VsZWN0aW9uIHBoYXNlJyk7XHJcblxyXG4gICAgICAgICAgLy8gS2VlcCBhIHJlZmVyZW5jZSB0byB0aGUgY3VycmVudCBmaWxlXHJcbiAgICAgICAgICBjb25zdCBjdXJyZW50RmlsZSA9IGZpbGU7XHJcblxyXG4gICAgICAgICAgLy8gRG91YmxlLWNoZWNrIHRoYXQgdGhlIGZpbGUgaXMgc2V0IGluIHRoZSBzdGF0ZSBiZWZvcmUgcHJvY2VlZGluZ1xyXG4gICAgICAgICAgaWYgKHN0YXRlLmZpbGUpIHtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coJ0ZpbGUgY29uZmlybWVkIGluIHN0YXRlIGJlZm9yZSBwaGFzZSBjaGFuZ2U6Jywgc3RhdGUuZmlsZS5uYW1lKTtcclxuICAgICAgICAgICAgc2V0UGhhc2UoJ3RodW1ibmFpbFNlbGVjdGlvbicpO1xyXG4gICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coJ0ZpbGUgbm90IGZvdW5kIGluIHN0YXRlIGJlZm9yZSBwaGFzZSBjaGFuZ2UsIHNldHRpbmcgaXQgYWdhaW4nKTtcclxuICAgICAgICAgICAgLy8gVHJ5IHNldHRpbmcgdGhlIGZpbGUgYWdhaW5cclxuICAgICAgICAgICAgc2V0RmlsZShjdXJyZW50RmlsZSk7XHJcbiAgICAgICAgICAgIC8vIEFkZCBhIHNtYWxsIGRlbGF5IHRvIGVuc3VyZSB0aGUgc3RhdGUgaXMgdXBkYXRlZFxyXG4gICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICAgICAgICAvLyBEb3VibGUtY2hlY2sgYWdhaW5cclxuICAgICAgICAgICAgICBpZiAoIXN0YXRlLmZpbGUpIHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdGaWxlIHN0aWxsIG5vdCBpbiBzdGF0ZSwgc2V0dGluZyBpdCBvbmUgbW9yZSB0aW1lJyk7XHJcbiAgICAgICAgICAgICAgICBzZXRGaWxlKGN1cnJlbnRGaWxlKTtcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ0RlbGF5ZWQgcGhhc2UgY2hhbmdlIHRvIHRodW1ibmFpbFNlbGVjdGlvbicpO1xyXG4gICAgICAgICAgICAgIHNldFBoYXNlKCd0aHVtYm5haWxTZWxlY3Rpb24nKTtcclxuICAgICAgICAgICAgfSwgMTAwKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY2FsY3VsYXRpbmcgdmlkZW8gZHVyYXRpb246JywgZXJyb3IpO1xyXG5cclxuICAgICAgICAgIC8vIEZvciBtb21lbnRzIHZpZGVvcywgd2UgbmVlZCB0byBlbmZvcmNlIHRoZSBkdXJhdGlvbiBjaGVja1xyXG4gICAgICAgICAgLy8gSWYgd2UgY2FuJ3QgY2FsY3VsYXRlIGR1cmF0aW9uLCB3ZSBjYW4ndCB2YWxpZGF0ZSBpdCwgc28gd2Ugc2hvdWxkIHJlamVjdCB0aGUgdXBsb2FkXHJcbiAgICAgICAgICBpZiAoc2VsZWN0ZWRUeXBlID09PSAnbW9tZW50cycpIHtcclxuICAgICAgICAgICAgc2hvd0Vycm9yQWxlcnQoJ1ZpZGVvIEVycm9yJywgJ1VuYWJsZSB0byBkZXRlcm1pbmUgdmlkZW8gZHVyYXRpb24uIFBsZWFzZSB0cnkgYSBkaWZmZXJlbnQgdmlkZW8gZmlsZS4nKTtcclxuICAgICAgICAgICAgcmVzZXRVcGxvYWQoKTtcclxuICAgICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgY29uc29sZS5sb2coJ01vdmluZyB0byB0aHVtYm5haWxTZWxlY3Rpb24gcGhhc2UgZGVzcGl0ZSBlcnJvcicpO1xyXG5cclxuICAgICAgICAgIC8vIEtlZXAgYSByZWZlcmVuY2UgdG8gdGhlIGN1cnJlbnQgZmlsZVxyXG4gICAgICAgICAgY29uc3QgY3VycmVudEZpbGUgPSBmaWxlO1xyXG5cclxuICAgICAgICAgIC8vIERvdWJsZS1jaGVjayB0aGF0IHRoZSBmaWxlIGlzIHNldCBpbiB0aGUgc3RhdGUgYmVmb3JlIHByb2NlZWRpbmdcclxuICAgICAgICAgIGlmIChzdGF0ZS5maWxlKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdGaWxlIGNvbmZpcm1lZCBpbiBzdGF0ZSBiZWZvcmUgcGhhc2UgY2hhbmdlIChlcnJvciBjYXNlKTonLCBzdGF0ZS5maWxlLm5hbWUpO1xyXG4gICAgICAgICAgICBzZXRQaGFzZSgndGh1bWJuYWlsU2VsZWN0aW9uJyk7XHJcbiAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygnRmlsZSBub3QgZm91bmQgaW4gc3RhdGUgYmVmb3JlIHBoYXNlIGNoYW5nZSAoZXJyb3IgY2FzZSksIHNldHRpbmcgaXQgYWdhaW4nKTtcclxuICAgICAgICAgICAgLy8gVHJ5IHNldHRpbmcgdGhlIGZpbGUgYWdhaW5cclxuICAgICAgICAgICAgc2V0RmlsZShjdXJyZW50RmlsZSk7XHJcbiAgICAgICAgICAgIC8vIEFkZCBhIHNtYWxsIGRlbGF5IHRvIGVuc3VyZSB0aGUgc3RhdGUgaXMgdXBkYXRlZFxyXG4gICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICAgICAgICAvLyBEb3VibGUtY2hlY2sgYWdhaW5cclxuICAgICAgICAgICAgICBpZiAoIXN0YXRlLmZpbGUpIHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdGaWxlIHN0aWxsIG5vdCBpbiBzdGF0ZSAoZXJyb3IgY2FzZSksIHNldHRpbmcgaXQgb25lIG1vcmUgdGltZScpO1xyXG4gICAgICAgICAgICAgICAgc2V0RmlsZShjdXJyZW50RmlsZSk7XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdEZWxheWVkIHBoYXNlIGNoYW5nZSB0byB0aHVtYm5haWxTZWxlY3Rpb24gKGVycm9yIGNhc2UpJyk7XHJcbiAgICAgICAgICAgICAgc2V0UGhhc2UoJ3RodW1ibmFpbFNlbGVjdGlvbicpO1xyXG4gICAgICAgICAgICB9LCAxMDApO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICAvLyBGb3IgcGhvdG9zIG9yIG1vbWVudHMgaW1hZ2VzXHJcbiAgICAgICAgaWYgKHNlbGVjdGVkVHlwZSA9PT0gJ21vbWVudHMnKSB7XHJcbiAgICAgICAgICAvLyBGb3IgbW9tZW50cywgd2UgbmVlZCB0byBzZXQgdGhlIG1lZGlhIHR5cGUgYmFzZWQgb24gdGhlIGZpbGUgdHlwZVxyXG4gICAgICAgICAgaWYgKGZpbGUudHlwZS5zdGFydHNXaXRoKCdpbWFnZS8nKSkge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygnTW9tZW50cyBpbWFnZSBkZXRlY3RlZCcpO1xyXG4gICAgICAgICAgICBzZXRNZWRpYVR5cGUoJ3Bob3RvJyk7XHJcbiAgICAgICAgICAgIC8vIEZvciBtb21lbnRzIGltYWdlcywgd2UgYWx3YXlzIHVzZSAnc3RvcnknIGFzIHRoZSBtZWRpYSBzdWJ0eXBlXHJcbiAgICAgICAgICAgIHNldE1lZGlhU3VidHlwZSgnc3RvcnknKTtcclxuXHJcbiAgICAgICAgICAgIC8vIENyZWF0ZSBhIGxvY2FsIHJlZmVyZW5jZSB0byB0aGUgZmlsZSBmb3IgdXNlIGluIHRoZSB0aW1lb3V0XHJcbiAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRGaWxlID0gZmlsZTtcclxuXHJcbiAgICAgICAgICAgIC8vIERvdWJsZS1jaGVjayB0aGF0IHRoZSBmaWxlIGlzIHNldCBpbiB0aGUgc3RhdGUgYmVmb3JlIHByb2NlZWRpbmdcclxuICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgICAgICAgLy8gQ2hlY2sgaWYgdGhlIGZpbGUgaXMgaW4gdGhlIHN0YXRlXHJcbiAgICAgICAgICAgICAgaWYgKCFzdGF0ZS5maWxlKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnTW9tZW50cyBwaG90byBub3QgZm91bmQgaW4gc3RhdGUgYWZ0ZXIgc2V0dGluZywgdHJ5aW5nIGFnYWluJyk7XHJcbiAgICAgICAgICAgICAgICAvLyBUcnkgc2V0dGluZyB0aGUgZmlsZSBhZ2FpblxyXG4gICAgICAgICAgICAgICAgc2V0RmlsZShjdXJyZW50RmlsZSk7XHJcbiAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdNb21lbnRzIHBob3RvIGNvbmZpcm1lZCBpbiBzdGF0ZTonLCBzdGF0ZS5maWxlLm5hbWUpO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSwgNTApO1xyXG4gICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coJ0ludmFsaWQgZmlsZSB0eXBlIGZvciBtb21lbnRzLiBPbmx5IGltYWdlcyBhbmQgdmlkZW9zIGxlc3MgdGhhbiAxIG1pbnV0ZSBhcmUgYWxsb3dlZC4nKTtcclxuICAgICAgICAgICAgc2hvd0Vycm9yQWxlcnQoJ0ludmFsaWQgRmlsZSBUeXBlJywgJ0ludmFsaWQgZmlsZSB0eXBlIGZvciBtb21lbnRzLiBPbmx5IGltYWdlcyBhbmQgdmlkZW9zIGxlc3MgdGhhbiAxIG1pbnV0ZSBhcmUgYWxsb3dlZC4nKTtcclxuXHJcbiAgICAgICAgICAgIC8vIFJlc2V0IHRoZSB1cGxvYWQgY29udGV4dCBidXQgcHJlc2VydmUgdGhlIHNlbGVjdGVkIHR5cGUgYW5kIGNhdGVnb3J5XHJcbiAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRUeXBlID0gc2VsZWN0ZWRUeXBlO1xyXG4gICAgICAgICAgICBjb25zdCBjdXJyZW50Q2F0ZWdvcnkgPSBzZWxlY3RlZENhdGVnb3J5O1xyXG5cclxuICAgICAgICAgICAgLy8gRmlyc3Qgc2V0IHRoZSBwaGFzZSBiYWNrIHRvIGNhdGVnb3J5IHNlbGVjdGlvblxyXG4gICAgICAgICAgICBzZXRQaGFzZSgnY2F0ZWdvcnlTZWxlY3Rpb24nKTtcclxuXHJcbiAgICAgICAgICAgIC8vIFRoZW4gcmVzZXQgdGhlIHVwbG9hZCBzdGF0ZVxyXG4gICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICAgICAgICByZXNldFVwbG9hZCgpO1xyXG4gICAgICAgICAgICAgIHNldFNlbGVjdGVkVHlwZShjdXJyZW50VHlwZSk7XHJcbiAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRDYXRlZ29yeShjdXJyZW50Q2F0ZWdvcnkpO1xyXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdSZXNldCB1cGxvYWQgc3RhdGUgYWZ0ZXIgaW52YWxpZCBmaWxlIHR5cGUgZm9yIG1vbWVudHMnKTtcclxuICAgICAgICAgICAgfSwgMTAwKTtcclxuICAgICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gSGFuZGxlIGltYWdlIHByZXZpZXcgYW5kIHNldCBwaGFzZVxyXG4gICAgICAgIGNvbnN0IHJlYWRlciA9IG5ldyBGaWxlUmVhZGVyKCk7XHJcbiAgICAgICAgcmVhZGVyLm9ubG9hZCA9IChlKSA9PiB7XHJcbiAgICAgICAgICBpZiAoZS50YXJnZXQ/LnJlc3VsdCkge1xyXG4gICAgICAgICAgICBzZXRQcmV2aWV3SW1hZ2UoZS50YXJnZXQucmVzdWx0IGFzIHN0cmluZyk7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdQcmV2aWV3IGltYWdlIHNldCBmb3IgZmlsZTonLCBmaWxlLm5hbWUpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH07XHJcbiAgICAgICAgcmVhZGVyLnJlYWRBc0RhdGFVUkwoZmlsZSk7XHJcblxyXG4gICAgICAgIC8vIENyZWF0ZSBhIGxvY2FsIHJlZmVyZW5jZSB0byB0aGUgZmlsZSBmb3IgdXNlIGluIHRoZSB0aW1lb3V0XHJcbiAgICAgICAgY29uc3QgY3VycmVudEZpbGUgPSBmaWxlO1xyXG5cclxuICAgICAgICAvLyBEb3VibGUtY2hlY2sgdGhhdCB0aGUgZmlsZSBpcyBzZXQgaW4gdGhlIHN0YXRlIGJlZm9yZSBwcm9jZWVkaW5nXHJcbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgICAvLyBDaGVjayBpZiB0aGUgZmlsZSBpcyBpbiB0aGUgc3RhdGVcclxuICAgICAgICAgIGlmICghc3RhdGUuZmlsZSkge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygnRmlsZSBub3QgZm91bmQgaW4gc3RhdGUgYmVmb3JlIG1vdmluZyB0byBwZXJzb25hbERldGFpbHMsIHNldHRpbmcgaXQgYWdhaW4nKTtcclxuICAgICAgICAgICAgLy8gVHJ5IHNldHRpbmcgdGhlIGZpbGUgYWdhaW5cclxuICAgICAgICAgICAgc2V0RmlsZShjdXJyZW50RmlsZSk7XHJcblxyXG4gICAgICAgICAgICAvLyBBZGQgYW5vdGhlciB0aW1lb3V0IHRvIGVuc3VyZSB0aGUgZmlsZSBpcyBzZXRcclxuICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgICAgICAgaWYgKCFzdGF0ZS5maWxlKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnRmlsZSBzdGlsbCBub3QgaW4gc3RhdGUsIHNldHRpbmcgaXQgb25lIG1vcmUgdGltZScpO1xyXG4gICAgICAgICAgICAgICAgc2V0RmlsZShjdXJyZW50RmlsZSk7XHJcbiAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdGaWxlIGNvbmZpcm1lZCBpbiBzdGF0ZSBhZnRlciBzZWNvbmQgYXR0ZW1wdDonLCBzdGF0ZS5maWxlLm5hbWUpO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAvLyBOZXcgZmxvdyBsb2dpYzogRm9yIG1vbWVudHMgKHN0b3JpZXMpLCBza2lwIHBlcnNvbmFsIGRldGFpbHMgYW5kIGdvIGRpcmVjdGx5IHRvIGZhY2UgdmVyaWZpY2F0aW9uXHJcbiAgICAgICAgICAgICAgaWYgKHNlbGVjdGVkVHlwZSA9PT0gJ21vbWVudHMnIHx8IHN0YXRlLm1lZGlhU3VidHlwZSA9PT0gJ3N0b3J5Jykge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ01vbWVudHMgaW1hZ2UgdXBsb2FkOiBza2lwcGluZyBwZXJzb25hbCBkZXRhaWxzLCBnb2luZyBkaXJlY3RseSB0byBmYWNlIHZlcmlmaWNhdGlvbicpO1xyXG4gICAgICAgICAgICAgICAgc2V0UGhhc2UoJ2ZhY2VWZXJpZmljYXRpb24nKTtcclxuICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ01vdmluZyB0byBwZXJzb25hbERldGFpbHMgcGhhc2UgZm9yIHBob3RvL2ltYWdlJyk7XHJcbiAgICAgICAgICAgICAgICBzZXRQaGFzZSgncGVyc29uYWxEZXRhaWxzJyk7XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9LCAxMDApO1xyXG4gICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coJ0ZpbGUgY29uZmlybWVkIGluIHN0YXRlOicsIHN0YXRlLmZpbGUubmFtZSk7XHJcbiAgICAgICAgICAgIC8vIE5ldyBmbG93IGxvZ2ljOiBGb3IgbW9tZW50cyAoc3RvcmllcyksIHNraXAgcGVyc29uYWwgZGV0YWlscyBhbmQgZ28gZGlyZWN0bHkgdG8gZmFjZSB2ZXJpZmljYXRpb25cclxuICAgICAgICAgICAgaWYgKHNlbGVjdGVkVHlwZSA9PT0gJ21vbWVudHMnIHx8IHN0YXRlLm1lZGlhU3VidHlwZSA9PT0gJ3N0b3J5Jykge1xyXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdNb21lbnRzIGltYWdlIHVwbG9hZDogc2tpcHBpbmcgcGVyc29uYWwgZGV0YWlscywgZ29pbmcgZGlyZWN0bHkgdG8gZmFjZSB2ZXJpZmljYXRpb24nKTtcclxuICAgICAgICAgICAgICBzZXRQaGFzZSgnZmFjZVZlcmlmaWNhdGlvbicpO1xyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdNb3ZpbmcgdG8gcGVyc29uYWxEZXRhaWxzIHBoYXNlIGZvciBwaG90by9pbWFnZScpO1xyXG4gICAgICAgICAgICAgIHNldFBoYXNlKCdwZXJzb25hbERldGFpbHMnKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0sIDEwMCk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgLy8gVHJpZ2dlciB0aGUgZmlsZSBkaWFsb2dcclxuICAgIGlucHV0LmNsaWNrKCk7XHJcbiAgfTtcclxuXHJcbiAgLy8gSGFuZGxlIHBlcnNvbmFsIGRldGFpbHMgY29tcGxldGVkXHJcbiAgY29uc3QgaGFuZGxlUGVyc29uYWxEZXRhaWxzQ29tcGxldGVkID0gKGRldGFpbHM6IFBlcnNvbmFsRGV0YWlsc0RhdGEpID0+IHtcclxuICAgIGNvbnNvbGUubG9nKCdQZXJzb25hbCBkZXRhaWxzIGNvbXBsZXRlZDonLCBkZXRhaWxzKTtcclxuXHJcbiAgICAvLyBTdG9yZSB0aGUgcGVyc29uYWwgZGV0YWlscyBpbiBsb2NhbCBzdGF0ZSBmb3IgY29tcG9uZW50IHBlcnNpc3RlbmNlXHJcbiAgICBzZXRMb2NhbFBlcnNvbmFsRGV0YWlscyhkZXRhaWxzKTtcclxuXHJcbiAgICAvLyBWYWxpZGF0ZSB0aGF0IHdlIGhhdmUgYSB0aXRsZVxyXG4gICAgaWYgKCFkZXRhaWxzLmNhcHRpb24gfHwgIWRldGFpbHMuY2FwdGlvbi50cmltKCkpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignQ2FwdGlvbi90aXRsZSBpcyBlbXB0eSwgdGhpcyBzaG91bGQgbm90IGhhcHBlbicpO1xyXG4gICAgICAvLyBHbyBiYWNrIHRvIHBlcnNvbmFsIGRldGFpbHMgdG8gZml4IHRoaXNcclxuICAgICAgc2V0UGhhc2UoJ3BlcnNvbmFsRGV0YWlscycpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgLy8gU2V0IHRoZSB0aXRsZSBpbiB0aGUgdXBsb2FkIGNvbnRleHRcclxuICAgIHNldFRpdGxlKGRldGFpbHMuY2FwdGlvbi50cmltKCkpO1xyXG5cclxuICAgIC8vIEFsc28gc3RvcmUgaW4gZ2xvYmFsIGNvbnRleHQgZm9yIHBlcnNpc3RlbmNlICh0aGlzIGlzIHRoZSB1cGxvYWQgY29udGV4dCBmdW5jdGlvbilcclxuICAgIHNldFBlcnNvbmFsRGV0YWlscyhkZXRhaWxzKTtcclxuXHJcbiAgICAvLyBDaGVjayBpZiB3ZSBoYXZlIGEgZmlsZSBpbiB0aGUgc3RhdGVcclxuICAgIGlmICghc3RhdGUuZmlsZSkge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdObyBmaWxlIGZvdW5kIGluIHN0YXRlIGFmdGVyIHBlcnNvbmFsIGRldGFpbHMnKTtcclxuICAgICAgc2hvd0Vycm9yQWxlcnQoJ1VwbG9hZCBFcnJvcicsICdTb21ldGhpbmcgd2VudCB3cm9uZyB3aXRoIHlvdXIgZmlsZSB1cGxvYWQuIFBsZWFzZSB0cnkgYWdhaW4uJyk7XHJcbiAgICAgIHNldFBoYXNlKCd0eXBlU2VsZWN0aW9uJyk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICBjb25zb2xlLmxvZygnRmlsZSBjb25maXJtZWQgaW4gc3RhdGUgYWZ0ZXIgcGVyc29uYWwgZGV0YWlsczonLCBzdGF0ZS5maWxlLm5hbWUpO1xyXG4gICAgY29uc29sZS5sb2coJ1BlcnNvbmFsIGRldGFpbHMgc2V0IHN1Y2Nlc3NmdWxseScpO1xyXG4gICAgY29uc29sZS5sb2coJ1RpdGxlIHNldCB0bzonLCBkZXRhaWxzLmNhcHRpb24udHJpbSgpKTtcclxuICAgIGNvbnNvbGUubG9nKCdDdXJyZW50IHNlbGVjdGVkVHlwZTonLCBzZWxlY3RlZFR5cGUpO1xyXG4gICAgY29uc29sZS5sb2coJ0N1cnJlbnQgbWVkaWFTdWJ0eXBlOicsIHN0YXRlLm1lZGlhU3VidHlwZSk7XHJcblxyXG4gICAgLy8gTmV3IGZsb3cgbG9naWMgYmFzZWQgb24gYmFja2VuZCByZXF1aXJlbWVudHM6XHJcbiAgICAvLyAtIE1vbWVudHMgKHN0b3JpZXMpOiBTa2lwIHBlcnNvbmFsIGRldGFpbHMsIGdvIGRpcmVjdGx5IHRvIGZhY2UgdmVyaWZpY2F0aW9uXHJcbiAgICAvLyAtIFBob3RvczogR28gdG8gZmFjZSB2ZXJpZmljYXRpb24gYWZ0ZXIgcGVyc29uYWwgZGV0YWlscyAobm8gdmVuZG9yIGRldGFpbHMpXHJcbiAgICAvLyAtIFZpZGVvczogR28gdG8gdmVuZG9yIGRldGFpbHMgYWZ0ZXIgcGVyc29uYWwgZGV0YWlsc1xyXG5cclxuICAgIGlmIChzdGF0ZS5tZWRpYVR5cGUgPT09ICdwaG90bycpIHtcclxuICAgICAgY29uc29sZS5sb2coJ1Bob3RvIHVwbG9hZDogcHJvY2VlZGluZyB0byBmYWNlIHZlcmlmaWNhdGlvbiAobm8gdmVuZG9yIGRldGFpbHMgbmVlZGVkKScpO1xyXG4gICAgICBzZXRQaGFzZSgnZmFjZVZlcmlmaWNhdGlvbicpO1xyXG4gICAgfSBlbHNlIGlmIChzZWxlY3RlZFR5cGUgPT09ICdtb21lbnRzJyB8fCBzdGF0ZS5tZWRpYVN1YnR5cGUgPT09ICdzdG9yeScpIHtcclxuICAgICAgY29uc29sZS5sb2coJ01vbWVudHMgdXBsb2FkOiBwcm9jZWVkaW5nIHRvIGZhY2UgdmVyaWZpY2F0aW9uIChubyB2ZW5kb3IgZGV0YWlscyBuZWVkZWQpJyk7XHJcbiAgICAgIHNldFBoYXNlKCdmYWNlVmVyaWZpY2F0aW9uJyk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICAvLyBGb3IgdmlkZW9zIChmbGFzaGVzLCBnbGltcHNlcywgbW92aWVzKSwgcHJvY2VlZCB0byB2ZW5kb3IgZGV0YWlsc1xyXG4gICAgICBjb25zb2xlLmxvZygnVmlkZW8gdXBsb2FkOiBwcm9jZWVkaW5nIHRvIHZlbmRvciBkZXRhaWxzJyk7XHJcbiAgICAgIHNldFBoYXNlKCd2ZW5kb3JEZXRhaWxzJyk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgLy8gSGFuZGxlIHZlbmRvciBkZXRhaWxzIGNvbXBsZXRlZFxyXG4gIGNvbnN0IGhhbmRsZVZlbmRvckRldGFpbHNDb21wbGV0ZWQgPSAodmVuZG9yRGV0YWlsczogUmVjb3JkPHN0cmluZywgVmVuZG9yRGV0YWlsSXRlbT4pID0+IHtcclxuICAgIC8vIGNvbnNvbGUubG9nKCdWZW5kb3IgZGV0YWlscyBjb21wbGV0ZWQ6JywgdmVuZG9yRGV0YWlscyk7XHJcblxyXG4gICAgLy8gTm9ybWFsaXplIHZlbmRvciBkZXRhaWxzIHRvIGVuc3VyZSBjb25zaXN0ZW50IGZpZWxkIG5hbWVzXHJcbiAgICBjb25zdCBub3JtYWxpemVkVmVuZG9yRGV0YWlscyA9IHsgLi4udmVuZG9yRGV0YWlscyB9O1xyXG5cclxuICAgIC8vIEVuc3VyZSB3ZSBoYXZlIGJvdGggZnJvbnRlbmQgYW5kIGJhY2tlbmQgZmllbGQgbmFtZXMgZm9yIG1ha2V1cCBhcnRpc3QgYW5kIGRlY29yYXRpb25zXHJcbiAgICBpZiAodmVuZG9yRGV0YWlscy5tYWtldXBBcnRpc3QpIHtcclxuICAgICAgbm9ybWFsaXplZFZlbmRvckRldGFpbHMubWFrZXVwX2FydGlzdCA9IHZlbmRvckRldGFpbHMubWFrZXVwQXJ0aXN0O1xyXG4gICAgfSBlbHNlIGlmICh2ZW5kb3JEZXRhaWxzLm1ha2V1cF9hcnRpc3QpIHtcclxuICAgICAgbm9ybWFsaXplZFZlbmRvckRldGFpbHMubWFrZXVwQXJ0aXN0ID0gdmVuZG9yRGV0YWlscy5tYWtldXBfYXJ0aXN0O1xyXG4gICAgfVxyXG5cclxuICAgIGlmICh2ZW5kb3JEZXRhaWxzLmRlY29yYXRpb25zKSB7XHJcbiAgICAgIG5vcm1hbGl6ZWRWZW5kb3JEZXRhaWxzLmRlY29yYXRpb24gPSB2ZW5kb3JEZXRhaWxzLmRlY29yYXRpb25zO1xyXG4gICAgfSBlbHNlIGlmICh2ZW5kb3JEZXRhaWxzLmRlY29yYXRpb24pIHtcclxuICAgICAgbm9ybWFsaXplZFZlbmRvckRldGFpbHMuZGVjb3JhdGlvbnMgPSB2ZW5kb3JEZXRhaWxzLmRlY29yYXRpb247XHJcbiAgICB9XHJcblxyXG4gICAgLy8gU3RvcmUgdGhlIG5vcm1hbGl6ZWQgdmVuZG9yIGRldGFpbHMgZm9yIHBlcnNpc3RlbmNlIGJldHdlZW4gc2NyZWVuc1xyXG4gICAgc2V0VmVuZG9yRGV0YWlsc0RhdGEobm9ybWFsaXplZFZlbmRvckRldGFpbHMpO1xyXG5cclxuICAgIC8vIEFsc28gc3RvcmUgaW4gdGhlIHJlZiBmb3IgRWRnZSBicm93c2VyIGNvbXBhdGliaWxpdHlcclxuICAgIHZlbmRvckRldGFpbHNSZWYuY3VycmVudCA9IG5vcm1hbGl6ZWRWZW5kb3JEZXRhaWxzO1xyXG5cclxuICAgIC8vIFN0b3JlIHZlbmRvciBkZXRhaWxzIGluIGxvY2FsU3RvcmFnZSBmb3IgcGVyc2lzdGVuY2VcclxuICAgIHRyeSB7XHJcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCd3ZWR6YXRfdmVuZG9yX2RldGFpbHMnLCBKU09OLnN0cmluZ2lmeShub3JtYWxpemVkVmVuZG9yRGV0YWlscykpO1xyXG4gICAgICBjb25zb2xlLmxvZygnVVBMT0FEIE1BTkFHRVIgLSBTdG9yZWQgdmVuZG9yIGRldGFpbHMgaW4gbG9jYWxTdG9yYWdlJyk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdVUExPQUQgTUFOQUdFUiAtIEZhaWxlZCB0byBzdG9yZSB2ZW5kb3IgZGV0YWlscyBpbiBsb2NhbFN0b3JhZ2U6JywgZXJyb3IpO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIFNhdmUgdGhlIGN1cnJlbnQgdmlkZW9fY2F0ZWdvcnkgYmVmb3JlIHNldHRpbmcgdmVuZG9yIGRldGFpbHNcclxuICAgIGNvbnN0IGN1cnJlbnRWaWRlb0NhdGVnb3J5ID0gc3RhdGUuZGV0YWlsRmllbGRzLnZpZGVvX2NhdGVnb3J5O1xyXG4gICAgY29uc29sZS5sb2coJ1VQTE9BRCBNQU5BR0VSIC0gU2F2aW5nIHZpZGVvX2NhdGVnb3J5IGJlZm9yZSB2ZW5kb3IgZGV0YWlsczonLCBjdXJyZW50VmlkZW9DYXRlZ29yeSk7XHJcblxyXG4gICAgLy8gU3RvcmUgdmlkZW9fY2F0ZWdvcnkgaW4gbG9jYWxTdG9yYWdlXHJcbiAgICBpZiAoY3VycmVudFZpZGVvQ2F0ZWdvcnkpIHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnd2VkemF0X3ZpZGVvX2NhdGVnb3J5JywgY3VycmVudFZpZGVvQ2F0ZWdvcnkpO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdVUExPQUQgTUFOQUdFUiAtIFN0b3JlZCB2aWRlb19jYXRlZ29yeSBpbiBsb2NhbFN0b3JhZ2U6JywgY3VycmVudFZpZGVvQ2F0ZWdvcnkpO1xyXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1VQTE9BRCBNQU5BR0VSIC0gRmFpbGVkIHRvIHN0b3JlIHZpZGVvX2NhdGVnb3J5IGluIGxvY2FsU3RvcmFnZTonLCBlcnJvcik7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvLyBTdG9yZSBpbiBnbG9iYWwgY29udGV4dCBmb3IgcGVyc2lzdGVuY2VcclxuICAgIHNldFZlbmRvckRldGFpbHMobm9ybWFsaXplZFZlbmRvckRldGFpbHMpO1xyXG5cclxuICAgIC8vIEV4cGxpY2l0bHkgc2V0IGVhY2ggdmVuZG9yIGRldGFpbCBmaWVsZFxyXG4gICAgT2JqZWN0LmVudHJpZXMobm9ybWFsaXplZFZlbmRvckRldGFpbHMpLmZvckVhY2goKFt2ZW5kb3JUeXBlLCBkZXRhaWxzXSkgPT4ge1xyXG4gICAgICBpZiAoZGV0YWlscyAmJiBkZXRhaWxzLm5hbWUgJiYgZGV0YWlscy5tb2JpbGVOdW1iZXIpIHtcclxuICAgICAgICBzZXREZXRhaWxGaWVsZChgdmVuZG9yXyR7dmVuZG9yVHlwZX1fbmFtZWAsIGRldGFpbHMubmFtZSk7XHJcbiAgICAgICAgc2V0RGV0YWlsRmllbGQoYHZlbmRvcl8ke3ZlbmRvclR5cGV9X2NvbnRhY3RgLCBkZXRhaWxzLm1vYmlsZU51bWJlcik7XHJcbiAgICAgIH1cclxuICAgIH0pO1xyXG5cclxuICAgIC8vIFJlLXNldCB0aGUgdmlkZW9fY2F0ZWdvcnkgYWZ0ZXIgdmVuZG9yIGRldGFpbHMgdG8gZW5zdXJlIGl0J3MgcHJlc2VydmVkXHJcbiAgICBpZiAoY3VycmVudFZpZGVvQ2F0ZWdvcnkpIHtcclxuICAgICAgY29uc29sZS5sb2coJ1VQTE9BRCBNQU5BR0VSIC0gUmUtc2V0dGluZyB2aWRlb19jYXRlZ29yeSBhZnRlciB2ZW5kb3IgZGV0YWlsczonLCBjdXJyZW50VmlkZW9DYXRlZ29yeSk7XHJcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgIHNldERldGFpbEZpZWxkKCd2aWRlb19jYXRlZ29yeScsIGN1cnJlbnRWaWRlb0NhdGVnb3J5KTtcclxuICAgICAgfSwgMTAwKTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBMb2cgYWxsIGRldGFpbCBmaWVsZHMgYWZ0ZXIgc2V0dGluZyB2ZW5kb3IgZGV0YWlsc1xyXG4gICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdBbGwgZGV0YWlsIGZpZWxkcyBhZnRlciB2ZW5kb3IgZGV0YWlsczonLCBzdGF0ZS5kZXRhaWxGaWVsZHMpO1xyXG4gICAgICBjb25zb2xlLmxvZygnRGV0YWlsIGZpZWxkcyBjb3VudDonLCBPYmplY3Qua2V5cyhzdGF0ZS5kZXRhaWxGaWVsZHMpLmxlbmd0aCk7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdOb3JtYWxpemVkIHZlbmRvciBkZXRhaWxzOicsIG5vcm1hbGl6ZWRWZW5kb3JEZXRhaWxzKTtcclxuICAgIH0sIDIwMCk7XHJcblxyXG4gICAgLy8gQWRkIGEgc21hbGwgZGVsYXkgdG8gZW5zdXJlIHRoZSBzdGF0ZSBpcyB1cGRhdGVkIGJlZm9yZSBwcm9jZWVkaW5nXHJcbiAgICAvLyBUaGlzIGhlbHBzIHdpdGggY3Jvc3MtYnJvd3NlciBjb21wYXRpYmlsaXR5LCBlc3BlY2lhbGx5IGluIEVkZ2VcclxuICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAvLyBEb3VibGUtY2hlY2sgdGhhdCB3ZSBoYXZlIGF0IGxlYXN0IDQgdmVuZG9yIGRldGFpbHMgYmVmb3JlIHByb2NlZWRpbmdcclxuICAgICAgY29uc3QgdmVuZG9yTmFtZUZpZWxkcyA9IE9iamVjdC5rZXlzKHN0YXRlLmRldGFpbEZpZWxkcykuZmlsdGVyKGtleSA9PiBrZXkuc3RhcnRzV2l0aCgndmVuZG9yXycpICYmIGtleS5lbmRzV2l0aCgnX25hbWUnKSk7XHJcbiAgICAgIGNvbnN0IHZlbmRvckNvbnRhY3RGaWVsZHMgPSBPYmplY3Qua2V5cyhzdGF0ZS5kZXRhaWxGaWVsZHMpLmZpbHRlcihrZXkgPT4ga2V5LnN0YXJ0c1dpdGgoJ3ZlbmRvcl8nKSAmJiBrZXkuZW5kc1dpdGgoJ19jb250YWN0JykpO1xyXG5cclxuICAgICAgY29uc29sZS5sb2coJ1VQTE9BRCBNQU5BR0VSIC0gVmVuZG9yIG5hbWUgZmllbGRzOicsIHZlbmRvck5hbWVGaWVsZHMubGVuZ3RoKTtcclxuICAgICAgY29uc29sZS5sb2coJ1VQTE9BRCBNQU5BR0VSIC0gVmVuZG9yIGNvbnRhY3QgZmllbGRzOicsIHZlbmRvckNvbnRhY3RGaWVsZHMubGVuZ3RoKTtcclxuXHJcbiAgICAgIC8vIEVkZ2UgYnJvd3NlciB3b3JrYXJvdW5kIC0gZGlyZWN0bHkgc2V0IHZlbmRvciBkZXRhaWxzIGluIHRoZSBzdGF0ZVxyXG4gICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgL0VkZ2V8RWRnLy50ZXN0KHdpbmRvdy5uYXZpZ2F0b3IudXNlckFnZW50KSkge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdVUExPQUQgTUFOQUdFUiAtIEVkZ2UgYnJvd3NlciBkZXRlY3RlZCwgYXBwbHlpbmcgZGlyZWN0IHZlbmRvciBkZXRhaWxzIHdvcmthcm91bmQnKTtcclxuXHJcbiAgICAgICAgLy8gQ3JlYXRlIHZlbmRvciBkZXRhaWwgZmllbGRzIGRpcmVjdGx5IGluIHRoZSBzdGF0ZVxyXG4gICAgICAgIC8vIFRoaXMgaXMgYSB3b3JrYXJvdW5kIGZvciBFZGdlIGJyb3dzZXIgd2hlcmUgdGhlIHN0YXRlIHVwZGF0ZSBkb2Vzbid0IHByb3Blcmx5IHByZXNlcnZlIHZlbmRvciBkZXRhaWxzXHJcbiAgICAgICAgT2JqZWN0LmVudHJpZXMobm9ybWFsaXplZFZlbmRvckRldGFpbHMpLmZvckVhY2goKFt2ZW5kb3JUeXBlLCBkZXRhaWxzXSkgPT4ge1xyXG4gICAgICAgICAgaWYgKGRldGFpbHMgJiYgZGV0YWlscy5uYW1lICYmIGRldGFpbHMubW9iaWxlTnVtYmVyKSB7XHJcbiAgICAgICAgICAgIC8vIFNldCB0aGUgdmVuZG9yIGRldGFpbHMgZGlyZWN0bHkgaW4gdGhlIHN0YXRlXHJcbiAgICAgICAgICAgIHN0YXRlLmRldGFpbEZpZWxkc1tgdmVuZG9yXyR7dmVuZG9yVHlwZX1fbmFtZWBdID0gZGV0YWlscy5uYW1lO1xyXG4gICAgICAgICAgICBzdGF0ZS5kZXRhaWxGaWVsZHNbYHZlbmRvcl8ke3ZlbmRvclR5cGV9X2NvbnRhY3RgXSA9IGRldGFpbHMubW9iaWxlTnVtYmVyO1xyXG5cclxuICAgICAgICAgICAgLy8gQWxzbyBzZXQgdGhlIG5vcm1hbGl6ZWQgdmVyc2lvblxyXG4gICAgICAgICAgICBjb25zdCBub3JtYWxpemVkVHlwZSA9IHZlbmRvclR5cGUgPT09ICdtYWtldXBBcnRpc3QnID8gJ21ha2V1cF9hcnRpc3QnIDpcclxuICAgICAgICAgICAgICB2ZW5kb3JUeXBlID09PSAnZGVjb3JhdGlvbnMnID8gJ2RlY29yYXRpb24nIDogdmVuZG9yVHlwZTtcclxuXHJcbiAgICAgICAgICAgIGlmIChub3JtYWxpemVkVHlwZSAhPT0gdmVuZG9yVHlwZSkge1xyXG4gICAgICAgICAgICAgIHN0YXRlLmRldGFpbEZpZWxkc1tgdmVuZG9yXyR7bm9ybWFsaXplZFR5cGV9X25hbWVgXSA9IGRldGFpbHMubmFtZTtcclxuICAgICAgICAgICAgICBzdGF0ZS5kZXRhaWxGaWVsZHNbYHZlbmRvcl8ke25vcm1hbGl6ZWRUeXBlfV9jb250YWN0YF0gPSBkZXRhaWxzLm1vYmlsZU51bWJlcjtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgY29uc29sZS5sb2coYFVQTE9BRCBNQU5BR0VSIC0gRWRnZSB3b3JrYXJvdW5kOiBBZGRlZCB2ZW5kb3IgJHt2ZW5kb3JUeXBlfSBkaXJlY3RseSB0byBzdGF0ZWApO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICAvLyBSZS1zZXQgdGhlIHZpZGVvX2NhdGVnb3J5IGRpcmVjdGx5XHJcbiAgICAgICAgaWYgKGN1cnJlbnRWaWRlb0NhdGVnb3J5KSB7XHJcbiAgICAgICAgICBzdGF0ZS5kZXRhaWxGaWVsZHMudmlkZW9fY2F0ZWdvcnkgPSBjdXJyZW50VmlkZW9DYXRlZ29yeTtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdVUExPQUQgTUFOQUdFUiAtIEVkZ2Ugd29ya2Fyb3VuZDogUmUtc2V0IHZpZGVvX2NhdGVnb3J5IGRpcmVjdGx5OicsIGN1cnJlbnRWaWRlb0NhdGVnb3J5KTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIFByb2NlZWQgdG8gZmFjZSB2ZXJpZmljYXRpb25cclxuICAgICAgc2V0UGhhc2UoJ2ZhY2VWZXJpZmljYXRpb24nKTtcclxuICAgIH0sIDMwMCk7XHJcbiAgfTtcclxuXHJcbiAgLy8gSGFuZGxlIHRodW1ibmFpbCBzZWxlY3Rpb25cclxuICBjb25zdCBoYW5kbGVUaHVtYm5haWxTZWxlY3RlZCA9ICh0aHVtYm5haWxGaWxlPzogRmlsZSkgPT4ge1xyXG4gICAgaWYgKHRodW1ibmFpbEZpbGUpIHtcclxuICAgICAgLy8gU2V0IHRoZSB0aHVtYm5haWwgaW4gdGhlIGNvbnRleHRcclxuICAgICAgc2V0VGh1bWJuYWlsKHRodW1ibmFpbEZpbGUpO1xyXG4gICAgICBjb25zb2xlLmxvZygnVGh1bWJuYWlsIHNlbGVjdGVkOicsIHRodW1ibmFpbEZpbGUubmFtZSk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBjb25zb2xlLmxvZygnTm8gdGh1bWJuYWlsIHNlbGVjdGVkLCB1c2luZyBhdXRvLWdlbmVyYXRlZCB0aHVtYm5haWwnKTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBOZXcgZmxvdyBsb2dpYzogRm9yIG1vbWVudHMgKHN0b3JpZXMpLCBza2lwIHBlcnNvbmFsIGRldGFpbHMgYW5kIGdvIGRpcmVjdGx5IHRvIGZhY2UgdmVyaWZpY2F0aW9uXHJcbiAgICBpZiAoc2VsZWN0ZWRUeXBlID09PSAnbW9tZW50cycgfHwgc3RhdGUubWVkaWFTdWJ0eXBlID09PSAnc3RvcnknKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdNb21lbnRzIHVwbG9hZDogc2tpcHBpbmcgcGVyc29uYWwgZGV0YWlscywgZ29pbmcgZGlyZWN0bHkgdG8gZmFjZSB2ZXJpZmljYXRpb24nKTtcclxuICAgICAgc2V0UGhhc2UoJ2ZhY2VWZXJpZmljYXRpb24nKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIC8vIEZvciBwaG90b3MgYW5kIHZpZGVvcywgZ28gdG8gcGVyc29uYWwgZGV0YWlsc1xyXG4gICAgICBjb25zb2xlLmxvZygnUGhvdG8vVmlkZW8gdXBsb2FkOiBwcm9jZWVkaW5nIHRvIHBlcnNvbmFsIGRldGFpbHMnKTtcclxuICAgICAgc2V0UGhhc2UoJ3BlcnNvbmFsRGV0YWlscycpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIEZ1bmN0aW9uIHRvIHByb2NlZWQgd2l0aCB1cGxvYWQgYWZ0ZXIgdmVuZG9yIGRldGFpbHMgYXJlIGFwcGxpZWRcclxuICBjb25zdCBwcm9jZWVkV2l0aFVwbG9hZCA9ICh2aWRlb0NhdGVnb3J5OiBzdHJpbmcgfCB1bmRlZmluZWQpID0+IHtcclxuICAgIC8vIEZvciBtb21lbnRzIChzdG9yaWVzKSwgdGhpcyBmdW5jdGlvbiBzaG91bGQgbm90IGJlIGNhbGxlZCwgYnV0IGFkZCBzYWZldHkgY2hlY2tcclxuICAgIGlmIChzZWxlY3RlZFR5cGUgPT09ICdtb21lbnRzJyB8fCBzdGF0ZS5tZWRpYVN1YnR5cGUgPT09ICdzdG9yeScpIHtcclxuICAgICAgY29uc29sZS5sb2coJ1VQTE9BRCBNQU5BR0VSIC0gTW9tZW50cyBkZXRlY3RlZCBpbiBwcm9jZWVkV2l0aFVwbG9hZCwgY2FsbGluZyBzdGFydFVwbG9hZCBkaXJlY3RseScpO1xyXG4gICAgICBzdGFydFVwbG9hZCgpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgLy8gRG91YmxlLWNoZWNrIHRoYXQgd2UgaGF2ZSBhIHRpdGxlIGJlZm9yZSBjaGFuZ2luZyB0byB1cGxvYWRpbmcgcGhhc2VcclxuICAgIGlmICghc3RhdGUudGl0bGUgfHwgIXN0YXRlLnRpdGxlLnRyaW0oKSkge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdUaXRsZSBpcyBtaXNzaW5nIGJlZm9yZSB1cGxvYWQsIHNldHRpbmcgaXQgZnJvbSBwZXJzb25hbCBkZXRhaWxzJyk7XHJcblxyXG4gICAgICAvLyBUcnkgdG8gc2V0IHRoZSB0aXRsZSBmcm9tIHBlcnNvbmFsIGRldGFpbHNcclxuICAgICAgaWYgKHBlcnNvbmFsRGV0YWlscy5jYXB0aW9uICYmIHBlcnNvbmFsRGV0YWlscy5jYXB0aW9uLnRyaW0oKSkge1xyXG4gICAgICAgIC8vIGNvbnNvbGUubG9nKCdTZXR0aW5nIHBlcnNvbmFsIGRldGFpbHMgZnJvbSBsb2NhbCBzdGF0ZTonLCBwZXJzb25hbERldGFpbHMpO1xyXG4gICAgICAgIC8vIFVzZSB0aGUgZ2xvYmFsIGNvbnRleHQgZnVuY3Rpb24gdG8gc2V0IGFsbCBwZXJzb25hbCBkZXRhaWxzIGF0IG9uY2VcclxuICAgICAgICBzZXRQZXJzb25hbERldGFpbHMocGVyc29uYWxEZXRhaWxzKTtcclxuICAgICAgICAvLyBFeHBsaWNpdGx5IHNldCB0aGUgdGl0bGUgYXMgd2VsbFxyXG4gICAgICAgIHNldFRpdGxlKHBlcnNvbmFsRGV0YWlscy5jYXB0aW9uLnRyaW0oKSk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignTm8gdGl0bGUgaW4gcGVyc29uYWwgZGV0YWlscyBlaXRoZXIsIGdvaW5nIGJhY2sgdG8gcGVyc29uYWwgZGV0YWlscycpO1xyXG4gICAgICAgIHNldFBoYXNlKCdwZXJzb25hbERldGFpbHMnKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvLyBFZGdlIGJyb3dzZXIgd29ya2Fyb3VuZCAtIGRpcmVjdGx5IHNldCB2ZW5kb3IgZGV0YWlscyBpbiB0aGUgc3RhdGVcclxuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiAvRWRnZXxFZGcvLnRlc3Qod2luZG93Lm5hdmlnYXRvci51c2VyQWdlbnQpKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdVUExPQUQgTUFOQUdFUiAtIEVkZ2UgYnJvd3NlciBkZXRlY3RlZCBpbiBmYWNlIHZlcmlmaWNhdGlvbiwgYXBwbHlpbmcgdmVuZG9yIGRldGFpbHMgd29ya2Fyb3VuZCcpO1xyXG5cclxuICAgICAgLy8gR2V0IHRoZSB2ZW5kb3IgZGV0YWlscyBmcm9tIHRoZSB2ZW5kb3IgZGV0YWlscyBkYXRhXHJcbiAgICAgIGNvbnN0IHZlbmRvckRldGFpbHNEYXRhID0gdmVuZG9yRGV0YWlsc1JlZi5jdXJyZW50O1xyXG4gICAgICBpZiAodmVuZG9yRGV0YWlsc0RhdGEpIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnVVBMT0FEIE1BTkFHRVIgLSBFZGdlIHdvcmthcm91bmQ6IFVzaW5nIHZlbmRvciBkZXRhaWxzIGZyb20gcmVmOicsIHZlbmRvckRldGFpbHNEYXRhKTtcclxuXHJcbiAgICAgICAgLy8gQ3JlYXRlIHZlbmRvciBkZXRhaWwgZmllbGRzIGRpcmVjdGx5IGluIHRoZSBzdGF0ZVxyXG4gICAgICAgIE9iamVjdC5lbnRyaWVzKHZlbmRvckRldGFpbHNEYXRhKS5mb3JFYWNoKChbdmVuZG9yVHlwZSwgZGV0YWlsc10pID0+IHtcclxuICAgICAgICAgIGlmIChkZXRhaWxzICYmIGRldGFpbHMubmFtZSAmJiBkZXRhaWxzLm1vYmlsZU51bWJlcikge1xyXG4gICAgICAgICAgICAvLyBTZXQgdGhlIHZlbmRvciBkZXRhaWxzIGRpcmVjdGx5IGluIHRoZSBzdGF0ZVxyXG4gICAgICAgICAgICBzdGF0ZS5kZXRhaWxGaWVsZHNbYHZlbmRvcl8ke3ZlbmRvclR5cGV9X25hbWVgXSA9IGRldGFpbHMubmFtZTtcclxuICAgICAgICAgICAgc3RhdGUuZGV0YWlsRmllbGRzW2B2ZW5kb3JfJHt2ZW5kb3JUeXBlfV9jb250YWN0YF0gPSBkZXRhaWxzLm1vYmlsZU51bWJlcjtcclxuXHJcbiAgICAgICAgICAgIC8vIEFsc28gc2V0IHRoZSBub3JtYWxpemVkIHZlcnNpb25cclxuICAgICAgICAgICAgY29uc3Qgbm9ybWFsaXplZFR5cGUgPSB2ZW5kb3JUeXBlID09PSAnbWFrZXVwQXJ0aXN0JyA/ICdtYWtldXBfYXJ0aXN0JyA6XHJcbiAgICAgICAgICAgICAgdmVuZG9yVHlwZSA9PT0gJ2RlY29yYXRpb25zJyA/ICdkZWNvcmF0aW9uJyA6IHZlbmRvclR5cGU7XHJcblxyXG4gICAgICAgICAgICBpZiAobm9ybWFsaXplZFR5cGUgIT09IHZlbmRvclR5cGUpIHtcclxuICAgICAgICAgICAgICBzdGF0ZS5kZXRhaWxGaWVsZHNbYHZlbmRvcl8ke25vcm1hbGl6ZWRUeXBlfV9uYW1lYF0gPSBkZXRhaWxzLm5hbWU7XHJcbiAgICAgICAgICAgICAgc3RhdGUuZGV0YWlsRmllbGRzW2B2ZW5kb3JfJHtub3JtYWxpemVkVHlwZX1fY29udGFjdGBdID0gZGV0YWlscy5tb2JpbGVOdW1iZXI7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGBVUExPQUQgTUFOQUdFUiAtIEVkZ2Ugd29ya2Fyb3VuZDogQWRkZWQgdmVuZG9yICR7dmVuZG9yVHlwZX0gZGlyZWN0bHkgdG8gc3RhdGVgKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9KTtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIEZvciB2aWRlb3MsIGNoZWNrIGlmIHdlIGhhdmUgYSB2aWRlb19jYXRlZ29yeVxyXG4gICAgaWYgKHN0YXRlLm1lZGlhVHlwZSA9PT0gJ3ZpZGVvJykge1xyXG4gICAgICBjb25zb2xlLmxvZyhgVVBMT0FEIE1BTkFHRVIgLSBDaGVja2luZyB2aWRlb19jYXRlZ29yeSBiZWZvcmUgdXBsb2FkYCk7XHJcbiAgICAgIGNvbnNvbGUubG9nKGBVUExPQUQgTUFOQUdFUiAtIEN1cnJlbnQgdmlkZW9fY2F0ZWdvcnk6ICR7c3RhdGUuZGV0YWlsRmllbGRzLnZpZGVvX2NhdGVnb3J5IHx8ICdOb3Qgc2V0J31gKTtcclxuICAgICAgY29uc29sZS5sb2coYFVQTE9BRCBNQU5BR0VSIC0gQ3VycmVudCBtZWRpYVN1YnR5cGU6ICR7c3RhdGUubWVkaWFTdWJ0eXBlfWApO1xyXG4gICAgICBjb25zb2xlLmxvZyhgVVBMT0FEIE1BTkFHRVIgLSBTZWxlY3RlZCBjYXRlZ29yeTogJHtzZWxlY3RlZENhdGVnb3J5IHx8ICdOb3Qgc2V0J31gKTtcclxuXHJcbiAgICAgIC8vIFNwZWNpYWwgaGFuZGxpbmcgZm9yIGdsaW1wc2VzXHJcbiAgICAgIGlmIChzdGF0ZS5tZWRpYVN1YnR5cGUgPT09ICdnbGltcHNlJykge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKGBVUExPQUQgTUFOQUdFUiAtIFNwZWNpYWwgaGFuZGxpbmcgZm9yIGdsaW1wc2VzYCk7XHJcblxyXG4gICAgICAgIC8vIElmIHdlIGRvbid0IGhhdmUgYSB2aWRlb19jYXRlZ29yeSB5ZXQsIHRyeSB0byBzZXQgaXQgZnJvbSBzZWxlY3RlZENhdGVnb3J5XHJcbiAgICAgICAgaWYgKCFzdGF0ZS5kZXRhaWxGaWVsZHMudmlkZW9fY2F0ZWdvcnkgJiYgc2VsZWN0ZWRDYXRlZ29yeSkge1xyXG4gICAgICAgICAgLy8gTWFwIHRoZSBVSSBjYXRlZ29yeSB0byB0aGUgYmFja2VuZCB2aWRlb19jYXRlZ29yeVxyXG4gICAgICAgICAgbGV0IHZpZGVvQ2F0ZWdvcnkgPSAnJztcclxuXHJcbiAgICAgICAgICBpZiAoc2VsZWN0ZWRDYXRlZ29yeSA9PT0gJ215X3dlZGRpbmdfdmlkZW9zJykge1xyXG4gICAgICAgICAgICB2aWRlb0NhdGVnb3J5ID0gJ215X3dlZGRpbmcnO1xyXG4gICAgICAgICAgfSBlbHNlIGlmIChzZWxlY3RlZENhdGVnb3J5ID09PSAnd2VkZGluZ192bG9nJykge1xyXG4gICAgICAgICAgICB2aWRlb0NhdGVnb3J5ID0gJ3dlZGRpbmdfdmxvZyc7XHJcbiAgICAgICAgICB9IGVsc2UgaWYgKHNlbGVjdGVkQ2F0ZWdvcnkgPT09ICdmcmllbmRzX2ZhbWlseV92aWRlb3MnKSB7XHJcbiAgICAgICAgICAgIHZpZGVvQ2F0ZWdvcnkgPSAnZnJpZW5kc19mYW1pbHlfdmlkZW8nO1xyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIGlmICh2aWRlb0NhdGVnb3J5KSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGBVUExPQUQgTUFOQUdFUiAtIFNldHRpbmcgdmlkZW9fY2F0ZWdvcnkgZm9yIGdsaW1wc2U6ICR7dmlkZW9DYXRlZ29yeX1gKTtcclxuICAgICAgICAgICAgc2V0RGV0YWlsRmllbGQoJ3ZpZGVvX2NhdGVnb3J5JywgdmlkZW9DYXRlZ29yeSk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKGBVUExPQUQgTUFOQUdFUiAtIEdsaW1wc2UgYWxyZWFkeSBoYXMgdmlkZW9fY2F0ZWdvcnk6ICR7c3RhdGUuZGV0YWlsRmllbGRzLnZpZGVvX2NhdGVnb3J5fWApO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gSWYgd2Ugc3RpbGwgZG9uJ3QgaGF2ZSBhIHZpZGVvX2NhdGVnb3J5LCB1c2UgYSBkZWZhdWx0IGJhc2VkIG9uIHNlbGVjdGVkQ2F0ZWdvcnlcclxuICAgICAgaWYgKCFzdGF0ZS5kZXRhaWxGaWVsZHMudmlkZW9fY2F0ZWdvcnkgJiYgc2VsZWN0ZWRDYXRlZ29yeSkge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKGBVUExPQUQgTUFOQUdFUiAtIE5vIHZpZGVvX2NhdGVnb3J5IHNldCwgdXNpbmcgc2VsZWN0ZWRDYXRlZ29yeTogJHtzZWxlY3RlZENhdGVnb3J5fWApO1xyXG5cclxuICAgICAgICAvLyBNYXAgdGhlIFVJIGNhdGVnb3J5IHRvIHRoZSBiYWNrZW5kIHZpZGVvX2NhdGVnb3J5XHJcbiAgICAgICAgbGV0IHZpZGVvQ2F0ZWdvcnkgPSAnJztcclxuXHJcbiAgICAgICAgaWYgKHNlbGVjdGVkQ2F0ZWdvcnkgPT09ICdteV93ZWRkaW5nX3ZpZGVvcycpIHtcclxuICAgICAgICAgIHZpZGVvQ2F0ZWdvcnkgPSAnbXlfd2VkZGluZyc7XHJcbiAgICAgICAgfSBlbHNlIGlmIChzZWxlY3RlZENhdGVnb3J5ID09PSAnd2VkZGluZ192bG9nJykge1xyXG4gICAgICAgICAgdmlkZW9DYXRlZ29yeSA9ICd3ZWRkaW5nX3Zsb2cnO1xyXG4gICAgICAgIH0gZWxzZSBpZiAoc2VsZWN0ZWRDYXRlZ29yeSA9PT0gJ2ZyaWVuZHNfZmFtaWx5X3ZpZGVvcycpIHtcclxuICAgICAgICAgIHZpZGVvQ2F0ZWdvcnkgPSAnZnJpZW5kc19mYW1pbHlfdmlkZW8nO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgaWYgKHZpZGVvQ2F0ZWdvcnkpIHtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKGBVUExPQUQgTUFOQUdFUiAtIFNldHRpbmcgdmlkZW9fY2F0ZWdvcnkgZnJvbSBzZWxlY3RlZENhdGVnb3J5OiAke3ZpZGVvQ2F0ZWdvcnl9YCk7XHJcbiAgICAgICAgICBzZXREZXRhaWxGaWVsZCgndmlkZW9fY2F0ZWdvcnknLCB2aWRlb0NhdGVnb3J5KTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIEZpbmFsIGNoZWNrIC0gaWYgd2Ugc3RpbGwgZG9uJ3QgaGF2ZSBhIHZpZGVvX2NhdGVnb3J5LCB1c2UgYSBkZWZhdWx0XHJcbiAgICAgIGlmICghc3RhdGUuZGV0YWlsRmllbGRzLnZpZGVvX2NhdGVnb3J5KSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ05vIHZpZGVvX2NhdGVnb3J5IGZvdW5kLCB1c2luZyBhIGRlZmF1bHQgb25lJyk7XHJcbiAgICAgICAgLy8gVXNlICdteV93ZWRkaW5nJyBhcyBhIGRlZmF1bHQgY2F0ZWdvcnkgaW5zdGVhZCBvZiBhc2tpbmcgdGhlIHVzZXIgYWdhaW5cclxuICAgICAgICBzZXREZXRhaWxGaWVsZCgndmlkZW9fY2F0ZWdvcnknLCAnbXlfd2VkZGluZycpO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdTZXQgZGVmYXVsdCB2aWRlb19jYXRlZ29yeSB0byBteV93ZWRkaW5nJyk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvLyBFZGdlIGJyb3dzZXIgd29ya2Fyb3VuZCAtIGRpcmVjdGx5IHNldCB2ZW5kb3IgZGV0YWlscyBpbiB0aGUgc3RhdGUgYmVmb3JlIHVwbG9hZFxyXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmIC9FZGdlfEVkZy8udGVzdCh3aW5kb3cubmF2aWdhdG9yLnVzZXJBZ2VudCkpIHtcclxuICAgICAgY29uc29sZS5sb2coJ1VQTE9BRCBNQU5BR0VSIC0gRWRnZSBicm93c2VyIGRldGVjdGVkIGJlZm9yZSB1cGxvYWQsIGFwcGx5aW5nIHZlbmRvciBkZXRhaWxzIHdvcmthcm91bmQnKTtcclxuXHJcbiAgICAgIC8vIEdldCB0aGUgdmVuZG9yIGRldGFpbHMgZnJvbSB0aGUgdmVuZG9yIGRldGFpbHMgZGF0YVxyXG4gICAgICBjb25zdCB2ZW5kb3JEZXRhaWxzRGF0YSA9IHZlbmRvckRldGFpbHNSZWYuY3VycmVudDtcclxuICAgICAgaWYgKHZlbmRvckRldGFpbHNEYXRhICYmIE9iamVjdC5rZXlzKHZlbmRvckRldGFpbHNEYXRhKS5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ1VQTE9BRCBNQU5BR0VSIC0gRWRnZSB3b3JrYXJvdW5kOiBVc2luZyB2ZW5kb3IgZGV0YWlscyBmcm9tIHJlZiBiZWZvcmUgdXBsb2FkOicsIHZlbmRvckRldGFpbHNEYXRhKTtcclxuXHJcbiAgICAgICAgLy8gQ3JlYXRlIHZlbmRvciBkZXRhaWwgZmllbGRzIGRpcmVjdGx5IGluIHRoZSBzdGF0ZVxyXG4gICAgICAgIE9iamVjdC5lbnRyaWVzKHZlbmRvckRldGFpbHNEYXRhKS5mb3JFYWNoKChbdmVuZG9yVHlwZSwgZGV0YWlsc10pID0+IHtcclxuICAgICAgICAgIGlmIChkZXRhaWxzICYmIGRldGFpbHMubmFtZSAmJiBkZXRhaWxzLm1vYmlsZU51bWJlcikge1xyXG4gICAgICAgICAgICAvLyBTZXQgdGhlIHZlbmRvciBkZXRhaWxzIGRpcmVjdGx5IGluIHRoZSBzdGF0ZVxyXG4gICAgICAgICAgICBzdGF0ZS5kZXRhaWxGaWVsZHNbYHZlbmRvcl8ke3ZlbmRvclR5cGV9X25hbWVgXSA9IGRldGFpbHMubmFtZTtcclxuICAgICAgICAgICAgc3RhdGUuZGV0YWlsRmllbGRzW2B2ZW5kb3JfJHt2ZW5kb3JUeXBlfV9jb250YWN0YF0gPSBkZXRhaWxzLm1vYmlsZU51bWJlcjtcclxuXHJcbiAgICAgICAgICAgIC8vIEFsc28gc2V0IHRoZSBub3JtYWxpemVkIHZlcnNpb25cclxuICAgICAgICAgICAgY29uc3Qgbm9ybWFsaXplZFR5cGUgPSB2ZW5kb3JUeXBlID09PSAnbWFrZXVwQXJ0aXN0JyA/ICdtYWtldXBfYXJ0aXN0JyA6XHJcbiAgICAgICAgICAgICAgdmVuZG9yVHlwZSA9PT0gJ2RlY29yYXRpb25zJyA/ICdkZWNvcmF0aW9uJyA6IHZlbmRvclR5cGU7XHJcblxyXG4gICAgICAgICAgICBpZiAobm9ybWFsaXplZFR5cGUgIT09IHZlbmRvclR5cGUpIHtcclxuICAgICAgICAgICAgICBzdGF0ZS5kZXRhaWxGaWVsZHNbYHZlbmRvcl8ke25vcm1hbGl6ZWRUeXBlfV9uYW1lYF0gPSBkZXRhaWxzLm5hbWU7XHJcbiAgICAgICAgICAgICAgc3RhdGUuZGV0YWlsRmllbGRzW2B2ZW5kb3JfJHtub3JtYWxpemVkVHlwZX1fY29udGFjdGBdID0gZGV0YWlscy5tb2JpbGVOdW1iZXI7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGBVUExPQUQgTUFOQUdFUiAtIEVkZ2Ugd29ya2Fyb3VuZDogQWRkZWQgdmVuZG9yICR7dmVuZG9yVHlwZX0gZGlyZWN0bHkgdG8gc3RhdGUgYmVmb3JlIHVwbG9hZGApO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0pO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLy8gQ2hlY2sgaWYgd2UgaGF2ZSBhIGZpbGUgYmVmb3JlIHByb2NlZWRpbmdcclxuICAgIGlmICghc3RhdGUuZmlsZSkge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdObyBmaWxlIGZvdW5kIGluIHN0YXRlIGJlZm9yZSB1cGxvYWQnKTtcclxuICAgICAgc2hvd0Vycm9yQWxlcnQoJ1VwbG9hZCBFcnJvcicsICdObyBmaWxlIHNlbGVjdGVkLiBQbGVhc2Ugc2VsZWN0IGEgZmlsZSB0byB1cGxvYWQuJyk7XHJcblxyXG4gICAgICAvLyBHbyBiYWNrIHRvIHR5cGUgc2VsZWN0aW9uIHRvIHN0YXJ0IG92ZXJcclxuICAgICAgc2V0UGhhc2UoJ3R5cGVTZWxlY3Rpb24nKTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIE5vdyB3ZSBjYW4gcHJvY2VlZCB0byB1cGxvYWRpbmcgcGhhc2VcclxuICAgIHNldFBoYXNlKCd1cGxvYWRpbmcnKTtcclxuXHJcbiAgICAvLyBMb2cgdGhlIGN1cnJlbnQgc3RhdGUgYmVmb3JlIHN0YXJ0aW5nIHVwbG9hZFxyXG4gICAgY29uc29sZS5sb2coJ0N1cnJlbnQgc3RhdGUgYmVmb3JlIHVwbG9hZDonLCB7XHJcbiAgICAgIGZpbGU6IHN0YXRlLmZpbGUgPyBzdGF0ZS5maWxlLm5hbWUgOiAnTm8gZmlsZScsXHJcbiAgICAgIG1lZGlhVHlwZTogc3RhdGUubWVkaWFUeXBlLFxyXG4gICAgICBtZWRpYVN1YnR5cGU6IHN0YXRlLm1lZGlhU3VidHlwZSxcclxuICAgICAgdGl0bGU6IHN0YXRlLnRpdGxlLFxyXG4gICAgICBkZXNjcmlwdGlvbjogc3RhdGUuZGVzY3JpcHRpb24sXHJcbiAgICAgIGRldGFpbEZpZWxkczogc3RhdGUuZGV0YWlsRmllbGRzLFxyXG4gICAgICBkZXRhaWxGaWVsZHNDb3VudDogT2JqZWN0LmtleXMoc3RhdGUuZGV0YWlsRmllbGRzKS5sZW5ndGhcclxuICAgIH0pO1xyXG5cclxuICAgIC8vIERvdWJsZS1jaGVjayB0aGF0IHdlJ3JlIHVzaW5nIHRoZSBjb3JyZWN0IGNhdGVnb3J5XHJcbiAgICBjb25zb2xlLmxvZyhgVVBMT0FEIE1BTkFHRVIgLSBGaW5hbCBjaGVjayAtIFNlbGVjdGVkIHR5cGU6ICR7c2VsZWN0ZWRUeXBlfWApO1xyXG4gICAgY29uc29sZS5sb2coYFVQTE9BRCBNQU5BR0VSIC0gRmluYWwgY2hlY2sgLSBNZWRpYVN1YnR5cGUgaW4gc3RhdGU6ICR7c3RhdGUubWVkaWFTdWJ0eXBlfWApO1xyXG5cclxuICAgIC8vIElmIHRoZSBtZWRpYVN1YnR5cGUgZG9lc24ndCBtYXRjaCB3aGF0IHdlIGV4cGVjdCBiYXNlZCBvbiB0aGUgc2VsZWN0ZWQgdHlwZSwgZml4IGl0XHJcbiAgICBpZiAoc2VsZWN0ZWRUeXBlICYmIHN0YXRlLm1lZGlhU3VidHlwZSAhPT0gZ2V0TWVkaWFTdWJ0eXBlRnJvbVNlbGVjdGVkVHlwZShzZWxlY3RlZFR5cGUpKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKGBVUExPQUQgTUFOQUdFUiAtIFdBUk5JTkc6IE1lZGlhU3VidHlwZSBtaXNtYXRjaCBkZXRlY3RlZCFgKTtcclxuICAgICAgY29uc29sZS5sb2coYFVQTE9BRCBNQU5BR0VSIC0gRXhwZWN0ZWQgbWVkaWFTdWJ0eXBlIGJhc2VkIG9uIHNlbGVjdGVkIHR5cGU6ICR7Z2V0TWVkaWFTdWJ0eXBlRnJvbVNlbGVjdGVkVHlwZShzZWxlY3RlZFR5cGUpfWApO1xyXG4gICAgICBjb25zb2xlLmxvZyhgVVBMT0FEIE1BTkFHRVIgLSBBY3R1YWwgbWVkaWFTdWJ0eXBlIGluIHN0YXRlOiAke3N0YXRlLm1lZGlhU3VidHlwZX1gKTtcclxuICAgICAgY29uc29sZS5sb2coYFVQTE9BRCBNQU5BR0VSIC0gQ29ycmVjdGluZyBjYXRlZ29yeSBiZWZvcmUgdXBsb2FkLi4uYCk7XHJcblxyXG4gICAgICAvLyBHZXQgdGhlIGNvcnJlY3RlZCBjYXRlZ29yeVxyXG4gICAgICBjb25zdCBjb3JyZWN0ZWRDYXRlZ29yeSA9IGdldE1lZGlhU3VidHlwZUZyb21TZWxlY3RlZFR5cGUoc2VsZWN0ZWRUeXBlKTtcclxuICAgICAgY29uc29sZS5sb2coYFVQTE9BRCBNQU5BR0VSIC0gQ2F0ZWdvcnkgY29ycmVjdGVkIHRvOiAke2NvcnJlY3RlZENhdGVnb3J5fWApO1xyXG5cclxuICAgICAgLy8gR2V0IHRoZSB2aWRlb19jYXRlZ29yeSBmcm9tIHRoZSBvcmlnaW5hbCBzZWxlY3Rpb25cclxuICAgICAgLy8gV2UgbmVlZCB0byBtYXAgaXQgdG8gdGhlIGNvcnJlY3QgYmFja2VuZCB2YWx1ZVxyXG4gICAgICBsZXQgdmlkZW9DYXRlZ29yeSA9ICcnO1xyXG5cclxuICAgICAgaWYgKHNlbGVjdGVkQ2F0ZWdvcnkgPT09ICdteV93ZWRkaW5nX3ZpZGVvcycpIHtcclxuICAgICAgICB2aWRlb0NhdGVnb3J5ID0gJ215X3dlZGRpbmcnO1xyXG4gICAgICB9IGVsc2UgaWYgKHNlbGVjdGVkQ2F0ZWdvcnkgPT09ICd3ZWRkaW5nX3Zsb2cnKSB7XHJcbiAgICAgICAgdmlkZW9DYXRlZ29yeSA9ICd3ZWRkaW5nX3Zsb2cnO1xyXG4gICAgICB9IGVsc2UgaWYgKHNlbGVjdGVkQ2F0ZWdvcnkgPT09ICdmcmllbmRzX2ZhbWlseV92aWRlb3MnKSB7XHJcbiAgICAgICAgdmlkZW9DYXRlZ29yeSA9ICdmcmllbmRzX2ZhbWlseV92aWRlbyc7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnNvbGUubG9nKGBVUExPQUQgTUFOQUdFUiAtIE9yaWdpbmFsIHNlbGVjdGVkIGNhdGVnb3J5OiAke3NlbGVjdGVkQ2F0ZWdvcnl9YCk7XHJcbiAgICAgIGNvbnNvbGUubG9nKGBVUExPQUQgTUFOQUdFUiAtIE1hcHBlZCB0byBiYWNrZW5kIHZpZGVvX2NhdGVnb3J5OiAke3ZpZGVvQ2F0ZWdvcnl9YCk7XHJcblxyXG4gICAgICAvLyBTdGFydCB0aGUgdXBsb2FkIHByb2Nlc3Mgd2l0aCB0aGUgY29ycmVjdGVkIGNhdGVnb3J5IGFuZCB2aWRlb19jYXRlZ29yeVxyXG4gICAgICBzdGFydFVwbG9hZFdpdGhDYXRlZ29yeShjb3JyZWN0ZWRDYXRlZ29yeSwgdmlkZW9DYXRlZ29yeSk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICAvLyBHZXQgdGhlIHZpZGVvX2NhdGVnb3J5IGZyb20gdGhlIHN0YXRlXHJcbiAgICAgIGNvbnN0IGZpbmFsVmlkZW9DYXRlZ29yeSA9IHZpZGVvQ2F0ZWdvcnkgfHwgc3RhdGUuZGV0YWlsRmllbGRzLnZpZGVvX2NhdGVnb3J5IHx8ICdteV93ZWRkaW5nJztcclxuICAgICAgY29uc29sZS5sb2coYFVQTE9BRCBNQU5BR0VSIC0gVXNpbmcgdmlkZW9fY2F0ZWdvcnkgZm9yIHVwbG9hZDogJHtmaW5hbFZpZGVvQ2F0ZWdvcnl9YCk7XHJcblxyXG4gICAgICAvLyBTdGFydCB0aGUgdXBsb2FkIHByb2Nlc3Mgd2l0aCB0aGUgY3VycmVudCBjYXRlZ29yeSBhbmQgdmlkZW9fY2F0ZWdvcnlcclxuICAgICAgc3RhcnRVcGxvYWRXaXRoQ2F0ZWdvcnkoc3RhdGUubWVkaWFTdWJ0eXBlLCBmaW5hbFZpZGVvQ2F0ZWdvcnkpLnRoZW4oKCkgPT4ge1xyXG4gICAgICAgIC8vIFVwbG9hZCBjb21wbGV0ZWQgc3VjY2Vzc2Z1bGx5XHJcbiAgICAgICAgY29uc29sZS5sb2coJ1VwbG9hZCBjb21wbGV0ZWQgc3VjY2Vzc2Z1bGx5Jyk7XHJcbiAgICAgIH0pLmNhdGNoKChlcnJvcikgPT4ge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1VwbG9hZCBmYWlsZWQ6JywgZXJyb3IpO1xyXG4gICAgICB9KTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyBIYW5kbGUgZmFjZSB2ZXJpZmljYXRpb24gY29tcGxldGVkIGFuZCBzdGFydCB1cGxvYWRcclxuICBjb25zdCBoYW5kbGVGYWNlVmVyaWZpY2F0aW9uQ29tcGxldGVkID0gKCkgPT4ge1xyXG4gICAgY29uc29sZS5sb2coJ0ZhY2UgdmVyaWZpY2F0aW9uIGNvbXBsZXRlZCwgc3RhcnRpbmcgdXBsb2FkIHByb2Nlc3MnKTtcclxuXHJcbiAgICAvLyBDaGVjayBpZiB3ZSBoYXZlIGEgZmlsZSBpbiB0aGUgc3RhdGVcclxuICAgIGlmICghc3RhdGUuZmlsZSkge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdObyBmaWxlIGZvdW5kIGluIHN0YXRlIGFmdGVyIGZhY2UgdmVyaWZpY2F0aW9uJyk7XHJcbiAgICAgIHNob3dFcnJvckFsZXJ0KCdVcGxvYWQgRXJyb3InLCAnU29tZXRoaW5nIHdlbnQgd3Jvbmcgd2l0aCB5b3VyIGZpbGUgdXBsb2FkLiBQbGVhc2UgdHJ5IGFnYWluLicpO1xyXG4gICAgICBzZXRQaGFzZSgndHlwZVNlbGVjdGlvbicpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgY29uc29sZS5sb2coJ0ZpbGUgY29uZmlybWVkIGluIHN0YXRlIGFmdGVyIGZhY2UgdmVyaWZpY2F0aW9uOicsIHN0YXRlLmZpbGUubmFtZSk7XHJcblxyXG4gICAgLy8gRm9yIG1vbWVudHMgKHN0b3JpZXMpLCB1c2UgY29tcGxldGVseSBzZXBhcmF0ZSB1cGxvYWQgZmxvd1xyXG4gICAgaWYgKHNlbGVjdGVkVHlwZSA9PT0gJ21vbWVudHMnKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdVUExPQUQgTUFOQUdFUiAtIE1vbWVudHMgZGV0ZWN0ZWQgYWZ0ZXIgZmFjZSB2ZXJpZmljYXRpb24sIHVzaW5nIGRlZGljYXRlZCBtb21lbnRzIHVwbG9hZCBmbG93Jyk7XHJcblxyXG4gICAgICAvLyBTZXQgYSBkZWZhdWx0IHRpdGxlIGlmIG5vdCBhbHJlYWR5IHNldCAodXNpbmcgZmlsZW5hbWUgd2l0aG91dCBleHRlbnNpb24pXHJcbiAgICAgIGlmICghc3RhdGUudGl0bGUgfHwgIXN0YXRlLnRpdGxlLnRyaW0oKSkge1xyXG4gICAgICAgIGNvbnN0IGRlZmF1bHRUaXRsZSA9IHN0YXRlLmZpbGUubmFtZS5yZXBsYWNlKC9cXC5bXi8uXSskLywgXCJcIik7IC8vIFJlbW92ZSBmaWxlIGV4dGVuc2lvblxyXG4gICAgICAgIHNldFRpdGxlKGRlZmF1bHRUaXRsZSk7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ1VQTE9BRCBNQU5BR0VSIC0gU2V0IGRlZmF1bHQgdGl0bGUgZm9yIG1vbWVudHM6JywgZGVmYXVsdFRpdGxlKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gQ2FsbCBkZWRpY2F0ZWQgbW9tZW50cyB1cGxvYWQgZnVuY3Rpb25cclxuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgaGFuZGxlTW9tZW50c1VwbG9hZCgpO1xyXG4gICAgICB9LCAxMDApO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgLy8gVHJ5IHRvIGdldCB2ZW5kb3IgZGV0YWlscyBmcm9tIGxvY2FsU3RvcmFnZSBmaXJzdFxyXG4gICAgbGV0IHZlbmRvckRldGFpbHNEYXRhID0gdmVuZG9yRGV0YWlsc1JlZi5jdXJyZW50O1xyXG5cclxuICAgIC8vIElmIG5vdCBpbiByZWYsIHRyeSBsb2NhbFN0b3JhZ2VcclxuICAgIGlmICghdmVuZG9yRGV0YWlsc0RhdGEgfHwgT2JqZWN0LmtleXModmVuZG9yRGV0YWlsc0RhdGEpLmxlbmd0aCA9PT0gMCkge1xyXG4gICAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IHN0b3JlZFZlbmRvckRldGFpbHMgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnd2VkemF0X3ZlbmRvcl9kZXRhaWxzJyk7XHJcbiAgICAgICAgaWYgKHN0b3JlZFZlbmRvckRldGFpbHMpIHtcclxuICAgICAgICAgIHZlbmRvckRldGFpbHNEYXRhID0gSlNPTi5wYXJzZShzdG9yZWRWZW5kb3JEZXRhaWxzKTtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdVUExPQUQgTUFOQUdFUiAtIFJldHJpZXZlZCB2ZW5kb3IgZGV0YWlscyBmcm9tIGxvY2FsU3RvcmFnZTonLCBzdG9yZWRWZW5kb3JEZXRhaWxzKTtcclxuXHJcbiAgICAgICAgICAvLyBVcGRhdGUgdGhlIHJlZiB3aXRoIHRoZSBsb2NhbFN0b3JhZ2UgZGF0YVxyXG4gICAgICAgICAgdmVuZG9yRGV0YWlsc1JlZi5jdXJyZW50ID0gdmVuZG9yRGV0YWlsc0RhdGE7XHJcblxyXG4gICAgICAgICAgLy8gTG9nIHRoZSB2ZW5kb3IgZGV0YWlscyB3ZSBmb3VuZFxyXG4gICAgICAgICAgY29uc29sZS5sb2coYFVQTE9BRCBNQU5BR0VSIC0gRm91bmQgJHtPYmplY3Qua2V5cyh2ZW5kb3JEZXRhaWxzRGF0YSkubGVuZ3RofSB2ZW5kb3IgZGV0YWlscyBpbiBsb2NhbFN0b3JhZ2VgKTtcclxuICAgICAgICAgIE9iamVjdC5lbnRyaWVzKHZlbmRvckRldGFpbHNEYXRhKS5mb3JFYWNoKChbdmVuZG9yVHlwZSwgZGV0YWlsc106IFtzdHJpbmcsIGFueV0pID0+IHtcclxuICAgICAgICAgICAgaWYgKGRldGFpbHMgJiYgZGV0YWlscy5uYW1lICYmIGRldGFpbHMubW9iaWxlTnVtYmVyKSB7XHJcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coYFVQTE9BRCBNQU5BR0VSIC0gVmVuZG9yICR7dmVuZG9yVHlwZX06ICR7ZGV0YWlscy5uYW1lfSAoJHtkZXRhaWxzLm1vYmlsZU51bWJlcn0pYCk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH0pO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnVVBMT0FEIE1BTkFHRVIgLSBObyB2ZW5kb3IgZGV0YWlscyBmb3VuZCBpbiBsb2NhbFN0b3JhZ2UnKTtcclxuICAgICAgICB9XHJcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignVVBMT0FEIE1BTkFHRVIgLSBGYWlsZWQgdG8gcmV0cmlldmUgdmVuZG9yIGRldGFpbHMgZnJvbSBsb2NhbFN0b3JhZ2U6JywgZXJyb3IpO1xyXG4gICAgICB9XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBjb25zb2xlLmxvZyhgVVBMT0FEIE1BTkFHRVIgLSBVc2luZyAke09iamVjdC5rZXlzKHZlbmRvckRldGFpbHNEYXRhKS5sZW5ndGh9IHZlbmRvciBkZXRhaWxzIGZyb20gcmVmYCk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gVHJ5IHRvIGdldCB2aWRlb19jYXRlZ29yeSBmcm9tIGxvY2FsU3RvcmFnZVxyXG4gICAgbGV0IHZpZGVvQ2F0ZWdvcnkgPSBzdGF0ZS5kZXRhaWxGaWVsZHMudmlkZW9fY2F0ZWdvcnk7XHJcbiAgICBpZiAoIXZpZGVvQ2F0ZWdvcnkpIHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zdCBzdG9yZWRWaWRlb0NhdGVnb3J5ID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3dlZHphdF92aWRlb19jYXRlZ29yeScpO1xyXG4gICAgICAgIGlmIChzdG9yZWRWaWRlb0NhdGVnb3J5KSB7XHJcbiAgICAgICAgICB2aWRlb0NhdGVnb3J5ID0gc3RvcmVkVmlkZW9DYXRlZ29yeTtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdVUExPQUQgTUFOQUdFUiAtIFJldHJpZXZlZCB2aWRlb19jYXRlZ29yeSBmcm9tIGxvY2FsU3RvcmFnZTonLCB2aWRlb0NhdGVnb3J5KTtcclxuXHJcbiAgICAgICAgICAvLyBTZXQgaXQgaW4gdGhlIHN0YXRlXHJcbiAgICAgICAgICBzZXREZXRhaWxGaWVsZCgndmlkZW9fY2F0ZWdvcnknLCB2aWRlb0NhdGVnb3J5KTtcclxuICAgICAgICB9XHJcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignVVBMT0FEIE1BTkFHRVIgLSBGYWlsZWQgdG8gcmV0cmlldmUgdmlkZW9fY2F0ZWdvcnkgZnJvbSBsb2NhbFN0b3JhZ2U6JywgZXJyb3IpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLy8gRW5zdXJlIHZlbmRvciBkZXRhaWxzIGFyZSBwcmVzZW50XHJcbiAgICBpZiAodmVuZG9yRGV0YWlsc0RhdGEpIHtcclxuICAgICAgY29uc29sZS5sb2coJ1VQTE9BRCBNQU5BR0VSIC0gQXBwbHlpbmcgdmVuZG9yIGRldGFpbHMgdG8gc3RhdGUnKTtcclxuXHJcbiAgICAgIC8vIENyZWF0ZSBhIGJhdGNoIG9mIGFsbCBkZXRhaWwgZmllbGRzIHRvIHVwZGF0ZSBhdCBvbmNlXHJcbiAgICAgIGNvbnN0IGRldGFpbEZpZWxkVXBkYXRlczogUmVjb3JkPHN0cmluZywgc3RyaW5nPiA9IHt9O1xyXG4gICAgICBsZXQgY29tcGxldGVWZW5kb3JDb3VudCA9IDA7XHJcblxyXG4gICAgICAvLyBSZS1hcHBseSB2ZW5kb3IgZGV0YWlscyB0byBlbnN1cmUgdGhleSdyZSBpbiB0aGUgc3RhdGVcclxuICAgICAgT2JqZWN0LmVudHJpZXModmVuZG9yRGV0YWlsc0RhdGEpLmZvckVhY2goKFt2ZW5kb3JUeXBlLCBkZXRhaWxzXSkgPT4ge1xyXG4gICAgICAgIGlmIChkZXRhaWxzICYmIGRldGFpbHMubmFtZSAmJiBkZXRhaWxzLm1vYmlsZU51bWJlcikge1xyXG4gICAgICAgICAgLy8gQWRkIHRvIHRoZSBiYXRjaFxyXG4gICAgICAgICAgZGV0YWlsRmllbGRVcGRhdGVzW2B2ZW5kb3JfJHt2ZW5kb3JUeXBlfV9uYW1lYF0gPSBkZXRhaWxzLm5hbWU7XHJcbiAgICAgICAgICBkZXRhaWxGaWVsZFVwZGF0ZXNbYHZlbmRvcl8ke3ZlbmRvclR5cGV9X2NvbnRhY3RgXSA9IGRldGFpbHMubW9iaWxlTnVtYmVyO1xyXG4gICAgICAgICAgY29tcGxldGVWZW5kb3JDb3VudCsrO1xyXG5cclxuICAgICAgICAgIC8vIEFsc28gc2V0IG5vcm1hbGl6ZWQgdmVyc2lvbnNcclxuICAgICAgICAgIGNvbnN0IG5vcm1hbGl6ZWRUeXBlID0gdmVuZG9yVHlwZSA9PT0gJ21ha2V1cEFydGlzdCcgPyAnbWFrZXVwX2FydGlzdCcgOlxyXG4gICAgICAgICAgICB2ZW5kb3JUeXBlID09PSAnZGVjb3JhdGlvbnMnID8gJ2RlY29yYXRpb24nIDogdmVuZG9yVHlwZTtcclxuXHJcbiAgICAgICAgICBpZiAobm9ybWFsaXplZFR5cGUgIT09IHZlbmRvclR5cGUpIHtcclxuICAgICAgICAgICAgZGV0YWlsRmllbGRVcGRhdGVzW2B2ZW5kb3JfJHtub3JtYWxpemVkVHlwZX1fbmFtZWBdID0gZGV0YWlscy5uYW1lO1xyXG4gICAgICAgICAgICBkZXRhaWxGaWVsZFVwZGF0ZXNbYHZlbmRvcl8ke25vcm1hbGl6ZWRUeXBlfV9jb250YWN0YF0gPSBkZXRhaWxzLm1vYmlsZU51bWJlcjtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgLy8gQXBwbHkgYWxsIHVwZGF0ZXMgYXQgb25jZVxyXG4gICAgICBjb25zb2xlLmxvZyhgVVBMT0FEIE1BTkFHRVIgLSBBcHBseWluZyAke2NvbXBsZXRlVmVuZG9yQ291bnR9IGNvbXBsZXRlIHZlbmRvciBkZXRhaWxzIHRvIHN0YXRlYCk7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdVUExPQUQgTUFOQUdFUiAtIERldGFpbCBmaWVsZCB1cGRhdGVzOicsIEpTT04uc3RyaW5naWZ5KGRldGFpbEZpZWxkVXBkYXRlcykpO1xyXG5cclxuICAgICAgLy8gQXBwbHkgZWFjaCB1cGRhdGUgaW5kaXZpZHVhbGx5IHRvIGVuc3VyZSB0aGV5J3JlIGFsbCBzZXRcclxuICAgICAgT2JqZWN0LmVudHJpZXMoZGV0YWlsRmllbGRVcGRhdGVzKS5mb3JFYWNoKChbZmllbGQsIHZhbHVlXSkgPT4ge1xyXG4gICAgICAgIHNldERldGFpbEZpZWxkKGZpZWxkLCB2YWx1ZSk7XHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgLy8gQWRkIGEgZGVsYXkgYmVmb3JlIHByb2NlZWRpbmcgdG8gZW5zdXJlIHN0YXRlIHVwZGF0ZXMgYXJlIGFwcGxpZWRcclxuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ1VQTE9BRCBNQU5BR0VSIC0gVmVuZG9yIGRldGFpbHMgYXBwbGllZCB0byBzdGF0ZSwgcHJvY2VlZGluZyB3aXRoIHVwbG9hZCcpO1xyXG4gICAgICAgIHByb2NlZWRXaXRoVXBsb2FkKHZpZGVvQ2F0ZWdvcnkpO1xyXG4gICAgICB9LCA1MDApO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgY29uc29sZS5sb2coJ1VQTE9BRCBNQU5BR0VSIC0gTm8gdmVuZG9yIGRldGFpbHMgZm91bmQsIHByb2NlZWRpbmcgd2l0aCB1cGxvYWQnKTtcclxuICAgICAgcHJvY2VlZFdpdGhVcGxvYWQodmlkZW9DYXRlZ29yeSk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gVGhpcyBjb2RlIGhhcyBiZWVuIG1vdmVkIHRvIHRoZSBwcm9jZWVkV2l0aFVwbG9hZCBmdW5jdGlvblxyXG4gIH07XHJcblxyXG4gIC8vIEhhbmRsZSBnb2luZyBiYWNrIHRvIHBlcnNvbmFsIGRldGFpbHMgZnJvbSB1cGxvYWQgZXJyb3JcclxuICBjb25zdCBoYW5kbGVCYWNrVG9QZXJzb25hbERldGFpbHMgPSAoKSA9PiB7XHJcbiAgICAvLyBjb25zb2xlLmxvZygnR29pbmcgYmFjayB0byBwZXJzb25hbCBkZXRhaWxzIHdpdGggc3RvcmVkIGRhdGE6JywgcGVyc29uYWxEZXRhaWxzKTtcclxuXHJcbiAgICAvLyBNYWtlIHN1cmUgdGhlIHBlcnNvbmFsIGRldGFpbHMgYXJlIHNldCBpbiB0aGUgY29udGV4dFxyXG4gICAgaWYgKHBlcnNvbmFsRGV0YWlscy5jYXB0aW9uICYmIHBlcnNvbmFsRGV0YWlscy5jYXB0aW9uLnRyaW0oKSkge1xyXG4gICAgICAvLyBVc2UgdGhlIGdsb2JhbCBjb250ZXh0IGZ1bmN0aW9uIHRvIHNldCBhbGwgcGVyc29uYWwgZGV0YWlscyBhdCBvbmNlXHJcbiAgICAgIHNldFBlcnNvbmFsRGV0YWlscyhwZXJzb25hbERldGFpbHMpO1xyXG4gICAgfVxyXG5cclxuICAgIHNldFBoYXNlKCdwZXJzb25hbERldGFpbHMnKTtcclxuICB9O1xyXG5cclxuICAvLyBIYW5kbGUgY2xvc2UgbW9kYWxcclxuICBjb25zdCBoYW5kbGVDbG9zZSA9ICgpID0+IHtcclxuICAgIC8vIENoZWNrIGlmIHVwbG9hZCB3YXMgc3VjY2Vzc2Z1bCBhbmQgY2FsbCBvblVwbG9hZENvbXBsZXRlXHJcbiAgICBpZiAoc3RhdGUuc3RlcCA9PT0gJ2NvbXBsZXRlJyAmJiBvblVwbG9hZENvbXBsZXRlKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdVcGxvYWQgY29tcGxldGVkIHN1Y2Nlc3NmdWxseSwgY2FsbGluZyBvblVwbG9hZENvbXBsZXRlIGNhbGxiYWNrJyk7XHJcbiAgICAgIG9uVXBsb2FkQ29tcGxldGUoKTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBSZXNldCB0aGUgcGhhc2UgZmlyc3RcclxuICAgIHNldFBoYXNlKCdjbG9zZWQnKTtcclxuXHJcbiAgICAvLyBDYWxsIHRoZSBvbkNsb3NlIGNhbGxiYWNrIGlmIHByb3ZpZGVkXHJcbiAgICBpZiAob25DbG9zZSkge1xyXG4gICAgICBvbkNsb3NlKCk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gUmVzZXQgdGhlIHVwbG9hZCBzdGF0ZSBhZnRlciBhIHNob3J0IGRlbGF5IHRvIGVuc3VyZSB0aGUgbW9kYWwgaXMgY2xvc2VkIGZpcnN0XHJcbiAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgcmVzZXRVcGxvYWQoKTtcclxuICAgICAgY29uc29sZS5sb2coJ1VwbG9hZCBzdGF0ZSByZXNldCBhZnRlciBtb2RhbCBjbG9zZScpO1xyXG4gICAgfSwgMTAwKTtcclxuICB9O1xyXG5cclxuICAvLyBSZW5kZXIgc2VsZWN0ZWQgcGhhc2UgY29tcG9uZW50XHJcbiAgaWYgKHBoYXNlID09PSAnY2xvc2VkJykge1xyXG4gICAgcmV0dXJuIG51bGw7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPD5cclxuICAgICAge3BoYXNlID09PSAndHlwZVNlbGVjdGlvbicgJiYgKFxyXG4gICAgICAgIDxVcGxvYWRUeXBlU2VsZWN0aW9uXHJcbiAgICAgICAgICBvbk5leHQ9e2hhbmRsZVR5cGVTZWxlY3RlZH1cclxuICAgICAgICAgIG9uQ2xvc2U9e2hhbmRsZUNsb3NlfVxyXG4gICAgICAgIC8+XHJcbiAgICAgICl9XHJcblxyXG4gICAgICB7cGhhc2UgPT09ICdjYXRlZ29yeVNlbGVjdGlvbicgJiYgKFxyXG4gICAgICAgIDxWaWRlb0NhdGVnb3J5U2VsZWN0aW9uXHJcbiAgICAgICAgICBvbk5leHQ9e2hhbmRsZUNhdGVnb3J5U2VsZWN0ZWR9XHJcbiAgICAgICAgICBvbkJhY2s9eygpID0+IHNldFBoYXNlKCd0eXBlU2VsZWN0aW9uJyl9XHJcbiAgICAgICAgICBvblVwbG9hZD17aGFuZGxlQ2F0ZWdvcnlTZWxlY3RlZH0gLy8gVGhpcyBpcyBub3cgcmVkdW5kYW50IGJ1dCBrZXB0IGZvciBjb21wYXRpYmlsaXR5XHJcbiAgICAgICAgICBvblRodW1ibmFpbFVwbG9hZD17aGFuZGxlVGh1bWJuYWlsVXBsb2FkfVxyXG4gICAgICAgICAgb25DbG9zZT17aGFuZGxlQ2xvc2V9XHJcbiAgICAgICAgICBtZWRpYVR5cGU9e3N0YXRlLm1lZGlhVHlwZSBhcyAncGhvdG8nIHwgJ3ZpZGVvJ30gLy8gUGFzcyB0aGUgY3VycmVudCBtZWRpYSB0eXBlXHJcbiAgICAgICAgICBzZWxlY3RlZFR5cGU9e3NlbGVjdGVkVHlwZX0gLy8gUGFzcyB0aGUgc2VsZWN0ZWQgdHlwZSAobW9tZW50cywgZmxhc2hlcywgZXRjLilcclxuICAgICAgICAvPlxyXG4gICAgICApfVxyXG5cclxuICAgICAge3BoYXNlID09PSAndGh1bWJuYWlsU2VsZWN0aW9uJyAmJiBzdGF0ZS5maWxlICYmIChcclxuICAgICAgICA8VGh1bWJuYWlsU2VsZWN0aW9uXHJcbiAgICAgICAgICB2aWRlb0ZpbGU9e3N0YXRlLmZpbGV9XHJcbiAgICAgICAgICBvbk5leHQ9e2hhbmRsZVRodW1ibmFpbFNlbGVjdGVkfVxyXG4gICAgICAgICAgb25CYWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgIC8vIEdvIGJhY2sgdG8gY2F0ZWdvcnkgc2VsZWN0aW9uIGluc3RlYWQgb2YgdHJpZ2dlcmluZyBmaWxlIHVwbG9hZCBhZ2FpblxyXG4gICAgICAgICAgICBpZiAoWydmbGFzaGVzJywgJ2dsaW1wc2VzJywgJ21vdmllcyddLmluY2x1ZGVzKHNlbGVjdGVkVHlwZSkpIHtcclxuICAgICAgICAgICAgICBzZXRQaGFzZSgnY2F0ZWdvcnlTZWxlY3Rpb24nKTtcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAvLyBGb3IgbW9tZW50cywgZ28gYmFjayB0byB0eXBlIHNlbGVjdGlvblxyXG4gICAgICAgICAgICAgIHNldFBoYXNlKCd0eXBlU2VsZWN0aW9uJyk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH19XHJcbiAgICAgICAgICBvbkNsb3NlPXsoKSA9PiB7XHJcbiAgICAgICAgICAgIC8vIENvbXBsZXRlbHkgcmVzZXQgdGhlIHN0YXRlIGJlZm9yZSBjbG9zaW5nXHJcbiAgICAgICAgICAgIHJlc2V0VXBsb2FkKCk7XHJcbiAgICAgICAgICAgIGhhbmRsZUNsb3NlKCk7XHJcbiAgICAgICAgICB9fVxyXG4gICAgICAgIC8+XHJcbiAgICAgICl9XHJcblxyXG4gICAgICB7cGhhc2UgPT09ICdwZXJzb25hbERldGFpbHMnICYmIChcclxuICAgICAgICA8UGVyc29uYWxEZXRhaWxzXHJcbiAgICAgICAgICBvbk5leHQ9e2hhbmRsZVBlcnNvbmFsRGV0YWlsc0NvbXBsZXRlZH1cclxuICAgICAgICAgIG9uQmFjaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAvLyBHbyBiYWNrIHRvIHRodW1ibmFpbCBzZWxlY3Rpb24gZm9yIHZpZGVvc1xyXG4gICAgICAgICAgICBpZiAoc3RhdGUubWVkaWFUeXBlID09PSAndmlkZW8nICYmIHN0YXRlLmZpbGUpIHtcclxuICAgICAgICAgICAgICBzZXRQaGFzZSgndGh1bWJuYWlsU2VsZWN0aW9uJyk7XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgLy8gRm9yIHBob3RvcywgZ28gYmFjayB0byB0eXBlIHNlbGVjdGlvblxyXG4gICAgICAgICAgICAgIHNldFBoYXNlKCd0eXBlU2VsZWN0aW9uJyk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH19XHJcbiAgICAgICAgICBvbkNsb3NlPXsoKSA9PiB7XHJcbiAgICAgICAgICAgIC8vIENvbXBsZXRlbHkgcmVzZXQgdGhlIHN0YXRlIGJlZm9yZSBjbG9zaW5nXHJcbiAgICAgICAgICAgIHJlc2V0VXBsb2FkKCk7XHJcbiAgICAgICAgICAgIGhhbmRsZUNsb3NlKCk7XHJcbiAgICAgICAgICB9fVxyXG4gICAgICAgICAgcHJldmlld0ltYWdlPXtwcmV2aWV3SW1hZ2V9XHJcbiAgICAgICAgICB2aWRlb0ZpbGU9e3N0YXRlLm1lZGlhVHlwZSA9PT0gJ3ZpZGVvJyA/IHN0YXRlLmZpbGUgOiBudWxsfVxyXG4gICAgICAgICAgbWVkaWFUeXBlPXtzdGF0ZS5tZWRpYVR5cGV9XHJcbiAgICAgICAgICBjb250ZW50VHlwZT17Z2V0Q29udGVudFR5cGUoKX0gLy8gUGFzcyB0aGUgY29udGVudCB0eXBlIHRvIGRldGVybWluZSB3aGljaCBmaWVsZHMgdG8gc2hvd1xyXG4gICAgICAgICAgaW5pdGlhbERldGFpbHM9e3BlcnNvbmFsRGV0YWlsc30gLy8gUGFzcyB0aGUgc3RvcmVkIHBlcnNvbmFsIGRldGFpbHNcclxuICAgICAgICAvPlxyXG4gICAgICApfVxyXG5cclxuICAgICAge3BoYXNlID09PSAndmVuZG9yRGV0YWlscycgJiYgKFxyXG4gICAgICAgIDxWZW5kb3JEZXRhaWxzXHJcbiAgICAgICAgICBvbk5leHQ9e2hhbmRsZVZlbmRvckRldGFpbHNDb21wbGV0ZWR9XHJcbiAgICAgICAgICBvbkJhY2s9eygpID0+IHNldFBoYXNlKCdwZXJzb25hbERldGFpbHMnKX1cclxuICAgICAgICAgIG9uQ2xvc2U9eygpID0+IHtcclxuICAgICAgICAgICAgLy8gQ29tcGxldGVseSByZXNldCB0aGUgc3RhdGUgYmVmb3JlIGNsb3NpbmdcclxuICAgICAgICAgICAgcmVzZXRVcGxvYWQoKTtcclxuICAgICAgICAgICAgaGFuZGxlQ2xvc2UoKTtcclxuICAgICAgICAgIH19XHJcbiAgICAgICAgICBpbml0aWFsVmVuZG9yRGV0YWlscz17dmVuZG9yRGV0YWlsc0RhdGF9IC8vIFBhc3MgdGhlIHN0b3JlZCB2ZW5kb3IgZGV0YWlsc1xyXG4gICAgICAgICAgdmlkZW9DYXRlZ29yeT17KCgpID0+IHtcclxuICAgICAgICAgICAgY29uc3QgY2F0ZWdvcnkgPSBzdGF0ZS5kZXRhaWxGaWVsZHMudmlkZW9fY2F0ZWdvcnkgfHwgJ215X3dlZGRpbmcnO1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygnVVBMT0FEIE1BTkFHRVIgLSBQYXNzaW5nIHZpZGVvIGNhdGVnb3J5IHRvIFZlbmRvckRldGFpbHM6JywgY2F0ZWdvcnkpO1xyXG4gICAgICAgICAgICByZXR1cm4gY2F0ZWdvcnk7XHJcbiAgICAgICAgICB9KSgpfSAvLyBQYXNzIHRoZSBiYWNrZW5kIHZpZGVvIGNhdGVnb3J5IHdpdGggZGVidWdnaW5nXHJcbiAgICAgICAgLz5cclxuICAgICAgKX1cclxuXHJcbiAgICAgIHtwaGFzZSA9PT0gJ2ZhY2VWZXJpZmljYXRpb24nICYmIChcclxuICAgICAgICA8RmFjZVZlcmlmaWNhdGlvblxyXG4gICAgICAgICAgb25VcGxvYWQ9e2hhbmRsZUZhY2VWZXJpZmljYXRpb25Db21wbGV0ZWR9XHJcbiAgICAgICAgICBvbkJhY2s9eygpID0+IHtcclxuICAgICAgICAgICAgLy8gTmV3IGZsb3cgbG9naWMgZm9yIGJhY2sgbmF2aWdhdGlvbjpcclxuICAgICAgICAgICAgLy8gLSBNb21lbnRzOiBHbyBiYWNrIHRvIHRodW1ibmFpbCBzZWxlY3Rpb24gKG9yIHR5cGUgc2VsZWN0aW9uIGZvciBpbWFnZXMpXHJcbiAgICAgICAgICAgIC8vIC0gUGhvdG9zOiBHbyBiYWNrIHRvIHBlcnNvbmFsIGRldGFpbHNcclxuICAgICAgICAgICAgLy8gLSBWaWRlb3M6IEdvIGJhY2sgdG8gdmVuZG9yIGRldGFpbHNcclxuICAgICAgICAgICAgaWYgKHNlbGVjdGVkVHlwZSA9PT0gJ21vbWVudHMnIHx8IHN0YXRlLm1lZGlhU3VidHlwZSA9PT0gJ3N0b3J5Jykge1xyXG4gICAgICAgICAgICAgIC8vIEZvciBtb21lbnRzLCBnbyBiYWNrIHRvIHRodW1ibmFpbCBzZWxlY3Rpb24gZm9yIHZpZGVvcywgb3IgdHlwZSBzZWxlY3Rpb24gZm9yIGltYWdlc1xyXG4gICAgICAgICAgICAgIGlmIChzdGF0ZS5tZWRpYVR5cGUgPT09ICd2aWRlbycpIHtcclxuICAgICAgICAgICAgICAgIHNldFBoYXNlKCd0aHVtYm5haWxTZWxlY3Rpb24nKTtcclxuICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgc2V0UGhhc2UoJ3R5cGVTZWxlY3Rpb24nKTtcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoc3RhdGUubWVkaWFUeXBlID09PSAncGhvdG8nKSB7XHJcbiAgICAgICAgICAgICAgc2V0UGhhc2UoJ3BlcnNvbmFsRGV0YWlscycpO1xyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgIHNldFBoYXNlKCd2ZW5kb3JEZXRhaWxzJyk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH19XHJcbiAgICAgICAgICBvbkNsb3NlPXsoKSA9PiB7XHJcbiAgICAgICAgICAgIC8vIENvbXBsZXRlbHkgcmVzZXQgdGhlIHN0YXRlIGJlZm9yZSBjbG9zaW5nXHJcbiAgICAgICAgICAgIHJlc2V0VXBsb2FkKCk7XHJcbiAgICAgICAgICAgIGhhbmRsZUNsb3NlKCk7XHJcbiAgICAgICAgICB9fVxyXG4gICAgICAgIC8+XHJcbiAgICAgICl9XHJcblxyXG4gICAgICB7KHBoYXNlID09PSAndXBsb2FkaW5nJyB8fCBwaGFzZSA9PT0gJ2NvbXBsZXRlJykgJiYgKFxyXG4gICAgICAgIDxVcGxvYWRQcm9ncmVzc1xyXG4gICAgICAgICAgb25DbG9zZT17KCkgPT4ge1xyXG4gICAgICAgICAgICAvLyBDb21wbGV0ZWx5IHJlc2V0IHRoZSBzdGF0ZSBiZWZvcmUgY2xvc2luZ1xyXG4gICAgICAgICAgICByZXNldFVwbG9hZCgpO1xyXG4gICAgICAgICAgICBoYW5kbGVDbG9zZSgpO1xyXG4gICAgICAgICAgfX1cclxuICAgICAgICAgIG9uR29CYWNrPXtoYW5kbGVCYWNrVG9QZXJzb25hbERldGFpbHN9XHJcbiAgICAgICAgLz5cclxuICAgICAgKX1cclxuICAgIDwvPlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBVcGxvYWRNYW5hZ2VyO1xyXG5cclxuXHJcblxyXG5cclxuXHJcblxyXG5cclxuXHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlUmVmIiwidXNlRWZmZWN0IiwiVXBsb2FkVHlwZVNlbGVjdGlvbiIsIlZpZGVvQ2F0ZWdvcnlTZWxlY3Rpb24iLCJUaHVtYm5haWxTZWxlY3Rpb24iLCJQZXJzb25hbERldGFpbHMiLCJWZW5kb3JEZXRhaWxzIiwiRmFjZVZlcmlmaWNhdGlvbiIsIlVwbG9hZFByb2dyZXNzIiwidXNlVXBsb2FkIiwiZ2V0VmlkZW9EdXJhdGlvbiIsInZhbGlkYXRlVmlkZW9EdXJhdGlvbiIsInNob3dXYXJuaW5nQWxlcnQiLCJzaG93RXJyb3JBbGVydCIsInNob3dBbGVydCIsInVzZU1lZGlhVXBsb2FkIiwiZm9ybWF0VGltZSIsInNlY29uZHMiLCJtaW51dGVzIiwiTWF0aCIsImZsb29yIiwicmVtYWluaW5nU2Vjb25kcyIsIlVwbG9hZE1hbmFnZXIiLCJvbkNsb3NlIiwiaW5pdGlhbFR5cGUiLCJvblVwbG9hZENvbXBsZXRlIiwic3RhdGUiLCJzZXRGaWxlIiwic2V0TWVkaWFUeXBlIiwic2V0Q2F0ZWdvcnkiLCJzZXRNZWRpYVN1YnR5cGUiLCJzZXRUaXRsZSIsInNldERlc2NyaXB0aW9uIiwic2V0RGV0YWlsRmllbGQiLCJzdGFydFVwbG9hZCIsInN0YXJ0VXBsb2FkV2l0aENhdGVnb3J5Iiwic2V0VGh1bWJuYWlsIiwic2V0VmVuZG9yRGV0YWlscyIsInNldFBlcnNvbmFsRGV0YWlscyIsInNldER1cmF0aW9uIiwicmVzZXRVcGxvYWQiLCJwaGFzZSIsInNldFBoYXNlIiwic2VsZWN0ZWRUeXBlIiwic2V0U2VsZWN0ZWRUeXBlIiwic2VsZWN0ZWRDYXRlZ29yeSIsInNldFNlbGVjdGVkQ2F0ZWdvcnkiLCJwcmV2aWV3SW1hZ2UiLCJzZXRQcmV2aWV3SW1hZ2UiLCJ0aHVtYm5haWxJbWFnZSIsInNldFRodW1ibmFpbEltYWdlIiwiZmlsZUlucHV0UmVmIiwidmVuZG9yRGV0YWlsc1JlZiIsImdldENvbnRlbnRUeXBlIiwibWVkaWFTdWJ0eXBlIiwibWVkaWFUeXBlIiwicGVyc29uYWxEZXRhaWxzIiwic2V0TG9jYWxQZXJzb25hbERldGFpbHMiLCJjYXB0aW9uIiwibGlmZVBhcnRuZXIiLCJ3ZWRkaW5nU3R5bGUiLCJwbGFjZSIsImV2ZW50VHlwZSIsImJ1ZGdldCIsImNvbnNvbGUiLCJsb2ciLCJoYW5kbGVUeXBlU2VsZWN0ZWQiLCJ2ZW5kb3JEZXRhaWxzRGF0YSIsInNldFZlbmRvckRldGFpbHNEYXRhIiwidmVudWUiLCJuYW1lIiwibW9iaWxlTnVtYmVyIiwicGhvdG9ncmFwaGVyIiwibWFrZXVwQXJ0aXN0IiwiZGVjb3JhdGlvbnMiLCJjYXRlcmVyIiwibXV0YXRlIiwidXBsb2FkTWVkaWEiLCJpc1BlbmRpbmciLCJpc1VwbG9hZGluZyIsInR5cGUiLCJpbmNsdWRlcyIsImhhbmRsZUZpbGVVcGxvYWQiLCJnZXRNZWRpYVN1YnR5cGVGcm9tU2VsZWN0ZWRUeXBlIiwiaGFuZGxlUGhvdG9VcGxvYWQiLCJoYW5kbGVDYXRlZ29yeVNlbGVjdGVkIiwiY2F0ZWdvcnkiLCJjdXJyZW50VHlwZSIsImN1cnJlbnRNZWRpYVR5cGUiLCJiYWNrZW5kVmlkZW9DYXRlZ29yeSIsImVycm9yIiwiYWxlcnQiLCJoYW5kbGVUaHVtYm5haWxVcGxvYWQiLCJpbnB1dCIsImRvY3VtZW50IiwiY3JlYXRlRWxlbWVudCIsImFjY2VwdCIsIm9uY2hhbmdlIiwiZSIsImZpbGVzIiwidGFyZ2V0IiwibGVuZ3RoIiwiZmlsZSIsInJlYWRlciIsIkZpbGVSZWFkZXIiLCJvbmxvYWQiLCJyZXN1bHQiLCJyZWFkQXNEYXRhVVJMIiwiY2xpY2siLCJnZXRDYXRlZ29yeURpc3BsYXlOYW1lIiwiY2hhckF0IiwidG9VcHBlckNhc2UiLCJzbGljZSIsImdldEFwcHJvcHJpYXRlQ2F0ZWdvcnkiLCJkdXJhdGlvbiIsInZhbHVlIiwic2l6ZSIsInZhbGlkSW1hZ2VUeXBlcyIsInN0YXJ0c1dpdGgiLCJjdXJyZW50RmlsZSIsInNldFRpbWVvdXQiLCJjdXJyZW50Q2F0ZWdvcnkiLCJ2YWxpZGF0aW9uUmVzdWx0IiwiaXNWYWxpZCIsInN1Z2dlc3RlZENhdGVnb3J5IiwiY29uZmlybVN3aXRjaCIsInRpdGxlIiwibWVzc2FnZSIsImNvbmZpcm1UZXh0IiwiY2FuY2VsVGV4dCIsIm9uQ29uZmlybSIsImhhbmRsZVBlcnNvbmFsRGV0YWlsc0NvbXBsZXRlZCIsImRldGFpbHMiLCJ0cmltIiwiaGFuZGxlVmVuZG9yRGV0YWlsc0NvbXBsZXRlZCIsInZlbmRvckRldGFpbHMiLCJub3JtYWxpemVkVmVuZG9yRGV0YWlscyIsIm1ha2V1cF9hcnRpc3QiLCJkZWNvcmF0aW9uIiwiY3VycmVudCIsImxvY2FsU3RvcmFnZSIsInNldEl0ZW0iLCJKU09OIiwic3RyaW5naWZ5IiwiY3VycmVudFZpZGVvQ2F0ZWdvcnkiLCJkZXRhaWxGaWVsZHMiLCJ2aWRlb19jYXRlZ29yeSIsIk9iamVjdCIsImVudHJpZXMiLCJmb3JFYWNoIiwidmVuZG9yVHlwZSIsImtleXMiLCJ2ZW5kb3JOYW1lRmllbGRzIiwiZmlsdGVyIiwia2V5IiwiZW5kc1dpdGgiLCJ2ZW5kb3JDb250YWN0RmllbGRzIiwidGVzdCIsIndpbmRvdyIsIm5hdmlnYXRvciIsInVzZXJBZ2VudCIsIm5vcm1hbGl6ZWRUeXBlIiwiaGFuZGxlVGh1bWJuYWlsU2VsZWN0ZWQiLCJ0aHVtYm5haWxGaWxlIiwicHJvY2VlZFdpdGhVcGxvYWQiLCJ2aWRlb0NhdGVnb3J5IiwiZGVzY3JpcHRpb24iLCJkZXRhaWxGaWVsZHNDb3VudCIsImNvcnJlY3RlZENhdGVnb3J5IiwiZmluYWxWaWRlb0NhdGVnb3J5IiwidGhlbiIsImNhdGNoIiwiaGFuZGxlRmFjZVZlcmlmaWNhdGlvbkNvbXBsZXRlZCIsImRlZmF1bHRUaXRsZSIsInJlcGxhY2UiLCJoYW5kbGVNb21lbnRzVXBsb2FkIiwic3RvcmVkVmVuZG9yRGV0YWlscyIsImdldEl0ZW0iLCJwYXJzZSIsInN0b3JlZFZpZGVvQ2F0ZWdvcnkiLCJkZXRhaWxGaWVsZFVwZGF0ZXMiLCJjb21wbGV0ZVZlbmRvckNvdW50IiwiZmllbGQiLCJoYW5kbGVCYWNrVG9QZXJzb25hbERldGFpbHMiLCJoYW5kbGVDbG9zZSIsInN0ZXAiLCJvbk5leHQiLCJvbkJhY2siLCJvblVwbG9hZCIsIm9uVGh1bWJuYWlsVXBsb2FkIiwidmlkZW9GaWxlIiwiY29udGVudFR5cGUiLCJpbml0aWFsRGV0YWlscyIsImluaXRpYWxWZW5kb3JEZXRhaWxzIiwib25Hb0JhY2siXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/upload/UploadManager.tsx\n"));

/***/ })

});