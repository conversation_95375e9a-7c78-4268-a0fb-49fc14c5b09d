"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7147052decbd\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xca29tbWlcXE9uZURyaXZlXFxEZXNrdG9wXFxXRURaQVRfXFxmcm9udGVuZFxcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjcxNDcwNTJkZWNiZFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/upload/UploadManager.tsx":
/*!*********************************************!*\
  !*** ./components/upload/UploadManager.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _UploadTypeSelection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./UploadTypeSelection */ \"(app-pages-browser)/./components/upload/UploadTypeSelection.tsx\");\n/* harmony import */ var _VideoCategorySelection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./VideoCategorySelection */ \"(app-pages-browser)/./components/upload/VideoCategorySelection.tsx\");\n/* harmony import */ var _ThumbnailSelection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ThumbnailSelection */ \"(app-pages-browser)/./components/upload/ThumbnailSelection.tsx\");\n/* harmony import */ var _PersonalDetails__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./PersonalDetails */ \"(app-pages-browser)/./components/upload/PersonalDetails.tsx\");\n/* harmony import */ var _VendorDetails__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./VendorDetails */ \"(app-pages-browser)/./components/upload/VendorDetails.tsx\");\n/* harmony import */ var _FaceVerification__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./FaceVerification */ \"(app-pages-browser)/./components/upload/FaceVerification.tsx\");\n/* harmony import */ var _UploadProgress__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./UploadProgress */ \"(app-pages-browser)/./components/upload/UploadProgress.tsx\");\n/* harmony import */ var _contexts_UploadContexts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../contexts/UploadContexts */ \"(app-pages-browser)/./contexts/UploadContexts.tsx\");\n/* harmony import */ var _utils_uploadUtils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../utils/uploadUtils */ \"(app-pages-browser)/./utils/uploadUtils.ts\");\n/* harmony import */ var _utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../utils/alertUtils */ \"(app-pages-browser)/./utils/alertUtils.tsx\");\n/* harmony import */ var _hooks_useMedia__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../hooks/useMedia */ \"(app-pages-browser)/./hooks/useMedia.ts\");\n// components/upload/UploadManager.tsx\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function to format time in minutes and seconds\nconst formatTime = (seconds)=>{\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    if (minutes === 0) {\n        return \"\".concat(remainingSeconds, \" seconds\");\n    } else if (minutes === 1 && remainingSeconds === 0) {\n        return '1 minute';\n    } else if (remainingSeconds === 0) {\n        return \"\".concat(minutes, \" minutes\");\n    } else {\n        return \"\".concat(minutes, \" minute\").concat(minutes > 1 ? 's' : '', \" and \").concat(remainingSeconds, \" second\").concat(remainingSeconds !== 1 ? 's' : '');\n    }\n};\nconst UploadManager = (param)=>{\n    let { onClose, initialType, onUploadComplete } = param;\n    _s();\n    const { state, setFile, setMediaType, setCategory, setMediaSubtype, setTitle, setDescription, setDetailField, startUpload, startUploadWithCategory, setThumbnail, setVendorDetails, setPersonalDetails, setDuration, resetUpload } = (0,_contexts_UploadContexts__WEBPACK_IMPORTED_MODULE_9__.useUpload)();\n    const [phase, setPhase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('typeSelection');\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialType || '');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [previewImage, setPreviewImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [thumbnailImage, setThumbnailImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const vendorDetailsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    // Helper function to determine content type for PersonalDetails\n    const getContentType = ()=>{\n        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n            return 'moment';\n        } else if (state.mediaType === 'video') {\n            return 'video';\n        } else {\n            return 'photo';\n        }\n    };\n    // Store personal details to persist between screens\n    const [personalDetails, setLocalPersonalDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        caption: '',\n        lifePartner: '',\n        weddingStyle: '',\n        place: '',\n        eventType: '',\n        budget: ''\n    });\n    // Auto-select the type if initialType is provided\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UploadManager.useEffect\": ()=>{\n            if (initialType) {\n                console.log('Auto-selecting type from initialType:', initialType);\n                handleTypeSelected(initialType);\n            }\n        }\n    }[\"UploadManager.useEffect\"], []);\n    // Store vendor details to persist between screens\n    const [vendorDetailsData, setVendorDetailsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        venue: {\n            name: '',\n            mobileNumber: ''\n        },\n        photographer: {\n            name: '',\n            mobileNumber: ''\n        },\n        makeupArtist: {\n            name: '',\n            mobileNumber: ''\n        },\n        decorations: {\n            name: '',\n            mobileNumber: ''\n        },\n        caterer: {\n            name: '',\n            mobileNumber: ''\n        }\n    });\n    // Use the new media upload hook\n    const { mutate: uploadMedia, isPending: isUploading } = (0,_hooks_useMedia__WEBPACK_IMPORTED_MODULE_12__.useMediaUpload)();\n    // Handle media type selection\n    const handleTypeSelected = (type)=>{\n        // First, completely reset everything\n        resetUpload();\n        setPreviewImage(null);\n        setThumbnailImage(null);\n        // Then set the new type\n        setSelectedType(type);\n        console.log(\"Selected type:\", type);\n        if ([\n            'flashes',\n            'glimpses',\n            'movies',\n            'photos',\n            'moments'\n        ].includes(type)) {\n            // For explicit video types, photos, and moments, set the appropriate media type and go to category selection\n            if (type === 'photos') {\n                console.log('Setting media type to photo for:', type);\n                setMediaType('photo');\n                setMediaSubtype('post');\n            } else if (type === 'moments') {\n                console.log('Setting media type for moments (will be determined by file type)');\n                // For moments, we'll set the media type later based on the file type (photo or video)\n                setMediaSubtype('story');\n            } else {\n                setMediaType('video');\n                setMediaSubtype(getMediaSubtypeFromSelectedType(type));\n            }\n            // Go to category selection for all media types\n            setPhase('categorySelection');\n        } else if (type === 'photo') {\n            // For single photo type (if it exists)\n            console.log('Setting media type to photo for:', type);\n            setMediaType('photo');\n            setMediaSubtype('post');\n            // Use a special photo-only upload handler for photos\n            handlePhotoUpload();\n        }\n    };\n    // Helper function to get the backend media subtype from the selected UI type\n    const getMediaSubtypeFromSelectedType = (type)=>{\n        // Map UI category to backend category for media_subtype\n        switch(type){\n            // Photo types\n            case 'moments':\n                return 'story'; // Backend expects 'story' for moments\n            case 'photos':\n                return 'post'; // Backend expects 'post' for regular photos\n            // Video types\n            case 'flashes':\n                return 'flash'; // Backend expects 'flash'\n            case 'glimpses':\n                return 'glimpse'; // Backend expects 'glimpse'\n            case 'movies':\n                return 'movie'; // Backend expects 'movie'\n            // Default fallback\n            default:\n                return type === 'moments' ? 'story' : 'post'; // Default based on type\n        }\n    };\n    // Handle category selection for both videos and photos\n    const handleCategorySelected = (category)=>{\n        // First, make sure we have a clean state for the new upload\n        // but preserve the selected type and media type\n        const currentType = selectedType;\n        const currentMediaType = state.mediaType;\n        resetUpload();\n        setSelectedType(currentType);\n        setMediaType(currentMediaType);\n        setPreviewImage(null);\n        setThumbnailImage(null);\n        // Now set the new category\n        setSelectedCategory(category);\n        // Get the media subtype based on the selected type\n        let mediaSubtype;\n        if (currentType === 'photos') {\n            // For photos, always use 'post' as the media subtype\n            mediaSubtype = 'post';\n            console.log(\"UPLOAD MANAGER - Using media subtype 'post' for photos\");\n        } else {\n            // For videos, use the subtype based on the selected type\n            mediaSubtype = getMediaSubtypeFromSelectedType(selectedType);\n            console.log(\"UPLOAD MANAGER - Using media subtype \".concat(mediaSubtype, \" based on selected type \").concat(selectedType));\n            console.log(\"UPLOAD MANAGER - Backend expects: flashes→flash, glimpses→glimpse, movies→movie, moments→story\");\n        }\n        console.log(\"UPLOAD MANAGER - Selected category:\", category);\n        console.log(\"UPLOAD MANAGER - Setting media subtype to:\", mediaSubtype);\n        // Set the media subtype in the context\n        setMediaSubtype(mediaSubtype);\n        // Map the selected category to a valid backend video_category\n        let backendVideoCategory = '';\n        if (category === 'my_wedding_videos') {\n            backendVideoCategory = 'my_wedding';\n        } else if (category === 'wedding_influencer') {\n            backendVideoCategory = 'wedding_vlog';\n        }\n        // Make sure we have a valid video_category\n        if (!backendVideoCategory) {\n            console.error('Invalid video category selected:', category);\n            alert('Please select a valid video category');\n            return;\n        }\n        // Set video category in the context for the backend\n        console.log(\"UPLOAD MANAGER - Setting video_category to:\", backendVideoCategory);\n        setDetailField('video_category', backendVideoCategory);\n        // Log the final values\n        console.log(\"UPLOAD MANAGER - Selected category:\", category);\n        console.log(\"UPLOAD MANAGER - Backend video category set to:\", backendVideoCategory);\n        console.log(\"UPLOAD MANAGER - Media subtype set to:\", mediaSubtype);\n        // Proceed to file upload after setting the category\n        if (currentType === 'photos') {\n            // For photos, use the photo-specific upload handler\n            handlePhotoUpload();\n        } else {\n            // For videos, use the standard file upload handler\n            handleFileUpload();\n        }\n    };\n    // Handle thumbnail upload\n    const handleThumbnailUpload = ()=>{\n        // Create a file input element\n        const input = document.createElement('input');\n        input.type = 'file';\n        input.accept = 'image/*';\n        // Handle file selection\n        input.onchange = (e)=>{\n            const files = e.target.files;\n            if (files && files.length > 0) {\n                const file = files[0];\n                // Store the thumbnail\n                setThumbnailImage(file);\n                setThumbnail(file);\n                console.log(\"Thumbnail selected:\", file.name);\n                // Show a preview if needed\n                const reader = new FileReader();\n                reader.onload = (e)=>{\n                    var _e_target;\n                    if ((_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result) {\n                        // You could set a thumbnail preview here if needed\n                        console.log(\"Thumbnail preview ready\");\n                    }\n                };\n                reader.readAsDataURL(file);\n            }\n        };\n        // Trigger the file dialog\n        input.click();\n    };\n    // Get user-friendly display name for a category\n    const getCategoryDisplayName = (category)=>{\n        switch(category){\n            case 'flash':\n                return 'Flash';\n            case 'glimpse':\n                return 'Glimpse';\n            case 'movie':\n                return 'Movie';\n            case 'story':\n                return 'Story';\n            case 'post':\n                return 'Photo';\n            default:\n                return category.charAt(0).toUpperCase() + category.slice(1);\n        }\n    };\n    // Get appropriate category based on duration\n    const getAppropriateCategory = (duration)=>{\n        // For very short videos (1 minute or less), use flash instead of story/moments\n        if (duration <= 60) {\n            return 'flash'; // Very short videos (1 minute or less) - changed from 'story' to 'flash'\n        } else if (duration <= 90) {\n            return 'flash'; // Short videos (1.5 minutes or less)\n        } else if (duration <= 420) {\n            return 'glimpse'; // Medium videos (7 minutes or less)\n        } else {\n            return 'movie'; // Long videos (over 7 minutes)\n        }\n    };\n    // Special handler for photo uploads that strictly enforces image-only files\n    const handlePhotoUpload = ()=>{\n        console.log('handlePhotoUpload called - strict image-only upload');\n        // Create a file input element specifically for photos\n        const input = document.createElement('input');\n        input.type = 'file';\n        // Reset the input value\n        input.value = '';\n        // Only accept image files - explicitly list allowed types\n        input.accept = 'image/jpeg,image/png,image/gif,image/webp';\n        // Handle file selection with strict validation\n        input.onchange = async (e)=>{\n            const files = e.target.files;\n            if (!files || files.length === 0) return;\n            const file = files[0];\n            console.log('Photo file selected:', file.name, file.type, file.size);\n            // Strict validation - must be an image file\n            const validImageTypes = [\n                'image/jpeg',\n                'image/png',\n                'image/gif',\n                'image/webp'\n            ];\n            if (!validImageTypes.includes(file.type)) {\n                console.error('Invalid file type for photos:', file.type);\n                alert('Only JPEG, PNG, GIF, or WebP images can be uploaded as photos. Videos are not allowed.');\n                return;\n            }\n            // Additional check - reject any file that might be a video\n            if (file.type.startsWith('video/')) {\n                console.error('Attempted to upload a video file as photo');\n                alert('Videos cannot be uploaded as photos. Please select an image file.');\n                return;\n            }\n            // For photos, we need to be more careful with state management\n            // First, set the media type and subtype\n            setMediaType('photo');\n            setMediaSubtype('post');\n            // Then set the file in the state\n            setFile(file);\n            console.log('Photo file set in state:', file.name);\n            // Create a local reference to the file for use in the timeout\n            const currentFile = file;\n            // Double-check that the file is set in the state before proceeding\n            setTimeout(()=>{\n                // Check if the file is in the state\n                if (!state.file) {\n                    console.log('File not found in state after setting, trying again');\n                    // Try setting the file again\n                    setFile(currentFile);\n                    // Add another timeout to ensure the file is set\n                    setTimeout(()=>{\n                        if (!state.file) {\n                            console.log('File still not in state, setting it one more time');\n                            setFile(currentFile);\n                        } else {\n                            console.log('File confirmed in state after second attempt:', state.file.name);\n                        }\n                        // New flow logic: For moments (stories), skip personal details and go directly to face verification\n                        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                            console.log('Moments photo upload: skipping personal details, going directly to face verification');\n                            setPhase('faceVerification');\n                        } else {\n                            console.log('Moving to personalDetails phase for photo');\n                            setPhase('personalDetails');\n                        }\n                    }, 100);\n                } else {\n                    console.log('File confirmed in state:', state.file.name);\n                    // New flow logic: For moments (stories), skip personal details and go directly to face verification\n                    if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                        console.log('Moments photo upload: skipping personal details, going directly to face verification');\n                        setPhase('faceVerification');\n                    } else {\n                        console.log('Moving to personalDetails phase for photo');\n                        setPhase('personalDetails');\n                    }\n                }\n            }, 100);\n            // Handle image preview\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                var _e_target;\n                if ((_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result) {\n                    setPreviewImage(e.target.result);\n                    console.log('Preview image set for photo');\n                }\n            };\n            reader.readAsDataURL(file);\n        // Note: We don't set the phase here anymore - it's handled in the timeout above\n        };\n        // Trigger the file dialog\n        input.click();\n    };\n    // This function was previously used but is now replaced by getAppropriateCategory\n    // Keeping a comment here for reference in case it needs to be restored\n    // Handle manual upload button click\n    const handleFileUpload = async (category)=>{\n        console.log('handleFileUpload called with category:', category || 'none');\n        // Create a file input element\n        const input = document.createElement('input');\n        input.type = 'file';\n        // Reset the input value to ensure we get a new file selection event even if the same file is selected\n        input.value = '';\n        if (selectedType === 'moments') {\n            input.accept = 'image/*,video/*';\n        } else {\n            input.accept = selectedType === 'photo' || selectedType === 'photos' ? 'image/jpeg,image/png,image/gif,image/webp' // Explicitly list image types only\n             : 'video/*';\n        }\n        // Handle file selection\n        input.onchange = async (e)=>{\n            const files = e.target.files;\n            if (!files || files.length === 0) return;\n            const file = files[0];\n            console.log('File selected:', file.name, file.type, file.size);\n            // Strict validation for photo uploads - must be an image file\n            if (selectedType === 'photo' || selectedType === 'photos') {\n                const validImageTypes = [\n                    'image/jpeg',\n                    'image/png',\n                    'image/gif',\n                    'image/webp'\n                ];\n                // Check if file is a video or not a valid image type\n                if (file.type.startsWith('video/') || !validImageTypes.includes(file.type)) {\n                    console.error('Invalid file type for photos:', file.type);\n                    (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Invalid File Type', 'Only JPEG, PNG, GIF, or WebP images can be uploaded as photos. Videos are not allowed.');\n                    return;\n                }\n            }\n            // Reset the upload context before setting the new file\n            resetUpload();\n            // Set the file in the state\n            setFile(file);\n            console.log('File set in state:', file.name);\n            // If it's a video, calculate and set the duration\n            // Double-check that we're not trying to upload a video as a photo\n            if (file.type.startsWith('video/')) {\n                // Safety check - don't process videos for photo uploads\n                if (selectedType === 'photo' || selectedType === 'photos') {\n                    console.error('Attempted to process a video file for photo upload');\n                    (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Invalid File Type', 'Videos cannot be uploaded as photos. Please select an image file.');\n                    resetUpload();\n                    return;\n                }\n                try {\n                    const duration = await (0,_utils_uploadUtils__WEBPACK_IMPORTED_MODULE_10__.getVideoDuration)(file);\n                    console.log('Video duration calculated:', duration);\n                    setDuration(duration);\n                    // For moments, check if it's a video and validate the duration (max 1 minute)\n                    if (selectedType === 'moments') {\n                        console.log('Validating moments video duration...');\n                        setMediaType('video');\n                        // Check if the video is longer than 1 minute (60 seconds)\n                        if (duration > 60) {\n                            console.log(\"Moments video too long: \".concat(duration, \" seconds (max: 60 seconds)\"));\n                            // Show a more detailed error message with custom alert\n                            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showWarningAlert)('Moments Video Too Long', \"Moments videos must be 1 minute or less.\\n\\nYour video is \".concat(formatTime(duration), \" long.\\n\\nPlease select a shorter video or trim this video to 1 minute or less.\"));\n                            // Reset the upload context but preserve the selected type and category\n                            const currentType = selectedType;\n                            const currentCategory = selectedCategory;\n                            // First set the phase back to category selection\n                            setPhase('categorySelection');\n                            // Then reset the upload state\n                            setTimeout(()=>{\n                                resetUpload();\n                                setSelectedType(currentType);\n                                setSelectedCategory(currentCategory);\n                                console.log('Reset upload state after moments video duration validation failure');\n                            }, 100);\n                            // Return early to prevent further processing\n                            return;\n                        }\n                        console.log(\"Moments video duration valid: \".concat(duration, \" seconds (max: 60 seconds)\"));\n                        // For moments, we always use 'story' as the media subtype\n                        console.log('Setting media subtype for moments video to story');\n                        setMediaSubtype('story');\n                    }\n                    // If we have a category, validate the duration for that category\n                    if (selectedType && [\n                        'flashes',\n                        'glimpses',\n                        'movies'\n                    ].includes(selectedType)) {\n                        const mediaSubtype = getMediaSubtypeFromSelectedType(selectedType);\n                        const validationResult = (0,_utils_uploadUtils__WEBPACK_IMPORTED_MODULE_10__.validateVideoDuration)(duration, mediaSubtype);\n                        if (!validationResult.isValid) {\n                            // If there's a suggested category, automatically switch to it\n                            if (validationResult.suggestedCategory) {\n                                // For videos that exceed the maximum duration, automatically switch without asking\n                                console.log(\"Video exceeds maximum duration for \".concat(mediaSubtype, \". Automatically switching to \").concat(validationResult.suggestedCategory));\n                                (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showWarningAlert)('Video Duration Notice', \"Your video is too long for the \".concat(getCategoryDisplayName(mediaSubtype), \" category. It will be uploaded as a \").concat(getCategoryDisplayName(validationResult.suggestedCategory), \" instead.\"));\n                                // Switch to the suggested category\n                                console.log(\"Switching to suggested category: \".concat(validationResult.suggestedCategory));\n                                // Make sure we keep a reference to the file\n                                const currentFile = file;\n                                // Update the media subtype\n                                setMediaSubtype(validationResult.suggestedCategory);\n                                // Update the selected type to match the new category\n                                // Never suggest 'story' (moments) for other categories\n                                if (validationResult.suggestedCategory === 'flash') {\n                                    setSelectedType('flashes');\n                                } else if (validationResult.suggestedCategory === 'glimpse') {\n                                    setSelectedType('glimpses');\n                                } else if (validationResult.suggestedCategory === 'movie') {\n                                    setSelectedType('movies');\n                                }\n                                // Removed the 'story' suggestion for short videos\n                                // Make sure the file is still set in the state\n                                setTimeout(()=>{\n                                    if (!state.file) {\n                                        console.log('Re-setting file after category change:', currentFile.name);\n                                        setFile(currentFile);\n                                    }\n                                }, 50);\n                            } else {\n                                // No suggested category, just show the error\n                                (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Video Duration Error', validationResult.error || 'The video duration is not valid for this category.');\n                            }\n                        } else if (validationResult.suggestedCategory && validationResult.suggestedCategory !== mediaSubtype) {\n                            // Video is valid for current category but there's a better category\n                            // For this case, we still give the user a choice since the video is valid for the current category\n                            // Use our custom confirm dialog instead of window.confirm\n                            const confirmSwitch = await (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showAlert)({\n                                title: 'Category Suggestion',\n                                message: \"\".concat(validationResult.error, \"\\n\\nWould you like to switch to the suggested category?\"),\n                                type: 'warning',\n                                confirmText: 'Yes, Switch Category',\n                                cancelText: 'No, Keep Current',\n                                onConfirm: ()=>{}\n                            });\n                            if (confirmSwitch) {\n                                // Switch to the suggested category\n                                console.log(\"Switching to suggested category: \".concat(validationResult.suggestedCategory));\n                                // Make sure we keep a reference to the file\n                                const currentFile = file;\n                                // Update the media subtype\n                                setMediaSubtype(validationResult.suggestedCategory);\n                                // Update the selected type to match the new category\n                                // Never suggest 'story' (moments) for other categories\n                                if (validationResult.suggestedCategory === 'flash') {\n                                    setSelectedType('flashes');\n                                } else if (validationResult.suggestedCategory === 'glimpse') {\n                                    setSelectedType('glimpses');\n                                } else if (validationResult.suggestedCategory === 'movie') {\n                                    setSelectedType('movies');\n                                }\n                                // Removed the 'story' suggestion for short videos\n                                // Make sure the file is still set in the state\n                                setTimeout(()=>{\n                                    if (!state.file) {\n                                        console.log('Re-setting file after category change:', currentFile.name);\n                                        setFile(currentFile);\n                                    }\n                                }, 50);\n                            }\n                        }\n                    }\n                    // Always go to thumbnail selection for videos\n                    console.log('Moving to thumbnailSelection phase');\n                    // Keep a reference to the current file\n                    const currentFile = file;\n                    // Double-check that the file is set in the state before proceeding\n                    if (state.file) {\n                        console.log('File confirmed in state before phase change:', state.file.name);\n                        setPhase('thumbnailSelection');\n                    } else {\n                        console.log('File not found in state before phase change, setting it again');\n                        // Try setting the file again\n                        setFile(currentFile);\n                        // Add a small delay to ensure the state is updated\n                        setTimeout(()=>{\n                            // Double-check again\n                            if (!state.file) {\n                                console.log('File still not in state, setting it one more time');\n                                setFile(currentFile);\n                            }\n                            console.log('Delayed phase change to thumbnailSelection');\n                            setPhase('thumbnailSelection');\n                        }, 100);\n                    }\n                } catch (error) {\n                    console.error('Error calculating video duration:', error);\n                    // For moments videos, we need to enforce the duration check\n                    // If we can't calculate duration, we can't validate it, so we should reject the upload\n                    if (selectedType === 'moments') {\n                        (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Video Error', 'Unable to determine video duration. Please try a different video file.');\n                        resetUpload();\n                        return;\n                    }\n                    console.log('Moving to thumbnailSelection phase despite error');\n                    // Keep a reference to the current file\n                    const currentFile = file;\n                    // Double-check that the file is set in the state before proceeding\n                    if (state.file) {\n                        console.log('File confirmed in state before phase change (error case):', state.file.name);\n                        setPhase('thumbnailSelection');\n                    } else {\n                        console.log('File not found in state before phase change (error case), setting it again');\n                        // Try setting the file again\n                        setFile(currentFile);\n                        // Add a small delay to ensure the state is updated\n                        setTimeout(()=>{\n                            // Double-check again\n                            if (!state.file) {\n                                console.log('File still not in state (error case), setting it one more time');\n                                setFile(currentFile);\n                            }\n                            console.log('Delayed phase change to thumbnailSelection (error case)');\n                            setPhase('thumbnailSelection');\n                        }, 100);\n                    }\n                }\n            } else {\n                // For photos or moments images\n                if (selectedType === 'moments') {\n                    // For moments, we need to set the media type based on the file type\n                    if (file.type.startsWith('image/')) {\n                        console.log('Moments image detected');\n                        setMediaType('photo');\n                        // For moments images, we always use 'story' as the media subtype\n                        setMediaSubtype('story');\n                        // Create a local reference to the file for use in the timeout\n                        const currentFile = file;\n                        // Double-check that the file is set in the state before proceeding\n                        setTimeout(()=>{\n                            // Check if the file is in the state\n                            if (!state.file) {\n                                console.log('Moments photo not found in state after setting, trying again');\n                                // Try setting the file again\n                                setFile(currentFile);\n                            } else {\n                                console.log('Moments photo confirmed in state:', state.file.name);\n                            }\n                        }, 50);\n                    } else {\n                        console.log('Invalid file type for moments. Only images and videos less than 1 minute are allowed.');\n                        (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Invalid File Type', 'Invalid file type for moments. Only images and videos less than 1 minute are allowed.');\n                        // Reset the upload context but preserve the selected type and category\n                        const currentType = selectedType;\n                        const currentCategory = selectedCategory;\n                        // First set the phase back to category selection\n                        setPhase('categorySelection');\n                        // Then reset the upload state\n                        setTimeout(()=>{\n                            resetUpload();\n                            setSelectedType(currentType);\n                            setSelectedCategory(currentCategory);\n                            console.log('Reset upload state after invalid file type for moments');\n                        }, 100);\n                        return;\n                    }\n                }\n                // Handle image preview and set phase\n                const reader = new FileReader();\n                reader.onload = (e)=>{\n                    var _e_target;\n                    if ((_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result) {\n                        setPreviewImage(e.target.result);\n                        console.log('Preview image set for file:', file.name);\n                    }\n                };\n                reader.readAsDataURL(file);\n                // Create a local reference to the file for use in the timeout\n                const currentFile = file;\n                // Double-check that the file is set in the state before proceeding\n                setTimeout(()=>{\n                    // Check if the file is in the state\n                    if (!state.file) {\n                        console.log('File not found in state before moving to personalDetails, setting it again');\n                        // Try setting the file again\n                        setFile(currentFile);\n                        // Add another timeout to ensure the file is set\n                        setTimeout(()=>{\n                            if (!state.file) {\n                                console.log('File still not in state, setting it one more time');\n                                setFile(currentFile);\n                            } else {\n                                console.log('File confirmed in state after second attempt:', state.file.name);\n                            }\n                            // New flow logic: For moments (stories), skip personal details and go directly to face verification\n                            if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                                console.log('Moments image upload: skipping personal details, going directly to face verification');\n                                setPhase('faceVerification');\n                            } else {\n                                console.log('Moving to personalDetails phase for photo/image');\n                                setPhase('personalDetails');\n                            }\n                        }, 100);\n                    } else {\n                        console.log('File confirmed in state:', state.file.name);\n                        // New flow logic: For moments (stories), skip personal details and go directly to face verification\n                        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                            console.log('Moments image upload: skipping personal details, going directly to face verification');\n                            setPhase('faceVerification');\n                        } else {\n                            console.log('Moving to personalDetails phase for photo/image');\n                            setPhase('personalDetails');\n                        }\n                    }\n                }, 100);\n            }\n        };\n        // Trigger the file dialog\n        input.click();\n    };\n    // Handle personal details completed\n    const handlePersonalDetailsCompleted = (details)=>{\n        console.log('Personal details completed:', details);\n        // Store the personal details in local state for component persistence\n        setLocalPersonalDetails(details);\n        // Validate that we have a title\n        if (!details.caption || !details.caption.trim()) {\n            console.error('Caption/title is empty, this should not happen');\n            // Go back to personal details to fix this\n            setPhase('personalDetails');\n            return;\n        }\n        // Set the title in the upload context\n        setTitle(details.caption.trim());\n        // Also store in global context for persistence (this is the upload context function)\n        setPersonalDetails(details);\n        // Check if we have a file in the state\n        if (!state.file) {\n            console.error('No file found in state after personal details');\n            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Upload Error', 'Something went wrong with your file upload. Please try again.');\n            setPhase('typeSelection');\n            return;\n        }\n        console.log('File confirmed in state after personal details:', state.file.name);\n        console.log('Personal details set successfully');\n        console.log('Title set to:', details.caption.trim());\n        console.log('Current selectedType:', selectedType);\n        console.log('Current mediaSubtype:', state.mediaSubtype);\n        // New flow logic based on backend requirements:\n        // - Moments (stories): Skip personal details, go directly to face verification\n        // - Photos: Go to face verification after personal details (no vendor details)\n        // - Videos: Go to vendor details after personal details\n        if (state.mediaType === 'photo') {\n            console.log('Photo upload: proceeding to face verification (no vendor details needed)');\n            setPhase('faceVerification');\n        } else if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n            console.log('Moments upload: proceeding to face verification (no vendor details needed)');\n            setPhase('faceVerification');\n        } else {\n            // For videos (flashes, glimpses, movies), proceed to vendor details\n            console.log('Video upload: proceeding to vendor details');\n            setPhase('vendorDetails');\n        }\n    };\n    // Handle vendor details completed\n    const handleVendorDetailsCompleted = (vendorDetails)=>{\n        // console.log('Vendor details completed:', vendorDetails);\n        // Normalize vendor details to ensure consistent field names\n        const normalizedVendorDetails = {\n            ...vendorDetails\n        };\n        // Ensure we have both frontend and backend field names for makeup artist and decorations\n        if (vendorDetails.makeupArtist) {\n            normalizedVendorDetails.makeup_artist = vendorDetails.makeupArtist;\n        } else if (vendorDetails.makeup_artist) {\n            normalizedVendorDetails.makeupArtist = vendorDetails.makeup_artist;\n        }\n        if (vendorDetails.decorations) {\n            normalizedVendorDetails.decoration = vendorDetails.decorations;\n        } else if (vendorDetails.decoration) {\n            normalizedVendorDetails.decorations = vendorDetails.decoration;\n        }\n        // Store the normalized vendor details for persistence between screens\n        setVendorDetailsData(normalizedVendorDetails);\n        // Also store in the ref for Edge browser compatibility\n        vendorDetailsRef.current = normalizedVendorDetails;\n        // Store vendor details in localStorage for persistence\n        try {\n            localStorage.setItem('wedzat_vendor_details', JSON.stringify(normalizedVendorDetails));\n            console.log('UPLOAD MANAGER - Stored vendor details in localStorage');\n        } catch (error) {\n            console.error('UPLOAD MANAGER - Failed to store vendor details in localStorage:', error);\n        }\n        // Save the current video_category before setting vendor details\n        const currentVideoCategory = state.detailFields.video_category;\n        console.log('UPLOAD MANAGER - Saving video_category before vendor details:', currentVideoCategory);\n        // Store video_category in localStorage\n        if (currentVideoCategory) {\n            try {\n                localStorage.setItem('wedzat_video_category', currentVideoCategory);\n                console.log('UPLOAD MANAGER - Stored video_category in localStorage:', currentVideoCategory);\n            } catch (error) {\n                console.error('UPLOAD MANAGER - Failed to store video_category in localStorage:', error);\n            }\n        }\n        // Store in global context for persistence\n        setVendorDetails(normalizedVendorDetails);\n        // Explicitly set each vendor detail field\n        Object.entries(normalizedVendorDetails).forEach((param)=>{\n            let [vendorType, details] = param;\n            if (details && details.name && details.mobileNumber) {\n                setDetailField(\"vendor_\".concat(vendorType, \"_name\"), details.name);\n                setDetailField(\"vendor_\".concat(vendorType, \"_contact\"), details.mobileNumber);\n            }\n        });\n        // Re-set the video_category after vendor details to ensure it's preserved\n        if (currentVideoCategory) {\n            console.log('UPLOAD MANAGER - Re-setting video_category after vendor details:', currentVideoCategory);\n            setTimeout(()=>{\n                setDetailField('video_category', currentVideoCategory);\n            }, 100);\n        }\n        // Log all detail fields after setting vendor details\n        setTimeout(()=>{\n            console.log('All detail fields after vendor details:', state.detailFields);\n            console.log('Detail fields count:', Object.keys(state.detailFields).length);\n            console.log('Normalized vendor details:', normalizedVendorDetails);\n        }, 200);\n        // Add a small delay to ensure the state is updated before proceeding\n        // This helps with cross-browser compatibility, especially in Edge\n        setTimeout(()=>{\n            // Double-check that we have at least 4 vendor details before proceeding\n            const vendorNameFields = Object.keys(state.detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_name'));\n            const vendorContactFields = Object.keys(state.detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_contact'));\n            console.log('UPLOAD MANAGER - Vendor name fields:', vendorNameFields.length);\n            console.log('UPLOAD MANAGER - Vendor contact fields:', vendorContactFields.length);\n            // Edge browser workaround - directly set vendor details in the state\n            if ( true && /Edge|Edg/.test(window.navigator.userAgent)) {\n                console.log('UPLOAD MANAGER - Edge browser detected, applying direct vendor details workaround');\n                // Create vendor detail fields directly in the state\n                // This is a workaround for Edge browser where the state update doesn't properly preserve vendor details\n                Object.entries(normalizedVendorDetails).forEach((param)=>{\n                    let [vendorType, details] = param;\n                    if (details && details.name && details.mobileNumber) {\n                        // Set the vendor details directly in the state\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                        // Also set the normalized version\n                        const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                        if (normalizedType !== vendorType) {\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                        }\n                        console.log(\"UPLOAD MANAGER - Edge workaround: Added vendor \".concat(vendorType, \" directly to state\"));\n                    }\n                });\n                // Re-set the video_category directly\n                if (currentVideoCategory) {\n                    state.detailFields.video_category = currentVideoCategory;\n                    console.log('UPLOAD MANAGER - Edge workaround: Re-set video_category directly:', currentVideoCategory);\n                }\n            }\n            // Proceed to face verification\n            setPhase('faceVerification');\n        }, 300);\n    };\n    // Handle thumbnail selection\n    const handleThumbnailSelected = (thumbnailFile)=>{\n        if (thumbnailFile) {\n            // Set the thumbnail in the context\n            setThumbnail(thumbnailFile);\n            console.log('Thumbnail selected:', thumbnailFile.name);\n        } else {\n            console.log('No thumbnail selected, using auto-generated thumbnail');\n        }\n        // New flow logic: For moments (stories), skip personal details and go directly to face verification\n        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n            console.log('Moments upload: skipping personal details, going directly to face verification');\n            setPhase('faceVerification');\n        } else {\n            // For photos and videos, go to personal details\n            console.log('Photo/Video upload: proceeding to personal details');\n            setPhase('personalDetails');\n        }\n    };\n    // Function to proceed with upload after vendor details are applied\n    const proceedWithUpload = (videoCategory)=>{\n        // Double-check that we have a title before changing to uploading phase\n        if (!state.title || !state.title.trim()) {\n            console.error('Title is missing before upload, setting it from personal details');\n            // Try to set the title from personal details\n            if (personalDetails.caption && personalDetails.caption.trim()) {\n                // console.log('Setting personal details from local state:', personalDetails);\n                // Use the global context function to set all personal details at once\n                setPersonalDetails(personalDetails);\n                // Explicitly set the title as well\n                setTitle(personalDetails.caption.trim());\n            } else {\n                console.error('No title in personal details either, going back to personal details');\n                setPhase('personalDetails');\n                return;\n            }\n        }\n        // Edge browser workaround - directly set vendor details in the state\n        if ( true && /Edge|Edg/.test(window.navigator.userAgent)) {\n            console.log('UPLOAD MANAGER - Edge browser detected in face verification, applying vendor details workaround');\n            // Get the vendor details from the vendor details data\n            const vendorDetailsData = vendorDetailsRef.current;\n            if (vendorDetailsData) {\n                console.log('UPLOAD MANAGER - Edge workaround: Using vendor details from ref:', vendorDetailsData);\n                // Create vendor detail fields directly in the state\n                Object.entries(vendorDetailsData).forEach((param)=>{\n                    let [vendorType, details] = param;\n                    if (details && details.name && details.mobileNumber) {\n                        // Set the vendor details directly in the state\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                        // Also set the normalized version\n                        const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                        if (normalizedType !== vendorType) {\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                        }\n                        console.log(\"UPLOAD MANAGER - Edge workaround: Added vendor \".concat(vendorType, \" directly to state\"));\n                    }\n                });\n            }\n        }\n        // For videos, check if we have a video_category\n        if (state.mediaType === 'video') {\n            console.log(\"UPLOAD MANAGER - Checking video_category before upload\");\n            console.log(\"UPLOAD MANAGER - Current video_category: \".concat(state.detailFields.video_category || 'Not set'));\n            console.log(\"UPLOAD MANAGER - Current mediaSubtype: \".concat(state.mediaSubtype));\n            console.log(\"UPLOAD MANAGER - Selected category: \".concat(selectedCategory || 'Not set'));\n            // Special handling for glimpses\n            if (state.mediaSubtype === 'glimpse') {\n                console.log(\"UPLOAD MANAGER - Special handling for glimpses\");\n                // If we don't have a video_category yet, try to set it from selectedCategory\n                if (!state.detailFields.video_category && selectedCategory) {\n                    // Map the UI category to the backend video_category\n                    let videoCategory = '';\n                    if (selectedCategory === 'my_wedding_videos') {\n                        videoCategory = 'my_wedding';\n                    } else if (selectedCategory === 'wedding_influencer') {\n                        videoCategory = 'wedding_influencer';\n                    } else if (selectedCategory === 'friends_family_videos') {\n                        videoCategory = 'friends_family_video';\n                    }\n                    if (videoCategory) {\n                        console.log(\"UPLOAD MANAGER - Setting video_category for glimpse: \".concat(videoCategory));\n                        setDetailField('video_category', videoCategory);\n                    }\n                } else {\n                    console.log(\"UPLOAD MANAGER - Glimpse already has video_category: \".concat(state.detailFields.video_category));\n                }\n            }\n            // If we still don't have a video_category, use a default based on selectedCategory\n            if (!state.detailFields.video_category && selectedCategory) {\n                console.log(\"UPLOAD MANAGER - No video_category set, using selectedCategory: \".concat(selectedCategory));\n                // Map the UI category to the backend video_category\n                let videoCategory = '';\n                if (selectedCategory === 'my_wedding_videos') {\n                    videoCategory = 'my_wedding';\n                } else if (selectedCategory === 'wedding_influencer') {\n                    videoCategory = 'wedding_influencer';\n                } else if (selectedCategory === 'friends_family_videos') {\n                    videoCategory = 'friends_family_video';\n                }\n                if (videoCategory) {\n                    console.log(\"UPLOAD MANAGER - Setting video_category from selectedCategory: \".concat(videoCategory));\n                    setDetailField('video_category', videoCategory);\n                }\n            }\n            // Final check - if we still don't have a video_category, use a default\n            if (!state.detailFields.video_category) {\n                console.log('No video_category found, using a default one');\n                // Use 'my_wedding' as a default category instead of asking the user again\n                setDetailField('video_category', 'my_wedding');\n                console.log('Set default video_category to my_wedding');\n            }\n        }\n        // Edge browser workaround - directly set vendor details in the state before upload\n        if ( true && /Edge|Edg/.test(window.navigator.userAgent)) {\n            console.log('UPLOAD MANAGER - Edge browser detected before upload, applying vendor details workaround');\n            // Get the vendor details from the vendor details data\n            const vendorDetailsData = vendorDetailsRef.current;\n            if (vendorDetailsData && Object.keys(vendorDetailsData).length > 0) {\n                console.log('UPLOAD MANAGER - Edge workaround: Using vendor details from ref before upload:', vendorDetailsData);\n                // Create vendor detail fields directly in the state\n                Object.entries(vendorDetailsData).forEach((param)=>{\n                    let [vendorType, details] = param;\n                    if (details && details.name && details.mobileNumber) {\n                        // Set the vendor details directly in the state\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                        // Also set the normalized version\n                        const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                        if (normalizedType !== vendorType) {\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                        }\n                        console.log(\"UPLOAD MANAGER - Edge workaround: Added vendor \".concat(vendorType, \" directly to state before upload\"));\n                    }\n                });\n            }\n        }\n        // Check if we have a file before proceeding\n        if (!state.file) {\n            console.error('No file found in state before upload');\n            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Upload Error', 'No file selected. Please select a file to upload.');\n            // Go back to type selection to start over\n            setPhase('typeSelection');\n            return;\n        }\n        // Now we can proceed to uploading phase\n        setPhase('uploading');\n        // Log the current state before starting upload\n        console.log('Current state before upload:', {\n            file: state.file ? state.file.name : 'No file',\n            mediaType: state.mediaType,\n            mediaSubtype: state.mediaSubtype,\n            title: state.title,\n            description: state.description,\n            detailFields: state.detailFields,\n            detailFieldsCount: Object.keys(state.detailFields).length\n        });\n        // Double-check that we're using the correct category\n        console.log(\"UPLOAD MANAGER - Final check - Selected type: \".concat(selectedType));\n        console.log(\"UPLOAD MANAGER - Final check - MediaSubtype in state: \".concat(state.mediaSubtype));\n        // If the mediaSubtype doesn't match what we expect based on the selected type, fix it\n        if (selectedType && state.mediaSubtype !== getMediaSubtypeFromSelectedType(selectedType)) {\n            console.log(\"UPLOAD MANAGER - WARNING: MediaSubtype mismatch detected!\");\n            console.log(\"UPLOAD MANAGER - Expected mediaSubtype based on selected type: \".concat(getMediaSubtypeFromSelectedType(selectedType)));\n            console.log(\"UPLOAD MANAGER - Actual mediaSubtype in state: \".concat(state.mediaSubtype));\n            console.log(\"UPLOAD MANAGER - Correcting category before upload...\");\n            // Get the corrected category\n            const correctedCategory = getMediaSubtypeFromSelectedType(selectedType);\n            console.log(\"UPLOAD MANAGER - Category corrected to: \".concat(correctedCategory));\n            // Get the video_category from the original selection\n            // We need to map it to the correct backend value\n            let videoCategory = '';\n            if (selectedCategory === 'my_wedding_videos') {\n                videoCategory = 'my_wedding';\n            } else if (selectedCategory === 'wedding_influencer') {\n                videoCategory = 'wedding_influencer';\n            } else if (selectedCategory === 'friends_family_videos') {\n                videoCategory = 'friends_family_video';\n            }\n            console.log(\"UPLOAD MANAGER - Original selected category: \".concat(selectedCategory));\n            console.log(\"UPLOAD MANAGER - Mapped to backend video_category: \".concat(videoCategory));\n            // Start the upload process with the corrected category and video_category\n            startUploadWithCategory(correctedCategory, videoCategory);\n        } else {\n            // Get the video_category from the state\n            const finalVideoCategory = videoCategory || state.detailFields.video_category || 'my_wedding';\n            console.log(\"UPLOAD MANAGER - Using video_category for upload: \".concat(finalVideoCategory));\n            // Start the upload process with the current category and video_category\n            startUploadWithCategory(state.mediaSubtype, finalVideoCategory).then(()=>{\n                // Upload completed successfully\n                console.log('Upload completed successfully');\n            }).catch((error)=>{\n                console.error('Upload failed:', error);\n            });\n        }\n    };\n    // Handle face verification completed and start upload\n    const handleFaceVerificationCompleted = ()=>{\n        console.log('Face verification completed, starting upload process');\n        // Check if we have a file in the state\n        if (!state.file) {\n            console.error('No file found in state after face verification');\n            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Upload Error', 'Something went wrong with your file upload. Please try again.');\n            setPhase('typeSelection');\n            return;\n        }\n        console.log('File confirmed in state after face verification:', state.file.name);\n        // Try to get vendor details from localStorage first\n        let vendorDetailsData = vendorDetailsRef.current;\n        // If not in ref, try localStorage\n        if (!vendorDetailsData || Object.keys(vendorDetailsData).length === 0) {\n            try {\n                const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n                if (storedVendorDetails) {\n                    vendorDetailsData = JSON.parse(storedVendorDetails);\n                    console.log('UPLOAD MANAGER - Retrieved vendor details from localStorage:', storedVendorDetails);\n                    // Update the ref with the localStorage data\n                    vendorDetailsRef.current = vendorDetailsData;\n                    // Log the vendor details we found\n                    console.log(\"UPLOAD MANAGER - Found \".concat(Object.keys(vendorDetailsData).length, \" vendor details in localStorage\"));\n                    Object.entries(vendorDetailsData).forEach((param)=>{\n                        let [vendorType, details] = param;\n                        if (details && details.name && details.mobileNumber) {\n                            console.log(\"UPLOAD MANAGER - Vendor \".concat(vendorType, \": \").concat(details.name, \" (\").concat(details.mobileNumber, \")\"));\n                        }\n                    });\n                } else {\n                    console.log('UPLOAD MANAGER - No vendor details found in localStorage');\n                }\n            } catch (error) {\n                console.error('UPLOAD MANAGER - Failed to retrieve vendor details from localStorage:', error);\n            }\n        } else {\n            console.log(\"UPLOAD MANAGER - Using \".concat(Object.keys(vendorDetailsData).length, \" vendor details from ref\"));\n        }\n        // Try to get video_category from localStorage\n        let videoCategory = state.detailFields.video_category;\n        if (!videoCategory) {\n            try {\n                const storedVideoCategory = localStorage.getItem('wedzat_video_category');\n                if (storedVideoCategory) {\n                    videoCategory = storedVideoCategory;\n                    console.log('UPLOAD MANAGER - Retrieved video_category from localStorage:', videoCategory);\n                    // Set it in the state\n                    setDetailField('video_category', videoCategory);\n                }\n            } catch (error) {\n                console.error('UPLOAD MANAGER - Failed to retrieve video_category from localStorage:', error);\n            }\n        }\n        // Ensure vendor details are present\n        if (vendorDetailsData) {\n            console.log('UPLOAD MANAGER - Applying vendor details to state');\n            // Create a batch of all detail fields to update at once\n            const detailFieldUpdates = {};\n            let completeVendorCount = 0;\n            // Re-apply vendor details to ensure they're in the state\n            Object.entries(vendorDetailsData).forEach((param)=>{\n                let [vendorType, details] = param;\n                if (details && details.name && details.mobileNumber) {\n                    // Add to the batch\n                    detailFieldUpdates[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                    detailFieldUpdates[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                    completeVendorCount++;\n                    // Also set normalized versions\n                    const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                    if (normalizedType !== vendorType) {\n                        detailFieldUpdates[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                        detailFieldUpdates[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                    }\n                }\n            });\n            // Apply all updates at once\n            console.log(\"UPLOAD MANAGER - Applying \".concat(completeVendorCount, \" complete vendor details to state\"));\n            console.log('UPLOAD MANAGER - Detail field updates:', JSON.stringify(detailFieldUpdates));\n            // Apply each update individually to ensure they're all set\n            Object.entries(detailFieldUpdates).forEach((param)=>{\n                let [field, value] = param;\n                setDetailField(field, value);\n            });\n            // Add a delay before proceeding to ensure state updates are applied\n            setTimeout(()=>{\n                console.log('UPLOAD MANAGER - Vendor details applied to state, proceeding with upload');\n                proceedWithUpload(videoCategory);\n            }, 500);\n        } else {\n            console.log('UPLOAD MANAGER - No vendor details found, proceeding with upload');\n            proceedWithUpload(videoCategory);\n        }\n    // This code has been moved to the proceedWithUpload function\n    };\n    // Handle going back to personal details from upload error\n    const handleBackToPersonalDetails = ()=>{\n        // console.log('Going back to personal details with stored data:', personalDetails);\n        // Make sure the personal details are set in the context\n        if (personalDetails.caption && personalDetails.caption.trim()) {\n            // Use the global context function to set all personal details at once\n            setPersonalDetails(personalDetails);\n        }\n        setPhase('personalDetails');\n    };\n    // Handle close modal\n    const handleClose = ()=>{\n        // Check if upload was successful and call onUploadComplete\n        if (state.step === 'complete' && onUploadComplete) {\n            console.log('Upload completed successfully, calling onUploadComplete callback');\n            onUploadComplete();\n        }\n        // Reset the phase first\n        setPhase('closed');\n        // Call the onClose callback if provided\n        if (onClose) {\n            onClose();\n        }\n        // Reset the upload state after a short delay to ensure the modal is closed first\n        setTimeout(()=>{\n            resetUpload();\n            console.log('Upload state reset after modal close');\n        }, 100);\n    };\n    // Render selected phase component\n    if (phase === 'closed') {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            phase === 'typeSelection' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UploadTypeSelection__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                onNext: handleTypeSelected,\n                onClose: handleClose\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1342,\n                columnNumber: 9\n            }, undefined),\n            phase === 'categorySelection' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VideoCategorySelection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onNext: handleCategorySelected,\n                onBack: ()=>setPhase('typeSelection'),\n                onUpload: handleCategorySelected,\n                onThumbnailUpload: handleThumbnailUpload,\n                onClose: handleClose,\n                mediaType: state.mediaType,\n                selectedType: selectedType\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1349,\n                columnNumber: 9\n            }, undefined),\n            phase === 'thumbnailSelection' && state.file && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ThumbnailSelection__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                videoFile: state.file,\n                onNext: handleThumbnailSelected,\n                onBack: ()=>{\n                    // Go back to category selection instead of triggering file upload again\n                    if ([\n                        'flashes',\n                        'glimpses',\n                        'movies'\n                    ].includes(selectedType)) {\n                        setPhase('categorySelection');\n                    } else {\n                        // For moments, go back to type selection\n                        setPhase('typeSelection');\n                    }\n                },\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1361,\n                columnNumber: 9\n            }, undefined),\n            phase === 'personalDetails' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PersonalDetails__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onNext: handlePersonalDetailsCompleted,\n                onBack: ()=>{\n                    // Go back to thumbnail selection for videos\n                    if (state.mediaType === 'video' && state.file) {\n                        setPhase('thumbnailSelection');\n                    } else {\n                        // For photos, go back to type selection\n                        setPhase('typeSelection');\n                    }\n                },\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                },\n                previewImage: previewImage,\n                videoFile: state.mediaType === 'video' ? state.file : null,\n                mediaType: state.mediaType,\n                contentType: getContentType(),\n                initialDetails: personalDetails\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1382,\n                columnNumber: 9\n            }, undefined),\n            phase === 'vendorDetails' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VendorDetails__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                onNext: handleVendorDetailsCompleted,\n                onBack: ()=>setPhase('personalDetails'),\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                },\n                initialVendorDetails: vendorDetailsData\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1407,\n                columnNumber: 9\n            }, undefined),\n            phase === 'faceVerification' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FaceVerification__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                onUpload: handleFaceVerificationCompleted,\n                onBack: ()=>{\n                    // New flow logic for back navigation:\n                    // - Moments: Go back to thumbnail selection (or type selection for images)\n                    // - Photos: Go back to personal details\n                    // - Videos: Go back to vendor details\n                    if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                        // For moments, go back to thumbnail selection for videos, or type selection for images\n                        if (state.mediaType === 'video') {\n                            setPhase('thumbnailSelection');\n                        } else {\n                            setPhase('typeSelection');\n                        }\n                    } else if (state.mediaType === 'photo') {\n                        setPhase('personalDetails');\n                    } else {\n                        setPhase('vendorDetails');\n                    }\n                },\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1420,\n                columnNumber: 9\n            }, undefined),\n            (phase === 'uploading' || phase === 'complete') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UploadProgress__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                },\n                onGoBack: handleBackToPersonalDetails\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1449,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(UploadManager, \"LQsMODnAAL1BJd6LrYNCqh7ZCEM=\", false, function() {\n    return [\n        _contexts_UploadContexts__WEBPACK_IMPORTED_MODULE_9__.useUpload,\n        _hooks_useMedia__WEBPACK_IMPORTED_MODULE_12__.useMediaUpload\n    ];\n});\n_c = UploadManager;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UploadManager);\nvar _c;\n$RefreshReg$(_c, \"UploadManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/upload/UploadManager.tsx\n"));

/***/ })

});