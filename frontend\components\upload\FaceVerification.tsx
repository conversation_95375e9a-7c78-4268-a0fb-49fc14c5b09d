// components/upload/FaceVerification.tsx
'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { X, Camera, CheckCircle, AlertCircle } from 'lucide-react';
import { uploadService } from '../../services/api';

interface FaceVerificationProps {
  onUpload: () => void;
  onBack: () => void;
  onClose: () => void;
}

const FaceVerification: React.FC<FaceVerificationProps> = ({
  onUpload,
  onBack,
  onClose
}) => {
  // We no longer need cameraActive state as we use stream presence instead
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [cameraError, setCameraError] = useState<string | null>(null);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [verifying, setVerifying] = useState(false);
  const [verificationStatus, setVerificationStatus] = useState<'none' | 'success' | 'error'>('none');
  const [verificationMessage, setVerificationMessage] = useState('');
  const [cameraLoading, setCameraLoading] = useState(false);
  const [videoReady, setVideoReady] = useState(false);

  // Automatically open camera when component mounts
  useEffect(() => {
    // Open camera automatically after a short delay
    const timer = setTimeout(() => {
      if (!stream && !capturedImage && !cameraLoading) {
        console.log('Auto-opening camera...');
        handleOpenCamera();
      }
    }, 1000);

    // Clean up camera resources when component unmounts
    return () => {
      clearTimeout(timer);
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
      // Reset video ready state when cleaning up
      setVideoReady(false);
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [stream, capturedImage]);

  // Effect to handle connecting the stream to the video element
  useEffect(() => {
    if (stream) {
      const videoElement = document.getElementById('camera-preview') as HTMLVideoElement;
      if (videoElement) {
        console.log('Connecting stream to video element');
        videoElement.srcObject = stream;

        // Check if video already has dimensions (might be ready immediately)
        if (videoElement.videoWidth > 0 && videoElement.videoHeight > 0) {
          console.log('Video already has dimensions:', {
            width: videoElement.videoWidth,
            height: videoElement.videoHeight
          });
          setVideoReady(true);
        }

        // Add event listeners to detect when video is ready
        const handleMetadataLoaded = () => {
          console.log('Video metadata loaded via event listener');
          if (videoElement.videoWidth > 0 && videoElement.videoHeight > 0) {
            setVideoReady(true);
          }
        };

        const handleCanPlay = () => {
          console.log('Video can play event fired');
          if (videoElement.videoWidth > 0 && videoElement.videoHeight > 0) {
            setVideoReady(true);
          }
        };

        videoElement.addEventListener('loadedmetadata', handleMetadataLoaded);
        videoElement.addEventListener('canplay', handleCanPlay);

        // Clean up event listeners
        return () => {
          videoElement.removeEventListener('loadedmetadata', handleMetadataLoaded);
          videoElement.removeEventListener('canplay', handleCanPlay);
        };
      }
    }
  }, [stream]);

  const handleOpenCamera = async () => {
    try {
      setCameraError(null);
      setCameraLoading(true);
      setCapturedImage(null); // Clear any previous captured image
      setVideoReady(false); // Reset video ready state

      // Clear any previous streams
      if (stream) {
        console.log("Stopping previous camera stream...");
        stream.getTracks().forEach(track => {
          console.log(`Stopping track: ${track.kind} - ${track.label}`);
          track.stop();
        });
        setStream(null); // Clear the stream reference
      }

      console.log("Requesting camera access...");

      // Use higher quality camera settings while maintaining compatibility
      console.log("Using improved camera settings for better quality");
      const constraints = {
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          facingMode: 'user' // Front camera for face verification
        },
        audio: false
      };

      console.log("Camera constraints:", constraints);

      let cameraStream = null;

      try {
        // First try with high-quality settings
        console.log("Attempting to get high-quality camera stream...");
        cameraStream = await navigator.mediaDevices.getUserMedia(constraints);

        // Log the tracks to help with debugging
        const videoTracks = cameraStream.getVideoTracks();
        console.log(`Using video device: ${videoTracks[0].label}`);
        console.log("Video track settings:", videoTracks[0].getSettings());
      } catch (highQualityError) {
        console.warn("Failed to get high-quality camera, falling back to basic settings", highQualityError);

        // Fall back to basic camera settings
        const basicConstraints = { video: true, audio: false };
        console.log("Attempting to get basic camera stream...");
        cameraStream = await navigator.mediaDevices.getUserMedia(basicConstraints);

        // Log the fallback tracks
        const fallbackTracks = cameraStream.getVideoTracks();
        console.log(`Using fallback video device: ${fallbackTracks[0].label}`);
        console.log("Fallback video track settings:", fallbackTracks[0].getSettings());
      }

      if (!cameraStream) {
        throw new Error("Failed to initialize camera stream");
      }

      // Ensure we have active tracks
      const activeTracks = cameraStream.getVideoTracks();
      if (activeTracks.length === 0 || !activeTracks[0].enabled) {
        console.warn("Camera tracks not enabled, attempting to enable...");
        activeTracks.forEach(track => track.enabled = true);
      }

      // Set the stream state - this will trigger the video element to update
      console.log("Setting camera stream to state...");
      setStream(cameraStream);

      // Camera is now active because we have a stream
      setCameraLoading(false);
      console.log("Camera activated successfully");

      // Video readiness will be detected by the onLoadedMetadata event

      // Connect to any existing video element immediately
      // We'll use a single attempt with a short delay to avoid race conditions
      setTimeout(() => {
        const videoElement = document.getElementById('camera-preview') as HTMLVideoElement;

        if (videoElement && cameraStream && videoElement.srcObject !== cameraStream) {
          console.log("Connecting stream to existing video element...");
          videoElement.srcObject = cameraStream;

          // We'll use a timeout to avoid race conditions with React rendering
          setTimeout(() => {
            if (videoElement && !videoElement.paused) {
              // Video is already playing, don't try to play again
              console.log("Video is already playing, skipping play() call");
              return;
            }

            // Use a single play attempt with proper error handling
            try {
              const playPromise = videoElement.play();
              if (playPromise !== undefined) {
                playPromise
                  .then(() => console.log("Video playback started successfully"))
                  .catch(err => {
                    if (err.name === 'NotAllowedError') {
                      console.warn("Autoplay prevented by browser. User interaction required.");
                    } else if (err.name === 'AbortError' || err.message.includes('interrupted')) {
                      console.warn("Play request was interrupted, this is normal during rapid state changes");
                    } else {
                      console.error("Error playing video after stream set:", err);
                    }
                  });
              }
            } catch (e) {
              console.warn("Exception when trying to play video:", e);
            }
          }, 100);
        }
      }, 200);
    } catch (error) {
      console.error("Error accessing camera:", error);
      setCameraError("Could not access camera. Please check your permissions and try again.");
      setCameraLoading(false);
    }
  };

  const handleCapturePhoto = () => {
    try {
      // Don't proceed if video isn't ready
      if (!videoReady) {
        console.log("Video not ready for capture yet");
        return;
      }

      console.log("Attempting to capture photo...");
      setCameraError(null);

      // Get the video element
      const videoElement = document.getElementById('camera-preview') as HTMLVideoElement;
      if (!videoElement) {
        console.error("Video element not found for capture");
        setCameraError("Error: Could not find camera preview");
        return;
      }

      console.log("Video element found, dimensions:", {
        videoWidth: videoElement.videoWidth,
        videoHeight: videoElement.videoHeight,
        offsetWidth: videoElement.offsetWidth,
        offsetHeight: videoElement.offsetHeight,
        readyState: videoElement.readyState
      });

      // Double-check if video is actually playing and has dimensions
      if (videoElement.videoWidth === 0 || videoElement.videoHeight === 0) {
        console.error("Video dimensions are zero. Video may not be playing yet.");
        setCameraError("Camera not ready. Please try refreshing the page and allowing camera access.");
        setVideoReady(false); // Reset video ready state
        return;
      }

      // Use fixed dimensions for consistency
      const width = 320;
      const height = 240;

      // Create canvas with fixed dimensions
      const canvas = document.createElement('canvas');
      canvas.width = width;
      canvas.height = height;

      try {
        const context = canvas.getContext('2d');
        if (!context) {
          throw new Error("Could not get canvas context");
        }

        // Draw the current video frame to the canvas
        context.drawImage(videoElement, 0, 0, width, height);
        console.log("Image captured successfully with dimensions:", { width, height });

        // Get image as base64 string
        const imageData = canvas.toDataURL('image/jpeg', 0.9);
        console.log('Captured image data length:', imageData.length);

        // Store the image data
        setCapturedImage(imageData);

        // Keep the camera active but just switch to showing the captured image
        // Don't stop the stream or set cameraActive to false
        // This allows the user to easily switch back to camera mode
      } catch (canvasError) {
        console.error("Canvas error:", canvasError);
        setCameraError("Failed to create capture context");
      }
    } catch (err) {
      console.error("Error during capture:", err);
      setCameraError("Failed to capture image");
    }
  };

  const handleRetake = () => {
    setCapturedImage(null);
    // No need to call handleOpenCamera() since we're keeping the camera active
    // Just clear the captured image to show the camera again
  };

  const handleVerifyAndUpload = async () => {
    // Prevent multiple uploads by checking if verification is already in progress or successful
    if (verifying || verificationStatus === 'success') {
      console.log('Verification already in progress or completed, ignoring additional upload attempts');
      return;
    }

    if (!capturedImage) {
      // Face image is required
      setVerificationStatus('error');
      setVerificationMessage('Please capture your face image before proceeding');
      return;
    }

    try {
      setVerifying(true);
      setVerificationStatus('none');
      setVerificationMessage('Verifying your face...');

      // Process the image data to ensure it's in the correct format
      // The backend expects a base64 string without the data URL prefix
      let imageBase64 = capturedImage;

      // Remove the data URL prefix if present
      if (imageBase64.startsWith('data:')) {
        imageBase64 = imageBase64.split(',')[1];
      }

      // Log the start time for performance tracking
      const startTime = Date.now();

      try {
        // Call the API to verify the face
        const response = await uploadService.verifyFace(imageBase64);

        // Log the end time and calculate duration
        const endTime = Date.now();
        console.log(`Face verification API call completed in ${endTime - startTime}ms`);
        console.log('API response:', response);

        setVerifying(false);
        setVerificationStatus('success');
        setVerificationMessage(response.message || 'Face verification successful');

        // Show success message for a moment before proceeding
        // Use a longer delay to ensure the user sees the success message
        setTimeout(() => {
          console.log('Verification successful, proceeding with upload');
          onUpload();
        }, 2000);
      } catch (apiError: any) {
        console.error('API error during face verification:', apiError);

        // Log more details about the error
        if (apiError.response) {
          console.error('API response status:', apiError.response.status);
          console.error('API response data:', apiError.response.data);
        } else if (apiError.request) {
          console.error('No response received from API');
        } else {
          console.error('Error message:', apiError.message);
          console.error('Error stack:', apiError.stack);
        }

        setVerifying(false);
        setVerificationStatus('error');

        // Show a more detailed error message
        const errorMessage = apiError instanceof Error
          ? apiError.message
          : 'Unknown error during face verification';

        setVerificationMessage(`Face verification failed: ${errorMessage}. Please try again.`);
      }
    } catch (error: any) {
      setVerifying(false);
      setVerificationStatus('error');
      setVerificationMessage(error instanceof Error ? error.message : 'Face verification failed');
      console.error('Face verification error:', error);
    }
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 p-4">
      <div className="bg-[#FFF7E8] rounded-2xl w-full max-w-[50%] p-4 sm:p-6 relative overflow-y-auto max-h-[90vh] md:max-w-[50%] sm:max-w-[70%] xs:max-w-[90%]">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-800 hover:text-red-600"
        >
          <X size={24} />
        </button>

        {/* Header */}
        <div className="flex items-center mb-4">
          <div className="flex items-center mr-2">
            <Image
              src="/pics/logo.png"
              alt="Wedzat logo"
              width={32}
              height={32}
              className="object-cover"
            />
          </div>
          <h2 className="text-xl font-bold">Face Verification</h2>
          <div className="ml-2">
            <Image
              src="/pics/user-profile.png"
              alt="User"
              width={20}
              height={20}
              className="object-cover"
            />
          </div>
        </div>

        <div className="flex items-center mb-6">
          <Image
            src="/pics/user-profile.png"
            alt="User"
            width={24}
            height={24}
            className="object-cover mr-2"
          />
          <p className="text-base">Verify your face ID</p>
        </div>

        {/* Camera or captured image display */}
        <div className="border border-gray-200 rounded-lg p-4 mb-6">
          {cameraLoading ? (
            <div className="flex flex-col items-center justify-center h-60">
              <div className="w-12 h-12 border-4 border-gray-300 border-t-red-600 rounded-full animate-spin mb-4"></div>
              <p className="text-center text-gray-600">Initializing camera...</p>
            </div>
          ) : stream ? (
            <div className="relative">
              {/* Always show the video stream when it exists */}
              {!capturedImage && (
                <video
                  id="camera-preview"
                  autoPlay
                  playsInline
                  muted
                  width="640"
                  height="480"
                  className="w-full object-contain rounded-lg shadow-md bg-black"
                  style={{ maxHeight: '300px', minHeight: '200px' }}
                  onLoadedMetadata={(e) => {
                    const video = e.target as HTMLVideoElement;
                    console.log("Video metadata loaded, dimensions:", {
                      videoWidth: video.videoWidth,
                      videoHeight: video.videoHeight
                    });
                    if (video.videoWidth > 0 && video.videoHeight > 0) {
                      setVideoReady(true);
                      // Don't call play() here to avoid conflicts
                    }
                  }}
                  onLoadedData={(e) => {
                    const video = e.target as HTMLVideoElement;
                    console.log("Video data loaded, dimensions:", {
                      videoWidth: video.videoWidth,
                      videoHeight: video.videoHeight
                    });
                    if (video.videoWidth > 0 && video.videoHeight > 0) {
                      setVideoReady(true);
                      // Don't call play() here to avoid conflicts
                    }
                  }}
                  ref={(videoElement) => {
                    if (videoElement && stream) {
                      // Only set srcObject if it's different to avoid unnecessary reloads
                      if (videoElement.srcObject !== stream) {
                        console.log("Setting stream to video element in ref callback");
                        videoElement.srcObject = stream;

                        // We'll use a timeout to avoid race conditions with React rendering
                        setTimeout(() => {
                          if (videoElement && !videoElement.paused) {
                            // Video is already playing, don't try to play again
                            console.log("Video is already playing, skipping play() call");
                            return;
                          }

                          // Use a single play attempt with proper error handling
                          try {
                            const playPromise = videoElement.play();
                            if (playPromise !== undefined) {
                              playPromise
                                .then(() => console.log("Video playback started successfully"))
                                .catch(err => {
                                  if (err.name === 'NotAllowedError') {
                                    console.warn("Autoplay prevented by browser. User interaction required.");
                                  } else if (err.name === 'AbortError' || err.message.includes('interrupted')) {
                                    console.warn("Play request was interrupted, this is normal during rapid state changes");
                                  } else {
                                    console.error("Error playing video in ref:", err);
                                  }
                                });
                            }
                          } catch (e) {
                            console.warn("Exception when trying to play video:", e);
                          }
                        }, 100);
                      }
                    }
                  }}
                />
              )}

              {/* Show captured image when available */}
              {capturedImage && (
                <img
                  src={capturedImage}
                  alt="Captured face"
                  className="w-full object-contain rounded-lg shadow-md"
                  style={{ maxHeight: '300px', minHeight: '200px' }}
                />
              )}

              {/* Camera controls */}
              <div className="absolute bottom-4 left-0 right-0 flex justify-center space-x-4">
                {!capturedImage ? (
                  <button
                    onClick={handleCapturePhoto}
                    disabled={!videoReady}
                    className={`${videoReady ? 'bg-white' : 'bg-gray-200'} p-3 rounded-full shadow-lg transition-colors duration-300`}
                  >
                    <Camera size={24} className={videoReady ? 'text-red-600' : 'text-gray-400'} />
                  </button>
                ) : (
                  <button
                    onClick={handleRetake}
                    className="bg-white p-3 rounded-full shadow-lg"
                    title="Retake photo"
                  >
                    <Camera size={24} className="text-red-600" />
                  </button>
                )}
              </div>

              {/* Loading overlay when camera is initializing */}
              {stream && !videoReady && !capturedImage && (
                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-lg">
                  <div className="text-white text-center p-4">
                    <div className="w-8 h-8 border-4 border-gray-300 border-t-red-600 rounded-full animate-spin mb-2 mx-auto"></div>
                    <p>Camera initializing...</p>
                  </div>
                </div>
              )}

              {/* Fallback camera button if video isn't showing but stream exists and no capture button is visible */}
              {stream && !capturedImage && !videoReady && (
                <div className="absolute bottom-4 left-0 right-0 flex justify-center">
                  <button
                    onClick={() => {
                      // Try to reinitialize the camera connection
                      const videoElement = document.getElementById('camera-preview') as HTMLVideoElement;
                      if (videoElement && stream) {
                        console.log("Reconnecting stream to video element...");

                        // Only set srcObject if it's different
                        if (videoElement.srcObject !== stream) {
                          videoElement.srcObject = stream;
                        }

                        // We'll use a timeout to avoid race conditions with React rendering
                        setTimeout(() => {
                          if (videoElement && !videoElement.paused) {
                            // Video is already playing, don't try to play again
                            console.log("Video is already playing, skipping play() call");
                            setVideoReady(true);
                            return;
                          }

                          // Use a single play attempt with proper error handling
                          try {
                            const playPromise = videoElement.play();
                            if (playPromise !== undefined) {
                              playPromise
                                .then(() => {
                                  console.log("Video playback started successfully");
                                  setVideoReady(true);
                                })
                                .catch(err => {
                                  if (err.name === 'NotAllowedError') {
                                    console.warn("Autoplay prevented by browser. User interaction required.");
                                  } else if (err.name === 'AbortError' || err.message.includes('interrupted')) {
                                    console.warn("Play request was interrupted, this is normal during rapid state changes");
                                  } else {
                                    console.error("Error playing video:", err);
                                  }
                                });
                            }
                          } catch (e) {
                            console.warn("Exception when trying to play video:", e);
                          }
                        }, 100);
                      }
                    }}
                    className="bg-white p-3 rounded-full shadow-lg"
                  >
                    <Camera size={24} className="text-red-600" />
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-60">
              <div
                onClick={handleOpenCamera}
                className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center cursor-pointer mb-4"
              >
                <Camera size={32} className="text-gray-600" />
              </div>
              <p className="text-center text-gray-600">
                Open Camera
              </p>
              {cameraError && (
                <p className="text-center text-red-500 text-sm mt-2">
                  {cameraError}
                </p>
              )}
              <p className="text-center text-gray-500 text-sm mt-4">
                This is a one-time face verification process.<br />
                It won't appear again next time.
              </p>
            </div>
          )}
        </div>

        {/* Verification status */}
        {verificationStatus !== 'none' && (
          <div className={`mb-4 p-3 rounded-lg flex items-center ${
            verificationStatus === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {verificationStatus === 'success' ? (
              <CheckCircle className="mr-2 h-5 w-5" />
            ) : (
              <AlertCircle className="mr-2 h-5 w-5" />
            )}
            <span>{verificationMessage}</span>
          </div>
        )}

        {/* Action buttons */}
        <div className="flex justify-between">
          {/* Only show back button if verification is not successful */}
          {verificationStatus !== 'success' && (
            <button
              onClick={onBack}
              className="flex items-center px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-100 transition duration-200"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 mr-1"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z"
                  clipRule="evenodd"
                />
              </svg>
              Back
            </button>
          )}

          {/* Show upload button only if verification is not successful and not in progress */}
          {verificationStatus !== 'success' && (
            <button
              onClick={handleVerifyAndUpload}
              disabled={!capturedImage || verifying}
              className={`flex items-center justify-center px-6 py-2 rounded-md ${
                !capturedImage || verifying
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-red-600 text-white hover:bg-red-700'
              } transition duration-200`}
            >
              {verifying ? (
                <>
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  Verifying...
                </>
              ) : (
                <>
                  Verify & Upload
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 ml-1"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </>
              )}
            </button>
          )}

          {/* Show success message when verification is successful */}
          {verificationStatus === 'success' && (
            <div className="w-full text-center text-green-600 font-medium">
              <CheckCircle className="inline-block mr-2 h-5 w-5" />
              Verification successful! Uploading your content...
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FaceVerification;
