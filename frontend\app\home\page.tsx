"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import HomeDashboard from "../../components/HomeDashboard/HomeDashboard";
import { useAuth } from "../../contexts/AuthContext";
import UserProfile from "../../components/userProfile/page";

export default function HomePage() {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const [overrideAuth, setOverrideAuth] = useState(false);
  const [debugInfo, setDebugInfo] = useState<string>("");
  const [isVendor, setIsVendor] = useState<boolean>(false);

  // Check localStorage directly
  useEffect(() => {
    const checkLocalStorage = () => {
      try {
        const tokenKeys = ["token", "jwt_token", "auth_token"];
        let tokenFound = false;
        let tokenInfo = "";
        let isVendorUser = false;

        // Check if this is a vendor user
        if (localStorage.getItem("is_vendor") === "true") {
          isVendorUser = true;
          setIsVendor(true); // Set the state
          tokenInfo += "\n- is_vendor: true";
        }

        // Check all possible token locations
        tokenKeys.forEach((key) => {
          const value = localStorage.getItem(key);
          tokenInfo += `\n- ${key}: ${value ? `${value.substring(0, 10)}...` : "not found"
            }`;
          if (value) tokenFound = true;
        });

        // Add cookie info
        const hasCookie = document.cookie.includes("token=");
        tokenInfo += `\n- Cookie: ${hasCookie ? "present" : "not found"}`;

        setDebugInfo(`Tokens check:${tokenInfo}`);

        // If token exists in localStorage but auth context doesn't detect it
        // OR if this is a vendor user with a token
        if ((tokenFound && !isAuthenticated && !isLoading) || (isVendorUser && tokenFound)) {
          console.log(
            "Token exists in localStorage but not detected by auth context, overriding"
          );
          setOverrideAuth(true);
        }
      } catch (error) {
        console.error("Error checking localStorage:", error);
      }
    };

    checkLocalStorage();
  }, [isAuthenticated, isLoading]);

  useEffect(() => {
    // If not loading and no authentication found, redirect immediately
    if (!isLoading && !isAuthenticated && !overrideAuth) {
      const token = localStorage.getItem("token") || 
                    localStorage.getItem("jwt_token") || 
                    localStorage.getItem("wedzat_token");
      
      // Only redirect if no token exists at all
      if (!token) {
        console.log("No token found, redirecting to login page");
        router.push("/");
      } else if (!isVendor) {
        // If token exists but auth context doesn't recognize it, force auth
        console.log("Token exists but not authenticated in context, forcing authentication");
        setOverrideAuth(true);
      }
    }
  }, [isLoading, isAuthenticated, overrideAuth, router, isVendor]);

  // Add a timeout to prevent infinite loading
  useEffect(() => {
    const loadingTimeout = setTimeout(() => {
      if (isLoading) {
        // Check if we have a token before redirecting
        const token = localStorage.getItem("token") || localStorage.getItem("jwt_token");
        
        if (!token) {
          console.log("Loading timed out and no token found, redirecting to login page");
          router.push("/");
        } else if (isVendor) {
          // If we have a vendor token, force authentication
          console.log("Loading timed out but vendor token found, forcing authentication");
          setOverrideAuth(true);
        }
      }
    }, 5000); // 5 seconds timeout

    return () => clearTimeout(loadingTimeout);
  }, [isLoading, router, setOverrideAuth, isVendor]);

  // When detecting a token mismatch, try to add it as a cookie
  useEffect(() => {
    if (overrideAuth) {
      try {
        // Get token from localStorage
        const token =
          localStorage.getItem("token") || localStorage.getItem("jwt_token");
        if (token) {
          // Set as cookie to help middleware
          document.cookie = `token=${token}; path=/; max-age=86400; SameSite=Strict`;
          setDebugInfo(
            (prev) => prev + "\nSet token as cookie for middleware detection"
          );
        }
      } catch (error) {
        console.error("Error setting cookie:", error);
      }
    }
  }, [overrideAuth]);

  // For development, add a helper to bypass auth with URL param
  useEffect(() => {
    if (process.env.NODE_ENV === "development") {
      setDebugInfo((prev) => prev + "\nDevelopment mode active");
      const addBypassParam = () => {
        const params = new URLSearchParams(window.location.search);
        if (!params.has("bypassAuth")) {
          params.set("bypassAuth", "true");
          router.replace(`${window.location.pathname}?${params.toString()}`);
          setDebugInfo((prev) => prev + "\nAdded bypassAuth parameter");
        }
      };

      // If we're having token issues, add the bypass
      if (!isAuthenticated && !isLoading && overrideAuth) {
        addBypassParam();
      }
    }
  }, [router, isAuthenticated, isLoading, overrideAuth]);

  // Special check for vendor tokens
  useEffect(() => {
    // If we're not authenticated but have a vendor token, force override
    if (!isAuthenticated && !overrideAuth && !isLoading) {
      const isVendor = localStorage.getItem("is_vendor") === "true";
      const hasToken = localStorage.getItem("token") || localStorage.getItem("jwt_token");

      if (isVendor && hasToken) {
        console.log("Vendor token detected, forcing authentication override");
        setOverrideAuth(true);

        // Set cookie for middleware
        document.cookie = 'is_vendor=true; path=/; max-age=86400; SameSite=Lax';
      }
    }
  }, [isAuthenticated, overrideAuth, isLoading, setOverrideAuth]);

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="flex flex-col justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-700 mb-4"></div>
        <div className="text-gray-600">Loading your account...</div>
      </div>
    );
  }

  // If authenticated by context or override, show the dashboard
  if (isAuthenticated || overrideAuth) {
    return (
      <>
        <HomeDashboard />
      </>
    );
  }

  // If not authenticated and not overridden, show loading while redirecting
  return (
    <div className="flex flex-col justify-center items-center h-screen">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-700 mb-4"></div>
      <div className="text-gray-600">Redirecting to login...</div>
      {process.env.NODE_ENV !== "production" && (
        <div className="mt-4 p-2 bg-gray-100 text-xs font-mono max-w-md">
          {debugInfo || "No auth token found"}
        </div>
      )}
    </div>
  );
}
