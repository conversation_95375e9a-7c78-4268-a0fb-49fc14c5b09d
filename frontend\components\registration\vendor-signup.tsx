// components/registration/vendor-signup.tsx
import React, { useState, useEffect, ChangeEvent, FormEvent } from "react";
import Image from "next/image";
import { <PERSON>Left, Eye, EyeOff } from "lucide-react";
import { useRouter } from "next/navigation";
import { vendorService } from "../../services/vendor-api"; // Import the API service

// Add custom styles to ensure dropdowns open downward
const customStyles = `
  .select-wrapper {
    position: relative;
    z-index: 10;
  }

  .select-wrapper select {
    position: relative;
    z-index: 10;
  }

  /* Force dropdown to open downward */
  .select-wrapper select option {
    position: absolute;
    top: 100%;
    left: 0;
  }
`;

interface BusinessType {
  type_id: string;
  name: string;
  description?: string;
}

interface VendorSignupProps {
  onSignup?: (data: {
    businessName: string;
    email: string;
    phone: string;
    password: string;
    confirmPassword: string;
    primaryBusinessType: string;
    secondaryBusinessTypes: string[];
    city: string;
    businessRegistrationType?: string;
    gstRegistered?: boolean;
    gstNumber?: string;
    businessAddress?: string;
    state?: string;
    pinCode?: string;
    contactName?: string;
  }) => void;
  onLoginClick?: () => void;
  onBack?: () => void;
}

const VendorSignup: React.FC<VendorSignupProps> = ({
  onSignup,
  onLoginClick,
  onBack,
}) => {
  const router = useRouter();
  const [formData, setFormData] = useState({
    businessName: "",
    email: "",
    phone: "",
    password: "",
    confirmPassword: "",
    primaryBusinessType: "",
    secondaryBusinessTypes: [] as string[],
    city: "",
    businessRegistrationType: "company",
    gstRegistered: false,
    gstNumber: "",
    businessAddress: "",
    state: "",
    pinCode: "",
    contactName: ""
  });

  const [businessTypes, setBusinessTypes] = useState<BusinessType[]>([]);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [step, setStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [serverError, setServerError] = useState("");
  const [showPrimaryDropdown, setShowPrimaryDropdown] = useState(false);
  const [showRegistrationTypeDropdown, setShowRegistrationTypeDropdown] = useState(false);

  // Fetch business types on component mount
  useEffect(() => {
    const fetchBusinessTypes = async () => {
      try {
        // Only run on client side
        if (typeof window !== 'undefined') {
          const types = await vendorService.getBusinessTypes();
          setBusinessTypes(types);
        }
      } catch (error) {
        console.error("Failed to fetch business types:", error);
      }
    };

    fetchBusinessTypes();
  }, []);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;

      // Close primary business type dropdown
      if (showPrimaryDropdown && !target.closest('.primary-dropdown-container')) {
        setShowPrimaryDropdown(false);
      }

      // Close business registration type dropdown
      if (showRegistrationTypeDropdown && !target.closest('.registration-type-dropdown-container')) {
        setShowRegistrationTypeDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showPrimaryDropdown, showRegistrationTypeDropdown]);

  const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>): void => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checkbox = e.target as HTMLInputElement;
      setFormData(prev => ({
        ...prev,
        [name]: checkbox.checked
      }));
    } else if (name === 'secondaryBusinessTypes') {
      // Handle multiple select for secondary business types
      const select = e.target as HTMLSelectElement;
      const selectedOptions = Array.from(select.selectedOptions).map(option => option.value);
      setFormData(prev => ({
        ...prev,
        secondaryBusinessTypes: selectedOptions
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }

    // Clear error when typing
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }

    // Clear server error
    if (serverError) {
      setServerError("");
    }
  };

  const validateStep1 = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.businessName.trim()) {
      newErrors.businessName = "Business name is required";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Please enter a valid email";
    }

    if (!formData.phone.trim()) {
      newErrors.phone = "Phone number is required";
    } else if (!/^\d{10}$/.test(formData.phone.replace(/[^0-9]/g, ''))) {
      newErrors.phone = "Please enter a valid 10-digit phone number";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateStep2 = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.password) {
      newErrors.password = "Password is required";
    } else if (formData.password.length < 8) {
      newErrors.password = "Password must be at least 8 characters";
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = "Please confirm your password";
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "Passwords don't match";
    }

    if (!formData.primaryBusinessType) {
      newErrors.primaryBusinessType = "Please select a primary business type";
    }

    if (formData.secondaryBusinessTypes.length > 6) {
      newErrors.secondaryBusinessTypes = "You can select up to 6 secondary business types";
    }

    if (!formData.city) {
      newErrors.city = "City is required";
    }

    if (formData.gstRegistered && !formData.gstNumber) {
      newErrors.gstNumber = "GST number is required when GST registered is selected";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = (): void => {
    if (validateStep1()) {
      setStep(2);
    }
  };

  const handleSubmit = async (e: FormEvent): Promise<void> => {
    e.preventDefault();

    if (step === 1) {
      handleNext();
      return;
    }

    if (validateStep2()) {
      setIsLoading(true);

      try {
        // Only proceed on client side
        if (typeof window === 'undefined') {
          setServerError("This operation can only be performed in a browser");
          setIsLoading(false);
          return;
        }

        // Prepare data for API call
        const vendorData = {
          name: formData.businessName,
          email: formData.email,
          password: formData.password,
          mobile_number: formData.phone,
          place: formData.city,
          primary_business_type: formData.primaryBusinessType,
          secondary_business_types: formData.secondaryBusinessTypes,
          business_registration_type: formData.businessRegistrationType,
          gst_registered: formData.gstRegistered,
          gst_number: formData.gstNumber,
          business_address: formData.businessAddress,
          state: formData.state,
          pin_code: formData.pinCode,
          contact_name: formData.contactName
        };

        // Call the vendor registration API
        const response = await vendorService.registerVendor(vendorData);

        // Token is already saved by the registerVendor function
        console.log('Vendor registered successfully:', response);

        // Set a flag to indicate we just registered (for the dashboard to handle)
        localStorage.setItem('just_registered', 'true');

        // Add a delay before redirecting to dashboard to allow backend to process
        setTimeout(() => {
          // Navigate to dashboard
          router.push('/vendor/dashboard');
        }, 3000); // 3 second delay

        // If using callback approach, also call this
        if (onSignup) {
          onSignup(formData);
        }
      } catch (err: any) {
        console.error("Vendor signup error:", err);
        setServerError(err.error || "Registration failed. Please try again.");
      } finally {
        setIsLoading(false);
      }
    }
  };

  const togglePasswordVisibility = (): void => {
    setShowPassword(!showPassword);
  };

  const toggleConfirmPasswordVisibility = (): void => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  return (
    <div className="flex justify-center items-center">
      {/* Add custom styles */}
      <style jsx>{customStyles}</style>
      <div className="w-full px-6 py-6 rounded-lg">
        {/* Back Button */}
        <button
          onClick={step === 1 ? onBack : () => setStep(1)}
          className="absolute top-4 left-4 text-gray-600 hover:text-gray-800 flex items-center"
          disabled={isLoading}
        >
          <ArrowLeft size={18} />
        </button>

        {/* Logo */}
        <div className="flex justify-center mb-6">
          <div className="text-red-600">
            <Image
              src="/pics/logo.png"
              alt="Wedzat logo"
              width={40}
              height={40}
              className="object-cover"
            />
          </div>
        </div>

        {/* Heading */}
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold">
            Vendor Registration
          </h1>
          <p className="text-sm mt-1 text-gray-600">
            {step === 1 ? "Step 1: Business Information" : "Step 2: Account Details"}
          </p>
        </div>

        {/* Server Error */}
        {serverError && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg">
            {serverError}
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          {step === 1 ? (
            // Step 1: Basic Info
            <>
              {/* Business Name */}
              <div>
                <div className="mb-1">
                  <label htmlFor="businessName" className="text-sm font-medium text-gray-700">
                    Business Name
                  </label>
                </div>
                <input
                  id="businessName"
                  type="text"
                  name="businessName"
                  placeholder="Enter your business name"
                  className={`w-full p-3 border ${errors.businessName ? 'border-red-500' : 'border-gray-300'} rounded-lg bg-white`}
                  value={formData.businessName}
                  onChange={handleChange}
                  disabled={isLoading}
                />
                {errors.businessName && (
                  <p className="text-red-500 text-xs mt-1">{errors.businessName}</p>
                )}
              </div>

              {/* Email */}
              <div>
                <div className="mb-1">
                  <label htmlFor="email" className="text-sm font-medium text-gray-700">
                    Email Address
                  </label>
                </div>
                <input
                  id="email"
                  type="email"
                  name="email"
                  placeholder="Enter your email"
                  className={`w-full p-3 border ${errors.email ? 'border-red-500' : 'border-gray-300'} rounded-lg bg-white`}
                  value={formData.email}
                  onChange={handleChange}
                  disabled={isLoading}
                />
                {errors.email && (
                  <p className="text-red-500 text-xs mt-1">{errors.email}</p>
                )}
              </div>

              {/* Phone */}
              <div>
                <div className="mb-1">
                  <label htmlFor="phone" className="text-sm font-medium text-gray-700">
                    Phone Number
                  </label>
                </div>
                <input
                  id="phone"
                  type="tel"
                  name="phone"
                  placeholder="Enter your phone number"
                  className={`w-full p-3 border ${errors.phone ? 'border-red-500' : 'border-gray-300'} rounded-lg bg-white`}
                  value={formData.phone}
                  onChange={handleChange}
                  disabled={isLoading}
                />
                {errors.phone && (
                  <p className="text-red-500 text-xs mt-1">{errors.phone}</p>
                )}
              </div>
            </>
          ) : (
            // Step 2: Account Details
            <>
              {/* Password */}
              <div>
                <div className="mb-1">
                  <label htmlFor="password" className="text-sm font-medium text-gray-700">
                    Password
                  </label>
                </div>
                <div className="relative">
                  <input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    name="password"
                    placeholder="Create a password"
                    className={`w-full p-3 border ${errors.password ? 'border-red-500' : 'border-gray-300'} rounded-lg bg-white`}
                    value={formData.password}
                    onChange={handleChange}
                    disabled={isLoading}
                  />
                  <button
                    type="button"
                    onClick={togglePasswordVisibility}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                  >
                    {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
                {errors.password && (
                  <p className="text-red-500 text-xs mt-1">{errors.password}</p>
                )}
              </div>

              {/* Confirm Password */}
              <div>
                <div className="mb-1">
                  <label htmlFor="confirmPassword" className="text-sm font-medium text-gray-700">
                    Confirm Password
                  </label>
                </div>
                <div className="relative">
                  <input
                    id="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    name="confirmPassword"
                    placeholder="Confirm your password"
                    className={`w-full p-3 border ${errors.confirmPassword ? 'border-red-500' : 'border-gray-300'} rounded-lg bg-white`}
                    value={formData.confirmPassword}
                    onChange={handleChange}
                    disabled={isLoading}
                  />
                  <button
                    type="button"
                    onClick={toggleConfirmPasswordVisibility}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                  >
                    {showConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
                {errors.confirmPassword && (
                  <p className="text-red-500 text-xs mt-1">{errors.confirmPassword}</p>
                )}
              </div>

              {/* Primary Business Type - Custom Dropdown */}
              <div>
                <div className="mb-1">
                  <label htmlFor="primaryBusinessType" className="text-sm font-medium text-gray-700">
                    Primary Business Type
                  </label>
                </div>
                <div className="relative primary-dropdown-container">
                  {/* Custom dropdown button */}
                  <button
                    type="button"
                    className={`w-full p-3 border ${errors.primaryBusinessType ? 'border-red-500' : 'border-gray-300'} rounded-lg bg-white text-left flex justify-between items-center`}
                    onClick={() => setShowPrimaryDropdown(!showPrimaryDropdown)}
                    disabled={isLoading}
                  >
                    <span>
                      {formData.primaryBusinessType
                        ? businessTypes.find(type => type.type_id === formData.primaryBusinessType)?.name || 'Select your primary business type'
                        : 'Select your primary business type'}
                    </span>
                    <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                  </button>

                  {/* Dropdown menu */}
                  {showPrimaryDropdown && (
                    <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                      {businessTypes.map((type) => (
                        <button
                          key={type.type_id}
                          type="button"
                          className={`w-full text-left px-4 py-2 hover:bg-gray-100 ${formData.primaryBusinessType === type.type_id ? 'bg-gray-100' : ''}`}
                          onClick={() => {
                            setFormData(prev => ({
                              ...prev,
                              primaryBusinessType: type.type_id
                            }));
                            setShowPrimaryDropdown(false);
                            // Clear error when selecting
                            if (errors.primaryBusinessType) {
                              setErrors(prev => {
                                const newErrors = { ...prev };
                                delete newErrors.primaryBusinessType;
                                return newErrors;
                              });
                            }
                          }}
                        >
                          {type.name}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
                {errors.primaryBusinessType && (
                  <p className="text-red-500 text-xs mt-1">{errors.primaryBusinessType}</p>
                )}
              </div>

              {/* Secondary Business Types */}
              <div>
                <div className="mb-1">
                  <label className="text-sm font-medium text-gray-700">
                    Secondary Business Types (Optional, max 6)
                  </label>
                </div>
                <div className="grid grid-cols-2 gap-2">
                  {businessTypes.map((type) => {
                    // Skip if this is already selected as primary
                    if (type.type_id === formData.primaryBusinessType) return null;

                    const isSelected = formData.secondaryBusinessTypes.includes(type.type_id);
                    return (
                      <div key={type.type_id} className="flex items-center">
                        <input
                          type="checkbox"
                          id={`secondary-${type.type_id}`}
                          checked={isSelected}
                          disabled={!isSelected && formData.secondaryBusinessTypes.length >= 6 || isLoading}
                          onChange={() => {
                            const updatedTypes = isSelected
                              ? formData.secondaryBusinessTypes.filter(id => id !== type.type_id)
                              : [...formData.secondaryBusinessTypes, type.type_id];

                            setFormData(prev => ({
                              ...prev,
                              secondaryBusinessTypes: updatedTypes
                            }));
                          }}
                          className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                        />
                        <label htmlFor={`secondary-${type.type_id}`} className="ml-2 text-sm text-gray-700">
                          {type.name}
                        </label>
                      </div>
                    );
                  })}
                </div>
                <p className="text-gray-500 text-xs mt-1">Select up to 6 secondary business types</p>
                {errors.secondaryBusinessTypes && (
                  <p className="text-red-500 text-xs mt-1">{errors.secondaryBusinessTypes}</p>
                )}
              </div>

              {/* Business Registration Type - Custom Dropdown */}
              <div>
                <div className="mb-1">
                  <label htmlFor="businessRegistrationType" className="text-sm font-medium text-gray-700">
                    Business Registration Type
                  </label>
                </div>
                <div className="relative registration-type-dropdown-container">
                  {/* Custom dropdown button */}
                  <button
                    type="button"
                    className={`w-full p-3 border ${errors.businessRegistrationType ? 'border-red-500' : 'border-gray-300'} rounded-lg bg-white text-left flex justify-between items-center`}
                    onClick={() => setShowRegistrationTypeDropdown(!showRegistrationTypeDropdown)}
                    disabled={isLoading}
                  >
                    <span>
                      {formData.businessRegistrationType === 'company' ? 'Company' : 'Freelancer'}
                    </span>
                    <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                  </button>

                  {/* Dropdown menu */}
                  {showRegistrationTypeDropdown && (
                    <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg">
                      <button
                        type="button"
                        className={`w-full text-left px-4 py-2 hover:bg-gray-100 ${formData.businessRegistrationType === 'company' ? 'bg-gray-100' : ''}`}
                        onClick={() => {
                          setFormData(prev => ({
                            ...prev,
                            businessRegistrationType: 'company'
                          }));
                          setShowRegistrationTypeDropdown(false);
                        }}
                      >
                        Company
                      </button>
                      <button
                        type="button"
                        className={`w-full text-left px-4 py-2 hover:bg-gray-100 ${formData.businessRegistrationType === 'freelancer' ? 'bg-gray-100' : ''}`}
                        onClick={() => {
                          setFormData(prev => ({
                            ...prev,
                            businessRegistrationType: 'freelancer'
                          }));
                          setShowRegistrationTypeDropdown(false);
                        }}
                      >
                        Freelancer
                      </button>
                    </div>
                  )}
                </div>
                {errors.businessRegistrationType && (
                  <p className="text-red-500 text-xs mt-1">{errors.businessRegistrationType}</p>
                )}
              </div>

              {/* GST Registration */}
              <div>
                <div className="flex items-center mb-1">
                  <input
                    id="gstRegistered"
                    type="checkbox"
                    name="gstRegistered"
                    className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                    checked={formData.gstRegistered}
                    onChange={handleChange}
                    disabled={isLoading}
                  />
                  <label htmlFor="gstRegistered" className="ml-2 text-sm font-medium text-gray-700">
                    GST Registered
                  </label>
                </div>
              </div>

              {/* GST Number (conditional) */}
              {formData.gstRegistered && (
                <div>
                  <div className="mb-1">
                    <label htmlFor="gstNumber" className="text-sm font-medium text-gray-700">
                      GST Number
                    </label>
                  </div>
                  <input
                    id="gstNumber"
                    type="text"
                    name="gstNumber"
                    placeholder="Enter your GST number"
                    className={`w-full p-3 border ${errors.gstNumber ? 'border-red-500' : 'border-gray-300'} rounded-lg bg-white`}
                    value={formData.gstNumber}
                    onChange={handleChange}
                    disabled={isLoading}
                  />
                  {errors.gstNumber && (
                    <p className="text-red-500 text-xs mt-1">{errors.gstNumber}</p>
                  )}
                </div>
              )}

              {/* Business Address */}
              <div>
                <div className="mb-1">
                  <label htmlFor="businessAddress" className="text-sm font-medium text-gray-700">
                    Business Address
                  </label>
                </div>
                <textarea
                  id="businessAddress"
                  name="businessAddress"
                  placeholder="Enter your business address"
                  className={`w-full p-3 border ${errors.businessAddress ? 'border-red-500' : 'border-gray-300'} rounded-lg bg-white`}
                  value={formData.businessAddress}
                  onChange={handleChange}
                  disabled={isLoading}
                  rows={3}
                />
                {errors.businessAddress && (
                  <p className="text-red-500 text-xs mt-1">{errors.businessAddress}</p>
                )}
              </div>

              {/* City */}
              <div>
                <div className="mb-1">
                  <label htmlFor="city" className="text-sm font-medium text-gray-700">
                    City
                  </label>
                </div>
                <input
                  id="city"
                  type="text"
                  name="city"
                  placeholder="Enter your city"
                  className={`w-full p-3 border ${errors.city ? 'border-red-500' : 'border-gray-300'} rounded-lg bg-white`}
                  value={formData.city}
                  onChange={handleChange}
                  disabled={isLoading}
                />
                {errors.city && (
                  <p className="text-red-500 text-xs mt-1">{errors.city}</p>
                )}
              </div>

              {/* State */}
              <div>
                <div className="mb-1">
                  <label htmlFor="state" className="text-sm font-medium text-gray-700">
                    State
                  </label>
                </div>
                <input
                  id="state"
                  type="text"
                  name="state"
                  placeholder="Enter your state"
                  className={`w-full p-3 border ${errors.state ? 'border-red-500' : 'border-gray-300'} rounded-lg bg-white`}
                  value={formData.state}
                  onChange={handleChange}
                  disabled={isLoading}
                />
                {errors.state && (
                  <p className="text-red-500 text-xs mt-1">{errors.state}</p>
                )}
              </div>

              {/* PIN Code */}
              <div>
                <div className="mb-1">
                  <label htmlFor="pinCode" className="text-sm font-medium text-gray-700">
                    PIN Code
                  </label>
                </div>
                <input
                  id="pinCode"
                  type="text"
                  name="pinCode"
                  placeholder="Enter your PIN code"
                  className={`w-full p-3 border ${errors.pinCode ? 'border-red-500' : 'border-gray-300'} rounded-lg bg-white`}
                  value={formData.pinCode}
                  onChange={handleChange}
                  disabled={isLoading}
                />
                {errors.pinCode && (
                  <p className="text-red-500 text-xs mt-1">{errors.pinCode}</p>
                )}
              </div>

              {/* Contact Name */}
              <div>
                <div className="mb-1">
                  <label htmlFor="contactName" className="text-sm font-medium text-gray-700">
                    Contact Person Name
                  </label>
                </div>
                <input
                  id="contactName"
                  type="text"
                  name="contactName"
                  placeholder="Enter contact person name"
                  className={`w-full p-3 border ${errors.contactName ? 'border-red-500' : 'border-gray-300'} rounded-lg bg-white`}
                  value={formData.contactName}
                  onChange={handleChange}
                  disabled={isLoading}
                />
                {errors.contactName && (
                  <p className="text-red-500 text-xs mt-1">{errors.contactName}</p>
                )}
              </div>
            </>
          )}

          {/* Submit/Next Button */}
          <button
            type="submit"
            className="w-full bg-red-700 text-white py-3 rounded-md hover:bg-red-800 transition duration-200 mt-6 disabled:bg-red-300"
            disabled={isLoading}
          >
            {isLoading
              ? "Please wait..."
              : (step === 1 ? "Continue" : "Create Account")}
          </button>
        </form>

        {/* Login Link */}
        <div className="text-center mt-6">
          <p className="text-gray-700 text-sm">
            Already have an account?
            <button
              type="button"
              onClick={onLoginClick}
              className="text-red-700 font-medium ml-1 hover:text-red-800"
              disabled={isLoading}
            >
              Log in
            </button>
          </p>
        </div>
      </div>
    </div>
  );
};

export default VendorSignup;