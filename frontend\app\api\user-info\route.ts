// frontend/app/api/user-info/route.ts
import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import jwt from 'jsonwebtoken';

interface ResponseData {
  success: boolean;
  user?: {
    first_name: string;
    last_name?: string;
    email?: string;
  };
  message?: string;
}

export async function GET(): Promise<NextResponse<ResponseData>> {
  try {
    // Get the token from cookies
    const token = cookies().get('token')?.value;
    
    if (!token) {
      return NextResponse.json({ 
        success: false, 
        message: "Authentication required" 
      }, { status: 401 });
    }
    
    // For simplicity, we'll just return a default user
    // In a real app, you would decode the token and fetch user info from your backend
    return NextResponse.json({ 
      success: true, 
      user: {
        first_name: "The",
        last_name: "Couple",
        email: "<EMAIL>"
      }
    });
  } catch (error) {
    console.error('Error getting user info:', error);
    
    return NextResponse.json({ 
      success: false, 
      message: "Failed to get user information" 
    }, { status: 500 });
  }
}
