// components/login/user-login.tsx
import React, { useState, ChangeEvent, FormEvent } from "react";
import Image from "next/image";
import { <PERSON>Left, Eye, EyeOff } from "lucide-react";
import { useRouter } from "next/navigation";
import { authService } from "../../services/api";
import { FcGoogle } from "react-icons/fc";
import { FaApple, FaFacebookF } from "react-icons/fa";

interface UserLoginProps {
  onLogin?: (credentials: {
    email?: string;
    mobile_number?: string;
    password: string;
  }) => void;
  onForgotPassword?: () => void;
  onSignupClick?: () => void;
  onBack?: () => void;
  onSocialSignin?: (provider: "Google" | "Apple" | "Facebook") => void;
}

const UserLogin: React.FC<UserLoginProps> = ({
  onLogin,
  onForgotPassword,
  onSignupClick,
  onBack,
  onSocialSignin,
}) => {
  const router = useRouter();
  const [loginType, setLoginType] = useState<"email" | "mobile">("mobile");
  const [credentials, setCredentials] = useState({
    mobile_number: "",
    email: "",
    password: "",
  });
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState({
    mobile_number: "",
    email: "",
    password: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [serverError, setServerError] = useState("");
  const [debugInfo, setDebugInfo] = useState(""); // For debugging

  const handleChange = (e: ChangeEvent<HTMLInputElement>): void => {
    const { name, value } = e.target;
    setCredentials((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when typing
    if (errors[name as keyof typeof errors]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }

    // Clear server error when typing
    if (serverError) {
      setServerError("");
    }
  };

  const validateForm = (): boolean => {
    let isValid = true;
    const newErrors = { mobile_number: "", email: "", password: "" };

    if (loginType === "email") {
      // Email validation
      if (!credentials.email) {
        newErrors.email = "Email is required";
        isValid = false;
      } else if (!/\S+@\S+\.\S+/.test(credentials.email)) {
        newErrors.email = "Please enter a valid email";
        isValid = false;
      }
    } else {
      // Mobile validation
      if (!credentials.mobile_number) {
        newErrors.mobile_number = "Mobile number is required";
        isValid = false;
      } else if (credentials.mobile_number.length < 10) {
        newErrors.mobile_number = "Please enter a valid mobile number";
        isValid = false;
      }
    }

    // Password validation
    if (!credentials.password) {
      newErrors.password = "Password is required";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e: FormEvent): Promise<void> => {
    e.preventDefault();

    if (validateForm()) {
      setIsLoading(true);
      setServerError("");
      setDebugInfo("Login attempt started...");

      try {
        // Call the login API with appropriate credentials
        let loginParams: any = { password: credentials.password };

        if (loginType === "email") {
          loginParams.email = credentials.email;
        } else {
          loginParams.mobile_number = credentials.mobile_number;
        }

        setDebugInfo(
          (prev) =>
            prev +
            `\nAttempting login with ${loginType}: ${loginType === "email"
              ? credentials.email
              : credentials.mobile_number
            }`
        );

        // Login with the API
        const response = await authService.login(loginParams);

        setDebugInfo(
          (prev) =>
            prev +
            `\nLogin response received: ${JSON.stringify({
              success: true,
              hasToken: !!response?.token,
              tokenPreview: response?.token
                ? `${response.token.substring(0, 10)}...`
                : "none",
            })}`
        );

        // Save token to localStorage with both key names for compatibility
        if (response?.token) {
          // Store token in multiple places to ensure it's detected
          try {
            // 1. Store in localStorage (both keys for compatibility)
            localStorage.setItem("token", response.token);
            localStorage.setItem("jwt_token", response.token);

            // 2. Also store as a cookie for middleware to detect
            document.cookie = `token=${response.token}; path=/; max-age=86400; SameSite=Strict`;

            // 3. Verify storage was successful
            const localToken = localStorage.getItem("token");
            const jwtToken = localStorage.getItem("jwt_token");
            const cookieSet = document.cookie.includes("token=");

            setDebugInfo(
              (prev) =>
                prev +
                `\nToken storage verification:
- localStorage (token): ${localToken ? "SUCCESS" : "FAILED"}
- localStorage (jwt_token): ${jwtToken ? "SUCCESS" : "FAILED"}
- cookie: ${cookieSet ? "SUCCESS" : "FAILED"}`
            );

            // If using callback approach, call it before redirecting
            if (onLogin) {
              onLogin(loginParams);
              setDebugInfo((prev) => prev + `\nonLogin callback executed`);
            }

            // Add short delay before redirecting to ensure tokens are saved
            setDebugInfo(
              (prev) => prev + `\nPreparing to redirect to home page...`
            );

            setTimeout(() => {
              setDebugInfo((prev) => prev + `\nRedirecting now!`);
              router.push("/home");
            }, 500);
          } catch (storageError) {
            console.error("Error storing token:", storageError);
            setDebugInfo(
              (prev) => prev + `\nError storing token: ${storageError}`
            );
            setServerError(
              "Error storing authentication token. Please try again."
            );
          }
        } else {
          // No token in response
          setDebugInfo((prev) => prev + `\nNo token in response`);
          setServerError(
            "Authentication failed: No token received from server"
          );
        }
      } catch (error: any) {
        console.error("User login error:", error);
        setServerError(error.error || "Invalid credentials. Please try again.");
        setDebugInfo(
          (prev) => prev + `\nLogin error: ${JSON.stringify(error)}`
        );
      } finally {
        setIsLoading(false);
      }
    }
  };

  const togglePasswordVisibility = (): void => {
    setShowPassword(!showPassword);
  };

  const toggleLoginType = (type: "email" | "mobile"): void => {
    setLoginType(type);
    setErrors({ mobile_number: "", email: "", password: "" });
    setServerError("");
    setDebugInfo("");
  };

  return (
    <div className="flex justify-center items-center">
      <div className="w-full px-6 py-6 rounded-lg overflow-y-auto h-screen">



        {/* Logo */}
        <div className="flex justify-center mb-6">
          <div className="text-red-600">
            <Image
              src="/pics/logo.png"
              alt="Wedzat logo"
              width={40}
              height={40}
              className="object-cover"
            />
          </div>
        </div>

        {/* Heading */}
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold">User Login</h1>
          <p className="text-sm mt-1 text-gray-600">
            Welcome back! Please login to continue
          </p>
        </div>

        {/* Login Type Toggle */}
        <div className="flex justify-center mb-6">
          <div className="flex rounded-full bg-gray-100 p-1">
            <button
              type="button"
              onClick={() => toggleLoginType("mobile")}
              className={`px-4 py-2 rounded-full text-sm font-medium ${loginType === "mobile"
                ? "bg-red-700 text-white"
                : "text-gray-700 hover:bg-gray-200"
                }`}
            >
              Mobile
            </button>
            <button
              type="button"
              onClick={() => toggleLoginType("email")}
              className={`px-4 py-2 rounded-full text-sm font-medium ${loginType === "email"
                ? "bg-red-700 text-white"
                : "text-gray-700 hover:bg-gray-200"
                }`}
            >
              Email
            </button>
          </div>
        </div>

        {/* Server Error */}
        {serverError && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg">
            {serverError}
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Mobile/Email Field */}
          {loginType === "mobile" ? (
            <div>
              <div className="mb-1">
                <label
                  htmlFor="mobile_number"
                  className="text-sm font-medium text-gray-700"
                >
                  Mobile Number
                </label>
              </div>
              <input
                id="mobile_number"
                type="tel"
                name="mobile_number"
                placeholder="Enter your mobile number"
                className={`w-full p-3 border ${errors.mobile_number ? "border-red-500" : "border-gray-300"
                  } rounded-lg bg-white`}
                value={credentials.mobile_number}
                onChange={handleChange}
                disabled={isLoading}
              />
              {errors.mobile_number && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.mobile_number}
                </p>
              )}
            </div>
          ) : (
            <div>
              <div className="mb-1">
                <label
                  htmlFor="email"
                  className="text-sm font-medium text-gray-700"
                >
                  Email Address
                </label>
              </div>
              <input
                id="email"
                type="email"
                name="email"
                placeholder="Enter your email"
                className={`w-full p-3 border ${errors.email ? "border-red-500" : "border-gray-300"
                  } rounded-lg bg-white`}
                value={credentials.email}
                onChange={handleChange}
                disabled={isLoading}
              />
              {errors.email && (
                <p className="text-red-500 text-xs mt-1">{errors.email}</p>
              )}
            </div>
          )}

          {/* Password Field */}
          <div>
            <div className="flex justify-between mb-1">
              <label
                htmlFor="password"
                className="text-sm font-medium text-gray-700"
              >
                Password
              </label>
              {onForgotPassword && (
                <button
                  type="button"
                  onClick={onForgotPassword}
                  className="text-sm text-red-700 hover:text-red-800"
                  disabled={isLoading}
                >
                  Forgot password?
                </button>
              )}
            </div>
            <div className="relative">
              <input
                id="password"
                type={showPassword ? "text" : "password"}
                name="password"
                placeholder="Enter your password"
                className={`w-full p-3 border ${errors.password ? "border-red-500" : "border-gray-300"
                  } rounded-lg bg-white`}
                value={credentials.password}
                onChange={handleChange}
                disabled={isLoading}
              />
              <button
                type="button"
                onClick={togglePasswordVisibility}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
              >
                {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
              </button>
            </div>
            {errors.password && (
              <p className="text-red-500 text-xs mt-1">{errors.password}</p>
            )}
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            className="w-full bg-red-700 text-white py-3 rounded-md hover:bg-red-800 transition duration-200 mt-6 disabled:bg-red-300"
            disabled={isLoading}
          >
            {isLoading ? "Logging in..." : "Login"}
          </button>
        </form>

        <div className="mt-6">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white text-gray-500">
                Or continue with
              </span>
            </div>
          </div>

          <div className="flex justify-between gap-4 mt-6">
            <button
              onClick={() => onSocialSignin?.("Google")}
              className="flex-1 h-12 border border-gray-300 rounded-lg bg-white flex items-center justify-center hover:bg-gray-50 transition-colors"
              disabled={isLoading}
            >
              <FcGoogle size={24} />
            </button>
            <button
              onClick={() => onSocialSignin?.("Apple")}
              className="flex-1 h-12 border border-gray-300 rounded-lg bg-white flex items-center justify-center hover:bg-gray-50 transition-colors"
              disabled={isLoading}
            >
              <FaApple size={24} />
            </button>
            <button
              onClick={() => onSocialSignin?.("Facebook")}
              className="flex-1 h-12 border border-gray-300 rounded-lg bg-white flex items-center justify-center hover:bg-gray-50 transition-colors"
              disabled={isLoading}
            >
              <FaFacebookF size={24} color="#1877F2" />
            </button>
          </div>
        </div>

        {/* Signup Link */}
        <div className="text-center mt-6">
          <p className="text-gray-700 text-sm">
            Don't have an account?
            <button
              type="button"
              onClick={onSignupClick}
              className="text-red-700 font-medium ml-1 hover:text-red-800"
            >
              Sign up
            </button>
          </p>
        </div>
      </div>
    </div>
  );
};

export default UserLogin;
