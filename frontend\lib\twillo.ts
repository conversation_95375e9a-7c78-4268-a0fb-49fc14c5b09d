// SMS verification functionality using Twilio Verify API
import twilio, { <PERSON>wi<PERSON> } from 'twilio';

// Initialize Twilio client
const accountSid = process.env.TWILIO_ACCOUNT_SID || '';
const authToken = process.env.TWILIO_AUTH_TOKEN || '';
const verifySid = process.env.TWILIO_VERIFY_SID || ''; // The Verify Service SID
const client: Twilio = twilio(accountSid, authToken);

/**
 * Sends a verification code via Twilio Verify
 * @param phone Recipient phone number
 * @returns Promise that resolves to verification SID on success
 */
export async function sendVerificationCode(phone: string): Promise<string> {
  try {
    // Format the phone number to E.164 format if not already formatted
    let formattedNumber: string = phone;
    if (!phone.startsWith('+')) {
      // Default to India country code if not specified
      formattedNumber = `+91${phone}`;
    }
    
    // Send verification
    const verification = await client.verify.v2
      .services(verifySid)
      .verifications.create({
        to: formattedNumber,
        channel: 'sms' // You can use 'call' or 'email' too
      });
    
    console.log(`Verification sent to ${phone}, Status: ${verification.status}`);
    return verification.sid;
  } catch (error) {
    console.error('Error sending verification:', error);
    throw error;
  }
}

/**
 * Checks a verification code submitted by user
 * @param phone Phone number that received the code
 * @param code Verification code entered by user
 * @returns Promise that resolves to boolean indicating success
 */
export async function checkVerificationCode(phone: string, code: string): Promise<boolean> {
  try {
    // Format the phone number to E.164 format if not already formatted
    let formattedNumber: string = phone;
    if (!phone.startsWith('+')) {
      // Default to India country code if not specified
      formattedNumber = `+91${phone}`;
    }
    
    // Check verification
    const verificationCheck = await client.verify.v2
      .services(verifySid)
      .verificationChecks.create({
        to: formattedNumber,
        code: code
      });
    
    console.log(`Verification check for ${phone}, Status: ${verificationCheck.status}`);
    return verificationCheck.status === 'approved';
  } catch (error) {
    console.error('Error checking verification code:', error);
    throw error;
  }
}

// Legacy function names to maintain compatibility
export async function sendOtpSMS(phone: string): Promise<boolean> {
  try {
    await sendVerificationCode(phone);
    return true;
  } catch (error) {
    throw error;
  }
}

export async function sendSMS(to: string, message: string): Promise<boolean> {
  console.warn('sendSMS is deprecated. Using Twilio Verify API instead.');
  try {
    await sendVerificationCode(to);
    return true;
  } catch (error) {
    throw error;
  }
}