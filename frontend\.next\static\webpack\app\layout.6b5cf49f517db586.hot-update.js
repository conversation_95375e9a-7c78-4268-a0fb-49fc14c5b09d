"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"062f93418aa8\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xca29tbWlcXE9uZURyaXZlXFxEZXNrdG9wXFxXRURaQVRfXFxmcm9udGVuZFxcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjA2MmY5MzQxOGFhOFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/upload/UploadManager.tsx":
/*!*********************************************!*\
  !*** ./components/upload/UploadManager.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _UploadTypeSelection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./UploadTypeSelection */ \"(app-pages-browser)/./components/upload/UploadTypeSelection.tsx\");\n/* harmony import */ var _VideoCategorySelection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./VideoCategorySelection */ \"(app-pages-browser)/./components/upload/VideoCategorySelection.tsx\");\n/* harmony import */ var _ThumbnailSelection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ThumbnailSelection */ \"(app-pages-browser)/./components/upload/ThumbnailSelection.tsx\");\n/* harmony import */ var _PersonalDetails__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./PersonalDetails */ \"(app-pages-browser)/./components/upload/PersonalDetails.tsx\");\n/* harmony import */ var _VendorDetails__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./VendorDetails */ \"(app-pages-browser)/./components/upload/VendorDetails.tsx\");\n/* harmony import */ var _FaceVerification__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./FaceVerification */ \"(app-pages-browser)/./components/upload/FaceVerification.tsx\");\n/* harmony import */ var _UploadProgress__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./UploadProgress */ \"(app-pages-browser)/./components/upload/UploadProgress.tsx\");\n/* harmony import */ var _contexts_UploadContexts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../contexts/UploadContexts */ \"(app-pages-browser)/./contexts/UploadContexts.tsx\");\n/* harmony import */ var _utils_uploadUtils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../utils/uploadUtils */ \"(app-pages-browser)/./utils/uploadUtils.ts\");\n/* harmony import */ var _utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../utils/alertUtils */ \"(app-pages-browser)/./utils/alertUtils.tsx\");\n/* harmony import */ var _hooks_useMedia__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../hooks/useMedia */ \"(app-pages-browser)/./hooks/useMedia.ts\");\n// components/upload/UploadManager.tsx\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function to format time in minutes and seconds\nconst formatTime = (seconds)=>{\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    if (minutes === 0) {\n        return \"\".concat(remainingSeconds, \" seconds\");\n    } else if (minutes === 1 && remainingSeconds === 0) {\n        return '1 minute';\n    } else if (remainingSeconds === 0) {\n        return \"\".concat(minutes, \" minutes\");\n    } else {\n        return \"\".concat(minutes, \" minute\").concat(minutes > 1 ? 's' : '', \" and \").concat(remainingSeconds, \" second\").concat(remainingSeconds !== 1 ? 's' : '');\n    }\n};\nconst UploadManager = (param)=>{\n    let { onClose, initialType, onUploadComplete } = param;\n    _s();\n    const { state, setFile, setMediaType, setCategory, setMediaSubtype, setTitle, setDescription, setDetailField, startUpload, startUploadWithCategory, setThumbnail, setVendorDetails, setPersonalDetails, setDuration, resetUpload } = (0,_contexts_UploadContexts__WEBPACK_IMPORTED_MODULE_9__.useUpload)();\n    const [phase, setPhase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('typeSelection');\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialType || '');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [previewImage, setPreviewImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [thumbnailImage, setThumbnailImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const vendorDetailsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    // Helper function to determine content type for PersonalDetails\n    const getContentType = ()=>{\n        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n            return 'moment';\n        } else if (state.mediaType === 'video') {\n            return 'video';\n        } else {\n            return 'photo';\n        }\n    };\n    // Store personal details to persist between screens\n    const [personalDetails, setLocalPersonalDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        caption: '',\n        lifePartner: '',\n        weddingStyle: '',\n        place: '',\n        eventType: '',\n        budget: ''\n    });\n    // Auto-select the type if initialType is provided\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UploadManager.useEffect\": ()=>{\n            if (initialType) {\n                console.log('Auto-selecting type from initialType:', initialType);\n                handleTypeSelected(initialType);\n            }\n        }\n    }[\"UploadManager.useEffect\"], []);\n    // Store vendor details to persist between screens\n    const [vendorDetailsData, setVendorDetailsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        venue: {\n            name: '',\n            mobileNumber: ''\n        },\n        photographer: {\n            name: '',\n            mobileNumber: ''\n        },\n        makeupArtist: {\n            name: '',\n            mobileNumber: ''\n        },\n        decorations: {\n            name: '',\n            mobileNumber: ''\n        },\n        caterer: {\n            name: '',\n            mobileNumber: ''\n        }\n    });\n    // Use the new media upload hook\n    const { mutate: uploadMedia, isPending: isUploading } = (0,_hooks_useMedia__WEBPACK_IMPORTED_MODULE_12__.useMediaUpload)();\n    // Handle media type selection\n    const handleTypeSelected = (type)=>{\n        // First, completely reset everything\n        resetUpload();\n        setPreviewImage(null);\n        setThumbnailImage(null);\n        // Then set the new type\n        setSelectedType(type);\n        console.log(\"Selected type:\", type);\n        if ([\n            'flashes',\n            'glimpses',\n            'movies',\n            'photos',\n            'moments'\n        ].includes(type)) {\n            // For explicit video types, photos, and moments, set the appropriate media type and go to category selection\n            if (type === 'photos') {\n                console.log('Setting media type to photo for:', type);\n                setMediaType('photo');\n                setMediaSubtype('post');\n            } else if (type === 'moments') {\n                console.log('Setting media type for moments (will be determined by file type)');\n                // For moments, we'll set the media type later based on the file type (photo or video)\n                setMediaSubtype('story');\n            } else {\n                setMediaType('video');\n                setMediaSubtype(getMediaSubtypeFromSelectedType(type));\n            }\n            // Go to category selection for all media types\n            setPhase('categorySelection');\n        } else if (type === 'photo') {\n            // For single photo type (if it exists)\n            console.log('Setting media type to photo for:', type);\n            setMediaType('photo');\n            setMediaSubtype('post');\n            // Use a special photo-only upload handler for photos\n            handlePhotoUpload();\n        }\n    };\n    // Helper function to get the backend media subtype from the selected UI type\n    const getMediaSubtypeFromSelectedType = (type)=>{\n        // Map UI category to backend category for media_subtype\n        switch(type){\n            // Photo types\n            case 'moments':\n                return 'story'; // Backend expects 'story' for moments\n            case 'photos':\n                return 'post'; // Backend expects 'post' for regular photos\n            // Video types\n            case 'flashes':\n                return 'flash'; // Backend expects 'flash'\n            case 'glimpses':\n                return 'glimpse'; // Backend expects 'glimpse'\n            case 'movies':\n                return 'movie'; // Backend expects 'movie'\n            // Default fallback\n            default:\n                return type === 'moments' ? 'story' : 'post'; // Default based on type\n        }\n    };\n    // Handle category selection for both videos and photos\n    const handleCategorySelected = (category)=>{\n        // First, make sure we have a clean state for the new upload\n        // but preserve the selected type and media type\n        const currentType = selectedType;\n        const currentMediaType = state.mediaType;\n        resetUpload();\n        setSelectedType(currentType);\n        setMediaType(currentMediaType);\n        setPreviewImage(null);\n        setThumbnailImage(null);\n        // Now set the new category\n        setSelectedCategory(category);\n        // Get the media subtype based on the selected type\n        let mediaSubtype;\n        if (currentType === 'photos') {\n            // For photos, always use 'post' as the media subtype\n            mediaSubtype = 'post';\n            console.log(\"UPLOAD MANAGER - Using media subtype 'post' for photos\");\n        } else {\n            // For videos, use the subtype based on the selected type\n            mediaSubtype = getMediaSubtypeFromSelectedType(selectedType);\n            console.log(\"UPLOAD MANAGER - Using media subtype \".concat(mediaSubtype, \" based on selected type \").concat(selectedType));\n            console.log(\"UPLOAD MANAGER - Backend expects: flashes→flash, glimpses→glimpse, movies→movie, moments→story\");\n        }\n        console.log(\"UPLOAD MANAGER - Selected category:\", category);\n        console.log(\"UPLOAD MANAGER - Setting media subtype to:\", mediaSubtype);\n        // Set the media subtype in the context\n        setMediaSubtype(mediaSubtype);\n        // Map the selected category to a valid backend video_category\n        let backendVideoCategory = '';\n        if (category === 'my_wedding_videos') {\n            backendVideoCategory = 'my_wedding';\n        } else if (category === 'wedding_vlog') {\n            backendVideoCategory = 'wedding_vlog';\n        }\n        // Make sure we have a valid video_category\n        if (!backendVideoCategory) {\n            console.error('Invalid video category selected:', category);\n            alert('Please select a valid video category');\n            return;\n        }\n        // Set video category in the context for the backend\n        console.log(\"UPLOAD MANAGER - Setting video_category to:\", backendVideoCategory);\n        setDetailField('video_category', backendVideoCategory);\n        // Log the final values\n        console.log(\"UPLOAD MANAGER - Selected category:\", category);\n        console.log(\"UPLOAD MANAGER - Backend video category set to:\", backendVideoCategory);\n        console.log(\"UPLOAD MANAGER - Media subtype set to:\", mediaSubtype);\n        // Proceed to file upload after setting the category\n        if (currentType === 'photos') {\n            // For photos, use the photo-specific upload handler\n            handlePhotoUpload();\n        } else {\n            // For videos, use the standard file upload handler\n            handleFileUpload();\n        }\n    };\n    // Handle thumbnail upload\n    const handleThumbnailUpload = ()=>{\n        // Create a file input element\n        const input = document.createElement('input');\n        input.type = 'file';\n        input.accept = 'image/*';\n        // Handle file selection\n        input.onchange = (e)=>{\n            const files = e.target.files;\n            if (files && files.length > 0) {\n                const file = files[0];\n                // Store the thumbnail\n                setThumbnailImage(file);\n                setThumbnail(file);\n                console.log(\"Thumbnail selected:\", file.name);\n                // Show a preview if needed\n                const reader = new FileReader();\n                reader.onload = (e)=>{\n                    var _e_target;\n                    if ((_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result) {\n                        // You could set a thumbnail preview here if needed\n                        console.log(\"Thumbnail preview ready\");\n                    }\n                };\n                reader.readAsDataURL(file);\n            }\n        };\n        // Trigger the file dialog\n        input.click();\n    };\n    // Get user-friendly display name for a category\n    const getCategoryDisplayName = (category)=>{\n        switch(category){\n            case 'flash':\n                return 'Flash';\n            case 'glimpse':\n                return 'Glimpse';\n            case 'movie':\n                return 'Movie';\n            case 'story':\n                return 'Story';\n            case 'post':\n                return 'Photo';\n            default:\n                return category.charAt(0).toUpperCase() + category.slice(1);\n        }\n    };\n    // Get appropriate category based on duration\n    const getAppropriateCategory = (duration)=>{\n        // For very short videos (1 minute or less), use flash instead of story/moments\n        if (duration <= 60) {\n            return 'flash'; // Very short videos (1 minute or less) - changed from 'story' to 'flash'\n        } else if (duration <= 90) {\n            return 'flash'; // Short videos (1.5 minutes or less)\n        } else if (duration <= 420) {\n            return 'glimpse'; // Medium videos (7 minutes or less)\n        } else {\n            return 'movie'; // Long videos (over 7 minutes)\n        }\n    };\n    // Special handler for photo uploads that strictly enforces image-only files\n    const handlePhotoUpload = ()=>{\n        console.log('handlePhotoUpload called - strict image-only upload');\n        // Create a file input element specifically for photos\n        const input = document.createElement('input');\n        input.type = 'file';\n        // Reset the input value\n        input.value = '';\n        // Only accept image files - explicitly list allowed types\n        input.accept = 'image/jpeg,image/png,image/gif,image/webp';\n        // Handle file selection with strict validation\n        input.onchange = async (e)=>{\n            const files = e.target.files;\n            if (!files || files.length === 0) return;\n            const file = files[0];\n            console.log('Photo file selected:', file.name, file.type, file.size);\n            // Strict validation - must be an image file\n            const validImageTypes = [\n                'image/jpeg',\n                'image/png',\n                'image/gif',\n                'image/webp'\n            ];\n            if (!validImageTypes.includes(file.type)) {\n                console.error('Invalid file type for photos:', file.type);\n                alert('Only JPEG, PNG, GIF, or WebP images can be uploaded as photos. Videos are not allowed.');\n                return;\n            }\n            // Additional check - reject any file that might be a video\n            if (file.type.startsWith('video/')) {\n                console.error('Attempted to upload a video file as photo');\n                alert('Videos cannot be uploaded as photos. Please select an image file.');\n                return;\n            }\n            // For photos, we need to be more careful with state management\n            // First, set the media type and subtype\n            setMediaType('photo');\n            setMediaSubtype('post');\n            // Then set the file in the state\n            setFile(file);\n            console.log('Photo file set in state:', file.name);\n            // Create a local reference to the file for use in the timeout\n            const currentFile = file;\n            // Double-check that the file is set in the state before proceeding\n            setTimeout(()=>{\n                // Check if the file is in the state\n                if (!state.file) {\n                    console.log('File not found in state after setting, trying again');\n                    // Try setting the file again\n                    setFile(currentFile);\n                    // Add another timeout to ensure the file is set\n                    setTimeout(()=>{\n                        if (!state.file) {\n                            console.log('File still not in state, setting it one more time');\n                            setFile(currentFile);\n                        } else {\n                            console.log('File confirmed in state after second attempt:', state.file.name);\n                        }\n                        // New flow logic: For moments (stories), skip personal details and go directly to face verification\n                        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                            console.log('Moments photo upload: skipping personal details, going directly to face verification');\n                            setPhase('faceVerification');\n                        } else {\n                            console.log('Moving to personalDetails phase for photo');\n                            setPhase('personalDetails');\n                        }\n                    }, 100);\n                } else {\n                    console.log('File confirmed in state:', state.file.name);\n                    // New flow logic: For moments (stories), skip personal details and go directly to face verification\n                    if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                        console.log('Moments photo upload: skipping personal details, going directly to face verification');\n                        setPhase('faceVerification');\n                    } else {\n                        console.log('Moving to personalDetails phase for photo');\n                        setPhase('personalDetails');\n                    }\n                }\n            }, 100);\n            // Handle image preview\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                var _e_target;\n                if ((_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result) {\n                    setPreviewImage(e.target.result);\n                    console.log('Preview image set for photo');\n                }\n            };\n            reader.readAsDataURL(file);\n        // Note: We don't set the phase here anymore - it's handled in the timeout above\n        };\n        // Trigger the file dialog\n        input.click();\n    };\n    // This function was previously used but is now replaced by getAppropriateCategory\n    // Keeping a comment here for reference in case it needs to be restored\n    // Handle manual upload button click\n    const handleFileUpload = async (category)=>{\n        console.log('handleFileUpload called with category:', category || 'none');\n        // Create a file input element\n        const input = document.createElement('input');\n        input.type = 'file';\n        // Reset the input value to ensure we get a new file selection event even if the same file is selected\n        input.value = '';\n        if (selectedType === 'moments') {\n            input.accept = 'image/*,video/*';\n        } else {\n            input.accept = selectedType === 'photo' || selectedType === 'photos' ? 'image/jpeg,image/png,image/gif,image/webp' // Explicitly list image types only\n             : 'video/*';\n        }\n        // Handle file selection\n        input.onchange = async (e)=>{\n            const files = e.target.files;\n            if (!files || files.length === 0) return;\n            const file = files[0];\n            console.log('File selected:', file.name, file.type, file.size);\n            // Strict validation for photo uploads - must be an image file\n            if (selectedType === 'photo' || selectedType === 'photos') {\n                const validImageTypes = [\n                    'image/jpeg',\n                    'image/png',\n                    'image/gif',\n                    'image/webp'\n                ];\n                // Check if file is a video or not a valid image type\n                if (file.type.startsWith('video/') || !validImageTypes.includes(file.type)) {\n                    console.error('Invalid file type for photos:', file.type);\n                    (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Invalid File Type', 'Only JPEG, PNG, GIF, or WebP images can be uploaded as photos. Videos are not allowed.');\n                    return;\n                }\n            }\n            // Reset the upload context before setting the new file\n            resetUpload();\n            // Set the file in the state\n            setFile(file);\n            console.log('File set in state:', file.name);\n            // If it's a video, calculate and set the duration\n            // Double-check that we're not trying to upload a video as a photo\n            if (file.type.startsWith('video/')) {\n                // Safety check - don't process videos for photo uploads\n                if (selectedType === 'photo' || selectedType === 'photos') {\n                    console.error('Attempted to process a video file for photo upload');\n                    (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Invalid File Type', 'Videos cannot be uploaded as photos. Please select an image file.');\n                    resetUpload();\n                    return;\n                }\n                try {\n                    const duration = await (0,_utils_uploadUtils__WEBPACK_IMPORTED_MODULE_10__.getVideoDuration)(file);\n                    console.log('Video duration calculated:', duration);\n                    setDuration(duration);\n                    // For moments, check if it's a video and validate the duration (max 1 minute)\n                    if (selectedType === 'moments') {\n                        console.log('Validating moments video duration...');\n                        setMediaType('video');\n                        // Check if the video is longer than 1 minute (60 seconds)\n                        if (duration > 60) {\n                            console.log(\"Moments video too long: \".concat(duration, \" seconds (max: 60 seconds)\"));\n                            // Show a more detailed error message with custom alert\n                            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showWarningAlert)('Moments Video Too Long', \"Moments videos must be 1 minute or less.\\n\\nYour video is \".concat(formatTime(duration), \" long.\\n\\nPlease select a shorter video or trim this video to 1 minute or less.\"));\n                            // Reset the upload context but preserve the selected type and category\n                            const currentType = selectedType;\n                            const currentCategory = selectedCategory;\n                            // First set the phase back to category selection\n                            setPhase('categorySelection');\n                            // Then reset the upload state\n                            setTimeout(()=>{\n                                resetUpload();\n                                setSelectedType(currentType);\n                                setSelectedCategory(currentCategory);\n                                console.log('Reset upload state after moments video duration validation failure');\n                            }, 100);\n                            // Return early to prevent further processing\n                            return;\n                        }\n                        console.log(\"Moments video duration valid: \".concat(duration, \" seconds (max: 60 seconds)\"));\n                        // For moments, we always use 'story' as the media subtype\n                        console.log('Setting media subtype for moments video to story');\n                        setMediaSubtype('story');\n                    }\n                    // If we have a category, validate the duration for that category\n                    if (selectedType && [\n                        'flashes',\n                        'glimpses',\n                        'movies'\n                    ].includes(selectedType)) {\n                        const mediaSubtype = getMediaSubtypeFromSelectedType(selectedType);\n                        const validationResult = (0,_utils_uploadUtils__WEBPACK_IMPORTED_MODULE_10__.validateVideoDuration)(duration, mediaSubtype);\n                        if (!validationResult.isValid) {\n                            // If there's a suggested category, automatically switch to it\n                            if (validationResult.suggestedCategory) {\n                                // For videos that exceed the maximum duration, automatically switch without asking\n                                console.log(\"Video exceeds maximum duration for \".concat(mediaSubtype, \". Automatically switching to \").concat(validationResult.suggestedCategory));\n                                (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showWarningAlert)('Video Duration Notice', \"Your video is too long for the \".concat(getCategoryDisplayName(mediaSubtype), \" category. It will be uploaded as a \").concat(getCategoryDisplayName(validationResult.suggestedCategory), \" instead.\"));\n                                // Switch to the suggested category\n                                console.log(\"Switching to suggested category: \".concat(validationResult.suggestedCategory));\n                                // Make sure we keep a reference to the file\n                                const currentFile = file;\n                                // Update the media subtype\n                                setMediaSubtype(validationResult.suggestedCategory);\n                                // Update the selected type to match the new category\n                                // Never suggest 'story' (moments) for other categories\n                                if (validationResult.suggestedCategory === 'flash') {\n                                    setSelectedType('flashes');\n                                } else if (validationResult.suggestedCategory === 'glimpse') {\n                                    setSelectedType('glimpses');\n                                } else if (validationResult.suggestedCategory === 'movie') {\n                                    setSelectedType('movies');\n                                }\n                                // Removed the 'story' suggestion for short videos\n                                // Make sure the file is still set in the state\n                                setTimeout(()=>{\n                                    if (!state.file) {\n                                        console.log('Re-setting file after category change:', currentFile.name);\n                                        setFile(currentFile);\n                                    }\n                                }, 50);\n                            } else {\n                                // No suggested category, just show the error\n                                (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Video Duration Error', validationResult.error || 'The video duration is not valid for this category.');\n                            }\n                        } else if (validationResult.suggestedCategory && validationResult.suggestedCategory !== mediaSubtype) {\n                            // Video is valid for current category but there's a better category\n                            // For this case, we still give the user a choice since the video is valid for the current category\n                            // Use our custom confirm dialog instead of window.confirm\n                            const confirmSwitch = await (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showAlert)({\n                                title: 'Category Suggestion',\n                                message: \"\".concat(validationResult.error, \"\\n\\nWould you like to switch to the suggested category?\"),\n                                type: 'warning',\n                                confirmText: 'Yes, Switch Category',\n                                cancelText: 'No, Keep Current',\n                                onConfirm: ()=>{}\n                            });\n                            if (confirmSwitch) {\n                                // Switch to the suggested category\n                                console.log(\"Switching to suggested category: \".concat(validationResult.suggestedCategory));\n                                // Make sure we keep a reference to the file\n                                const currentFile = file;\n                                // Update the media subtype\n                                setMediaSubtype(validationResult.suggestedCategory);\n                                // Update the selected type to match the new category\n                                // Never suggest 'story' (moments) for other categories\n                                if (validationResult.suggestedCategory === 'flash') {\n                                    setSelectedType('flashes');\n                                } else if (validationResult.suggestedCategory === 'glimpse') {\n                                    setSelectedType('glimpses');\n                                } else if (validationResult.suggestedCategory === 'movie') {\n                                    setSelectedType('movies');\n                                }\n                                // Removed the 'story' suggestion for short videos\n                                // Make sure the file is still set in the state\n                                setTimeout(()=>{\n                                    if (!state.file) {\n                                        console.log('Re-setting file after category change:', currentFile.name);\n                                        setFile(currentFile);\n                                    }\n                                }, 50);\n                            }\n                        }\n                    }\n                    // Always go to thumbnail selection for videos\n                    console.log('Moving to thumbnailSelection phase');\n                    // Keep a reference to the current file\n                    const currentFile = file;\n                    // Double-check that the file is set in the state before proceeding\n                    if (state.file) {\n                        console.log('File confirmed in state before phase change:', state.file.name);\n                        setPhase('thumbnailSelection');\n                    } else {\n                        console.log('File not found in state before phase change, setting it again');\n                        // Try setting the file again\n                        setFile(currentFile);\n                        // Add a small delay to ensure the state is updated\n                        setTimeout(()=>{\n                            // Double-check again\n                            if (!state.file) {\n                                console.log('File still not in state, setting it one more time');\n                                setFile(currentFile);\n                            }\n                            console.log('Delayed phase change to thumbnailSelection');\n                            setPhase('thumbnailSelection');\n                        }, 100);\n                    }\n                } catch (error) {\n                    console.error('Error calculating video duration:', error);\n                    // For moments videos, we need to enforce the duration check\n                    // If we can't calculate duration, we can't validate it, so we should reject the upload\n                    if (selectedType === 'moments') {\n                        (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Video Error', 'Unable to determine video duration. Please try a different video file.');\n                        resetUpload();\n                        return;\n                    }\n                    console.log('Moving to thumbnailSelection phase despite error');\n                    // Keep a reference to the current file\n                    const currentFile = file;\n                    // Double-check that the file is set in the state before proceeding\n                    if (state.file) {\n                        console.log('File confirmed in state before phase change (error case):', state.file.name);\n                        setPhase('thumbnailSelection');\n                    } else {\n                        console.log('File not found in state before phase change (error case), setting it again');\n                        // Try setting the file again\n                        setFile(currentFile);\n                        // Add a small delay to ensure the state is updated\n                        setTimeout(()=>{\n                            // Double-check again\n                            if (!state.file) {\n                                console.log('File still not in state (error case), setting it one more time');\n                                setFile(currentFile);\n                            }\n                            console.log('Delayed phase change to thumbnailSelection (error case)');\n                            setPhase('thumbnailSelection');\n                        }, 100);\n                    }\n                }\n            } else {\n                // For photos or moments images\n                if (selectedType === 'moments') {\n                    // For moments, we need to set the media type based on the file type\n                    if (file.type.startsWith('image/')) {\n                        console.log('Moments image detected');\n                        setMediaType('photo');\n                        // For moments images, we always use 'story' as the media subtype\n                        setMediaSubtype('story');\n                        // Create a local reference to the file for use in the timeout\n                        const currentFile = file;\n                        // Double-check that the file is set in the state before proceeding\n                        setTimeout(()=>{\n                            // Check if the file is in the state\n                            if (!state.file) {\n                                console.log('Moments photo not found in state after setting, trying again');\n                                // Try setting the file again\n                                setFile(currentFile);\n                            } else {\n                                console.log('Moments photo confirmed in state:', state.file.name);\n                            }\n                        }, 50);\n                    } else {\n                        console.log('Invalid file type for moments. Only images and videos less than 1 minute are allowed.');\n                        (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Invalid File Type', 'Invalid file type for moments. Only images and videos less than 1 minute are allowed.');\n                        // Reset the upload context but preserve the selected type and category\n                        const currentType = selectedType;\n                        const currentCategory = selectedCategory;\n                        // First set the phase back to category selection\n                        setPhase('categorySelection');\n                        // Then reset the upload state\n                        setTimeout(()=>{\n                            resetUpload();\n                            setSelectedType(currentType);\n                            setSelectedCategory(currentCategory);\n                            console.log('Reset upload state after invalid file type for moments');\n                        }, 100);\n                        return;\n                    }\n                }\n                // Handle image preview and set phase\n                const reader = new FileReader();\n                reader.onload = (e)=>{\n                    var _e_target;\n                    if ((_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result) {\n                        setPreviewImage(e.target.result);\n                        console.log('Preview image set for file:', file.name);\n                    }\n                };\n                reader.readAsDataURL(file);\n                // Create a local reference to the file for use in the timeout\n                const currentFile = file;\n                // Double-check that the file is set in the state before proceeding\n                setTimeout(()=>{\n                    // Check if the file is in the state\n                    if (!state.file) {\n                        console.log('File not found in state before moving to personalDetails, setting it again');\n                        // Try setting the file again\n                        setFile(currentFile);\n                        // Add another timeout to ensure the file is set\n                        setTimeout(()=>{\n                            if (!state.file) {\n                                console.log('File still not in state, setting it one more time');\n                                setFile(currentFile);\n                            } else {\n                                console.log('File confirmed in state after second attempt:', state.file.name);\n                            }\n                            // New flow logic: For moments (stories), skip personal details and go directly to face verification\n                            if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                                console.log('Moments image upload: skipping personal details, going directly to face verification');\n                                setPhase('faceVerification');\n                            } else {\n                                console.log('Moving to personalDetails phase for photo/image');\n                                setPhase('personalDetails');\n                            }\n                        }, 100);\n                    } else {\n                        console.log('File confirmed in state:', state.file.name);\n                        // New flow logic: For moments (stories), skip personal details and go directly to face verification\n                        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                            console.log('Moments image upload: skipping personal details, going directly to face verification');\n                            setPhase('faceVerification');\n                        } else {\n                            console.log('Moving to personalDetails phase for photo/image');\n                            setPhase('personalDetails');\n                        }\n                    }\n                }, 100);\n            }\n        };\n        // Trigger the file dialog\n        input.click();\n    };\n    // Handle personal details completed\n    const handlePersonalDetailsCompleted = (details)=>{\n        console.log('Personal details completed:', details);\n        // Store the personal details in local state for component persistence\n        setLocalPersonalDetails(details);\n        // Validate that we have a title\n        if (!details.caption || !details.caption.trim()) {\n            console.error('Caption/title is empty, this should not happen');\n            // Go back to personal details to fix this\n            setPhase('personalDetails');\n            return;\n        }\n        // Set the title in the upload context\n        setTitle(details.caption.trim());\n        // Also store in global context for persistence (this is the upload context function)\n        setPersonalDetails(details);\n        // Check if we have a file in the state\n        if (!state.file) {\n            console.error('No file found in state after personal details');\n            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Upload Error', 'Something went wrong with your file upload. Please try again.');\n            setPhase('typeSelection');\n            return;\n        }\n        console.log('File confirmed in state after personal details:', state.file.name);\n        console.log('Personal details set successfully');\n        console.log('Title set to:', details.caption.trim());\n        console.log('Current selectedType:', selectedType);\n        console.log('Current mediaSubtype:', state.mediaSubtype);\n        // New flow logic based on backend requirements:\n        // - Moments (stories): Skip personal details, go directly to face verification\n        // - Photos: Go to face verification after personal details (no vendor details)\n        // - Videos: Go to vendor details after personal details\n        if (state.mediaType === 'photo') {\n            console.log('Photo upload: proceeding to face verification (no vendor details needed)');\n            setPhase('faceVerification');\n        } else if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n            console.log('Moments upload: proceeding to face verification (no vendor details needed)');\n            setPhase('faceVerification');\n        } else {\n            // For videos (flashes, glimpses, movies), proceed to vendor details\n            console.log('Video upload: proceeding to vendor details');\n            setPhase('vendorDetails');\n        }\n    };\n    // Handle vendor details completed\n    const handleVendorDetailsCompleted = (vendorDetails)=>{\n        // console.log('Vendor details completed:', vendorDetails);\n        // Normalize vendor details to ensure consistent field names\n        const normalizedVendorDetails = {\n            ...vendorDetails\n        };\n        // Ensure we have both frontend and backend field names for makeup artist and decorations\n        if (vendorDetails.makeupArtist) {\n            normalizedVendorDetails.makeup_artist = vendorDetails.makeupArtist;\n        } else if (vendorDetails.makeup_artist) {\n            normalizedVendorDetails.makeupArtist = vendorDetails.makeup_artist;\n        }\n        if (vendorDetails.decorations) {\n            normalizedVendorDetails.decoration = vendorDetails.decorations;\n        } else if (vendorDetails.decoration) {\n            normalizedVendorDetails.decorations = vendorDetails.decoration;\n        }\n        // Store the normalized vendor details for persistence between screens\n        setVendorDetailsData(normalizedVendorDetails);\n        // Also store in the ref for Edge browser compatibility\n        vendorDetailsRef.current = normalizedVendorDetails;\n        // Store vendor details in localStorage for persistence\n        try {\n            localStorage.setItem('wedzat_vendor_details', JSON.stringify(normalizedVendorDetails));\n            console.log('UPLOAD MANAGER - Stored vendor details in localStorage');\n        } catch (error) {\n            console.error('UPLOAD MANAGER - Failed to store vendor details in localStorage:', error);\n        }\n        // Save the current video_category before setting vendor details\n        const currentVideoCategory = state.detailFields.video_category;\n        console.log('UPLOAD MANAGER - Saving video_category before vendor details:', currentVideoCategory);\n        // Store video_category in localStorage\n        if (currentVideoCategory) {\n            try {\n                localStorage.setItem('wedzat_video_category', currentVideoCategory);\n                console.log('UPLOAD MANAGER - Stored video_category in localStorage:', currentVideoCategory);\n            } catch (error) {\n                console.error('UPLOAD MANAGER - Failed to store video_category in localStorage:', error);\n            }\n        }\n        // Store in global context for persistence\n        setVendorDetails(normalizedVendorDetails);\n        // Explicitly set each vendor detail field\n        Object.entries(normalizedVendorDetails).forEach((param)=>{\n            let [vendorType, details] = param;\n            if (details && details.name && details.mobileNumber) {\n                setDetailField(\"vendor_\".concat(vendorType, \"_name\"), details.name);\n                setDetailField(\"vendor_\".concat(vendorType, \"_contact\"), details.mobileNumber);\n            }\n        });\n        // Re-set the video_category after vendor details to ensure it's preserved\n        if (currentVideoCategory) {\n            console.log('UPLOAD MANAGER - Re-setting video_category after vendor details:', currentVideoCategory);\n            setTimeout(()=>{\n                setDetailField('video_category', currentVideoCategory);\n            }, 100);\n        }\n        // Log all detail fields after setting vendor details\n        setTimeout(()=>{\n            console.log('All detail fields after vendor details:', state.detailFields);\n            console.log('Detail fields count:', Object.keys(state.detailFields).length);\n            console.log('Normalized vendor details:', normalizedVendorDetails);\n        }, 200);\n        // Add a small delay to ensure the state is updated before proceeding\n        // This helps with cross-browser compatibility, especially in Edge\n        setTimeout(()=>{\n            // Double-check that we have at least 4 vendor details before proceeding\n            const vendorNameFields = Object.keys(state.detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_name'));\n            const vendorContactFields = Object.keys(state.detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_contact'));\n            console.log('UPLOAD MANAGER - Vendor name fields:', vendorNameFields.length);\n            console.log('UPLOAD MANAGER - Vendor contact fields:', vendorContactFields.length);\n            // Edge browser workaround - directly set vendor details in the state\n            if ( true && /Edge|Edg/.test(window.navigator.userAgent)) {\n                console.log('UPLOAD MANAGER - Edge browser detected, applying direct vendor details workaround');\n                // Create vendor detail fields directly in the state\n                // This is a workaround for Edge browser where the state update doesn't properly preserve vendor details\n                Object.entries(normalizedVendorDetails).forEach((param)=>{\n                    let [vendorType, details] = param;\n                    if (details && details.name && details.mobileNumber) {\n                        // Set the vendor details directly in the state\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                        // Also set the normalized version\n                        const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                        if (normalizedType !== vendorType) {\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                        }\n                        console.log(\"UPLOAD MANAGER - Edge workaround: Added vendor \".concat(vendorType, \" directly to state\"));\n                    }\n                });\n                // Re-set the video_category directly\n                if (currentVideoCategory) {\n                    state.detailFields.video_category = currentVideoCategory;\n                    console.log('UPLOAD MANAGER - Edge workaround: Re-set video_category directly:', currentVideoCategory);\n                }\n            }\n            // Proceed to face verification\n            setPhase('faceVerification');\n        }, 300);\n    };\n    // Handle thumbnail selection\n    const handleThumbnailSelected = (thumbnailFile)=>{\n        if (thumbnailFile) {\n            // Set the thumbnail in the context\n            setThumbnail(thumbnailFile);\n            console.log('Thumbnail selected:', thumbnailFile.name);\n        } else {\n            console.log('No thumbnail selected, using auto-generated thumbnail');\n        }\n        // New flow logic: For moments (stories), skip personal details and go directly to face verification\n        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n            console.log('Moments upload: skipping personal details, going directly to face verification');\n            setPhase('faceVerification');\n        } else {\n            // For photos and videos, go to personal details\n            console.log('Photo/Video upload: proceeding to personal details');\n            setPhase('personalDetails');\n        }\n    };\n    // Function to proceed with upload after vendor details are applied\n    const proceedWithUpload = (videoCategory)=>{\n        // Double-check that we have a title before changing to uploading phase\n        if (!state.title || !state.title.trim()) {\n            console.error('Title is missing before upload, setting it from personal details');\n            // Try to set the title from personal details\n            if (personalDetails.caption && personalDetails.caption.trim()) {\n                // console.log('Setting personal details from local state:', personalDetails);\n                // Use the global context function to set all personal details at once\n                setPersonalDetails(personalDetails);\n                // Explicitly set the title as well\n                setTitle(personalDetails.caption.trim());\n            } else {\n                console.error('No title in personal details either, going back to personal details');\n                setPhase('personalDetails');\n                return;\n            }\n        }\n        // Edge browser workaround - directly set vendor details in the state\n        if ( true && /Edge|Edg/.test(window.navigator.userAgent)) {\n            console.log('UPLOAD MANAGER - Edge browser detected in face verification, applying vendor details workaround');\n            // Get the vendor details from the vendor details data\n            const vendorDetailsData = vendorDetailsRef.current;\n            if (vendorDetailsData) {\n                console.log('UPLOAD MANAGER - Edge workaround: Using vendor details from ref:', vendorDetailsData);\n                // Create vendor detail fields directly in the state\n                Object.entries(vendorDetailsData).forEach((param)=>{\n                    let [vendorType, details] = param;\n                    if (details && details.name && details.mobileNumber) {\n                        // Set the vendor details directly in the state\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                        // Also set the normalized version\n                        const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                        if (normalizedType !== vendorType) {\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                        }\n                        console.log(\"UPLOAD MANAGER - Edge workaround: Added vendor \".concat(vendorType, \" directly to state\"));\n                    }\n                });\n            }\n        }\n        // For videos, check if we have a video_category\n        if (state.mediaType === 'video') {\n            console.log(\"UPLOAD MANAGER - Checking video_category before upload\");\n            console.log(\"UPLOAD MANAGER - Current video_category: \".concat(state.detailFields.video_category || 'Not set'));\n            console.log(\"UPLOAD MANAGER - Current mediaSubtype: \".concat(state.mediaSubtype));\n            console.log(\"UPLOAD MANAGER - Selected category: \".concat(selectedCategory || 'Not set'));\n            // Special handling for glimpses\n            if (state.mediaSubtype === 'glimpse') {\n                console.log(\"UPLOAD MANAGER - Special handling for glimpses\");\n                // If we don't have a video_category yet, try to set it from selectedCategory\n                if (!state.detailFields.video_category && selectedCategory) {\n                    // Map the UI category to the backend video_category\n                    let videoCategory = '';\n                    if (selectedCategory === 'my_wedding_videos') {\n                        videoCategory = 'my_wedding';\n                    } else if (selectedCategory === 'wedding_vlog') {\n                        videoCategory = 'wedding_vlog';\n                    } else if (selectedCategory === 'friends_family_videos') {\n                        videoCategory = 'friends_family_video';\n                    }\n                    if (videoCategory) {\n                        console.log(\"UPLOAD MANAGER - Setting video_category for glimpse: \".concat(videoCategory));\n                        setDetailField('video_category', videoCategory);\n                    }\n                } else {\n                    console.log(\"UPLOAD MANAGER - Glimpse already has video_category: \".concat(state.detailFields.video_category));\n                }\n            }\n            // If we still don't have a video_category, use a default based on selectedCategory\n            if (!state.detailFields.video_category && selectedCategory) {\n                console.log(\"UPLOAD MANAGER - No video_category set, using selectedCategory: \".concat(selectedCategory));\n                // Map the UI category to the backend video_category\n                let videoCategory = '';\n                if (selectedCategory === 'my_wedding_videos') {\n                    videoCategory = 'my_wedding';\n                } else if (selectedCategory === 'wedding_influencer') {\n                    videoCategory = 'wedding_influencer';\n                } else if (selectedCategory === 'friends_family_videos') {\n                    videoCategory = 'friends_family_video';\n                }\n                if (videoCategory) {\n                    console.log(\"UPLOAD MANAGER - Setting video_category from selectedCategory: \".concat(videoCategory));\n                    setDetailField('video_category', videoCategory);\n                }\n            }\n            // Final check - if we still don't have a video_category, use a default\n            if (!state.detailFields.video_category) {\n                console.log('No video_category found, using a default one');\n                // Use 'my_wedding' as a default category instead of asking the user again\n                setDetailField('video_category', 'my_wedding');\n                console.log('Set default video_category to my_wedding');\n            }\n        }\n        // Edge browser workaround - directly set vendor details in the state before upload\n        if ( true && /Edge|Edg/.test(window.navigator.userAgent)) {\n            console.log('UPLOAD MANAGER - Edge browser detected before upload, applying vendor details workaround');\n            // Get the vendor details from the vendor details data\n            const vendorDetailsData = vendorDetailsRef.current;\n            if (vendorDetailsData && Object.keys(vendorDetailsData).length > 0) {\n                console.log('UPLOAD MANAGER - Edge workaround: Using vendor details from ref before upload:', vendorDetailsData);\n                // Create vendor detail fields directly in the state\n                Object.entries(vendorDetailsData).forEach((param)=>{\n                    let [vendorType, details] = param;\n                    if (details && details.name && details.mobileNumber) {\n                        // Set the vendor details directly in the state\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                        // Also set the normalized version\n                        const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                        if (normalizedType !== vendorType) {\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                        }\n                        console.log(\"UPLOAD MANAGER - Edge workaround: Added vendor \".concat(vendorType, \" directly to state before upload\"));\n                    }\n                });\n            }\n        }\n        // Check if we have a file before proceeding\n        if (!state.file) {\n            console.error('No file found in state before upload');\n            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Upload Error', 'No file selected. Please select a file to upload.');\n            // Go back to type selection to start over\n            setPhase('typeSelection');\n            return;\n        }\n        // Now we can proceed to uploading phase\n        setPhase('uploading');\n        // Log the current state before starting upload\n        console.log('Current state before upload:', {\n            file: state.file ? state.file.name : 'No file',\n            mediaType: state.mediaType,\n            mediaSubtype: state.mediaSubtype,\n            title: state.title,\n            description: state.description,\n            detailFields: state.detailFields,\n            detailFieldsCount: Object.keys(state.detailFields).length\n        });\n        // Double-check that we're using the correct category\n        console.log(\"UPLOAD MANAGER - Final check - Selected type: \".concat(selectedType));\n        console.log(\"UPLOAD MANAGER - Final check - MediaSubtype in state: \".concat(state.mediaSubtype));\n        // If the mediaSubtype doesn't match what we expect based on the selected type, fix it\n        if (selectedType && state.mediaSubtype !== getMediaSubtypeFromSelectedType(selectedType)) {\n            console.log(\"UPLOAD MANAGER - WARNING: MediaSubtype mismatch detected!\");\n            console.log(\"UPLOAD MANAGER - Expected mediaSubtype based on selected type: \".concat(getMediaSubtypeFromSelectedType(selectedType)));\n            console.log(\"UPLOAD MANAGER - Actual mediaSubtype in state: \".concat(state.mediaSubtype));\n            console.log(\"UPLOAD MANAGER - Correcting category before upload...\");\n            // Get the corrected category\n            const correctedCategory = getMediaSubtypeFromSelectedType(selectedType);\n            console.log(\"UPLOAD MANAGER - Category corrected to: \".concat(correctedCategory));\n            // Get the video_category from the original selection\n            // We need to map it to the correct backend value\n            let videoCategory = '';\n            if (selectedCategory === 'my_wedding_videos') {\n                videoCategory = 'my_wedding';\n            } else if (selectedCategory === 'wedding_influencer') {\n                videoCategory = 'wedding_influencer';\n            } else if (selectedCategory === 'friends_family_videos') {\n                videoCategory = 'friends_family_video';\n            }\n            console.log(\"UPLOAD MANAGER - Original selected category: \".concat(selectedCategory));\n            console.log(\"UPLOAD MANAGER - Mapped to backend video_category: \".concat(videoCategory));\n            // Start the upload process with the corrected category and video_category\n            startUploadWithCategory(correctedCategory, videoCategory);\n        } else {\n            // Get the video_category from the state\n            const finalVideoCategory = videoCategory || state.detailFields.video_category || 'my_wedding';\n            console.log(\"UPLOAD MANAGER - Using video_category for upload: \".concat(finalVideoCategory));\n            // Start the upload process with the current category and video_category\n            startUploadWithCategory(state.mediaSubtype, finalVideoCategory).then(()=>{\n                // Upload completed successfully\n                console.log('Upload completed successfully');\n            }).catch((error)=>{\n                console.error('Upload failed:', error);\n            });\n        }\n    };\n    // Handle face verification completed and start upload\n    const handleFaceVerificationCompleted = ()=>{\n        console.log('Face verification completed, starting upload process');\n        // Check if we have a file in the state\n        if (!state.file) {\n            console.error('No file found in state after face verification');\n            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Upload Error', 'Something went wrong with your file upload. Please try again.');\n            setPhase('typeSelection');\n            return;\n        }\n        console.log('File confirmed in state after face verification:', state.file.name);\n        // Try to get vendor details from localStorage first\n        let vendorDetailsData = vendorDetailsRef.current;\n        // If not in ref, try localStorage\n        if (!vendorDetailsData || Object.keys(vendorDetailsData).length === 0) {\n            try {\n                const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n                if (storedVendorDetails) {\n                    vendorDetailsData = JSON.parse(storedVendorDetails);\n                    console.log('UPLOAD MANAGER - Retrieved vendor details from localStorage:', storedVendorDetails);\n                    // Update the ref with the localStorage data\n                    vendorDetailsRef.current = vendorDetailsData;\n                    // Log the vendor details we found\n                    console.log(\"UPLOAD MANAGER - Found \".concat(Object.keys(vendorDetailsData).length, \" vendor details in localStorage\"));\n                    Object.entries(vendorDetailsData).forEach((param)=>{\n                        let [vendorType, details] = param;\n                        if (details && details.name && details.mobileNumber) {\n                            console.log(\"UPLOAD MANAGER - Vendor \".concat(vendorType, \": \").concat(details.name, \" (\").concat(details.mobileNumber, \")\"));\n                        }\n                    });\n                } else {\n                    console.log('UPLOAD MANAGER - No vendor details found in localStorage');\n                }\n            } catch (error) {\n                console.error('UPLOAD MANAGER - Failed to retrieve vendor details from localStorage:', error);\n            }\n        } else {\n            console.log(\"UPLOAD MANAGER - Using \".concat(Object.keys(vendorDetailsData).length, \" vendor details from ref\"));\n        }\n        // Try to get video_category from localStorage\n        let videoCategory = state.detailFields.video_category;\n        if (!videoCategory) {\n            try {\n                const storedVideoCategory = localStorage.getItem('wedzat_video_category');\n                if (storedVideoCategory) {\n                    videoCategory = storedVideoCategory;\n                    console.log('UPLOAD MANAGER - Retrieved video_category from localStorage:', videoCategory);\n                    // Set it in the state\n                    setDetailField('video_category', videoCategory);\n                }\n            } catch (error) {\n                console.error('UPLOAD MANAGER - Failed to retrieve video_category from localStorage:', error);\n            }\n        }\n        // Ensure vendor details are present\n        if (vendorDetailsData) {\n            console.log('UPLOAD MANAGER - Applying vendor details to state');\n            // Create a batch of all detail fields to update at once\n            const detailFieldUpdates = {};\n            let completeVendorCount = 0;\n            // Re-apply vendor details to ensure they're in the state\n            Object.entries(vendorDetailsData).forEach((param)=>{\n                let [vendorType, details] = param;\n                if (details && details.name && details.mobileNumber) {\n                    // Add to the batch\n                    detailFieldUpdates[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                    detailFieldUpdates[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                    completeVendorCount++;\n                    // Also set normalized versions\n                    const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                    if (normalizedType !== vendorType) {\n                        detailFieldUpdates[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                        detailFieldUpdates[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                    }\n                }\n            });\n            // Apply all updates at once\n            console.log(\"UPLOAD MANAGER - Applying \".concat(completeVendorCount, \" complete vendor details to state\"));\n            console.log('UPLOAD MANAGER - Detail field updates:', JSON.stringify(detailFieldUpdates));\n            // Apply each update individually to ensure they're all set\n            Object.entries(detailFieldUpdates).forEach((param)=>{\n                let [field, value] = param;\n                setDetailField(field, value);\n            });\n            // Add a delay before proceeding to ensure state updates are applied\n            setTimeout(()=>{\n                console.log('UPLOAD MANAGER - Vendor details applied to state, proceeding with upload');\n                proceedWithUpload(videoCategory);\n            }, 500);\n        } else {\n            console.log('UPLOAD MANAGER - No vendor details found, proceeding with upload');\n            proceedWithUpload(videoCategory);\n        }\n    // This code has been moved to the proceedWithUpload function\n    };\n    // Handle going back to personal details from upload error\n    const handleBackToPersonalDetails = ()=>{\n        // console.log('Going back to personal details with stored data:', personalDetails);\n        // Make sure the personal details are set in the context\n        if (personalDetails.caption && personalDetails.caption.trim()) {\n            // Use the global context function to set all personal details at once\n            setPersonalDetails(personalDetails);\n        }\n        setPhase('personalDetails');\n    };\n    // Handle close modal\n    const handleClose = ()=>{\n        // Check if upload was successful and call onUploadComplete\n        if (state.step === 'complete' && onUploadComplete) {\n            console.log('Upload completed successfully, calling onUploadComplete callback');\n            onUploadComplete();\n        }\n        // Reset the phase first\n        setPhase('closed');\n        // Call the onClose callback if provided\n        if (onClose) {\n            onClose();\n        }\n        // Reset the upload state after a short delay to ensure the modal is closed first\n        setTimeout(()=>{\n            resetUpload();\n            console.log('Upload state reset after modal close');\n        }, 100);\n    };\n    // Render selected phase component\n    if (phase === 'closed') {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            phase === 'typeSelection' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UploadTypeSelection__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                onNext: handleTypeSelected,\n                onClose: handleClose\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1342,\n                columnNumber: 9\n            }, undefined),\n            phase === 'categorySelection' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VideoCategorySelection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onNext: handleCategorySelected,\n                onBack: ()=>setPhase('typeSelection'),\n                onUpload: handleCategorySelected,\n                onThumbnailUpload: handleThumbnailUpload,\n                onClose: handleClose,\n                mediaType: state.mediaType,\n                selectedType: selectedType\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1349,\n                columnNumber: 9\n            }, undefined),\n            phase === 'thumbnailSelection' && state.file && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ThumbnailSelection__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                videoFile: state.file,\n                onNext: handleThumbnailSelected,\n                onBack: ()=>{\n                    // Go back to category selection instead of triggering file upload again\n                    if ([\n                        'flashes',\n                        'glimpses',\n                        'movies'\n                    ].includes(selectedType)) {\n                        setPhase('categorySelection');\n                    } else {\n                        // For moments, go back to type selection\n                        setPhase('typeSelection');\n                    }\n                },\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1361,\n                columnNumber: 9\n            }, undefined),\n            phase === 'personalDetails' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PersonalDetails__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onNext: handlePersonalDetailsCompleted,\n                onBack: ()=>{\n                    // Go back to thumbnail selection for videos\n                    if (state.mediaType === 'video' && state.file) {\n                        setPhase('thumbnailSelection');\n                    } else {\n                        // For photos, go back to type selection\n                        setPhase('typeSelection');\n                    }\n                },\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                },\n                previewImage: previewImage,\n                videoFile: state.mediaType === 'video' ? state.file : null,\n                mediaType: state.mediaType,\n                contentType: getContentType(),\n                initialDetails: personalDetails\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1382,\n                columnNumber: 9\n            }, undefined),\n            phase === 'vendorDetails' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VendorDetails__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                onNext: handleVendorDetailsCompleted,\n                onBack: ()=>setPhase('personalDetails'),\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                },\n                initialVendorDetails: vendorDetailsData,\n                videoCategory: state.detailFields.video_category || 'my_wedding'\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1407,\n                columnNumber: 9\n            }, undefined),\n            phase === 'faceVerification' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FaceVerification__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                onUpload: handleFaceVerificationCompleted,\n                onBack: ()=>{\n                    // New flow logic for back navigation:\n                    // - Moments: Go back to thumbnail selection (or type selection for images)\n                    // - Photos: Go back to personal details\n                    // - Videos: Go back to vendor details\n                    if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                        // For moments, go back to thumbnail selection for videos, or type selection for images\n                        if (state.mediaType === 'video') {\n                            setPhase('thumbnailSelection');\n                        } else {\n                            setPhase('typeSelection');\n                        }\n                    } else if (state.mediaType === 'photo') {\n                        setPhase('personalDetails');\n                    } else {\n                        setPhase('vendorDetails');\n                    }\n                },\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1421,\n                columnNumber: 9\n            }, undefined),\n            (phase === 'uploading' || phase === 'complete') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UploadProgress__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                },\n                onGoBack: handleBackToPersonalDetails\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1450,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(UploadManager, \"LQsMODnAAL1BJd6LrYNCqh7ZCEM=\", false, function() {\n    return [\n        _contexts_UploadContexts__WEBPACK_IMPORTED_MODULE_9__.useUpload,\n        _hooks_useMedia__WEBPACK_IMPORTED_MODULE_12__.useMediaUpload\n    ];\n});\n_c = UploadManager;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UploadManager);\nvar _c;\n$RefreshReg$(_c, \"UploadManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/upload/UploadManager.tsx\n"));

/***/ })

});