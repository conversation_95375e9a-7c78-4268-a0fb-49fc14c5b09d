import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  // Await params before accessing properties (Next.js 13+ requirement)
  const resolvedParams = await context.params;
  const photoId = resolvedParams.id;

  try {
    // Get the authorization header (same pattern as working like/unlike routes)
    const authHeader = request.headers.get('authorization');

    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization header is required' },
        { status: 401 }
      );
    }

    console.log(`[PHOTO API] Fetching photo details for ID: ${photoId}`);
    console.log('[PHOTO API] Authorization header received:', authHeader.substring(0, 50) + '...');
    console.log('[PHOTO API] Full URL:', `https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/photo/${photoId}`);

    // Call the updated backend endpoint (same pattern as like/unlike routes)
    const response = await axios.get(
      `https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/photo/${photoId}`,
      {
        headers: {
          'Authorization': authHeader,  // Pass the full header directly
          'Content-Type': 'application/json',
        },
        timeout: 15000,
      }
    );

    console.log('Photo API response received:', response.status);

    // Check if the backend returned an error response
    if (response.data.statusCode && response.data.statusCode !== 200) {
      const errorBody = typeof response.data.body === 'string'
        ? JSON.parse(response.data.body)
        : response.data.body;

      return NextResponse.json(
        { error: errorBody.error || 'Backend error' },
        { status: response.data.statusCode }
      );
    }

    return NextResponse.json(response.data);
  } catch (error: any) {
    console.error('Error fetching photo:', error);

    // Detailed error response
    let errorMessage = 'Failed to fetch photo details';
    let statusCode = 500;

    if (error.response) {
      console.error('Response error data:', error.response.data);
      statusCode = error.response.status;
      errorMessage = `Server error: ${error.response.data?.error || error.response.data?.message || error.message}`;
    } else if (error.request) {
      console.error('Request error:', error.request);
      errorMessage = 'No response received from server';
    } else {
      console.error('Error message:', error.message);
      errorMessage = error.message;
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}


