import os
import psycopg2
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database connection parameters
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_PORT = os.getenv('DB_PORT')
DB_NAME = os.getenv('DB_NAME')

def get_db_connection():
    return psycopg2.connect(
        host=DB_HOST,
        user=DB_USER,
        password=DB_PASSWORD,
        port=DB_PORT,
        dbname=DB_NAME
    )

def create_wedding_tables():
    """Create all tables needed for the wedding planning tool"""
    conn = None
    try:
        # Connect to the database
        conn = get_db_connection()
        cursor = conn.cursor()

        print("Starting to create wedding tables...")

        # Wedding Checklist Table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS wedding_checklist (
            item_id UUID PRIMARY KEY,
            user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
            task TEXT NOT NULL,
            category VARCHAR(100),
            due_date DATE,
            status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'in_progress')),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        ''')

        # Wedding Vendors Table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS wedding_vendors (
            vendor_id UUID PRIMARY KEY,
            user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
            name VARCHAR(255) NOT NULL,
            category VARCHAR(100) NOT NULL,
            contact VARCHAR(100),
            email VARCHAR(255),
            website VARCHAR(255),
            notes TEXT,
            price DECIMAL(10, 2),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        ''')

        # Wedding Budget Categories Table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS wedding_budget_categories (
            category_id UUID PRIMARY KEY,
            user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
            name VARCHAR(100) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        ''')

        # Wedding Budget Expenses Table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS wedding_budget_expenses (
            expense_id UUID PRIMARY KEY,
            category_id UUID REFERENCES wedding_budget_categories(category_id) ON DELETE CASCADE,
            user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
            name VARCHAR(255) NOT NULL,
            estimated_budget DECIMAL(10, 2) NOT NULL,
            final_cost DECIMAL(10, 2) DEFAULT 0,
            amount_paid DECIMAL(10, 2) DEFAULT 0,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        ''')

        # Wedding Budget Payments Table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS wedding_budget_payments (
            payment_id UUID PRIMARY KEY,
            expense_id UUID REFERENCES wedding_budget_expenses(expense_id) ON DELETE CASCADE,
            user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
            amount DECIMAL(10, 2) NOT NULL,
            payment_date DATE NOT NULL,
            payment_method VARCHAR(100),
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        ''')

        # Wedding Guest Groups Table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS wedding_guest_groups (
            group_id UUID PRIMARY KEY,
            user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        ''')

        # Wedding Menu Options Table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS wedding_menu_options (
            menu_id UUID PRIMARY KEY,
            user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            is_default BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        ''')

        # Wedding Guests Table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS wedding_guests (
            guest_id UUID PRIMARY KEY,
            user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
            group_id UUID REFERENCES wedding_guest_groups(group_id) ON DELETE CASCADE,
            first_name VARCHAR(100) NOT NULL,
            last_name VARCHAR(100) NOT NULL,
            email VARCHAR(255),
            phone VARCHAR(50),
            menu_id UUID REFERENCES wedding_menu_options(menu_id) ON DELETE SET NULL,
            attendance_status VARCHAR(20) DEFAULT 'pending' CHECK (attendance_status IN ('attending', 'pending', 'declined')),
            invitation_sent BOOLEAN DEFAULT FALSE,
            invitation_sent_date TIMESTAMP,
            response_date TIMESTAMP,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        ''')


        # Website Templates Table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS website_templates (
            template_id UUID PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            thumbnail_url TEXT,
            description TEXT,
            default_colors JSONB DEFAULT '{}',
            default_fonts JSONB DEFAULT '{}',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        ''')

        # User Websites Table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS user_websites (
            website_id UUID PRIMARY KEY,
            user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
            title VARCHAR(255) NOT NULL,
            couple_names VARCHAR(255),
            template_id UUID REFERENCES website_templates(template_id),
            wedding_date DATE,
            wedding_location TEXT,
            about_couple TEXT,
            design_settings JSONB DEFAULT '{}',
            deployed_url TEXT,
            is_published BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        ''')

        # Check if default templates exist
        cursor.execute('SELECT COUNT(*) FROM website_templates')
        template_count = cursor.fetchone()[0]

        # Insert default templates if they don't exist
        if template_count == 0:
            import uuid
            import json

            # Insert some default templates
            templates = [
                {
                    'id': str(uuid.uuid4()),
                    'name': 'Classic Elegance',
                    'thumbnail_url': '',
                    'description': 'A timeless and elegant design for your special day.',
                    'default_colors': json.dumps({
                        'primary': '#B31B1E',
                        'secondary': '#333333',
                        'background': '#FFFFFF',
                        'text': '#000000'
                    }),
                    'default_fonts': json.dumps({
                        'heading': ['Playfair Display', 'Georgia', 'serif'],
                        'body': ['Roboto', 'Arial', 'sans-serif']
                    })
                },
                {
                    'id': str(uuid.uuid4()),
                    'name': 'Modern Minimalist',
                    'thumbnail_url': '',
                    'description': 'A clean, modern design with minimalist aesthetics.',
                    'default_colors': json.dumps({
                        'primary': '#000000',
                        'secondary': '#CCCCCC',
                        'background': '#FFFFFF',
                        'text': '#333333'
                    }),
                    'default_fonts': json.dumps({
                        'heading': ['Montserrat', 'Helvetica', 'sans-serif'],
                        'body': ['Open Sans', 'Arial', 'sans-serif']
                    })
                },
                {
                    'id': str(uuid.uuid4()),
                    'name': 'Rustic Charm',
                    'thumbnail_url': '',
                    'description': 'A warm, rustic design with natural elements.',
                    'default_colors': json.dumps({
                        'primary': '#8B4513',
                        'secondary': '#D2B48C',
                        'background': '#FFF8E7',
                        'text': '#3E2723'
                    }),
                    'default_fonts': json.dumps({
                        'heading': ['Amatic SC', 'cursive'],
                        'body': ['Lato', 'sans-serif']
                    })
                }
            ]

            for template in templates:
                cursor.execute(
                    '''INSERT INTO website_templates
                    (template_id, name, thumbnail_url, description, default_colors, default_fonts)
                    VALUES (%s, %s, %s, %s, %s, %s)''',
                    (template['id'], template['name'], template['thumbnail_url'],
                     template['description'], template['default_colors'], template['default_fonts'])
                )

            print("Default website templates inserted successfully")

        # Check if default menu options exist
        cursor.execute('SELECT COUNT(*) FROM wedding_menu_options')
        menu_count = cursor.fetchone()[0]

        # Insert default menu options if they don't exist
        if menu_count == 0:
            # Insert default menu options (Adult and Children)
            menu_options = [
                {
                    'id': str(uuid.uuid4()),
                    'name': 'Adult Menu',
                    'description': 'Standard adult meal options',
                    'is_default': True
                },
                {
                    'id': str(uuid.uuid4()),
                    'name': 'Children Menu',
                    'description': 'Kid-friendly meal options',
                    'is_default': True
                }
            ]

            for option in menu_options:
                cursor.execute(
                    '''INSERT INTO wedding_menu_options
                    (menu_id, user_id, name, description, is_default)
                    VALUES (%s, NULL, %s, %s, %s)''',
                    (option['id'], option['name'], option['description'], option['is_default'])
                )

            print("Default menu options inserted successfully")

        # Check if wedding_guest_groups table exists
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_name = 'wedding_guest_groups'
            );
        """)
        table_exists = cursor.fetchone()[0]

        if not table_exists:
            print("WARNING: wedding_guest_groups table does not exist. Recreating it...")
            # Recreate the Wedding Guest Groups Table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS wedding_guest_groups (
                group_id UUID PRIMARY KEY,
                user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            ''')
            print("wedding_guest_groups table created.")

        # Check if wedding_menu_options table exists
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_name = 'wedding_menu_options'
            );
        """)
        table_exists = cursor.fetchone()[0]

        if not table_exists:
            print("WARNING: wedding_menu_options table does not exist. Recreating it...")
            # Recreate the Wedding Menu Options Table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS wedding_menu_options (
                menu_id UUID PRIMARY KEY,
                user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                is_default BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            ''')
            print("wedding_menu_options table created.")

        # Check if wedding_guests table exists
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_name = 'wedding_guests'
            );
        """)
        table_exists = cursor.fetchone()[0]

        if not table_exists:
            print("WARNING: wedding_guests table does not exist. Recreating it...")
            # Recreate the Wedding Guests Table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS wedding_guests (
                guest_id UUID PRIMARY KEY,
                user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
                group_id UUID REFERENCES wedding_guest_groups(group_id) ON DELETE CASCADE,
                first_name VARCHAR(100) NOT NULL,
                last_name VARCHAR(100) NOT NULL,
                email VARCHAR(255),
                phone VARCHAR(50),
                menu_id UUID REFERENCES wedding_menu_options(menu_id) ON DELETE SET NULL,
                attendance_status VARCHAR(20) DEFAULT 'pending' CHECK (attendance_status IN ('attending', 'pending', 'declined')),
                invitation_sent BOOLEAN DEFAULT FALSE,
                invitation_sent_date TIMESTAMP,
                response_date TIMESTAMP,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            ''')
            print("wedding_guests table created.")

        # Now check if default guest groups exist
        cursor.execute('SELECT COUNT(*) FROM wedding_guest_groups WHERE user_id IS NULL')
        group_count = cursor.fetchone()[0]

        # Insert default guest groups if they don't exist
        if group_count == 0:
            print("Creating default guest group templates...")
            # Create default guest group templates with NULL user_id
            default_groups = [
                {
                    'id': str(uuid.uuid4()),
                    'name': 'Couple',
                    'description': 'The couple getting married'
                },
                {
                    'id': str(uuid.uuid4()),
                    'name': 'Partner 1 Family',
                    'description': 'Family members of Partner 1'
                },
                {
                    'id': str(uuid.uuid4()),
                    'name': 'Partner 2 Family',
                    'description': 'Family members of Partner 2'
                },
                {
                    'id': str(uuid.uuid4()),
                    'name': 'Partner 1 Friends',
                    'description': 'Friends of Partner 1'
                },
                {
                    'id': str(uuid.uuid4()),
                    'name': 'Partner 2 Friends',
                    'description': 'Friends of Partner 2'
                },
                {
                    'id': str(uuid.uuid4()),
                    'name': 'Mutual Friends',
                    'description': 'Friends shared by the couple'
                },
                {
                    'id': str(uuid.uuid4()),
                    'name': 'Partner 1 Colleagues',
                    'description': 'Work colleagues of Partner 1'
                },
                {
                    'id': str(uuid.uuid4()),
                    'name': 'Partner 2 Colleagues',
                    'description': 'Work colleagues of Partner 2'
                }
            ]

            for group in default_groups:
                cursor.execute(
                    '''INSERT INTO wedding_guest_groups
                    (group_id, user_id, name, description, created_at, updated_at)
                    VALUES (%s, NULL, %s, %s, NOW(), NOW())''',
                    (group['id'], group['name'], group['description'])
                )

            print("Default guest group templates created successfully")



        # Commit the changes
        conn.commit()
        print("Wedding planning tables created successfully!")
        return True
    except Exception as e:
        # Roll back any failed transactions
        if conn:
            conn.rollback()
        print(f"Database error: {e}")
        return False
    finally:
        # Close connection
        if conn:
            conn.close()

if __name__ == '__main__':
    # Create tables
    create_wedding_tables()

