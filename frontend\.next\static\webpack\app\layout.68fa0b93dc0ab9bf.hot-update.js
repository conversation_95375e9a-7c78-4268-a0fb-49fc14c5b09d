"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9eefcbdaf30e\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xca29tbWlcXE9uZURyaXZlXFxEZXNrdG9wXFxXRURaQVRfXFxmcm9udGVuZFxcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjllZWZjYmRhZjMwZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./contexts/UploadContexts.tsx":
/*!*************************************!*\
  !*** ./contexts/UploadContexts.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UploadProvider: () => (/* binding */ UploadProvider),\n/* harmony export */   useUpload: () => (/* binding */ useUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/api */ \"(app-pages-browser)/./services/api.ts\");\n/* harmony import */ var _utils_uploadUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/uploadUtils */ \"(app-pages-browser)/./utils/uploadUtils.ts\");\n// contexts/UploadContext.tsx\n/* __next_internal_client_entry_do_not_use__ UploadProvider,useUpload auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Initial state\nconst initialState = {\n    file: null,\n    thumbnail: null,\n    mediaType: 'photo',\n    mediaSubtype: 'story',\n    category: '',\n    title: '',\n    description: '',\n    tags: [],\n    detailFields: {},\n    step: 'selecting',\n    progress: 0,\n    isUploading: false\n};\n// Create context\nconst UploadContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Provider component\nconst UploadProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialState);\n    // Set file and automatically determine media type\n    const setFile = async (file)=>{\n        if (!file) {\n            setState({\n                ...state,\n                file: null\n            });\n            return;\n        }\n        const isVideo = file.type.startsWith('video/');\n        const mediaType = isVideo ? 'video' : 'photo';\n        // Default media subtypes based on media type\n        const mediaSubtype = isVideo ? 'glimpse' : 'post'; // Default to 'post' for photos\n        setState({\n            ...state,\n            file,\n            mediaType,\n            mediaSubtype,\n            step: 'details'\n        });\n    };\n    // Set thumbnail image\n    const setThumbnail = (thumbnail)=>{\n        setState({\n            ...state,\n            thumbnail\n        });\n    };\n    const setMediaType = (type)=>{\n        // Don't set a default category - let the user's selection flow through the process\n        // Just update the media type\n        setState({\n            ...state,\n            mediaType: type\n        });\n    };\n    const setMediaSubtype = (mediaSubtype)=>{\n        setState({\n            ...state,\n            mediaSubtype\n        });\n    };\n    // Keep the old function for backward compatibility\n    const setCategory = (category)=>{\n        console.log('UPLOAD CONTEXT - setCategory is deprecated, use setMediaSubtype instead');\n        setState({\n            ...state,\n            mediaSubtype: category\n        });\n    };\n    const setTitle = (title)=>{\n        // Ensure title is not empty\n        if (!title || !title.trim()) {\n            console.warn('Attempted to set empty title');\n            return;\n        }\n        console.log('Setting title to:', title.trim());\n        setState({\n            ...state,\n            title: title.trim()\n        });\n    };\n    const setDescription = (description)=>{\n        setState({\n            ...state,\n            description\n        });\n    };\n    const addTag = (tag)=>{\n        if (tag.trim() && !state.tags.includes(tag.trim())) {\n            setState({\n                ...state,\n                tags: [\n                    ...state.tags,\n                    tag.trim()\n                ]\n            });\n        }\n    };\n    const removeTag = (tag)=>{\n        setState({\n            ...state,\n            tags: state.tags.filter((t)=>t !== tag)\n        });\n    };\n    const setDetailField = (field, value)=>{\n        // For moments (stories), skip all localStorage operations and vendor field handling\n        if (state.mediaSubtype === 'story') {\n            console.log(\"UPLOAD CONTEXT - Moments detected, setting field \".concat(field, \" without localStorage operations\"));\n            setState((prevState)=>({\n                    ...prevState,\n                    detailFields: {\n                        ...prevState.detailFields,\n                        [field]: value\n                    }\n                }));\n            return;\n        }\n        // Special handling for video_category to ensure it's properly set\n        if (field === 'video_category') {\n            console.log(\"UPLOAD CONTEXT - Setting video_category to: \".concat(value));\n            // Store video_category in localStorage\n            try {\n                localStorage.setItem('wedzat_video_category', value);\n                console.log(\"UPLOAD CONTEXT - Stored video_category in localStorage: \".concat(value));\n            } catch (error) {\n                console.error('UPLOAD CONTEXT - Failed to store video_category in localStorage:', error);\n            }\n        }\n        // Special handling for vendor fields to ensure they're properly set\n        if (field.startsWith('vendor_')) {\n            // If this is a vendor field, update the vendor details in localStorage\n            try {\n                // Get existing vendor details from localStorage\n                const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n                let vendorDetails = storedVendorDetails ? JSON.parse(storedVendorDetails) : {};\n                // If this is a vendor name field, extract the vendor type and update the name\n                if (field.endsWith('_name')) {\n                    const vendorType = field.replace('vendor_', '').replace('_name', '');\n                    // Check if we already have this vendor in the details\n                    if (!vendorDetails[vendorType]) {\n                        vendorDetails[vendorType] = {\n                            name: value,\n                            mobileNumber: ''\n                        };\n                    } else {\n                        vendorDetails[vendorType].name = value;\n                    }\n                }\n                // If this is a vendor contact field, extract the vendor type and update the contact\n                if (field.endsWith('_contact')) {\n                    const vendorType = field.replace('vendor_', '').replace('_contact', '');\n                    // Check if we already have this vendor in the details\n                    if (!vendorDetails[vendorType]) {\n                        vendorDetails[vendorType] = {\n                            name: '',\n                            mobileNumber: value\n                        };\n                    } else {\n                        vendorDetails[vendorType].mobileNumber = value;\n                    }\n                }\n                // Store the updated vendor details in localStorage\n                localStorage.setItem('wedzat_vendor_details', JSON.stringify(vendorDetails));\n            } catch (error) {\n                console.error('UPLOAD CONTEXT - Failed to update vendor details in localStorage:', error);\n            }\n        }\n        // Create a new detailFields object with the updated field\n        const updatedDetailFields = {\n            ...state.detailFields,\n            [field]: value\n        };\n        // Update the state with the new detailFields\n        setState((prevState)=>({\n                ...prevState,\n                detailFields: updatedDetailFields\n            }));\n        // For video_category, log the updated state after a short delay\n        if (field === 'video_category') {\n            setTimeout(()=>{\n                console.log(\"UPLOAD CONTEXT - Verified video_category is set to: \".concat(state.detailFields.video_category || 'Not set'));\n            }, 100);\n        }\n    };\n    // Set all personal details at once and update the title and related detail fields\n    const setPersonalDetails = (details)=>{\n        // console.log('Setting all personal details:', details);\n        // Validate caption/title\n        if (!details.caption || !details.caption.trim()) {\n            console.warn('Attempted to set personal details with empty caption/title');\n            return;\n        }\n        // Update title\n        const title = details.caption.trim();\n        // console.log('Setting title from personal details:', title);\n        // Update detail fields with backend-compatible field names\n        const updatedDetailFields = {\n            ...state.detailFields,\n            'personal_caption': title,\n            'personal_life_partner': details.lifePartner || '',\n            'personal_wedding_style': details.weddingStyle || '',\n            'personal_place': details.place || '',\n            'personal_event_type': details.eventType || '',\n            'personal_budget': details.budget || '',\n            // Keep legacy field names for compatibility\n            'lifePartner': details.lifePartner || '',\n            'location': details.place || '',\n            'place': details.place || '',\n            'eventType': details.eventType || '',\n            'budget': details.budget || '',\n            'weddingStyle': details.weddingStyle || ''\n        };\n        // Update state with all changes at once\n        setState({\n            ...state,\n            title,\n            description: details.weddingStyle || '',\n            detailFields: updatedDetailFields\n        });\n        // Log the description being set\n        console.log('Setting description to:', details.weddingStyle || '');\n        // Log the updated state after a short delay to ensure state has updated\n        setTimeout(()=>{\n            console.log('Personal details set successfully');\n            console.log('Title after update:', title);\n        }, 0);\n    };\n    // Set all vendor details at once and update the related detail fields\n    const setVendorDetails = (vendorDetails)=>{\n        console.log('UPLOAD CONTEXT - Setting all vendor details:', JSON.stringify(vendorDetails, null, 2));\n        // Create a copy of the current detail fields\n        const updatedDetailFields = {\n            ...state.detailFields\n        };\n        // Save the video_category if it exists\n        const videoCategory = state.detailFields.video_category;\n        console.log('UPLOAD CONTEXT - Preserving video_category:', videoCategory);\n        // Count how many complete vendor details we're receiving\n        const completeVendorCount = Object.entries(vendorDetails).filter((param)=>{\n            let [_, detail] = param;\n            return detail && detail.name && detail.mobileNumber && detail.name.trim() !== '' && detail.mobileNumber.trim() !== '';\n        }).length;\n        console.log(\"UPLOAD CONTEXT - Received \".concat(completeVendorCount, \" complete vendor details\"));\n        // Process vendor details\n        Object.entries(vendorDetails).forEach((param)=>{\n            let [vendorType, details] = param;\n            if (details) {\n                // Only include vendors that have BOTH name AND mobile number\n                if (details.name && details.mobileNumber && details.name.trim() !== '' && details.mobileNumber.trim() !== '') {\n                    // Handle special mappings for makeup_artist and decoration\n                    let backendVendorType = vendorType;\n                    // Map frontend field names to backend field names\n                    if (vendorType === 'makeupArtist') {\n                        backendVendorType = 'makeup_artist';\n                    } else if (vendorType === 'decorations') {\n                        backendVendorType = 'decoration';\n                    }\n                    // Store vendor details in the format expected by the backend\n                    updatedDetailFields[\"vendor_\".concat(backendVendorType, \"_name\")] = details.name || '';\n                    updatedDetailFields[\"vendor_\".concat(backendVendorType, \"_contact\")] = details.mobileNumber || '';\n                    // Always store with the original vendorType to ensure we count it correctly\n                    // This ensures both frontend and backend field names are present\n                    // This is especially important for Edge browser compatibility\n                    if (vendorType !== backendVendorType) {\n                        updatedDetailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name || '';\n                        updatedDetailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber || '';\n                    }\n                    // Also store with common vendor types to ensure cross-browser compatibility\n                    if (vendorType === 'makeupArtist' || vendorType === 'makeup_artist') {\n                        // Ensure both makeupArtist and makeup_artist are present\n                        updatedDetailFields[\"vendor_makeupArtist_name\"] = details.name || '';\n                        updatedDetailFields[\"vendor_makeupArtist_contact\"] = details.mobileNumber || '';\n                        updatedDetailFields[\"vendor_makeup_artist_name\"] = details.name || '';\n                        updatedDetailFields[\"vendor_makeup_artist_contact\"] = details.mobileNumber || '';\n                    } else if (vendorType === 'decorations' || vendorType === 'decoration') {\n                        // Ensure both decorations and decoration are present\n                        updatedDetailFields[\"vendor_decorations_name\"] = details.name || '';\n                        updatedDetailFields[\"vendor_decorations_contact\"] = details.mobileNumber || '';\n                        updatedDetailFields[\"vendor_decoration_name\"] = details.name || '';\n                        updatedDetailFields[\"vendor_decoration_contact\"] = details.mobileNumber || '';\n                    }\n                // console.log(`UPLOAD CONTEXT - Setting vendor detail: ${vendorType} (backend: ${backendVendorType})`, {\n                //   name: details.name || '',\n                //   contact: details.mobileNumber || ''\n                // });\n                } else {\n                    console.log(\"UPLOAD CONTEXT - Skipping incomplete vendor detail: \".concat(vendorType));\n                }\n            }\n        });\n        // Don't update state here - we'll do it after restoring the video_category\n        // console.log('UPLOAD CONTEXT - Vendor details set successfully');\n        // console.log('UPLOAD CONTEXT - Total detail fields count:', Object.keys(updatedDetailFields).length);\n        // Count how many complete vendor details we have after processing\n        let completeVendorPairs = 0;\n        const vendorNames = new Set();\n        const vendorContacts = new Set();\n        // Log all vendor details for debugging\n        Object.keys(updatedDetailFields).forEach((key)=>{\n            if (key.startsWith('vendor_')) {\n                // console.log(`UPLOAD CONTEXT - Vendor field: ${key} = ${updatedDetailFields[key]}`);\n                if (key.endsWith('_name')) {\n                    vendorNames.add(key.replace('vendor_', '').replace('_name', ''));\n                } else if (key.endsWith('_contact')) {\n                    vendorContacts.add(key.replace('vendor_', '').replace('_contact', ''));\n                }\n            }\n        });\n        // Count complete pairs (both name and contact)\n        vendorNames.forEach((name)=>{\n            if (vendorContacts.has(name)) {\n                completeVendorPairs++;\n            }\n        });\n        // console.log(`UPLOAD CONTEXT - Found ${completeVendorPairs} complete vendor pairs (name + contact)`);\n        // console.log(`UPLOAD CONTEXT - Vendor names: ${Array.from(vendorNames).join(', ')}`);\n        // console.log(`UPLOAD CONTEXT - Vendor contacts: ${Array.from(vendorContacts).join(', ')}`);\n        // Restore the video_category if it exists\n        if (videoCategory) {\n            console.log('UPLOAD CONTEXT - Restoring video_category:', videoCategory);\n            updatedDetailFields.video_category = videoCategory;\n        }\n        // Log the detail fields before updating state\n        console.log('UPLOAD CONTEXT - Detail fields before update:', JSON.stringify(state.detailFields, null, 2));\n        console.log('UPLOAD CONTEXT - Updated detail fields to be set:', JSON.stringify(updatedDetailFields, null, 2));\n        // Create a completely new state object to ensure Edge updates correctly\n        const newState = {\n            ...state,\n            detailFields: {\n                ...updatedDetailFields\n            }\n        };\n        // For Edge browser compatibility, directly set the vendor fields in the state\n        // This is a workaround for Edge where the state update doesn't properly preserve vendor details\n        if ( true && /Edge|Edg/.test(window.navigator.userAgent)) {\n            console.log('UPLOAD CONTEXT - Edge browser detected, using direct state update');\n            // Create a direct reference to the state object\n            const directState = state;\n            // Directly set the detailFields\n            directState.detailFields = {\n                ...updatedDetailFields\n            };\n            // Log the direct update\n            console.log('UPLOAD CONTEXT - Direct state update:', JSON.stringify(directState.detailFields, null, 2));\n        }\n        // Update the state with the updated detail fields\n        setState(newState);\n        // Force a re-render to ensure the state is updated\n        setTimeout(()=>{\n            console.log('UPLOAD CONTEXT - Vendor details set successfully');\n            console.log('UPLOAD CONTEXT - Total detail fields count:', Object.keys(state.detailFields).length);\n            console.log('UPLOAD CONTEXT - Detail fields after update:', JSON.stringify(state.detailFields, null, 2));\n            // Double-check that the vendor details were set correctly\n            const vendorNameFields = Object.keys(state.detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_name'));\n            const vendorContactFields = Object.keys(state.detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_contact'));\n            console.log('UPLOAD CONTEXT - Vendor name fields count:', vendorNameFields.length);\n            console.log('UPLOAD CONTEXT - Vendor contact fields count:', vendorContactFields.length);\n        }, 100);\n    };\n    const resetUpload = ()=>{\n        console.log('UPLOAD CONTEXT - Completely resetting upload state');\n        // Create a fresh copy of the initial state\n        const freshState = {\n            file: null,\n            thumbnail: null,\n            mediaType: '',\n            mediaSubtype: '',\n            title: '',\n            description: '',\n            tags: [],\n            detailFields: {},\n            step: 'select',\n            duration: 0\n        };\n        // Set the state to the fresh state\n        setState(freshState);\n        // Log the reset\n        console.log('UPLOAD CONTEXT - Upload state reset to:', JSON.stringify(freshState, null, 2));\n    };\n    // Helper function to detect Edge browser\n    const isEdgeBrowser = ()=>{\n        if (true) {\n            return /Edge|Edg/.test(window.navigator.userAgent);\n        }\n        return false;\n    };\n    const validateForm = ()=>{\n        // For moments (stories), only validate file and title - skip all other validations\n        if (state.mediaSubtype === 'story') {\n            console.log('VALIDATE FORM - Moments/Stories detected, using simplified validation');\n            // Check if file is selected\n            if (!state.file) {\n                console.log('Validation failed: No file selected for moments');\n                return {\n                    isValid: false,\n                    error: 'Please select a file to upload'\n                };\n            }\n            // Validate file type and size\n            const fileValidation = (0,_utils_uploadUtils__WEBPACK_IMPORTED_MODULE_3__.validateFile)(state.file, state.mediaType);\n            if (!fileValidation.isValid) {\n                console.log('Validation failed: File validation failed for moments', fileValidation);\n                return fileValidation;\n            }\n            // Check if title is provided\n            if (!state.title || !state.title.trim()) {\n                console.log('Validation failed: Title is empty for moments');\n                return {\n                    isValid: false,\n                    error: 'Please provide a title for your upload'\n                };\n            }\n            console.log('VALIDATE FORM - Moments validation passed');\n            return {\n                isValid: true\n            };\n        }\n        // Check if we're running in Edge browser\n        const isEdge = isEdgeBrowser();\n        if (isEdge) {\n            console.log('VALIDATE FORM - Running in Edge browser, applying special handling');\n        }\n        // console.log('VALIDATE FORM - Validating form with state:', {\n        //   file: state.file ? state.file.name : 'No file',\n        //   mediaType: state.mediaType,\n        //   title: state.title,\n        //   description: state.description,\n        //   detailFieldsCount: Object.keys(state.detailFields).length,\n        //   tags: state.tags\n        // });\n        // Log all detail fields for debugging\n        // console.log('VALIDATE FORM - All detail fields:', JSON.stringify(state.detailFields, null, 2));\n        // Check if file is selected\n        if (!state.file) {\n            // console.log('Validation failed: No file selected');\n            return {\n                isValid: false,\n                error: 'Please select a file to upload'\n            };\n        }\n        // Validate file type and size\n        const fileValidation = (0,_utils_uploadUtils__WEBPACK_IMPORTED_MODULE_3__.validateFile)(state.file, state.mediaType);\n        if (!fileValidation.isValid) {\n            console.log('Validation failed: File validation failed', fileValidation);\n            return fileValidation;\n        }\n        // Check if title is provided\n        if (!state.title || !state.title.trim()) {\n            console.log('Validation failed: Title is empty');\n            return {\n                isValid: false,\n                error: 'Please provide a title for your upload'\n            };\n        }\n        // First, try to get vendor details from localStorage\n        let detailFields = {\n            ...state.detailFields\n        };\n        let vendorDetailsFromStorage = null;\n        try {\n            const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n            if (storedVendorDetails) {\n                vendorDetailsFromStorage = JSON.parse(storedVendorDetails);\n                console.log('VALIDATE FORM - Retrieved vendor details from localStorage:', storedVendorDetails);\n                // Process vendor details from localStorage\n                if (vendorDetailsFromStorage && Object.keys(vendorDetailsFromStorage).length > 0) {\n                    Object.entries(vendorDetailsFromStorage).forEach((param)=>{\n                        let [vendorType, details] = param;\n                        if (details && details.name && details.mobileNumber) {\n                            // Add to detailFields\n                            detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                            detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                            console.log(\"VALIDATE FORM - Added vendor \".concat(vendorType, \" with name: \").concat(details.name, \" and contact: \").concat(details.mobileNumber));\n                            // Also add normalized versions\n                            const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                            if (normalizedType !== vendorType) {\n                                detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                                detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                                console.log(\"VALIDATE FORM - Also added normalized vendor \".concat(normalizedType));\n                            }\n                        }\n                    });\n                    console.log('VALIDATE FORM - Updated detail fields with vendor details from localStorage');\n                }\n            }\n        } catch (error) {\n            console.error('VALIDATE FORM - Failed to retrieve vendor details from localStorage:', error);\n        }\n        // Now use the updated detailFields for validation\n        console.log('Detail fields count:', Object.keys(detailFields).length);\n        console.log('Detail fields present:', Object.keys(detailFields));\n        console.log('Detail fields values:', detailFields);\n        // For videos, check if required vendor details are present based on video category\n        if (state.mediaType === 'video') {\n            // Determine required vendor count based on video category\n            const videoCategory = detailFields.video_category || 'my_wedding';\n            const requiredVendorCount = videoCategory === 'wedding_vlog' ? 1 : 4;\n            console.log(\"VALIDATE FORM - Video category: \".concat(videoCategory, \", Required vendors: \").concat(requiredVendorCount));\n            // Special handling for Edge browser\n            if (isEdge) {\n                console.log('VALIDATE FORM - Edge browser detected, checking vendor details directly');\n                // In Edge, we'll count vendor details directly from the detailFields\n                const vendorNameFields = Object.keys(detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_name'));\n                const vendorContactFields = Object.keys(detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_contact'));\n                console.log('VALIDATE FORM - Edge browser - Vendor name fields:', vendorNameFields);\n                console.log('VALIDATE FORM - Edge browser - Vendor contact fields:', vendorContactFields);\n                // If we have at least 4 vendor name fields and 4 vendor contact fields, assume we have enough vendors\n                if (vendorNameFields.length >= 4 && vendorContactFields.length >= 4) {\n                    console.log('VALIDATE FORM - Edge browser - Found enough vendor fields, validation passed');\n                    return {\n                        isValid: true\n                    };\n                }\n                // Edge browser workaround - if we're uploading a video, assume vendor details are valid\n                // This is a temporary workaround for Edge browser compatibility\n                if (state.mediaType === 'video') {\n                    console.log('VALIDATE FORM - Edge browser workaround: Assuming vendor details are valid for video upload');\n                    return {\n                        isValid: true\n                    };\n                }\n            }\n            console.log('VALIDATE FORM - Checking vendor details for video upload');\n            console.log('VALIDATE FORM - Detail fields:', JSON.stringify(detailFields, null, 2));\n            // Count how many complete vendor details we have (where BOTH name AND contact are provided)\n            let validVendorCount = 0;\n            // Include both frontend and backend field names to ensure we count all vendor details\n            const vendorPrefixes = [\n                'venue',\n                'photographer',\n                'makeup_artist',\n                'makeupArtist',\n                'decoration',\n                'decorations',\n                'caterer',\n                'additional1',\n                'additional2',\n                'additionalVendor1',\n                'additionalVendor2'\n            ];\n            console.log('VALIDATE FORM - Checking these vendor prefixes:', vendorPrefixes.join(', '));\n            // Keep track of which vendors we've already counted to avoid duplicates\n            const countedVendors = new Set();\n            // First, log all vendor-related fields for debugging\n            const vendorFields = Object.keys(detailFields).filter((key)=>key.startsWith('vendor_'));\n            console.log('VALIDATE FORM - All vendor fields:', vendorFields.join(', '));\n            for (const prefix of vendorPrefixes){\n                // Skip if we've already counted this vendor (to avoid counting both makeupArtist and makeup_artist)\n                const normalizedPrefix = prefix === 'makeupArtist' ? 'makeup_artist' : prefix === 'decorations' ? 'decoration' : prefix;\n                if (countedVendors.has(normalizedPrefix)) {\n                    console.log(\"VALIDATE FORM - Skipping \".concat(prefix, \" as we already counted \").concat(normalizedPrefix));\n                    continue;\n                }\n                const nameField = \"vendor_\".concat(prefix, \"_name\");\n                const contactField = \"vendor_\".concat(prefix, \"_contact\");\n                console.log(\"VALIDATE FORM - Checking vendor \".concat(prefix, \":\"), {\n                    nameField,\n                    nameValue: detailFields[nameField],\n                    contactField,\n                    contactValue: detailFields[contactField],\n                    hasName: !!detailFields[nameField],\n                    hasContact: !!detailFields[contactField]\n                });\n                if (detailFields[nameField] && detailFields[contactField]) {\n                    validVendorCount++;\n                    countedVendors.add(normalizedPrefix);\n                    console.log(\"VALIDATE FORM - Found valid vendor: \".concat(prefix, \" with name: \").concat(detailFields[nameField], \" and contact: \").concat(detailFields[contactField]));\n                }\n            }\n            // Also check for any other vendor_ fields that might have been added\n            console.log('VALIDATE FORM - Checking for additional vendor fields');\n            Object.keys(detailFields).forEach((key)=>{\n                if (key.startsWith('vendor_') && key.endsWith('_name')) {\n                    const baseKey = key.replace('vendor_', '').replace('_name', '');\n                    const contactKey = \"vendor_\".concat(baseKey, \"_contact\");\n                    // Skip if we've already counted this vendor\n                    const normalizedPrefix = baseKey === 'makeupArtist' ? 'makeup_artist' : baseKey === 'decorations' ? 'decoration' : baseKey;\n                    console.log(\"VALIDATE FORM - Checking additional vendor \".concat(baseKey, \":\"), {\n                        normalizedPrefix,\n                        alreadyCounted: countedVendors.has(normalizedPrefix),\n                        hasName: !!detailFields[key],\n                        hasContact: !!detailFields[contactKey]\n                    });\n                    if (!countedVendors.has(normalizedPrefix) && detailFields[key] && detailFields[contactKey]) {\n                        validVendorCount++;\n                        countedVendors.add(normalizedPrefix);\n                        console.log(\"VALIDATE FORM - Found additional valid vendor: \".concat(baseKey, \" with name: \").concat(detailFields[key], \" and contact: \").concat(detailFields[contactKey]));\n                    }\n                }\n            });\n            console.log(\"VALIDATE FORM - Total valid vendor count: \".concat(validVendorCount));\n            console.log(\"VALIDATE FORM - Counted vendors: \".concat(Array.from(countedVendors).join(', ')));\n            // Special handling for Edge browser - if we have vendor fields but they're not being counted correctly\n            let edgeVendorNameFields = Object.keys(detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_name'));\n            let edgeVendorContactFields = Object.keys(detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_contact'));\n            console.log('VALIDATE FORM - Edge browser check - Vendor name fields:', edgeVendorNameFields);\n            console.log('VALIDATE FORM - Edge browser check - Vendor contact fields:', edgeVendorContactFields);\n            // If we have at least required vendor name fields and contact fields, but validVendorCount is less than required,\n            // this is likely an Edge browser issue where the fields aren't being properly counted\n            if (validVendorCount < requiredVendorCount && edgeVendorNameFields.length >= requiredVendorCount && edgeVendorContactFields.length >= requiredVendorCount) {\n                console.log('VALIDATE FORM - Edge browser workaround: Found enough vendor fields but they were not counted correctly');\n                console.log('VALIDATE FORM - Vendor name fields:', edgeVendorNameFields.join(', '));\n                console.log('VALIDATE FORM - Vendor contact fields:', edgeVendorContactFields.join(', '));\n                // Count unique vendor prefixes (excluding the _name/_contact suffix)\n                const vendorPrefixSet = new Set();\n                edgeVendorNameFields.forEach((field)=>{\n                    const prefix = field.replace('vendor_', '').replace('_name', '');\n                    if (edgeVendorContactFields.includes(\"vendor_\".concat(prefix, \"_contact\"))) {\n                        vendorPrefixSet.add(prefix);\n                    }\n                });\n                const uniqueVendorCount = vendorPrefixSet.size;\n                console.log(\"VALIDATE FORM - Unique vendor count: \".concat(uniqueVendorCount));\n                if (uniqueVendorCount >= requiredVendorCount) {\n                    console.log(\"VALIDATE FORM - Edge browser workaround: Found at least \".concat(requiredVendorCount, \" unique vendors with both name and contact\"));\n                    validVendorCount = uniqueVendorCount;\n                }\n            }\n            // Log the vendor field counts\n            console.log('VALIDATE FORM - Vendor name fields count:', edgeVendorNameFields.length);\n            console.log('VALIDATE FORM - Vendor contact fields count:', edgeVendorContactFields.length);\n            if (validVendorCount < requiredVendorCount) {\n                console.log('VALIDATE FORM - Validation failed: Not enough vendor details', validVendorCount);\n                const categoryText = videoCategory === 'wedding_vlog' ? 'wedding vlog' : 'my wedding';\n                return {\n                    isValid: false,\n                    error: \"At least \".concat(requiredVendorCount, \" complete vendor detail\").concat(requiredVendorCount > 1 ? 's' : '', \" (with both name and contact) \").concat(requiredVendorCount > 1 ? 'are' : 'is', \" required for \").concat(categoryText, \" videos. You provided \").concat(validVendorCount, \"/\").concat(requiredVendorCount, \".\")\n                };\n            } else {\n                console.log('VALIDATE FORM - Vendor validation passed with', validVendorCount, 'vendors');\n            }\n        }\n        // Just log the detail fields count for now\n        console.log('Detail fields count:', Object.keys(state.detailFields).length);\n        // Log the detail fields that are present\n        console.log('Detail fields present:', Object.keys(state.detailFields));\n        console.log('Detail fields values:', state.detailFields);\n        console.log('Form validation passed');\n        return {\n            isValid: true\n        };\n    };\n    // Start upload with a specific category and video_category (used when correcting the category)\n    const startUploadWithCategory = async (category, videoCategory)=>{\n        console.log(\"Starting upload process with corrected category: \".concat(category));\n        console.log(\"Using video_category: \".concat(videoCategory || 'Not provided'));\n        // Try to get vendor details from localStorage\n        let vendorDetails = {};\n        try {\n            const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n            if (storedVendorDetails) {\n                vendorDetails = JSON.parse(storedVendorDetails);\n                console.log('UPLOAD CONTEXT - Retrieved vendor details from localStorage');\n            }\n        } catch (error) {\n            console.error('UPLOAD CONTEXT - Failed to retrieve vendor details from localStorage:', error);\n        }\n        // Create detail fields from vendor details\n        const detailFields = {\n            ...state.detailFields\n        };\n        // Process vendor details to create detail fields\n        if (Object.keys(vendorDetails).length > 0) {\n            console.log('UPLOAD CONTEXT - Processing vendor details from localStorage:', JSON.stringify(vendorDetails));\n            // Track how many complete vendor details we've added\n            let completeVendorCount = 0;\n            Object.entries(vendorDetails).forEach((param)=>{\n                let [vendorType, details] = param;\n                if (details && details.name && details.mobileNumber) {\n                    detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                    detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                    console.log(\"UPLOAD CONTEXT - Added vendor \".concat(vendorType, \" with name: \").concat(details.name, \" and contact: \").concat(details.mobileNumber));\n                    completeVendorCount++;\n                    // Also set normalized versions\n                    const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                    if (normalizedType !== vendorType) {\n                        detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                        detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                        console.log(\"UPLOAD CONTEXT - Also added normalized vendor \".concat(normalizedType));\n                    }\n                }\n            });\n            console.log(\"UPLOAD CONTEXT - Added \".concat(completeVendorCount, \" complete vendor details from localStorage to detailFields\"));\n            console.log('UPLOAD CONTEXT - Detail fields after adding vendors:', JSON.stringify(detailFields));\n        }\n        // Update the state with the corrected category and video_category if provided\n        const updatedState = {\n            ...state,\n            mediaSubtype: category,\n            category: category,\n            detailFields: detailFields\n        };\n        // If videoCategory is provided, update the detailFields\n        if (videoCategory) {\n            updatedState.detailFields.video_category = videoCategory;\n            console.log(\"Setting video_category in state to: \".concat(videoCategory));\n            // Also store in localStorage\n            try {\n                localStorage.setItem('wedzat_video_category', videoCategory);\n                console.log('UPLOAD CONTEXT - Stored video_category in localStorage:', videoCategory);\n            } catch (error) {\n                console.error('UPLOAD CONTEXT - Failed to store video_category in localStorage:', error);\n            }\n        }\n        // Apply the state update immediately\n        setState(updatedState);\n        // Then start the upload process with the updated category\n        const validation = validateForm();\n        if (!validation.isValid) {\n            console.log('Upload validation failed:', validation.error);\n            setState({\n                ...state,\n                mediaSubtype: category,\n                error: validation.error,\n                step: 'error'\n            });\n            return;\n        }\n        if (!state.file) {\n            console.log('No file to upload');\n            return;\n        }\n        setState({\n            ...state,\n            mediaSubtype: category,\n            isUploading: true,\n            progress: 0,\n            step: 'uploading',\n            error: undefined\n        });\n        try {\n            console.log('UPLOAD CONTEXT - Starting upload process with corrected category...');\n            console.log('UPLOAD CONTEXT - Upload details:', {\n                file: state.file ? state.file.name : 'No file',\n                fileSize: state.file ? Math.round(state.file.size / (1024 * 1024) * 100) / 100 + ' MB' : 'N/A',\n                mediaType: state.mediaType,\n                category: category,\n                title: state.title,\n                videoCategory: videoCategory || 'Not set'\n            });\n            // Log the video_category that will be used\n            console.log('UPLOAD CONTEXT - Using explicit video_category:', videoCategory);\n            console.log('UPLOAD CONTEXT - Video category from detail fields:', state.detailFields.video_category);\n            // Create a copy of the detail fields with the explicit video_category\n            const updatedDetailFields = {\n                ...state.detailFields\n            };\n            // If videoCategory is provided, use it\n            if (videoCategory) {\n                updatedDetailFields.video_category = videoCategory;\n                console.log('UPLOAD CONTEXT - Setting video_category in details to:', videoCategory);\n            }\n            // Use the upload service to handle the complete upload process with the corrected category\n            const result = await _services_api__WEBPACK_IMPORTED_MODULE_2__.uploadService.handleUpload(state.file, state.mediaType, category, state.title, state.description, state.tags, updatedDetailFields, state.duration, state.thumbnail, (progress)=>{\n                setState({\n                    ...state,\n                    mediaSubtype: category,\n                    progress\n                });\n            });\n            // Update the state with the upload result\n            setState({\n                ...state,\n                mediaSubtype: category,\n                isUploading: false,\n                progress: 100,\n                step: 'complete',\n                uploadResult: result\n            });\n        // Upload completed successfully\n        } catch (error) {\n            console.error('Upload failed:', error);\n            setState({\n                ...state,\n                mediaSubtype: category,\n                isUploading: false,\n                error: error instanceof Error ? error.message : 'Unknown error'\n            });\n            throw error;\n        }\n    };\n    // Perform the actual upload without vendor processing\n    const performUpload = async ()=>{\n        console.log('UPLOAD CONTEXT - Starting direct upload for moments');\n        // Simple validation for moments - only check file and title\n        if (!state.file) {\n            console.log('No file to upload');\n            setState({\n                ...state,\n                error: 'No file selected',\n                step: 'error'\n            });\n            return;\n        }\n        if (!state.title || !state.title.trim()) {\n            console.log('No title provided for moments');\n            setState({\n                ...state,\n                error: 'Please provide a title for your upload',\n                step: 'error'\n            });\n            return;\n        }\n        setState({\n            ...state,\n            isUploading: true,\n            progress: 0,\n            step: 'uploading',\n            error: undefined\n        });\n        try {\n            console.log('UPLOAD CONTEXT - Starting upload process for moments...');\n            console.log('UPLOAD CONTEXT - Upload details:', {\n                file: state.file ? state.file.name : 'No file',\n                fileSize: state.file ? Math.round(state.file.size / (1024 * 1024) * 100) / 100 + ' MB' : 'N/A',\n                mediaType: state.mediaType,\n                mediaSubtype: state.mediaSubtype,\n                title: state.title\n            });\n            // Use the upload service to handle the complete upload process\n            const result = await _services_api__WEBPACK_IMPORTED_MODULE_2__.uploadService.handleUpload(state.file, state.mediaType, state.mediaSubtype, state.title, state.description, state.tags, {}, state.duration, state.thumbnail, (progress)=>{\n                console.log(\"Upload progress: \".concat(progress, \"%\"));\n                setState((prevState)=>{\n                    // Only update if the new progress is greater\n                    if (progress > prevState.progress) {\n                        return {\n                            ...prevState,\n                            progress,\n                            // Change to processing state when we reach 80%\n                            step: progress >= 80 && progress < 100 ? 'processing' : prevState.step\n                        };\n                    }\n                    return prevState;\n                });\n            });\n            console.log('UPLOAD CONTEXT - Upload completed successfully for moments:', result);\n            setState({\n                ...state,\n                isUploading: false,\n                progress: 100,\n                step: 'success',\n                error: undefined\n            });\n        // Upload completed successfully\n        } catch (error) {\n            console.error('Upload failed for moments:', error);\n            setState({\n                ...state,\n                isUploading: false,\n                error: error instanceof Error ? error.message : 'Unknown error',\n                step: 'error'\n            });\n            throw error;\n        }\n    };\n    const startUpload = async ()=>{\n        console.log('Starting upload process...');\n        // For moments (stories), skip vendor details processing and go directly to upload\n        if (state.mediaSubtype === 'story') {\n            console.log('UPLOAD CONTEXT - Moments/Stories detected, skipping vendor details processing');\n            await performUpload();\n            return;\n        }\n        // Try to get vendor details from localStorage\n        try {\n            const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n            if (storedVendorDetails) {\n                const vendorDetails = JSON.parse(storedVendorDetails);\n                console.log('UPLOAD CONTEXT - Retrieved vendor details from localStorage for startUpload:', storedVendorDetails);\n                // Create a new detailFields object to hold all the vendor details\n                const updatedDetailFields = {\n                    ...state.detailFields\n                };\n                let completeVendorCount = 0;\n                // Process vendor details to create detail fields\n                if (Object.keys(vendorDetails).length > 0) {\n                    Object.entries(vendorDetails).forEach((param)=>{\n                        let [vendorType, details] = param;\n                        if (details && details.name && details.mobileNumber) {\n                            // Add to the updated detail fields\n                            updatedDetailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                            updatedDetailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                            console.log(\"UPLOAD CONTEXT - Added vendor \".concat(vendorType, \" with name: \").concat(details.name, \" and contact: \").concat(details.mobileNumber));\n                            completeVendorCount++;\n                            // Also set normalized versions\n                            const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                            if (normalizedType !== vendorType) {\n                                updatedDetailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                                updatedDetailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                                console.log(\"UPLOAD CONTEXT - Also added normalized vendor \".concat(normalizedType));\n                            }\n                        }\n                    });\n                    // Update the state with all vendor details at once\n                    setState((prevState)=>({\n                            ...prevState,\n                            detailFields: updatedDetailFields\n                        }));\n                    console.log(\"UPLOAD CONTEXT - Added \".concat(completeVendorCount, \" complete vendor details from localStorage to state\"));\n                    console.log('UPLOAD CONTEXT - Updated detail fields:', JSON.stringify(updatedDetailFields));\n                }\n            }\n        } catch (error) {\n            console.error('UPLOAD CONTEXT - Failed to retrieve vendor details from localStorage:', error);\n        }\n        // Check if we have a video_category for videos\n        if (state.mediaType === 'video') {\n            // Try to get video_category from localStorage if not in state\n            let videoCategory = state.detailFields.video_category;\n            if (!videoCategory) {\n                try {\n                    const storedVideoCategory = localStorage.getItem('wedzat_video_category');\n                    if (storedVideoCategory) {\n                        videoCategory = storedVideoCategory;\n                        console.log('UPLOAD CONTEXT - Retrieved video_category from localStorage:', videoCategory);\n                        setDetailField('video_category', videoCategory);\n                    }\n                } catch (error) {\n                    console.error('UPLOAD CONTEXT - Failed to retrieve video_category from localStorage:', error);\n                }\n            }\n            console.log(\"UPLOAD CONTEXT - Current video_category: \".concat(videoCategory || 'Not set'));\n            // If we don't have a video_category, use a default one\n            if (!videoCategory) {\n                console.log('UPLOAD CONTEXT - No video_category found, using default: my_wedding');\n                // Use startUploadWithCategory to ensure the video_category is properly set\n                return startUploadWithCategory(state.mediaSubtype, 'my_wedding');\n            } else {\n                // Use startUploadWithCategory to ensure the video_category is properly passed\n                console.log(\"UPLOAD CONTEXT - Using existing video_category: \".concat(videoCategory));\n                return startUploadWithCategory(state.mediaSubtype, videoCategory);\n            }\n        }\n        // For photos, just use the regular upload flow\n        const validation = validateForm();\n        if (!validation.isValid) {\n            console.log('Upload validation failed:', validation.error);\n            setState({\n                ...state,\n                error: validation.error,\n                step: 'error'\n            });\n            return;\n        }\n        if (!state.file) {\n            console.log('No file to upload');\n            return;\n        }\n        setState({\n            ...state,\n            isUploading: true,\n            progress: 0,\n            step: 'uploading',\n            error: undefined\n        });\n        try {\n            console.log('UPLOAD CONTEXT - Starting upload process...');\n            console.log('UPLOAD CONTEXT - Upload details:', {\n                file: state.file ? state.file.name : 'No file',\n                fileSize: state.file ? Math.round(state.file.size / (1024 * 1024) * 100) / 100 + ' MB' : 'N/A',\n                mediaType: state.mediaType,\n                mediaSubtype: state.mediaSubtype,\n                title: state.title,\n                videoCategory: state.detailFields.video_category || 'Not set'\n            });\n            // Use the upload service to handle the complete upload process\n            const result = await _services_api__WEBPACK_IMPORTED_MODULE_2__.uploadService.handleUpload(state.file, state.mediaType, state.mediaSubtype, state.title, state.description, state.tags, state.detailFields, state.duration, state.thumbnail, (progress)=>{\n                console.log(\"Upload progress: \".concat(progress, \"%\"));\n                setState((prevState)=>{\n                    // Only update if the new progress is greater\n                    if (progress > prevState.progress) {\n                        return {\n                            ...prevState,\n                            progress,\n                            // Change to processing state when we reach 80%\n                            step: progress >= 80 && progress < 100 ? 'processing' : prevState.step\n                        };\n                    }\n                    return prevState;\n                });\n            });\n            console.log('Upload completed successfully:', result);\n            // Upload complete\n            setState({\n                ...state,\n                isUploading: false,\n                progress: 100,\n                step: 'complete',\n                response: result\n            });\n        } catch (error) {\n            console.error('Upload failed:', error);\n            setState({\n                ...state,\n                isUploading: false,\n                progress: 0,\n                step: 'error',\n                error: error instanceof Error ? error.message : 'Upload failed. Please try again.'\n            });\n        }\n    };\n    const goToStep = (step)=>{\n        setState({\n            ...state,\n            step\n        });\n    };\n    // Set video duration\n    const setDuration = (duration)=>{\n        setState({\n            ...state,\n            duration\n        });\n        console.log(\"Duration set to \".concat(duration, \" seconds\"));\n    };\n    // Effect to initialize the upload context and listen for vendor details updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UploadProvider.useEffect\": ()=>{\n            // For moments (stories), skip localStorage initialization\n            if (state.mediaSubtype === 'story') {\n                console.log('UPLOAD CONTEXT - Moments detected, skipping localStorage initialization');\n                return;\n            }\n            // Check if we have a video_category in localStorage\n            try {\n                const storedVideoCategory = localStorage.getItem('wedzat_video_category');\n                if (storedVideoCategory) {\n                    console.log('UPLOAD CONTEXT - Retrieved video_category from localStorage:', storedVideoCategory);\n                    setDetailField('video_category', storedVideoCategory);\n                }\n            } catch (error) {\n                console.error('UPLOAD CONTEXT - Failed to retrieve video_category from localStorage:', error);\n            }\n            // Add event listener for vendor details updates from API service\n            const handleVendorDetailsUpdate = {\n                \"UploadProvider.useEffect.handleVendorDetailsUpdate\": (event)=>{\n                    if (event.detail) {\n                        console.log('UPLOAD CONTEXT - Received vendor-details-update event:', event.detail);\n                        // Process vendor details from event\n                        const vendorDetails = event.detail;\n                        const updatedDetailFields = {\n                            ...state.detailFields\n                        };\n                        let completeVendorCount = 0;\n                        Object.entries(vendorDetails).forEach({\n                            \"UploadProvider.useEffect.handleVendorDetailsUpdate\": (param)=>{\n                                let [vendorType, details] = param;\n                                if (details && details.name && details.mobileNumber) {\n                                    // Add to detailFields\n                                    updatedDetailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                                    updatedDetailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                                    console.log(\"UPLOAD CONTEXT - Event handler added vendor \".concat(vendorType, \" with name: \").concat(details.name, \" and contact: \").concat(details.mobileNumber));\n                                    completeVendorCount++;\n                                    // Also add normalized versions\n                                    const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                                    if (normalizedType !== vendorType) {\n                                        updatedDetailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                                        updatedDetailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                                        console.log(\"UPLOAD CONTEXT - Event handler also added normalized vendor \".concat(normalizedType));\n                                    }\n                                }\n                            }\n                        }[\"UploadProvider.useEffect.handleVendorDetailsUpdate\"]);\n                        // Update the state with all vendor details at once\n                        setState({\n                            \"UploadProvider.useEffect.handleVendorDetailsUpdate\": (prevState)=>({\n                                    ...prevState,\n                                    detailFields: updatedDetailFields\n                                })\n                        }[\"UploadProvider.useEffect.handleVendorDetailsUpdate\"]);\n                        console.log(\"UPLOAD CONTEXT - Event handler added \".concat(completeVendorCount, \" complete vendor details to state\"));\n                        console.log('UPLOAD CONTEXT - Event handler updated detail fields:', JSON.stringify(updatedDetailFields));\n                    }\n                }\n            }[\"UploadProvider.useEffect.handleVendorDetailsUpdate\"];\n            // Add event listener\n            window.addEventListener('vendor-details-update', handleVendorDetailsUpdate);\n            // Remove event listener on cleanup\n            return ({\n                \"UploadProvider.useEffect\": ()=>{\n                    window.removeEventListener('vendor-details-update', handleVendorDetailsUpdate);\n                }\n            })[\"UploadProvider.useEffect\"];\n        }\n    }[\"UploadProvider.useEffect\"], []);\n    // Create the context value\n    const contextValue = {\n        state,\n        setFile,\n        setThumbnail,\n        setMediaType,\n        setMediaSubtype,\n        setCategory,\n        setTitle,\n        setDescription,\n        addTag,\n        removeTag,\n        setDetailField,\n        setPersonalDetails,\n        setVendorDetails,\n        setDuration,\n        resetUpload,\n        startUpload,\n        startUploadWithCategory,\n        validateForm,\n        goToStep\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UploadContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\contexts\\\\UploadContexts.tsx\",\n        lineNumber: 1312,\n        columnNumber: 5\n    }, undefined);\n};\n_s(UploadProvider, \"g9yWDQF6ixWa1r5sfsm7YAeGJG4=\");\n_c = UploadProvider;\n// Custom hook to use the context\nconst useUpload = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(UploadContext);\n    if (context === undefined) {\n        throw new Error('useUpload must be used within an UploadProvider');\n    }\n    return context;\n};\n_s1(useUpload, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"UploadProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/UploadContexts.tsx\n"));

/***/ })

});