"use client";
import React, { useState, useEffect } from "react";
import { TopNavigation, SideNavigation, MobileNavigation } from "../../../components/HomeDashboard/Navigation";
import Image from "next/image";
import { ChevronDown, ChevronUp } from "lucide-react";

// Define interface for subtype items
interface Subtype {
  id: number;
  name: string;
}

// Define interface for category items
interface Category {
  id: number;
  name: string;
  image: string;
  subtypes?: Subtype[];
}

export default function CategoriesPage() {
  const [isClient, setIsClient] = useState(false);
  const [sidebarExpanded, setSidebarExpanded] = useState(false);

  useEffect(() => setIsClient(true), []);

  // State to track which category is expanded
  const [expandedCategory, setExpandedCategory] = useState<number | null>(null);

  // Toggle category expansion
  const toggleCategory = (categoryId: number) => {
    if (expandedCategory === categoryId) {
      setExpandedCategory(null);
    } else {
      setExpandedCategory(categoryId);
    }
  };

  // Categories array with subtypes
  const categories: Category[] = [
    {
      name: "Venues",
      id: 1,
      image:
        "https://images.unsplash.com/photo-1519167758481-83f550bb49b3?q=80&w=200&h=120&fit=crop",
      subtypes: [
        { id: 101, name: "Kalyana Mandapam" },
        { id: 102, name: "Banquet Halls" },
        { id: 103, name: "Small Party/Engagement Halls" },
        { id: 104, name: "Outdoor Lawn/Garden" },
        { id: 105, name: "Resort" },
        { id: 106, name: "4-Star/5-Star Hotel" },
        { id: 107, name: "Destination Weddings" },
      ],
    },
    {
      name: "Photography",
      id: 2,
      image:
        "https://images.unsplash.com/photo-1520854221256-17451cc331bf?q=80&w=200&h=120&fit=crop",
      subtypes: [
        { id: 201, name: "Wedding Photography" },
        { id: 202, name: "Pre-Wedding Shoots" },
        { id: 203, name: "Candid Photography" },
        { id: 204, name: "Traditional Photography" },
        { id: 205, name: "Drone Photography" },
      ],
    },
    {
      name: "Videographer",
      id: 3,
      image:
        "https://images.unsplash.com/photo-1604152135912-04a022e23696?q=80&w=200&h=120&fit=crop",
      subtypes: [
        { id: 301, name: "Wedding Films" },
        { id: 302, name: "Pre-Wedding Videos" },
        { id: 303, name: "Cinematic Videos" },
        { id: 304, name: "Drone Videography" },
        { id: 305, name: "Same-Day Edit" },
      ],
    },
    {
      name: "Makeup Artist",
      id: 4,
      image:
        "https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?q=80&w=200&h=120&fit=crop",
      subtypes: [
        { id: 401, name: "Bridal Makeup" },
        { id: 402, name: "Party Makeup" },
        { id: 403, name: "HD Makeup" },
        { id: 404, name: "Airbrush Makeup" },
        { id: 405, name: "Celebrity Makeup Artists" },
      ],
    },
    {
      name: "Decoration",
      id: 5,
      image:
        "https://images.unsplash.com/photo-1519741497674-611481863552?q=80&w=200&h=120&fit=crop",
      subtypes: [
        { id: 501, name: "Wedding Stage Decoration" },
        { id: 502, name: "Floral Decoration" },
        { id: 503, name: "Lighting Decoration" },
        { id: 504, name: "Entrance Decoration" },
        { id: 505, name: "Mandap Decoration" },
      ],
    },
    {
      name: "Caterers",
      id: 6,
      image:
        "https://images.unsplash.com/photo-1464366400600-7168b8af9bc3?q=80&w=200&h=120&fit=crop",
      subtypes: [
        { id: 601, name: "Vegetarian Catering" },
        { id: 602, name: "Non-Vegetarian Catering" },
        { id: 603, name: "Chaat & Street Food" },
        { id: 604, name: "Multi-Cuisine Catering" },
        { id: 605, name: "Dessert Counters" },
      ],
    },
    {
      name: "Mehendi",
      id: 7,
      image:
        "https://images.unsplash.com/photo-1525135850648-b42365991054?q=80&w=200&h=120&fit=crop",
      subtypes: [
        { id: 701, name: "Traditional Mehendi" },
        { id: 702, name: "Arabic Mehendi" },
        { id: 703, name: "Bridal Mehendi" },
        { id: 704, name: "Rajasthani Mehendi" },
        { id: 705, name: "Indo-Arabic Mehendi" },
      ],
    },
    {
      name: "Bridal Outfit",
      id: 8,
      image:
        "https://images.unsplash.com/photo-1585241920473-b472eb9ffbae?q=80&w=200&h=120&fit=crop",
      subtypes: [
        { id: 801, name: "Bridal Lehenga" },
        { id: 802, name: "Bridal Saree" },
        { id: 803, name: "Designer Wear" },
        { id: 804, name: "Indo-Western" },
        { id: 805, name: "Reception Outfits" },
      ],
    },
    {
      name: "Groom Outfit",
      id: 9,
      image:
        "https://images.unsplash.com/photo-1596436889106-be35e843f974?q=80&w=200&h=120&fit=crop",
      subtypes: [
        { id: 901, name: "Sherwani" },
        { id: 902, name: "Indo-Western" },
        { id: 903, name: "Wedding Suits" },
        { id: 904, name: "Kurta Pajama" },
        { id: 905, name: "Designer Wear" },
      ],
    },
    {
      name: "Invitations",
      id: 10,
      image:
        "https://images.unsplash.com/photo-1607190074257-dd4b7af0309f?q=80&w=200&h=120&fit=crop",
      subtypes: [
        { id: 1001, name: "Traditional Invitations" },
        { id: 1002, name: "Digital Invitations" },
        { id: 1003, name: "Handmade Cards" },
        { id: 1004, name: "Boxed Invitations" },
        { id: 1005, name: "Scroll Invitations" },
      ],
    },
    {
      name: "Music & Dance",
      id: 11,
      image:
        "https://images.unsplash.com/photo-1429962714451-bb934ecdc4ec?q=80&w=200&h=120&fit=crop",
      subtypes: [
        { id: 1101, name: "DJ Services" },
        { id: 1102, name: "Live Band" },
        { id: 1103, name: "Choreographers" },
        { id: 1104, name: "Sangeet Performers" },
        { id: 1105, name: "Folk Musicians" },
      ],
    },
    {
      name: "Pandits",
      id: 12,
      image:
        "https://images.unsplash.com/photo-1639575668910-9a14063add58?q=80&w=200&h=120&fit=crop",
      subtypes: [
        { id: 1201, name: "Wedding Ceremonies" },
        { id: 1202, name: "Pre-Wedding Rituals" },
        { id: 1203, name: "Muhurat Selection" },
        { id: 1204, name: "Vedic Ceremonies" },
        { id: 1205, name: "Regional Specialists" },
      ],
    },
    {
      name: "Jewelry",
      id: 13,
      image:
        "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?q=80&w=200&h=120&fit=crop",
      subtypes: [
        { id: 1301, name: "Bridal Jewelry" },
        { id: 1302, name: "Gold Jewelry" },
        { id: 1303, name: "Diamond Jewelry" },
        { id: 1304, name: "Kundan Jewelry" },
        { id: 1305, name: "Temple Jewelry" },
      ],
    },
    {
      name: "Dermatologist",
      id: 14,
      image:
        "https://images.unsplash.com/photo-1612776572997-76cc42e058c3?q=80&w=200&h=120&fit=crop",
      subtypes: [
        { id: 1401, name: "Pre-Wedding Skin Care" },
        { id: 1402, name: "Bridal Packages" },
        { id: 1403, name: "Skin Treatments" },
        { id: 1404, name: "Hair Treatments" },
        { id: 1405, name: "Cosmetic Procedures" },
      ],
    },
    {
      name: "Esthetic Dentist",
      id: 15,
      image:
        "https://images.unsplash.com/photo-1606811971618-4486d14f3f99?q=80&w=200&h=120&fit=crop",
      subtypes: [
        { id: 1501, name: "Teeth Whitening" },
        { id: 1502, name: "Smile Makeover" },
        { id: 1503, name: "Dental Veneers" },
        { id: 1504, name: "Cosmetic Bonding" },
        { id: 1505, name: "Dental Implants" },
      ],
    },
    {
      name: "Beauty Grooming",
      id: 16,
      image:
        "https://images.unsplash.com/photo-1560750588-73207b1ef5b8?q=80&w=200&h=120&fit=crop",
      subtypes: [
        { id: 1601, name: "Bridal Grooming" },
        { id: 1602, name: "Groom Grooming" },
        { id: 1603, name: "Hair Styling" },
        { id: 1604, name: "Nail Art" },
        { id: 1605, name: "Spa Treatments" },
      ],
    },
    {
      name: "Event Planners",
      id: 17,
      image:
        "https://images.unsplash.com/photo-1511795409834-ef04bbd61622?q=80&w=200&h=120&fit=crop",
      subtypes: [
        { id: 1701, name: "Full Wedding Planning" },
        { id: 1702, name: "Partial Planning" },
        { id: 1703, name: "Day-of Coordination" },
        { id: 1704, name: "Destination Wedding Planning" },
        { id: 1705, name: "Theme Wedding Planning" },
      ],
    },
    {
      name: "Transportation",
      id: 18,
      image:
        "https://images.unsplash.com/photo-1515876305430-f06edab8282a?q=80&w=200&h=120&fit=crop",
      subtypes: [
        { id: 1801, name: "Luxury Cars" },
        { id: 1802, name: "Vintage Cars" },
        { id: 1803, name: "Decorated Cars" },
        { id: 1804, name: "Horse Carriages" },
        { id: 1805, name: "Guest Transportation" },
      ],
    },
    {
      name: "Hospitality",
      id: 19,
      image:
        "https://images.unsplash.com/photo-1566073771259-6a8506099945?q=80&w=200&h=120&fit=crop",
      subtypes: [
        { id: 1901, name: "Guest Accommodation" },
        { id: 1902, name: "Welcome Kits" },
        { id: 1903, name: "Guest Management" },
        { id: 1904, name: "Concierge Services" },
        { id: 1905, name: "Out-of-Town Guest Services" },
      ],
    },
    {
      name: "Gifts",
      id: 20,
      image:
        "https://images.unsplash.com/photo-1549465220-1a8b9238cd48?q=80&w=200&h=120&fit=crop",
      subtypes: [
        { id: 2001, name: "Wedding Favors" },
        { id: 2002, name: "Return Gifts" },
        { id: 2003, name: "Bridesmaid Gifts" },
        { id: 2004, name: "Groomsmen Gifts" },
        { id: 2005, name: "Customized Gifts" },
      ],
    },
    {
      name: "Astrologers",
      id: 21,
      image:
        "https://images.unsplash.com/photo-1515942661900-94b3d1972591?q=80&w=200&h=120&fit=crop",
      subtypes: [
        { id: 2101, name: "Wedding Date Selection" },
        { id: 2102, name: "Horoscope Matching" },
        { id: 2103, name: "Muhurat Consultation" },
        { id: 2104, name: "Vedic Astrology" },
        { id: 2105, name: "Numerology" },
      ],
    },
    {
      name: "Honeymoon Planners",
      id: 22,
      image:
        "https://images.unsplash.com/photo-1507525428034-b723cf961d3e?q=80&w=200&h=120&fit=crop",
      subtypes: [
        { id: 2201, name: "Domestic Packages" },
        { id: 2202, name: "International Packages" },
        { id: 2203, name: "Luxury Honeymoons" },
        { id: 2204, name: "Adventure Honeymoons" },
        { id: 2205, name: "Cruise Honeymoons" },
      ],
    },
    {
      name: "Bridal Shower",
      id: 23,
      image:
        "https://images.unsplash.com/photo-1556035511-3168381ea4d4?q=80&w=200&h=120&fit=crop",
      subtypes: [
        { id: 2301, name: "Bridal Shower Planning" },
        { id: 2302, name: "Themed Bridal Showers" },
        { id: 2303, name: "Bridal Shower Venues" },
        { id: 2304, name: "Bridal Shower Games" },
        { id: 2305, name: "Bridal Shower Catering" },
      ],
    },
    {
      name: "Baby Shower",
      id: 24,
      image:
        "https://images.unsplash.com/photo-1544006659-f0b21884ce1d?q=80&w=200&h=120&fit=crop",
      subtypes: [
        { id: 2401, name: "Baby Shower Planning" },
        { id: 2402, name: "Themed Baby Showers" },
        { id: 2403, name: "Baby Shower Venues" },
        { id: 2404, name: "Baby Shower Games" },
        { id: 2405, name: "Baby Shower Catering" },
      ],
    },
    {
      name: "Bachelor Party",
      id: 25,
      image:
        "https://images.unsplash.com/photo-1575444758702-4a6b9222336e?q=80&w=200&h=120&fit=crop",
      subtypes: [
        { id: 2501, name: "Bachelor Party Planning" },
        { id: 2502, name: "Bachelor Party Venues" },
        { id: 2503, name: "Adventure Activities" },
        { id: 2504, name: "Party Games" },
        { id: 2505, name: "Party Packages" },
      ],
    },
    {
      name: "Wedding Planner",
      id: 26,
      image:
        "https://images.unsplash.com/photo-1469371670807-013ccf25f16a?q=80&w=200&h=120&fit=crop",
      subtypes: [
        { id: 2601, name: "Full Service Planning" },
        { id: 2602, name: "Month-of Coordination" },
        { id: 2603, name: "Destination Wedding Planning" },
        { id: 2604, name: "Budget Planning" },
        { id: 2605, name: "Vendor Management" },
      ],
    },
    {
      name: "Others",
      id: 27,
      image:
        "https://images.unsplash.com/photo-1523438885200-e635ba2c371e?q=80&w=200&h=120&fit=crop",
      subtypes: [
        { id: 2701, name: "Wedding Insurance" },
        { id: 2702, name: "Wedding Loans" },
        { id: 2703, name: "Legal Services" },
        { id: 2704, name: "Name Change Services" },
        { id: 2705, name: "Custom Services" },
      ],
    },
  ];

  return (
    <div className={isClient ? "flex flex-col min-h-screen bg-white w-full" : "opacity-0"}>
      {/* Top Navigation Bar */}
      <TopNavigation />

      <div className="flex flex-1 bg-white">
        <SideNavigation
          expanded={sidebarExpanded}
          onExpand={() => setSidebarExpanded(true)}
          onCollapse={() => setSidebarExpanded(false)}
        />

        {/* Main Content */}
        <main
          className={`flex-1 p-4 bg-white mt-20 ${sidebarExpanded ? "md:ml-48" : "md:ml-20"}`}
          style={{
            transition: "all 300ms ease-in-out",
            overflowY: "auto",
            overflowX: "hidden",
          }}
        >
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-6">
              <h1 className="text-2xl font-bold">Vendor Category</h1>
              <div className="text-sm text-gray-500">
                Showing {categories.length} categories
              </div>
            </div>

            {/* Categories Grid with Dropdowns */}
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {categories.map((category) => (
                <div key={category.id} className="flex flex-col">
                  {/* Category Card */}
                  <div
                    className="rounded-t-[10px] overflow-hidden shadow-md relative hover:brightness-105 transition-all cursor-pointer border border-[#D9D9D9]"
                    style={{
                      height: "160px",
                      boxShadow: "0px 4px 14px 0px #00000026, inset 4px 4px 14px 0px #9747FF1A",
                      backgroundImage: `url(${category.image})`,
                      backgroundSize: "cover",
                      backgroundPosition: "center",
                      borderBottomLeftRadius: expandedCategory === category.id ? 0 : '10px',
                      borderBottomRightRadius: expandedCategory === category.id ? 0 : '10px',
                    }}
                    onClick={() => toggleCategory(category.id)}
                  >
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                    <div className="absolute bottom-0 left-0 right-0 p-4 flex justify-between items-center">
                      <div className="text-white font-medium text-lg">{category.name}</div>
                      {category.subtypes && category.subtypes.length > 0 && (
                        <div className="text-white bg-black/30 rounded-full p-1">
                          {expandedCategory === category.id ? (
                            <ChevronUp size={20} />
                          ) : (
                            <ChevronDown size={20} />
                          )}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Subtypes Dropdown */}
                  {category.subtypes && expandedCategory === category.id && (
                    <div className="bg-white border border-t-0 border-[#D9D9D9] rounded-b-[10px] shadow-md overflow-hidden transition-all">
                      <div className="p-2 max-h-[200px] overflow-y-auto">
                        {category.subtypes.map((subtype) => (
                          <div
                            key={subtype.id}
                            className="p-2 hover:bg-gray-100 cursor-pointer rounded-md transition-colors"
                            onClick={() => console.log(`Selected ${category.name} > ${subtype.name}`)}
                          >
                            {subtype.name}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </main>
      </div>

      <MobileNavigation />
    </div>
  );
}
