"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d34271475a7d\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xca29tbWlcXE9uZURyaXZlXFxEZXNrdG9wXFxXRURaQVRfXFxmcm9udGVuZFxcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImQzNDI3MTQ3NWE3ZFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./contexts/UploadContexts.tsx":
/*!*************************************!*\
  !*** ./contexts/UploadContexts.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UploadProvider: () => (/* binding */ UploadProvider),\n/* harmony export */   useUpload: () => (/* binding */ useUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/api */ \"(app-pages-browser)/./services/api.ts\");\n/* harmony import */ var _utils_uploadUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/uploadUtils */ \"(app-pages-browser)/./utils/uploadUtils.ts\");\n// contexts/UploadContext.tsx\n/* __next_internal_client_entry_do_not_use__ UploadProvider,useUpload auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Initial state\nconst initialState = {\n    file: null,\n    thumbnail: null,\n    mediaType: 'photo',\n    mediaSubtype: 'story',\n    category: '',\n    title: '',\n    description: '',\n    tags: [],\n    detailFields: {},\n    isMoments: false,\n    step: 'selecting',\n    progress: 0,\n    isUploading: false\n};\n// Create context\nconst UploadContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Provider component\nconst UploadProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialState);\n    // Set file and automatically determine media type\n    const setFile = async (file)=>{\n        if (!file) {\n            setState({\n                ...state,\n                file: null\n            });\n            return;\n        }\n        const isVideo = file.type.startsWith('video/');\n        const mediaType = isVideo ? 'video' : 'photo';\n        // Default media subtypes based on media type\n        const mediaSubtype = isVideo ? 'glimpse' : 'post'; // Default to 'post' for photos\n        setState({\n            ...state,\n            file,\n            mediaType,\n            mediaSubtype,\n            step: 'details'\n        });\n    };\n    // Set thumbnail image\n    const setThumbnail = (thumbnail)=>{\n        setState({\n            ...state,\n            thumbnail\n        });\n    };\n    const setMediaType = (type)=>{\n        // Don't set a default category - let the user's selection flow through the process\n        // Just update the media type\n        setState({\n            ...state,\n            mediaType: type\n        });\n    };\n    const setMediaSubtype = (mediaSubtype)=>{\n        setState({\n            ...state,\n            mediaSubtype\n        });\n    };\n    // Keep the old function for backward compatibility\n    const setCategory = (category)=>{\n        console.log('UPLOAD CONTEXT - setCategory is deprecated, use setMediaSubtype instead');\n        setState({\n            ...state,\n            mediaSubtype: category\n        });\n    };\n    const setTitle = (title)=>{\n        // Ensure title is not empty\n        if (!title || !title.trim()) {\n            console.warn('Attempted to set empty title');\n            return;\n        }\n        console.log('Setting title to:', title.trim());\n        setState({\n            ...state,\n            title: title.trim()\n        });\n    };\n    const setDescription = (description)=>{\n        setState({\n            ...state,\n            description\n        });\n    };\n    const addTag = (tag)=>{\n        if (tag.trim() && !state.tags.includes(tag.trim())) {\n            setState({\n                ...state,\n                tags: [\n                    ...state.tags,\n                    tag.trim()\n                ]\n            });\n        }\n    };\n    const removeTag = (tag)=>{\n        setState({\n            ...state,\n            tags: state.tags.filter((t)=>t !== tag)\n        });\n    };\n    const setDetailField = (field, value)=>{\n        // For moments (stories), skip all localStorage operations and vendor field handling\n        if (state.mediaSubtype === 'story') {\n            console.log(\"UPLOAD CONTEXT - Moments detected, setting field \".concat(field, \" without localStorage operations\"));\n            setState((prevState)=>({\n                    ...prevState,\n                    detailFields: {\n                        ...prevState.detailFields,\n                        [field]: value\n                    }\n                }));\n            return;\n        }\n        // Special handling for video_category to ensure it's properly set\n        if (field === 'video_category') {\n            console.log(\"UPLOAD CONTEXT - Setting video_category to: \".concat(value));\n            // Store video_category in localStorage\n            try {\n                localStorage.setItem('wedzat_video_category', value);\n                console.log(\"UPLOAD CONTEXT - Stored video_category in localStorage: \".concat(value));\n            } catch (error) {\n                console.error('UPLOAD CONTEXT - Failed to store video_category in localStorage:', error);\n            }\n        }\n        // Special handling for vendor fields to ensure they're properly set\n        if (field.startsWith('vendor_')) {\n            // If this is a vendor field, update the vendor details in localStorage\n            try {\n                // Get existing vendor details from localStorage\n                const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n                let vendorDetails = storedVendorDetails ? JSON.parse(storedVendorDetails) : {};\n                // If this is a vendor name field, extract the vendor type and update the name\n                if (field.endsWith('_name')) {\n                    const vendorType = field.replace('vendor_', '').replace('_name', '');\n                    // Check if we already have this vendor in the details\n                    if (!vendorDetails[vendorType]) {\n                        vendorDetails[vendorType] = {\n                            name: value,\n                            mobileNumber: ''\n                        };\n                    } else {\n                        vendorDetails[vendorType].name = value;\n                    }\n                }\n                // If this is a vendor contact field, extract the vendor type and update the contact\n                if (field.endsWith('_contact')) {\n                    const vendorType = field.replace('vendor_', '').replace('_contact', '');\n                    // Check if we already have this vendor in the details\n                    if (!vendorDetails[vendorType]) {\n                        vendorDetails[vendorType] = {\n                            name: '',\n                            mobileNumber: value\n                        };\n                    } else {\n                        vendorDetails[vendorType].mobileNumber = value;\n                    }\n                }\n                // Store the updated vendor details in localStorage\n                localStorage.setItem('wedzat_vendor_details', JSON.stringify(vendorDetails));\n            } catch (error) {\n                console.error('UPLOAD CONTEXT - Failed to update vendor details in localStorage:', error);\n            }\n        }\n        // Create a new detailFields object with the updated field\n        const updatedDetailFields = {\n            ...state.detailFields,\n            [field]: value\n        };\n        // Update the state with the new detailFields\n        setState((prevState)=>({\n                ...prevState,\n                detailFields: updatedDetailFields\n            }));\n        // For video_category, log the updated state after a short delay\n        if (field === 'video_category') {\n            setTimeout(()=>{\n                console.log(\"UPLOAD CONTEXT - Verified video_category is set to: \".concat(state.detailFields.video_category || 'Not set'));\n            }, 100);\n        }\n    };\n    // Set all personal details at once and update the title and related detail fields\n    const setPersonalDetails = (details)=>{\n        // console.log('Setting all personal details:', details);\n        // Validate caption/title\n        if (!details.caption || !details.caption.trim()) {\n            console.warn('Attempted to set personal details with empty caption/title');\n            return;\n        }\n        // Update title\n        const title = details.caption.trim();\n        // console.log('Setting title from personal details:', title);\n        // Update detail fields with backend-compatible field names\n        const updatedDetailFields = {\n            ...state.detailFields,\n            'personal_caption': title,\n            'personal_life_partner': details.lifePartner || '',\n            'personal_wedding_style': details.weddingStyle || '',\n            'personal_place': details.place || '',\n            'personal_event_type': details.eventType || '',\n            'personal_budget': details.budget || '',\n            // Keep legacy field names for compatibility\n            'lifePartner': details.lifePartner || '',\n            'location': details.place || '',\n            'place': details.place || '',\n            'eventType': details.eventType || '',\n            'budget': details.budget || '',\n            'weddingStyle': details.weddingStyle || ''\n        };\n        // Update state with all changes at once\n        setState({\n            ...state,\n            title,\n            description: details.weddingStyle || '',\n            detailFields: updatedDetailFields\n        });\n        // Log the description being set\n        console.log('Setting description to:', details.weddingStyle || '');\n        // Log the updated state after a short delay to ensure state has updated\n        setTimeout(()=>{\n            console.log('Personal details set successfully');\n            console.log('Title after update:', title);\n        }, 0);\n    };\n    // Set all vendor details at once and update the related detail fields\n    const setVendorDetails = (vendorDetails)=>{\n        console.log('UPLOAD CONTEXT - Setting all vendor details:', JSON.stringify(vendorDetails, null, 2));\n        // Create a copy of the current detail fields\n        const updatedDetailFields = {\n            ...state.detailFields\n        };\n        // Save the video_category if it exists\n        const videoCategory = state.detailFields.video_category;\n        console.log('UPLOAD CONTEXT - Preserving video_category:', videoCategory);\n        // Count how many complete vendor details we're receiving\n        const completeVendorCount = Object.entries(vendorDetails).filter((param)=>{\n            let [_, detail] = param;\n            return detail && detail.name && detail.mobileNumber && detail.name.trim() !== '' && detail.mobileNumber.trim() !== '';\n        }).length;\n        console.log(\"UPLOAD CONTEXT - Received \".concat(completeVendorCount, \" complete vendor details\"));\n        // Process vendor details\n        Object.entries(vendorDetails).forEach((param)=>{\n            let [vendorType, details] = param;\n            if (details) {\n                // Only include vendors that have BOTH name AND mobile number\n                if (details.name && details.mobileNumber && details.name.trim() !== '' && details.mobileNumber.trim() !== '') {\n                    // Handle special mappings for makeup_artist and decoration\n                    let backendVendorType = vendorType;\n                    // Map frontend field names to backend field names\n                    if (vendorType === 'makeupArtist') {\n                        backendVendorType = 'makeup_artist';\n                    } else if (vendorType === 'decorations') {\n                        backendVendorType = 'decoration';\n                    }\n                    // Store vendor details in the format expected by the backend\n                    updatedDetailFields[\"vendor_\".concat(backendVendorType, \"_name\")] = details.name || '';\n                    updatedDetailFields[\"vendor_\".concat(backendVendorType, \"_contact\")] = details.mobileNumber || '';\n                    // Always store with the original vendorType to ensure we count it correctly\n                    // This ensures both frontend and backend field names are present\n                    // This is especially important for Edge browser compatibility\n                    if (vendorType !== backendVendorType) {\n                        updatedDetailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name || '';\n                        updatedDetailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber || '';\n                    }\n                    // Also store with common vendor types to ensure cross-browser compatibility\n                    if (vendorType === 'makeupArtist' || vendorType === 'makeup_artist') {\n                        // Ensure both makeupArtist and makeup_artist are present\n                        updatedDetailFields[\"vendor_makeupArtist_name\"] = details.name || '';\n                        updatedDetailFields[\"vendor_makeupArtist_contact\"] = details.mobileNumber || '';\n                        updatedDetailFields[\"vendor_makeup_artist_name\"] = details.name || '';\n                        updatedDetailFields[\"vendor_makeup_artist_contact\"] = details.mobileNumber || '';\n                    } else if (vendorType === 'decorations' || vendorType === 'decoration') {\n                        // Ensure both decorations and decoration are present\n                        updatedDetailFields[\"vendor_decorations_name\"] = details.name || '';\n                        updatedDetailFields[\"vendor_decorations_contact\"] = details.mobileNumber || '';\n                        updatedDetailFields[\"vendor_decoration_name\"] = details.name || '';\n                        updatedDetailFields[\"vendor_decoration_contact\"] = details.mobileNumber || '';\n                    }\n                // console.log(`UPLOAD CONTEXT - Setting vendor detail: ${vendorType} (backend: ${backendVendorType})`, {\n                //   name: details.name || '',\n                //   contact: details.mobileNumber || ''\n                // });\n                } else {\n                    console.log(\"UPLOAD CONTEXT - Skipping incomplete vendor detail: \".concat(vendorType));\n                }\n            }\n        });\n        // Don't update state here - we'll do it after restoring the video_category\n        // console.log('UPLOAD CONTEXT - Vendor details set successfully');\n        // console.log('UPLOAD CONTEXT - Total detail fields count:', Object.keys(updatedDetailFields).length);\n        // Count how many complete vendor details we have after processing\n        let completeVendorPairs = 0;\n        const vendorNames = new Set();\n        const vendorContacts = new Set();\n        // Log all vendor details for debugging\n        Object.keys(updatedDetailFields).forEach((key)=>{\n            if (key.startsWith('vendor_')) {\n                // console.log(`UPLOAD CONTEXT - Vendor field: ${key} = ${updatedDetailFields[key]}`);\n                if (key.endsWith('_name')) {\n                    vendorNames.add(key.replace('vendor_', '').replace('_name', ''));\n                } else if (key.endsWith('_contact')) {\n                    vendorContacts.add(key.replace('vendor_', '').replace('_contact', ''));\n                }\n            }\n        });\n        // Count complete pairs (both name and contact)\n        vendorNames.forEach((name)=>{\n            if (vendorContacts.has(name)) {\n                completeVendorPairs++;\n            }\n        });\n        // console.log(`UPLOAD CONTEXT - Found ${completeVendorPairs} complete vendor pairs (name + contact)`);\n        // console.log(`UPLOAD CONTEXT - Vendor names: ${Array.from(vendorNames).join(', ')}`);\n        // console.log(`UPLOAD CONTEXT - Vendor contacts: ${Array.from(vendorContacts).join(', ')}`);\n        // Restore the video_category if it exists\n        if (videoCategory) {\n            console.log('UPLOAD CONTEXT - Restoring video_category:', videoCategory);\n            updatedDetailFields.video_category = videoCategory;\n        }\n        // Log the detail fields before updating state\n        console.log('UPLOAD CONTEXT - Detail fields before update:', JSON.stringify(state.detailFields, null, 2));\n        console.log('UPLOAD CONTEXT - Updated detail fields to be set:', JSON.stringify(updatedDetailFields, null, 2));\n        // Create a completely new state object to ensure Edge updates correctly\n        const newState = {\n            ...state,\n            detailFields: {\n                ...updatedDetailFields\n            }\n        };\n        // For Edge browser compatibility, directly set the vendor fields in the state\n        // This is a workaround for Edge where the state update doesn't properly preserve vendor details\n        if ( true && /Edge|Edg/.test(window.navigator.userAgent)) {\n            console.log('UPLOAD CONTEXT - Edge browser detected, using direct state update');\n            // Create a direct reference to the state object\n            const directState = state;\n            // Directly set the detailFields\n            directState.detailFields = {\n                ...updatedDetailFields\n            };\n            // Log the direct update\n            console.log('UPLOAD CONTEXT - Direct state update:', JSON.stringify(directState.detailFields, null, 2));\n        }\n        // Update the state with the updated detail fields\n        setState(newState);\n        // Force a re-render to ensure the state is updated\n        setTimeout(()=>{\n            console.log('UPLOAD CONTEXT - Vendor details set successfully');\n            console.log('UPLOAD CONTEXT - Total detail fields count:', Object.keys(state.detailFields).length);\n            console.log('UPLOAD CONTEXT - Detail fields after update:', JSON.stringify(state.detailFields, null, 2));\n            // Double-check that the vendor details were set correctly\n            const vendorNameFields = Object.keys(state.detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_name'));\n            const vendorContactFields = Object.keys(state.detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_contact'));\n            console.log('UPLOAD CONTEXT - Vendor name fields count:', vendorNameFields.length);\n            console.log('UPLOAD CONTEXT - Vendor contact fields count:', vendorContactFields.length);\n        }, 100);\n    };\n    const resetUpload = ()=>{\n        console.log('UPLOAD CONTEXT - Completely resetting upload state');\n        // Create a fresh copy of the initial state\n        const freshState = {\n            file: null,\n            thumbnail: null,\n            mediaType: '',\n            mediaSubtype: '',\n            title: '',\n            description: '',\n            tags: [],\n            detailFields: {},\n            step: 'select',\n            duration: 0\n        };\n        // Set the state to the fresh state\n        setState(freshState);\n        // Log the reset\n        console.log('UPLOAD CONTEXT - Upload state reset to:', JSON.stringify(freshState, null, 2));\n    };\n    // Helper function to detect Edge browser\n    const isEdgeBrowser = ()=>{\n        if (true) {\n            return /Edge|Edg/.test(window.navigator.userAgent);\n        }\n        return false;\n    };\n    const validateForm = ()=>{\n        // For moments (stories), only validate file and title - skip all other validations\n        if (state.mediaSubtype === 'story') {\n            console.log('VALIDATE FORM - Moments/Stories detected, using simplified validation');\n            // Check if file is selected\n            if (!state.file) {\n                console.log('Validation failed: No file selected for moments');\n                return {\n                    isValid: false,\n                    error: 'Please select a file to upload'\n                };\n            }\n            // Validate file type and size\n            const fileValidation = (0,_utils_uploadUtils__WEBPACK_IMPORTED_MODULE_3__.validateFile)(state.file, state.mediaType);\n            if (!fileValidation.isValid) {\n                console.log('Validation failed: File validation failed for moments', fileValidation);\n                return fileValidation;\n            }\n            // Check if title is provided\n            if (!state.title || !state.title.trim()) {\n                console.log('Validation failed: Title is empty for moments');\n                return {\n                    isValid: false,\n                    error: 'Please provide a title for your upload'\n                };\n            }\n            console.log('VALIDATE FORM - Moments validation passed');\n            return {\n                isValid: true\n            };\n        }\n        // Check if we're running in Edge browser\n        const isEdge = isEdgeBrowser();\n        if (isEdge) {\n            console.log('VALIDATE FORM - Running in Edge browser, applying special handling');\n        }\n        // console.log('VALIDATE FORM - Validating form with state:', {\n        //   file: state.file ? state.file.name : 'No file',\n        //   mediaType: state.mediaType,\n        //   title: state.title,\n        //   description: state.description,\n        //   detailFieldsCount: Object.keys(state.detailFields).length,\n        //   tags: state.tags\n        // });\n        // Log all detail fields for debugging\n        // console.log('VALIDATE FORM - All detail fields:', JSON.stringify(state.detailFields, null, 2));\n        // Check if file is selected\n        if (!state.file) {\n            // console.log('Validation failed: No file selected');\n            return {\n                isValid: false,\n                error: 'Please select a file to upload'\n            };\n        }\n        // Validate file type and size\n        const fileValidation = (0,_utils_uploadUtils__WEBPACK_IMPORTED_MODULE_3__.validateFile)(state.file, state.mediaType);\n        if (!fileValidation.isValid) {\n            console.log('Validation failed: File validation failed', fileValidation);\n            return fileValidation;\n        }\n        // Check if title is provided\n        if (!state.title || !state.title.trim()) {\n            console.log('Validation failed: Title is empty');\n            return {\n                isValid: false,\n                error: 'Please provide a title for your upload'\n            };\n        }\n        // First, try to get vendor details from localStorage\n        let detailFields = {\n            ...state.detailFields\n        };\n        let vendorDetailsFromStorage = null;\n        try {\n            const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n            if (storedVendorDetails) {\n                vendorDetailsFromStorage = JSON.parse(storedVendorDetails);\n                console.log('VALIDATE FORM - Retrieved vendor details from localStorage:', storedVendorDetails);\n                // Process vendor details from localStorage\n                if (vendorDetailsFromStorage && Object.keys(vendorDetailsFromStorage).length > 0) {\n                    Object.entries(vendorDetailsFromStorage).forEach((param)=>{\n                        let [vendorType, details] = param;\n                        if (details && details.name && details.mobileNumber) {\n                            // Add to detailFields\n                            detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                            detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                            console.log(\"VALIDATE FORM - Added vendor \".concat(vendorType, \" with name: \").concat(details.name, \" and contact: \").concat(details.mobileNumber));\n                            // Also add normalized versions\n                            const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                            if (normalizedType !== vendorType) {\n                                detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                                detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                                console.log(\"VALIDATE FORM - Also added normalized vendor \".concat(normalizedType));\n                            }\n                        }\n                    });\n                    console.log('VALIDATE FORM - Updated detail fields with vendor details from localStorage');\n                }\n            }\n        } catch (error) {\n            console.error('VALIDATE FORM - Failed to retrieve vendor details from localStorage:', error);\n        }\n        // Now use the updated detailFields for validation\n        console.log('Detail fields count:', Object.keys(detailFields).length);\n        console.log('Detail fields present:', Object.keys(detailFields));\n        console.log('Detail fields values:', detailFields);\n        // For videos, check if required vendor details are present based on video category\n        if (state.mediaType === 'video') {\n            // Determine required vendor count based on video category\n            const videoCategory = detailFields.video_category || 'my_wedding';\n            const requiredVendorCount = videoCategory === 'wedding_vlog' ? 1 : 4;\n            console.log(\"VALIDATE FORM - Video category: \".concat(videoCategory, \", Required vendors: \").concat(requiredVendorCount));\n            // Special handling for Edge browser\n            if (isEdge) {\n                console.log('VALIDATE FORM - Edge browser detected, checking vendor details directly');\n                // In Edge, we'll count vendor details directly from the detailFields\n                const vendorNameFields = Object.keys(detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_name'));\n                const vendorContactFields = Object.keys(detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_contact'));\n                console.log('VALIDATE FORM - Edge browser - Vendor name fields:', vendorNameFields);\n                console.log('VALIDATE FORM - Edge browser - Vendor contact fields:', vendorContactFields);\n                // If we have at least 4 vendor name fields and 4 vendor contact fields, assume we have enough vendors\n                if (vendorNameFields.length >= 4 && vendorContactFields.length >= 4) {\n                    console.log('VALIDATE FORM - Edge browser - Found enough vendor fields, validation passed');\n                    return {\n                        isValid: true\n                    };\n                }\n                // Edge browser workaround - if we're uploading a video, assume vendor details are valid\n                // This is a temporary workaround for Edge browser compatibility\n                if (state.mediaType === 'video') {\n                    console.log('VALIDATE FORM - Edge browser workaround: Assuming vendor details are valid for video upload');\n                    return {\n                        isValid: true\n                    };\n                }\n            }\n            console.log('VALIDATE FORM - Checking vendor details for video upload');\n            console.log('VALIDATE FORM - Detail fields:', JSON.stringify(detailFields, null, 2));\n            // Count how many complete vendor details we have (where BOTH name AND contact are provided)\n            let validVendorCount = 0;\n            // Include both frontend and backend field names to ensure we count all vendor details\n            const vendorPrefixes = [\n                'venue',\n                'photographer',\n                'makeup_artist',\n                'makeupArtist',\n                'decoration',\n                'decorations',\n                'caterer',\n                'additional1',\n                'additional2',\n                'additionalVendor1',\n                'additionalVendor2'\n            ];\n            console.log('VALIDATE FORM - Checking these vendor prefixes:', vendorPrefixes.join(', '));\n            // Keep track of which vendors we've already counted to avoid duplicates\n            const countedVendors = new Set();\n            // First, log all vendor-related fields for debugging\n            const vendorFields = Object.keys(detailFields).filter((key)=>key.startsWith('vendor_'));\n            console.log('VALIDATE FORM - All vendor fields:', vendorFields.join(', '));\n            for (const prefix of vendorPrefixes){\n                // Skip if we've already counted this vendor (to avoid counting both makeupArtist and makeup_artist)\n                const normalizedPrefix = prefix === 'makeupArtist' ? 'makeup_artist' : prefix === 'decorations' ? 'decoration' : prefix;\n                if (countedVendors.has(normalizedPrefix)) {\n                    console.log(\"VALIDATE FORM - Skipping \".concat(prefix, \" as we already counted \").concat(normalizedPrefix));\n                    continue;\n                }\n                const nameField = \"vendor_\".concat(prefix, \"_name\");\n                const contactField = \"vendor_\".concat(prefix, \"_contact\");\n                console.log(\"VALIDATE FORM - Checking vendor \".concat(prefix, \":\"), {\n                    nameField,\n                    nameValue: detailFields[nameField],\n                    contactField,\n                    contactValue: detailFields[contactField],\n                    hasName: !!detailFields[nameField],\n                    hasContact: !!detailFields[contactField]\n                });\n                if (detailFields[nameField] && detailFields[contactField]) {\n                    validVendorCount++;\n                    countedVendors.add(normalizedPrefix);\n                    console.log(\"VALIDATE FORM - Found valid vendor: \".concat(prefix, \" with name: \").concat(detailFields[nameField], \" and contact: \").concat(detailFields[contactField]));\n                }\n            }\n            // Also check for any other vendor_ fields that might have been added\n            console.log('VALIDATE FORM - Checking for additional vendor fields');\n            Object.keys(detailFields).forEach((key)=>{\n                if (key.startsWith('vendor_') && key.endsWith('_name')) {\n                    const baseKey = key.replace('vendor_', '').replace('_name', '');\n                    const contactKey = \"vendor_\".concat(baseKey, \"_contact\");\n                    // Skip if we've already counted this vendor\n                    const normalizedPrefix = baseKey === 'makeupArtist' ? 'makeup_artist' : baseKey === 'decorations' ? 'decoration' : baseKey;\n                    console.log(\"VALIDATE FORM - Checking additional vendor \".concat(baseKey, \":\"), {\n                        normalizedPrefix,\n                        alreadyCounted: countedVendors.has(normalizedPrefix),\n                        hasName: !!detailFields[key],\n                        hasContact: !!detailFields[contactKey]\n                    });\n                    if (!countedVendors.has(normalizedPrefix) && detailFields[key] && detailFields[contactKey]) {\n                        validVendorCount++;\n                        countedVendors.add(normalizedPrefix);\n                        console.log(\"VALIDATE FORM - Found additional valid vendor: \".concat(baseKey, \" with name: \").concat(detailFields[key], \" and contact: \").concat(detailFields[contactKey]));\n                    }\n                }\n            });\n            console.log(\"VALIDATE FORM - Total valid vendor count: \".concat(validVendorCount));\n            console.log(\"VALIDATE FORM - Counted vendors: \".concat(Array.from(countedVendors).join(', ')));\n            // Special handling for Edge browser - if we have vendor fields but they're not being counted correctly\n            let edgeVendorNameFields = Object.keys(detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_name'));\n            let edgeVendorContactFields = Object.keys(detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_contact'));\n            console.log('VALIDATE FORM - Edge browser check - Vendor name fields:', edgeVendorNameFields);\n            console.log('VALIDATE FORM - Edge browser check - Vendor contact fields:', edgeVendorContactFields);\n            // If we have at least required vendor name fields and contact fields, but validVendorCount is less than required,\n            // this is likely an Edge browser issue where the fields aren't being properly counted\n            if (validVendorCount < requiredVendorCount && edgeVendorNameFields.length >= requiredVendorCount && edgeVendorContactFields.length >= requiredVendorCount) {\n                console.log('VALIDATE FORM - Edge browser workaround: Found enough vendor fields but they were not counted correctly');\n                console.log('VALIDATE FORM - Vendor name fields:', edgeVendorNameFields.join(', '));\n                console.log('VALIDATE FORM - Vendor contact fields:', edgeVendorContactFields.join(', '));\n                // Count unique vendor prefixes (excluding the _name/_contact suffix)\n                const vendorPrefixSet = new Set();\n                edgeVendorNameFields.forEach((field)=>{\n                    const prefix = field.replace('vendor_', '').replace('_name', '');\n                    if (edgeVendorContactFields.includes(\"vendor_\".concat(prefix, \"_contact\"))) {\n                        vendorPrefixSet.add(prefix);\n                    }\n                });\n                const uniqueVendorCount = vendorPrefixSet.size;\n                console.log(\"VALIDATE FORM - Unique vendor count: \".concat(uniqueVendorCount));\n                if (uniqueVendorCount >= requiredVendorCount) {\n                    console.log(\"VALIDATE FORM - Edge browser workaround: Found at least \".concat(requiredVendorCount, \" unique vendors with both name and contact\"));\n                    validVendorCount = uniqueVendorCount;\n                }\n            }\n            // Log the vendor field counts\n            console.log('VALIDATE FORM - Vendor name fields count:', edgeVendorNameFields.length);\n            console.log('VALIDATE FORM - Vendor contact fields count:', edgeVendorContactFields.length);\n            if (validVendorCount < requiredVendorCount) {\n                console.log('VALIDATE FORM - Validation failed: Not enough vendor details', validVendorCount);\n                const categoryText = videoCategory === 'wedding_vlog' ? 'wedding vlog' : 'my wedding';\n                return {\n                    isValid: false,\n                    error: \"At least \".concat(requiredVendorCount, \" complete vendor detail\").concat(requiredVendorCount > 1 ? 's' : '', \" (with both name and contact) \").concat(requiredVendorCount > 1 ? 'are' : 'is', \" required for \").concat(categoryText, \" videos. You provided \").concat(validVendorCount, \"/\").concat(requiredVendorCount, \".\")\n                };\n            } else {\n                console.log('VALIDATE FORM - Vendor validation passed with', validVendorCount, 'vendors');\n            }\n        }\n        // Just log the detail fields count for now\n        console.log('Detail fields count:', Object.keys(state.detailFields).length);\n        // Log the detail fields that are present\n        console.log('Detail fields present:', Object.keys(state.detailFields));\n        console.log('Detail fields values:', state.detailFields);\n        console.log('Form validation passed');\n        return {\n            isValid: true\n        };\n    };\n    // Start upload with a specific category and video_category (used when correcting the category)\n    const startUploadWithCategory = async (category, videoCategory)=>{\n        console.log(\"Starting upload process with corrected category: \".concat(category));\n        console.log(\"Using video_category: \".concat(videoCategory || 'Not provided'));\n        // Try to get vendor details from localStorage\n        let vendorDetails = {};\n        try {\n            const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n            if (storedVendorDetails) {\n                vendorDetails = JSON.parse(storedVendorDetails);\n                console.log('UPLOAD CONTEXT - Retrieved vendor details from localStorage');\n            }\n        } catch (error) {\n            console.error('UPLOAD CONTEXT - Failed to retrieve vendor details from localStorage:', error);\n        }\n        // Create detail fields from vendor details\n        const detailFields = {\n            ...state.detailFields\n        };\n        // Process vendor details to create detail fields\n        if (Object.keys(vendorDetails).length > 0) {\n            console.log('UPLOAD CONTEXT - Processing vendor details from localStorage:', JSON.stringify(vendorDetails));\n            // Track how many complete vendor details we've added\n            let completeVendorCount = 0;\n            Object.entries(vendorDetails).forEach((param)=>{\n                let [vendorType, details] = param;\n                if (details && details.name && details.mobileNumber) {\n                    detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                    detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                    console.log(\"UPLOAD CONTEXT - Added vendor \".concat(vendorType, \" with name: \").concat(details.name, \" and contact: \").concat(details.mobileNumber));\n                    completeVendorCount++;\n                    // Also set normalized versions\n                    const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                    if (normalizedType !== vendorType) {\n                        detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                        detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                        console.log(\"UPLOAD CONTEXT - Also added normalized vendor \".concat(normalizedType));\n                    }\n                }\n            });\n            console.log(\"UPLOAD CONTEXT - Added \".concat(completeVendorCount, \" complete vendor details from localStorage to detailFields\"));\n            console.log('UPLOAD CONTEXT - Detail fields after adding vendors:', JSON.stringify(detailFields));\n        }\n        // Update the state with the corrected category and video_category if provided\n        const updatedState = {\n            ...state,\n            mediaSubtype: category,\n            category: category,\n            detailFields: detailFields\n        };\n        // If videoCategory is provided, update the detailFields\n        if (videoCategory) {\n            updatedState.detailFields.video_category = videoCategory;\n            console.log(\"Setting video_category in state to: \".concat(videoCategory));\n            // Also store in localStorage\n            try {\n                localStorage.setItem('wedzat_video_category', videoCategory);\n                console.log('UPLOAD CONTEXT - Stored video_category in localStorage:', videoCategory);\n            } catch (error) {\n                console.error('UPLOAD CONTEXT - Failed to store video_category in localStorage:', error);\n            }\n        }\n        // Apply the state update immediately\n        setState(updatedState);\n        // Then start the upload process with the updated category\n        const validation = validateForm();\n        if (!validation.isValid) {\n            console.log('Upload validation failed:', validation.error);\n            setState({\n                ...state,\n                mediaSubtype: category,\n                error: validation.error,\n                step: 'error'\n            });\n            return;\n        }\n        if (!state.file) {\n            console.log('No file to upload');\n            return;\n        }\n        setState({\n            ...state,\n            mediaSubtype: category,\n            isUploading: true,\n            progress: 0,\n            step: 'uploading',\n            error: undefined\n        });\n        try {\n            console.log('UPLOAD CONTEXT - Starting upload process with corrected category...');\n            console.log('UPLOAD CONTEXT - Upload details:', {\n                file: state.file ? state.file.name : 'No file',\n                fileSize: state.file ? Math.round(state.file.size / (1024 * 1024) * 100) / 100 + ' MB' : 'N/A',\n                mediaType: state.mediaType,\n                category: category,\n                title: state.title,\n                videoCategory: videoCategory || 'Not set'\n            });\n            // Log the video_category that will be used\n            console.log('UPLOAD CONTEXT - Using explicit video_category:', videoCategory);\n            console.log('UPLOAD CONTEXT - Video category from detail fields:', state.detailFields.video_category);\n            // Create a copy of the detail fields with the explicit video_category\n            const updatedDetailFields = {\n                ...state.detailFields\n            };\n            // If videoCategory is provided, use it\n            if (videoCategory) {\n                updatedDetailFields.video_category = videoCategory;\n                console.log('UPLOAD CONTEXT - Setting video_category in details to:', videoCategory);\n            }\n            // Use the upload service to handle the complete upload process with the corrected category\n            const result = await _services_api__WEBPACK_IMPORTED_MODULE_2__.uploadService.handleUpload(state.file, state.mediaType, category, state.title, state.description, state.tags, updatedDetailFields, state.duration, state.thumbnail, (progress)=>{\n                setState({\n                    ...state,\n                    mediaSubtype: category,\n                    progress\n                });\n            });\n            // Update the state with the upload result\n            setState({\n                ...state,\n                mediaSubtype: category,\n                isUploading: false,\n                progress: 100,\n                step: 'complete',\n                uploadResult: result\n            });\n        // Upload completed successfully\n        } catch (error) {\n            console.error('Upload failed:', error);\n            setState({\n                ...state,\n                mediaSubtype: category,\n                isUploading: false,\n                error: error instanceof Error ? error.message : 'Unknown error'\n            });\n            throw error;\n        }\n    };\n    // Perform the actual upload without vendor processing\n    const performUpload = async ()=>{\n        console.log('UPLOAD CONTEXT - Starting direct upload for moments');\n        // Simple validation for moments - only check file and title\n        if (!state.file) {\n            console.log('No file to upload');\n            setState({\n                ...state,\n                error: 'No file selected',\n                step: 'error'\n            });\n            return;\n        }\n        if (!state.title || !state.title.trim()) {\n            console.log('No title provided for moments');\n            setState({\n                ...state,\n                error: 'Please provide a title for your upload',\n                step: 'error'\n            });\n            return;\n        }\n        setState({\n            ...state,\n            isUploading: true,\n            progress: 0,\n            step: 'uploading',\n            error: undefined\n        });\n        try {\n            console.log('UPLOAD CONTEXT - Starting upload process for moments...');\n            console.log('UPLOAD CONTEXT - Upload details:', {\n                file: state.file ? state.file.name : 'No file',\n                fileSize: state.file ? Math.round(state.file.size / (1024 * 1024) * 100) / 100 + ' MB' : 'N/A',\n                mediaType: state.mediaType,\n                mediaSubtype: state.mediaSubtype,\n                title: state.title\n            });\n            // Use the upload service to handle the complete upload process\n            const result = await _services_api__WEBPACK_IMPORTED_MODULE_2__.uploadService.handleUpload(state.file, state.mediaType, state.mediaSubtype, state.title, state.description, state.tags, {}, state.duration, state.thumbnail, (progress)=>{\n                console.log(\"Upload progress: \".concat(progress, \"%\"));\n                setState((prevState)=>{\n                    // Only update if the new progress is greater\n                    if (progress > prevState.progress) {\n                        return {\n                            ...prevState,\n                            progress,\n                            // Change to processing state when we reach 80%\n                            step: progress >= 80 && progress < 100 ? 'processing' : prevState.step\n                        };\n                    }\n                    return prevState;\n                });\n            });\n            console.log('UPLOAD CONTEXT - Upload completed successfully for moments:', result);\n            setState({\n                ...state,\n                isUploading: false,\n                progress: 100,\n                step: 'success',\n                error: undefined\n            });\n        // Upload completed successfully\n        } catch (error) {\n            console.error('Upload failed for moments:', error);\n            setState({\n                ...state,\n                isUploading: false,\n                error: error instanceof Error ? error.message : 'Unknown error',\n                step: 'error'\n            });\n            throw error;\n        }\n    };\n    const startUpload = async ()=>{\n        console.log('Starting upload process...');\n        // For moments (stories), skip vendor details processing and go directly to upload\n        if (state.mediaSubtype === 'story') {\n            console.log('UPLOAD CONTEXT - Moments/Stories detected, skipping vendor details processing');\n            await performUpload();\n            return;\n        }\n        // Try to get vendor details from localStorage\n        try {\n            const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n            if (storedVendorDetails) {\n                const vendorDetails = JSON.parse(storedVendorDetails);\n                console.log('UPLOAD CONTEXT - Retrieved vendor details from localStorage for startUpload:', storedVendorDetails);\n                // Create a new detailFields object to hold all the vendor details\n                const updatedDetailFields = {\n                    ...state.detailFields\n                };\n                let completeVendorCount = 0;\n                // Process vendor details to create detail fields\n                if (Object.keys(vendorDetails).length > 0) {\n                    Object.entries(vendorDetails).forEach((param)=>{\n                        let [vendorType, details] = param;\n                        if (details && details.name && details.mobileNumber) {\n                            // Add to the updated detail fields\n                            updatedDetailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                            updatedDetailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                            console.log(\"UPLOAD CONTEXT - Added vendor \".concat(vendorType, \" with name: \").concat(details.name, \" and contact: \").concat(details.mobileNumber));\n                            completeVendorCount++;\n                            // Also set normalized versions\n                            const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                            if (normalizedType !== vendorType) {\n                                updatedDetailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                                updatedDetailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                                console.log(\"UPLOAD CONTEXT - Also added normalized vendor \".concat(normalizedType));\n                            }\n                        }\n                    });\n                    // Update the state with all vendor details at once\n                    setState((prevState)=>({\n                            ...prevState,\n                            detailFields: updatedDetailFields\n                        }));\n                    console.log(\"UPLOAD CONTEXT - Added \".concat(completeVendorCount, \" complete vendor details from localStorage to state\"));\n                    console.log('UPLOAD CONTEXT - Updated detail fields:', JSON.stringify(updatedDetailFields));\n                }\n            }\n        } catch (error) {\n            console.error('UPLOAD CONTEXT - Failed to retrieve vendor details from localStorage:', error);\n        }\n        // Check if we have a video_category for videos\n        if (state.mediaType === 'video') {\n            // Try to get video_category from localStorage if not in state\n            let videoCategory = state.detailFields.video_category;\n            if (!videoCategory) {\n                try {\n                    const storedVideoCategory = localStorage.getItem('wedzat_video_category');\n                    if (storedVideoCategory) {\n                        videoCategory = storedVideoCategory;\n                        console.log('UPLOAD CONTEXT - Retrieved video_category from localStorage:', videoCategory);\n                        setDetailField('video_category', videoCategory);\n                    }\n                } catch (error) {\n                    console.error('UPLOAD CONTEXT - Failed to retrieve video_category from localStorage:', error);\n                }\n            }\n            console.log(\"UPLOAD CONTEXT - Current video_category: \".concat(videoCategory || 'Not set'));\n            // If we don't have a video_category, use a default one\n            if (!videoCategory) {\n                console.log('UPLOAD CONTEXT - No video_category found, using default: my_wedding');\n                // Use startUploadWithCategory to ensure the video_category is properly set\n                return startUploadWithCategory(state.mediaSubtype, 'my_wedding');\n            } else {\n                // Use startUploadWithCategory to ensure the video_category is properly passed\n                console.log(\"UPLOAD CONTEXT - Using existing video_category: \".concat(videoCategory));\n                return startUploadWithCategory(state.mediaSubtype, videoCategory);\n            }\n        }\n        // For photos, just use the regular upload flow\n        const validation = validateForm();\n        if (!validation.isValid) {\n            console.log('Upload validation failed:', validation.error);\n            setState({\n                ...state,\n                error: validation.error,\n                step: 'error'\n            });\n            return;\n        }\n        if (!state.file) {\n            console.log('No file to upload');\n            return;\n        }\n        setState({\n            ...state,\n            isUploading: true,\n            progress: 0,\n            step: 'uploading',\n            error: undefined\n        });\n        try {\n            console.log('UPLOAD CONTEXT - Starting upload process...');\n            console.log('UPLOAD CONTEXT - Upload details:', {\n                file: state.file ? state.file.name : 'No file',\n                fileSize: state.file ? Math.round(state.file.size / (1024 * 1024) * 100) / 100 + ' MB' : 'N/A',\n                mediaType: state.mediaType,\n                mediaSubtype: state.mediaSubtype,\n                title: state.title,\n                videoCategory: state.detailFields.video_category || 'Not set'\n            });\n            // Use the upload service to handle the complete upload process\n            const result = await _services_api__WEBPACK_IMPORTED_MODULE_2__.uploadService.handleUpload(state.file, state.mediaType, state.mediaSubtype, state.title, state.description, state.tags, state.detailFields, state.duration, state.thumbnail, (progress)=>{\n                console.log(\"Upload progress: \".concat(progress, \"%\"));\n                setState((prevState)=>{\n                    // Only update if the new progress is greater\n                    if (progress > prevState.progress) {\n                        return {\n                            ...prevState,\n                            progress,\n                            // Change to processing state when we reach 80%\n                            step: progress >= 80 && progress < 100 ? 'processing' : prevState.step\n                        };\n                    }\n                    return prevState;\n                });\n            });\n            console.log('Upload completed successfully:', result);\n            // Upload complete\n            setState({\n                ...state,\n                isUploading: false,\n                progress: 100,\n                step: 'complete',\n                response: result\n            });\n        } catch (error) {\n            console.error('Upload failed:', error);\n            setState({\n                ...state,\n                isUploading: false,\n                progress: 0,\n                step: 'error',\n                error: error instanceof Error ? error.message : 'Upload failed. Please try again.'\n            });\n        }\n    };\n    const goToStep = (step)=>{\n        setState({\n            ...state,\n            step\n        });\n    };\n    // Set video duration\n    const setDuration = (duration)=>{\n        setState({\n            ...state,\n            duration\n        });\n        console.log(\"Duration set to \".concat(duration, \" seconds\"));\n    };\n    // Set moments flag\n    const setIsMoments = (isMoments)=>{\n        setState({\n            ...state,\n            isMoments\n        });\n        console.log(\"UPLOAD CONTEXT - isMoments set to \".concat(isMoments));\n    };\n    // Effect to initialize the upload context and listen for vendor details updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UploadProvider.useEffect\": ()=>{\n            // For moments (stories), skip localStorage initialization\n            if (state.mediaSubtype === 'story') {\n                console.log('UPLOAD CONTEXT - Moments detected, skipping localStorage initialization');\n                return;\n            }\n            // Check if we have a video_category in localStorage\n            try {\n                const storedVideoCategory = localStorage.getItem('wedzat_video_category');\n                if (storedVideoCategory) {\n                    console.log('UPLOAD CONTEXT - Retrieved video_category from localStorage:', storedVideoCategory);\n                    setDetailField('video_category', storedVideoCategory);\n                }\n            } catch (error) {\n                console.error('UPLOAD CONTEXT - Failed to retrieve video_category from localStorage:', error);\n            }\n            // Add event listener for vendor details updates from API service\n            const handleVendorDetailsUpdate = {\n                \"UploadProvider.useEffect.handleVendorDetailsUpdate\": (event)=>{\n                    if (event.detail) {\n                        console.log('UPLOAD CONTEXT - Received vendor-details-update event:', event.detail);\n                        // Process vendor details from event\n                        const vendorDetails = event.detail;\n                        const updatedDetailFields = {\n                            ...state.detailFields\n                        };\n                        let completeVendorCount = 0;\n                        Object.entries(vendorDetails).forEach({\n                            \"UploadProvider.useEffect.handleVendorDetailsUpdate\": (param)=>{\n                                let [vendorType, details] = param;\n                                if (details && details.name && details.mobileNumber) {\n                                    // Add to detailFields\n                                    updatedDetailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                                    updatedDetailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                                    console.log(\"UPLOAD CONTEXT - Event handler added vendor \".concat(vendorType, \" with name: \").concat(details.name, \" and contact: \").concat(details.mobileNumber));\n                                    completeVendorCount++;\n                                    // Also add normalized versions\n                                    const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                                    if (normalizedType !== vendorType) {\n                                        updatedDetailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                                        updatedDetailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                                        console.log(\"UPLOAD CONTEXT - Event handler also added normalized vendor \".concat(normalizedType));\n                                    }\n                                }\n                            }\n                        }[\"UploadProvider.useEffect.handleVendorDetailsUpdate\"]);\n                        // Update the state with all vendor details at once\n                        setState({\n                            \"UploadProvider.useEffect.handleVendorDetailsUpdate\": (prevState)=>({\n                                    ...prevState,\n                                    detailFields: updatedDetailFields\n                                })\n                        }[\"UploadProvider.useEffect.handleVendorDetailsUpdate\"]);\n                        console.log(\"UPLOAD CONTEXT - Event handler added \".concat(completeVendorCount, \" complete vendor details to state\"));\n                        console.log('UPLOAD CONTEXT - Event handler updated detail fields:', JSON.stringify(updatedDetailFields));\n                    }\n                }\n            }[\"UploadProvider.useEffect.handleVendorDetailsUpdate\"];\n            // Add event listener\n            window.addEventListener('vendor-details-update', handleVendorDetailsUpdate);\n            // Remove event listener on cleanup\n            return ({\n                \"UploadProvider.useEffect\": ()=>{\n                    window.removeEventListener('vendor-details-update', handleVendorDetailsUpdate);\n                }\n            })[\"UploadProvider.useEffect\"];\n        }\n    }[\"UploadProvider.useEffect\"], []);\n    // Create the context value\n    const contextValue = {\n        state,\n        setFile,\n        setThumbnail,\n        setMediaType,\n        setMediaSubtype,\n        setCategory,\n        setTitle,\n        setDescription,\n        addTag,\n        removeTag,\n        setDetailField,\n        setPersonalDetails,\n        setVendorDetails,\n        setDuration,\n        setIsMoments,\n        resetUpload,\n        startUpload,\n        startUploadWithCategory,\n        validateForm,\n        goToStep\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UploadContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\contexts\\\\UploadContexts.tsx\",\n        lineNumber: 1324,\n        columnNumber: 5\n    }, undefined);\n};\n_s(UploadProvider, \"g9yWDQF6ixWa1r5sfsm7YAeGJG4=\");\n_c = UploadProvider;\n// Custom hook to use the context\nconst useUpload = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(UploadContext);\n    if (context === undefined) {\n        throw new Error('useUpload must be used within an UploadProvider');\n    }\n    return context;\n};\n_s1(useUpload, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"UploadProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/UploadContexts.tsx\n"));

/***/ })

});