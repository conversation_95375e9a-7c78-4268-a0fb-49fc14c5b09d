"use client";
import React, { useState, useEffect } from 'react';
import axios from "../../services/axiosConfig";

interface EditProfileModalProps {
    isOpen: boolean;
    onClose: () => void;
    userProfile: any;
    onProfileUpdate: () => void;
}

const EditProfileModal: React.FC<EditProfileModalProps> = ({ isOpen, onClose, userProfile, onProfileUpdate }) => {
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        mobile_number: '',
        dob: '',
        marital_status: '',
        place: '',
        bio: '',
        user_avatar: '',
        user_short_audio: '',
        chapters_of_love: []
    });
    const [avatarFile, setAvatarFile] = useState<File | null>(null);
    const [avatarPreview, setAvatarPreview] = useState<string>('');
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState<string | null>(null);

    // Initialize form data when user profile changes
    useEffect(() => {
        if (userProfile) {
            setFormData({
                name: userProfile.name || '',
                email: userProfile.email || '',
                mobile_number: userProfile.mobile_number || '',
                dob: userProfile.dob || '',
                marital_status: userProfile.marital_status || '',
                place: userProfile.place || '',
                bio: userProfile.bio || '',
                user_avatar: userProfile.user_avatar || '',
                user_short_audio: userProfile.user_short_audio || '',
                chapters_of_love: userProfile.chapters_of_love || []
            });

            // Set avatar preview if available
            if (userProfile.user_avatar) {
                setAvatarPreview(userProfile.user_avatar);
            }
        }
    }, [userProfile]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0];

            // Check file size (limit to 2MB)
            if (file.size > 2 * 1024 * 1024) {
                setError('Image size should be less than 2MB');
                return;
            }

            // Check file type
            if (!file.type.match('image.*')) {
                setError('Please select an image file');
                return;
            }

            setAvatarFile(file);
            setError(null); // Clear any previous errors

            // Create a preview URL
            const reader = new FileReader();
            reader.onloadend = () => {
                setAvatarPreview(reader.result as string);
            };
            reader.readAsDataURL(file);
        }
    };

    // Convert file to base64 string
    const convertFileToBase64 = (file: File): Promise<string> => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => resolve(reader.result as string);
            reader.onerror = error => reject(error);
        });
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setLoading(true);
        setError(null);
        setSuccess(null);

        try {
            // Get the token
            let token = localStorage.getItem('token') ||
                localStorage.getItem('jwt_token') ||
                localStorage.getItem('wedzat_token');

            if (!token) {
                setError('Authentication token not found');
                setLoading(false);
                return;
            }

            // Ensure token is properly formatted
            if (!token.startsWith('Bearer ')) {
                token = `Bearer ${token}`;
            }

            // Create a copy of the form data to send to the API
            const dataToSend = { ...formData };

            // If there's a new avatar file, convert it to base64
            if (avatarFile) {
                try {
                    const base64Avatar = await convertFileToBase64(avatarFile);
                    dataToSend.user_avatar = base64Avatar;
                } catch (error) {
                    console.error('Error converting avatar to base64:', error);
                    setError('Failed to process the image. Please try a different image.');
                    setLoading(false);
                    return;
                }
            }

            // Send the update request directly to the update-user API
            console.log('Sending update request with token:', token);
            console.log('Sending profile data:', dataToSend);
            const response = await axios.put(
                'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/update-user',
                dataToSend,
                {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            console.log('Profile update response:', response.data);
            setSuccess('Profile updated successfully');

            // Update localStorage with the new avatar if it was changed
            if (avatarFile) {
                // Store the avatar in localStorage for quick access
                localStorage.setItem('user_avatar', dataToSend.user_avatar);
            }

            onProfileUpdate(); // Notify parent component to refresh profile data

            // Close the modal after a short delay
            setTimeout(() => {
                onClose();
            }, 1500);
        } catch (err: any) {
            console.error('Error updating profile:', err);
            setError(err.response?.data?.error || 'Failed to update profile');
        } finally {
            setLoading(false);
        }
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto">
                <div className="p-6">
                    <div className="flex justify-between items-center mb-4">
                        <h2 className="text-xl font-semibold">Edit Profile</h2>
                        <button
                            onClick={onClose}
                            className="text-gray-500 hover:text-gray-700"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    {error && (
                        <div className="mb-4 p-2 bg-red-100 text-red-700 rounded">
                            {error}
                        </div>
                    )}

                    {success && (
                        <div className="mb-4 p-2 bg-green-100 text-green-700 rounded">
                            {success}
                        </div>
                    )}

                    <form onSubmit={handleSubmit}>
                        {/* Avatar Upload */}
                        <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Profile Picture
                            </label>
                            <div className="flex items-center space-x-4">
                                <div className="w-20 h-20 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center border border-gray-300">
                                    {avatarPreview ? (
                                        <img
                                            src={avatarPreview}
                                            alt="Avatar Preview"
                                            className="w-full h-full object-cover"
                                        />
                                    ) : (
                                        <span className="text-gray-400 text-xs text-center px-1">No Image</span>
                                    )}
                                </div>
                                <div className="flex flex-col">
                                    <label className="cursor-pointer bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm mb-1">
                                        Upload Photo
                                        <input
                                            type="file"
                                            accept="image/jpeg, image/png, image/gif"
                                            className="hidden"
                                            onChange={handleAvatarChange}
                                        />
                                    </label>
                                    <span className="text-xs text-gray-500">Max size: 2MB</span>
                                </div>
                            </div>
                        </div>

                        {/* Name */}
                        <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Name
                            </label>
                            <input
                                type="text"
                                name="name"
                                value={formData.name}
                                onChange={handleChange}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                            />
                        </div>

                        {/* Email */}
                        <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Email
                            </label>
                            <input
                                type="email"
                                name="email"
                                value={formData.email}
                                onChange={handleChange}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                            />
                        </div>

                        {/* Mobile Number */}
                        <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Mobile Number
                            </label>
                            <input
                                type="text"
                                name="mobile_number"
                                value={formData.mobile_number}
                                onChange={handleChange}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>

                        {/* Date of Birth */}
                        <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Date of Birth
                            </label>
                            <input
                                type="date"
                                name="dob"
                                value={formData.dob}
                                onChange={handleChange}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>

                        {/* Marital Status */}
                        <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Marital Status
                            </label>
                            <select
                                name="marital_status"
                                value={formData.marital_status}
                                onChange={handleChange}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="">Select Status</option>
                                <option value="single">Single</option>
                                <option value="married">Married</option>
                                <option value="divorced">Divorced</option>
                                <option value="widowed">Widowed</option>
                            </select>
                        </div>

                        {/* Place */}
                        <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Place
                            </label>
                            <input
                                type="text"
                                name="place"
                                value={formData.place}
                                onChange={handleChange}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>

                        {/* Bio */}
                        <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Bio
                            </label>
                            <textarea
                                name="bio"
                                value={formData.bio}
                                onChange={handleChange}
                                rows={3}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>

                        <div className="flex justify-end space-x-2 mt-6">
                            <button
                                type="button"
                                onClick={onClose}
                                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                                disabled={loading}
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500"
                                disabled={loading}
                            >
                                {loading ? 'Saving...' : 'Save Changes'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default EditProfileModal;
