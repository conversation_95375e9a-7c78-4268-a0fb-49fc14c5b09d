// frontend/app/api/verify-otp/route.ts
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';

interface RequestBody {
  otp: string;
  // For Firebase phone verification
  firebaseVerified?: boolean;
  verifiedPhone?: string;
}

interface ResponseData {
  success: boolean;
  message: string;
  method?: string;
  email?: string;
  phone?: string;
}

export async function POST(request: Request): Promise<NextResponse<ResponseData>> {
  try {
    const body: RequestBody = await request.json();
    const { otp, firebaseVerified, verifiedPhone } = body;
    
    const useFirebase = (await cookies()).get('useFirebase')?.value === 'true';
    const verifyMethod = (await cookies()).get('verifyMethod')?.value;
    const email = (await cookies()).get('verifyEmail')?.value;
    const phone = (await cookies()).get('verifyPhone')?.value;
    
    // Handle Firebase phone verification
    if (verifyMethod === 'phone' && useFirebase && firebaseVerified) {
      if (!verifiedPhone) {
        return NextResponse.json({
          success: false,
          message: "Firebase verification missing phone number"
        }, { status: 400 });
      }
      
      // Verify the phone number matches what we expect
      if (phone && verifiedPhone !== phone) {
        return NextResponse.json({
          success: false,
          message: "Phone number mismatch"
        }, { status: 400 });
      }
      
      console.log('Firebase phone verification successful:', verifiedPhone);
      
      // Clear the verification cookies
      (await cookies()).delete('verifyMethod');
      (await cookies()).delete('verifyPhone');
      (await cookies()).delete('useFirebase');
      
      // Set a verification cookie to maintain verified state (1 hour)
      (await cookies()).set({
        name: 'verified',
        value: 'true',
        httpOnly: true,
        maxAge: 3600, // 1 hour
        path: '/',
        sameSite: 'strict',
      });
      
      // Store the verified phone number
      (await cookies()).set({
        name: 'verifiedPhone',
        value: verifiedPhone,
        httpOnly: true,
        maxAge: 3600, // 1 hour
        path: '/',
        sameSite: 'strict',
      });
      
      return NextResponse.json({
        success: true,
        message: "Phone verification successful",
        method: 'phone',
        phone: verifiedPhone
      });
    }
    
    // Handle traditional email OTP verification
    const storedOTP = (await cookies()).get('pendingOtp')?.value;
    
    console.log('Verification attempt:', {
      enteredOTP: otp,
      storedOTP: storedOTP,
      verifyMethod: verifyMethod,
      email: email,
      phone: phone,
      useFirebase: useFirebase
    });
    
    if (!storedOTP && verifyMethod !== 'phone') {
      return NextResponse.json({ 
        success: false, 
        message: "No verification in progress or verification expired" 
      }, { status: 400 });
    }
    
    // For regular email OTP verification
    if (otp === storedOTP) {
      console.log('Email OTP verified successfully');
      
      // Clear the OTP cookies
      (await cookies()).delete('pendingOtp');
      (await cookies()).delete('verifyMethod');
      (await cookies()).delete('verifyEmail');
      
      // Set a verification cookie to maintain verified state (1 hour)
      (await cookies()).set({
        name: 'verified',
        value: 'true',
        httpOnly: true,
        maxAge: 3600, // 1 hour
        path: '/',
        sameSite: 'strict',
      });
      
      // Store the verification email
      if (email) {
        (await cookies()).set({
          name: 'verifiedEmail',
          value: email,
          httpOnly: true,
          maxAge: 3600, // 1 hour
          path: '/',
          sameSite: 'strict',
        });
      }
      
      return NextResponse.json({
        success: true,
        message: "Verification successful",
        method: verifyMethod || 'unknown',
        email: email
      });
    } else {
      console.log('OTP verification failed. Entered:', otp, 'Stored:', storedOTP);
      
      return NextResponse.json({ 
        success: false, 
        message: "Invalid verification code" 
      }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in verify-otp:', error);
    return NextResponse.json({ 
      success: false, 
      message: "Internal server error" 
    }, { status: 500 });
  }
}