import React, { useState, useEffect } from 'react';
import VendorDetails from './VendorDetails';

interface FlashVendorDetailsProps {
  videoId: string;
  isVerified?: boolean;
}

const FlashVendorDetails: React.FC<FlashVendorDetailsProps> = ({
  videoId,
  isVerified = false
}) => {
  const [token, setToken] = useState<string>('');
  const [isBlurred, setIsBlurred] = useState<boolean>(!isVerified);

  useEffect(() => {
    // Function to safely access localStorage (avoid errors in SSR)
    const getFromLocalStorage = (key: string) => {
      try {
        if (typeof window !== 'undefined') {
          return localStorage.getItem(key);
        }
        return null;
      } catch (e) {
        console.error(`Error accessing localStorage for key ${key}:`, e);
        return null;
      }
    };

    // Get token from localStorage
    const storedToken = getFromLocalStorage('token');
    if (storedToken) {
      setToken(storedToken);
    }
  }, []);

  const handleUnlock = () => {
    setIsBlurred(false);
  };

  return (
    <div className="mt-4">
      <h3 className="text-lg font-semibold mb-2">Vendor Details</h3>
      {token ? (
        <VendorDetails 
          videoId={videoId} 
          token={token} 
          isBlurred={isBlurred}
          onUnlock={handleUnlock}
        />
      ) : (
        <div className="text-sm text-gray-500">
          <p>Please log in to view vendor details</p>
        </div>
      )}
    </div>
  );
};

export default FlashVendorDetails;
