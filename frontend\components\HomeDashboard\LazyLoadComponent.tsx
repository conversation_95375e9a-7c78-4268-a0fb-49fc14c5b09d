import React, { useEffect, useRef, useState } from 'react';

interface LazyLoadComponentProps {
  children: ((props: { isVisible: boolean, shouldLoad: boolean }) => React.ReactNode) | React.ReactNode;
  placeholder?: React.ReactNode;
  threshold?: number;
  rootMargin?: string;
  delayLoadUntilVisible?: boolean; // New prop to control data loading
  id?: string; // Unique identifier for the component
}

// Keep track of which components have already been loaded to prevent duplicate loading
const loadedComponents = new Set<string>();

const LazyLoadComponent: React.FC<LazyLoadComponentProps> = ({
  children,
  placeholder = <div className="h-40 w-full bg-gray-100 animate-pulse rounded-lg"></div>,
  threshold = 0.1,
  rootMargin = '200px 0px',
  delayLoadUntilVisible = true, // Default to true to delay data loading
  id = 'component-' + Math.random().toString(36).substr(2, 9), // Generate a random ID if none provided
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [shouldLoad, setShouldLoad] = useState(!delayLoadUntilVisible); // Start loading immediately if not delayed
  const [hasLoaded, setHasLoaded] = useState(loadedComponents.has(id));
  const ref = useRef<HTMLDivElement>(null);

  // Check if this component has already been loaded in a previous render
  useEffect(() => {
    if (loadedComponents.has(id)) {
      setIsVisible(true);
      setShouldLoad(true);
      setHasLoaded(true);
    }
  }, [id]);

  useEffect(() => {
    // Skip if already loaded
    if (hasLoaded) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasLoaded) {
          console.log(`Component ${id} is now visible, loading content...`);
          setIsVisible(true);
          setShouldLoad(true); // Allow data loading when visible
          setHasLoaded(true);

          // Mark this component as loaded to prevent duplicate loading
          loadedComponents.add(id);

          // Once loaded, we can disconnect the observer
          observer.disconnect();
        }
      },
      {
        threshold,
        rootMargin,
      }
    );

    const currentRef = ref.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [hasLoaded, threshold, rootMargin, id]);

  return (
    <div ref={ref} className="w-full">
      {isVisible
        ? typeof children === 'function'
          ? children({ isVisible, shouldLoad })
          : children
        : placeholder}
    </div>
  );
};

export default LazyLoadComponent;
