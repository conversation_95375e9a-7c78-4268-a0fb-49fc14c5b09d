// services/mediaTypesService.ts
import axios from './axiosConfig';

// Media type interfaces
export interface MediaType {
  id: string;
  name: string;
  displayName: string;
  maxSize: number; // in MB
  maxDuration?: number; // in seconds (for videos only)
  description: string;
}

export interface VideoType extends MediaType {
  maxDuration: number;
}

export interface PhotoType extends MediaType {
  // Photo-specific properties can be added here
}

// Backend API response interfaces
export interface ApiResponse<T> {
  data: T;
  total_count?: number;
  page?: number;
  total_pages?: number;
}

export interface VideoItem {
  video_id: string;
  video_name: string;
  video_url: string;
  video_description?: string;
  video_tags?: string[];
  video_thumbnail?: string;
  video_duration?: number;
  video_category?: string;
  created_at: string;
  user_name?: string;
  user_id?: string;
  is_own_content?: boolean;
  video_views?: number;
  video_likes?: number;
  video_comments?: number;
}

export interface PhotoItem {
  photo_id: string;
  photo_name: string;
  photo_url: string;
  photo_description?: string;
  photo_tags?: string[];
  created_at: string;
  user_name?: string;
  user_id?: string;
  is_own_content?: boolean;
  photo_likes?: number;
  photo_comments?: number;
}

// Define fallback media types with their restrictions (used if API calls fail)
const FALLBACK_VIDEO_TYPES: Record<string, VideoType> = {
  story: {
    id: 'story',
    name: 'story',
    displayName: 'Moments',
    maxSize: 50, // 50MB
    maxDuration: 61, // 61 seconds
    description: 'Short video clips that disappear after 24 hours'
  },
  flash: {
    id: 'flash',
    name: 'flash',
    displayName: 'Flashes',
    maxSize: 100, // 100MB
    maxDuration: 91, // 91 seconds
    description: 'Quick video highlights to share with your network'
  },
  glimpse: {
    id: 'glimpse',
    name: 'glimpse',
    displayName: 'Glimpses',
    maxSize: 250, // 250MB
    maxDuration: 421, // 421 seconds (7 minutes)
    description: 'Longer videos to showcase special moments'
  },
  movie: {
    id: 'movie',
    name: 'movie',
    displayName: 'Movies',
    maxSize: 2000, // 2GB
    maxDuration: 3601, // 3601 seconds (60 minutes)
    description: 'Full-length wedding videos and documentaries'
  }
};

const FALLBACK_PHOTO_TYPES: Record<string, PhotoType> = {
  story: {
    id: 'story',
    name: 'story',
    displayName: 'Story',
    maxSize: 10, // 10MB
    description: 'Photos that disappear after 24 hours'
  },
  post: {
    id: 'post',
    name: 'post',
    displayName: 'Post',
    maxSize: 20, // 20MB
    description: 'Permanent photos to share with your network'
  }
};

const FALLBACK_VIDEO_CATEGORIES = [
  { id: 'my_wedding', name: 'My Wedding Videos' },
  { id: 'wedding_influencer', name: 'Wedding Vlogs' }
];

// Disable caching completely
let cachedVideoTypes: Record<string, VideoType> | null = null;
let cachedPhotoTypes: Record<string, PhotoType> | null = null;
let cachedVideoCategories: Array<{id: string, name: string}> | null = null;

// Service functions to fetch media types from the backend
const mediaTypesService = {
  // Get all video types
  getVideoTypes: async (): Promise<Record<string, VideoType>> => {
    // Disable caching completely

    try {
      // Create a new object to store the video types
      const videoTypes: Record<string, VideoType> = {};

      // Fetch data from each endpoint
      const endpoints = ['flashes', 'glimpses', 'movies', 'stories'];
      const apiCalls = endpoints.map(endpoint =>
        axios.get<ApiResponse<VideoItem[]>>(`/hub/${endpoint}?page=1&limit=1`)
      );

      // Wait for all API calls to complete
      const results = await Promise.allSettled(apiCalls);

      // Process the results
      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          const endpoint = endpoints[index];
          const response = result.value;

          // Map endpoint names to backend media subtypes
          let mediaSubtype = '';
          switch (endpoint) {
            case 'flashes':
              mediaSubtype = 'flash';
              break;
            case 'glimpses':
              mediaSubtype = 'glimpse';
              break;
            case 'movies':
              mediaSubtype = 'movie';
              break;
            case 'stories':
              mediaSubtype = 'story';
              break;
          }

          if (mediaSubtype && response.data && response.data.data) {
            // Add the video type to the object
            videoTypes[mediaSubtype] = {
              ...FALLBACK_VIDEO_TYPES[mediaSubtype], // Use fallback for basic info
              id: mediaSubtype,
              name: mediaSubtype,
              // Update with any additional info from the API if available
            };
            console.log(`${endpoint} API is working, added ${mediaSubtype} to video types`);
          }
        } else {
          console.warn(`${endpoints[index]} API failed:`, result.reason);
        }
      });

      // If we didn't get any video types from the API, use the fallbacks
      if (Object.keys(videoTypes).length === 0) {
        console.warn('No video types retrieved from API, using fallback values');
        cachedVideoTypes = FALLBACK_VIDEO_TYPES;
        return FALLBACK_VIDEO_TYPES;
      }

      // Disable caching completely
      return videoTypes;
    } catch (error) {
      console.error('Error fetching video types:', error);
      return FALLBACK_VIDEO_TYPES; // Fallback to hardcoded values
    }
  },

  // Get all photo types
  getPhotoTypes: async (): Promise<Record<string, PhotoType>> => {
    // Disable caching completely

    try {
      // Create a new object to store the photo types
      const photoTypes: Record<string, PhotoType> = {};

      // Fetch data from each endpoint
      const endpoints = ['photos', 'stories'];
      const apiCalls = endpoints.map(endpoint =>
        axios.get<ApiResponse<PhotoItem[]>>(`/hub/${endpoint}?page=1&limit=1`)
      );

      // Wait for all API calls to complete
      const results = await Promise.allSettled(apiCalls);

      // Process the results
      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          const endpoint = endpoints[index];
          const response = result.value;

          // Map endpoint names to backend media subtypes
          let mediaSubtype = '';
          switch (endpoint) {
            case 'photos':
              mediaSubtype = 'post';
              break;
            case 'stories':
              mediaSubtype = 'story';
              break;
          }

          if (mediaSubtype && response.data && response.data.data) {
            // Add the photo type to the object
            photoTypes[mediaSubtype] = {
              ...FALLBACK_PHOTO_TYPES[mediaSubtype], // Use fallback for basic info
              id: mediaSubtype,
              name: mediaSubtype,
              // Update with any additional info from the API if available
            };
            console.log(`${endpoint} API is working, added ${mediaSubtype} to photo types`);
          }
        } else {
          console.warn(`${endpoints[index]} API failed:`, result.reason);
        }
      });

      // If we didn't get any photo types from the API, use the fallbacks
      if (Object.keys(photoTypes).length === 0) {
        console.warn('No photo types retrieved from API, using fallback values');
        return FALLBACK_PHOTO_TYPES;
      }

      // Disable caching completely
      return photoTypes;
    } catch (error) {
      console.error('Error fetching photo types:', error);
      return FALLBACK_PHOTO_TYPES; // Fallback to hardcoded values
    }
  },

  // Get video categories
  getVideoCategories: async (): Promise<Array<{id: string, name: string}>> => {
    // Return cached data if available (disabled in development)
    if (process.env.NODE_ENV !== 'development' && cachedVideoCategories) {
      return cachedVideoCategories;
    }

    try {
      // Try to get video categories from the movies endpoint
      const response = await axios.get<ApiResponse<VideoItem[]>>('/hub/movies?page=1&limit=10');

      if (response.data && response.data.data && response.data.data.length > 0) {
        // Extract unique categories from the videos
        const categories = new Set<string>();
        response.data.data.forEach((video: VideoItem) => {
          if (video.video_category) {
            categories.add(video.video_category);
          }
        });

        // If we found categories, format and return them
        if (categories.size > 0) {
          const formattedCategories = Array.from(categories).map(category => {
            // Format the category name for display
            let displayName = category.replace(/_/g, ' ');
            displayName = displayName.split(' ').map(word =>
              word.charAt(0).toUpperCase() + word.slice(1)
            ).join(' ');

            return { id: category, name: displayName };
          });

          cachedVideoCategories = formattedCategories;
          return formattedCategories;
        }
      }

      // If we couldn't get categories from the API, use the fallbacks
      console.warn('No video categories retrieved from API, using fallback values');
      cachedVideoCategories = FALLBACK_VIDEO_CATEGORIES;
      return FALLBACK_VIDEO_CATEGORIES;
    } catch (error) {
      console.error('Error fetching video categories:', error);
      cachedVideoCategories = FALLBACK_VIDEO_CATEGORIES;
      return FALLBACK_VIDEO_CATEGORIES; // Fallback to hardcoded values
    }
  },

  // Force refresh the cached data
  refreshMediaTypes: async (): Promise<void> => {
    try {
      // Clear the cache
      cachedVideoTypes = null;
      cachedPhotoTypes = null;
      cachedVideoCategories = null;

      // Fetch fresh data
      await Promise.all([
        mediaTypesService.getVideoTypes(),
        mediaTypesService.getPhotoTypes(),
        mediaTypesService.getVideoCategories()
      ]);

      console.log('Media types refreshed successfully');
    } catch (error) {
      console.error('Error refreshing media types:', error);
    }
  },

  // Get data for a specific API endpoint
  getEndpointData: async (endpoint: string, page: number = 1, limit: number = 10): Promise<ApiResponse<any>> => {
    try {
      const response = await axios.get<ApiResponse<any>>(`/hub/${endpoint}?page=${page}&limit=${limit}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching data from ${endpoint}:`, error);
      return { data: [] };
    }
  },

  // Get flashes (flash videos)
  getFlashes: async (page: number = 1, limit: number = 10): Promise<ApiResponse<VideoItem[]>> => {
    return mediaTypesService.getEndpointData('flashes', page, limit);
  },

  // Get glimpses (glimpse videos)
  getGlimpses: async (page: number = 1, limit: number = 10): Promise<ApiResponse<VideoItem[]>> => {
    return mediaTypesService.getEndpointData('glimpses', page, limit);
  },

  // Get movies (movie videos)
  getMovies: async (page: number = 1, limit: number = 10): Promise<ApiResponse<VideoItem[]>> => {
    return mediaTypesService.getEndpointData('movies', page, limit);
  },

  // Get stories (story videos/moments)
  getStories: async (page: number = 1, limit: number = 10): Promise<ApiResponse<VideoItem[]>> => {
    return mediaTypesService.getEndpointData('stories', page, limit);
  },

  // Get photos (post photos)
  getPhotos: async (page: number = 1, limit: number = 10): Promise<ApiResponse<PhotoItem[]>> => {
    return mediaTypesService.getEndpointData('photos', page, limit);
  }
};

export default mediaTypesService;
