import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import userService from '../services/api/userService';
import { UserProfile } from '../utils/auth';
import { UserUpdateData } from '../services/api/userService';

// Query keys
export const userKeys = {
  all: ['user'] as const,
  details: () => [...userKeys.all, 'details'] as const,
  events: () => [...userKeys.all, 'events'] as const,
};

// Hooks
export const useUserDetails = () => {
  return useQuery({
    queryKey: userKeys.details(),
    queryFn: userService.getUserDetails,
  });
};

export const useUpdateProfile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UserUpdateData) => userService.updateProfile(data),
    onSuccess: (data) => {
      queryClient.setQueryData(userKeys.details(), data);
      queryClient.invalidateQueries({ queryKey: userKeys.details() });
    },
  });
};

export const useUserEvents = () => {
  return useQuery({
    queryKey: userKeys.events(),
    queryFn: userService.getEvents,
  });
};

export const useChangePassword = () => {
  return useMutation({
    mutationFn: ({ oldPassword, newPassword }: { oldPassword: string; newPassword: string }) =>
      userService.changePassword(oldPassword, newPassword),
  });
};

export const useRequestPasswordReset = () => {
  return useMutation({
    mutationFn: (email: string) => userService.requestPasswordReset(email),
  });
};

export const useResetPassword = () => {
  return useMutation({
    mutationFn: ({ token, newPassword }: { token: string; newPassword: string }) =>
      userService.resetPassword(token, newPassword),
  });
}; 