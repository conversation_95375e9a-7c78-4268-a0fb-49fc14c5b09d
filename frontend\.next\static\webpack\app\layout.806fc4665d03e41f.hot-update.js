"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"3cd14cadcdbe\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xca29tbWlcXE9uZURyaXZlXFxEZXNrdG9wXFxXRURaQVRfXFxmcm9udGVuZFxcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjNjZDE0Y2FkY2RiZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/upload/UploadManager.tsx":
/*!*********************************************!*\
  !*** ./components/upload/UploadManager.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _UploadTypeSelection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./UploadTypeSelection */ \"(app-pages-browser)/./components/upload/UploadTypeSelection.tsx\");\n/* harmony import */ var _VideoCategorySelection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./VideoCategorySelection */ \"(app-pages-browser)/./components/upload/VideoCategorySelection.tsx\");\n/* harmony import */ var _ThumbnailSelection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ThumbnailSelection */ \"(app-pages-browser)/./components/upload/ThumbnailSelection.tsx\");\n/* harmony import */ var _PersonalDetails__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./PersonalDetails */ \"(app-pages-browser)/./components/upload/PersonalDetails.tsx\");\n/* harmony import */ var _VendorDetails__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./VendorDetails */ \"(app-pages-browser)/./components/upload/VendorDetails.tsx\");\n/* harmony import */ var _FaceVerification__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./FaceVerification */ \"(app-pages-browser)/./components/upload/FaceVerification.tsx\");\n/* harmony import */ var _UploadProgress__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./UploadProgress */ \"(app-pages-browser)/./components/upload/UploadProgress.tsx\");\n/* harmony import */ var _contexts_UploadContexts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../contexts/UploadContexts */ \"(app-pages-browser)/./contexts/UploadContexts.tsx\");\n/* harmony import */ var _utils_uploadUtils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../utils/uploadUtils */ \"(app-pages-browser)/./utils/uploadUtils.ts\");\n/* harmony import */ var _utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../utils/alertUtils */ \"(app-pages-browser)/./utils/alertUtils.tsx\");\n/* harmony import */ var _hooks_useMedia__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../hooks/useMedia */ \"(app-pages-browser)/./hooks/useMedia.ts\");\n// components/upload/UploadManager.tsx\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function to format time in minutes and seconds\nconst formatTime = (seconds)=>{\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    if (minutes === 0) {\n        return \"\".concat(remainingSeconds, \" seconds\");\n    } else if (minutes === 1 && remainingSeconds === 0) {\n        return '1 minute';\n    } else if (remainingSeconds === 0) {\n        return \"\".concat(minutes, \" minutes\");\n    } else {\n        return \"\".concat(minutes, \" minute\").concat(minutes > 1 ? 's' : '', \" and \").concat(remainingSeconds, \" second\").concat(remainingSeconds !== 1 ? 's' : '');\n    }\n};\nconst UploadManager = (param)=>{\n    let { onClose, initialType, onUploadComplete } = param;\n    _s();\n    const { state, setFile, setMediaType, setCategory, setMediaSubtype, setTitle, setDescription, setDetailField, startUpload, startUploadWithCategory, setThumbnail, setVendorDetails, setPersonalDetails, setDuration, resetUpload } = (0,_contexts_UploadContexts__WEBPACK_IMPORTED_MODULE_9__.useUpload)();\n    const [phase, setPhase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('typeSelection');\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialType || '');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [previewImage, setPreviewImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [thumbnailImage, setThumbnailImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const vendorDetailsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    // Helper function to determine content type for PersonalDetails\n    const getContentType = ()=>{\n        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n            return 'moment';\n        } else if (state.mediaType === 'video') {\n            return 'video';\n        } else {\n            return 'photo';\n        }\n    };\n    // Store personal details to persist between screens\n    const [personalDetails, setLocalPersonalDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        caption: '',\n        lifePartner: '',\n        weddingStyle: '',\n        place: '',\n        eventType: '',\n        budget: ''\n    });\n    // Auto-select the type if initialType is provided\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UploadManager.useEffect\": ()=>{\n            if (initialType) {\n                console.log('Auto-selecting type from initialType:', initialType);\n                handleTypeSelected(initialType);\n            }\n        }\n    }[\"UploadManager.useEffect\"], []);\n    // Store vendor details to persist between screens\n    const [vendorDetailsData, setVendorDetailsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        venue: {\n            name: '',\n            mobileNumber: ''\n        },\n        photographer: {\n            name: '',\n            mobileNumber: ''\n        },\n        makeupArtist: {\n            name: '',\n            mobileNumber: ''\n        },\n        decorations: {\n            name: '',\n            mobileNumber: ''\n        },\n        caterer: {\n            name: '',\n            mobileNumber: ''\n        }\n    });\n    // Use the new media upload hook\n    const { mutate: uploadMedia, isPending: isUploading } = (0,_hooks_useMedia__WEBPACK_IMPORTED_MODULE_12__.useMediaUpload)();\n    // Handle media type selection\n    const handleTypeSelected = (type)=>{\n        // First, completely reset everything\n        resetUpload();\n        setPreviewImage(null);\n        setThumbnailImage(null);\n        // Then set the new type\n        setSelectedType(type);\n        console.log(\"Selected type:\", type);\n        if ([\n            'flashes',\n            'glimpses',\n            'movies',\n            'photos',\n            'moments'\n        ].includes(type)) {\n            // For explicit video types, photos, and moments, set the appropriate media type\n            if (type === 'photos') {\n                console.log('Setting media type to photo for:', type);\n                setMediaType('photo');\n                setMediaSubtype('post');\n                // Go to category selection for photos\n                setPhase('categorySelection');\n            } else if (type === 'moments') {\n                console.log('Setting media type for moments (will be determined by file type)');\n                // For moments, we'll set the media type later based on the file type (photo or video)\n                setMediaSubtype('story');\n                // For moments, skip category selection and go directly to file upload\n                console.log('Moments selected: skipping category selection, going directly to file upload');\n                handleFileUpload();\n                return; // Early return to prevent further processing\n            } else {\n                setMediaType('video');\n                setMediaSubtype(getMediaSubtypeFromSelectedType(type));\n                // Go to category selection for videos\n                setPhase('categorySelection');\n            }\n        } else if (type === 'photo') {\n            // For single photo type (if it exists)\n            console.log('Setting media type to photo for:', type);\n            setMediaType('photo');\n            setMediaSubtype('post');\n            // Use a special photo-only upload handler for photos\n            handlePhotoUpload();\n        }\n    };\n    // Helper function to get the backend media subtype from the selected UI type\n    const getMediaSubtypeFromSelectedType = (type)=>{\n        // Map UI category to backend category for media_subtype\n        switch(type){\n            // Photo types\n            case 'moments':\n                return 'story'; // Backend expects 'story' for moments\n            case 'photos':\n                return 'post'; // Backend expects 'post' for regular photos\n            // Video types\n            case 'flashes':\n                return 'flash'; // Backend expects 'flash'\n            case 'glimpses':\n                return 'glimpse'; // Backend expects 'glimpse'\n            case 'movies':\n                return 'movie'; // Backend expects 'movie'\n            // Default fallback\n            default:\n                return type === 'moments' ? 'story' : 'post'; // Default based on type\n        }\n    };\n    // Handle category selection for both videos and photos\n    const handleCategorySelected = (category)=>{\n        // First, make sure we have a clean state for the new upload\n        // but preserve the selected type and media type\n        const currentType = selectedType;\n        const currentMediaType = state.mediaType;\n        resetUpload();\n        setSelectedType(currentType);\n        setMediaType(currentMediaType);\n        setPreviewImage(null);\n        setThumbnailImage(null);\n        // Now set the new category\n        setSelectedCategory(category);\n        // Get the media subtype based on the selected type\n        let mediaSubtype;\n        if (currentType === 'photos') {\n            // For photos, always use 'post' as the media subtype\n            mediaSubtype = 'post';\n            console.log(\"UPLOAD MANAGER - Using media subtype 'post' for photos\");\n        } else {\n            // For videos, use the subtype based on the selected type\n            mediaSubtype = getMediaSubtypeFromSelectedType(selectedType);\n            console.log(\"UPLOAD MANAGER - Using media subtype \".concat(mediaSubtype, \" based on selected type \").concat(selectedType));\n            console.log(\"UPLOAD MANAGER - Backend expects: flashes→flash, glimpses→glimpse, movies→movie, moments→story\");\n        }\n        console.log(\"UPLOAD MANAGER - Selected category:\", category);\n        console.log(\"UPLOAD MANAGER - Setting media subtype to:\", mediaSubtype);\n        // Set the media subtype in the context\n        setMediaSubtype(mediaSubtype);\n        // Map the selected category to a valid backend video_category\n        let backendVideoCategory = '';\n        if (category === 'my_wedding_videos') {\n            backendVideoCategory = 'my_wedding';\n        } else if (category === 'wedding_vlog') {\n            backendVideoCategory = 'wedding_vlog';\n        }\n        // Make sure we have a valid video_category\n        if (!backendVideoCategory) {\n            console.error('Invalid video category selected:', category);\n            alert('Please select a valid video category');\n            return;\n        }\n        // Set video category in the context for the backend\n        console.log(\"UPLOAD MANAGER - Setting video_category to:\", backendVideoCategory);\n        setDetailField('video_category', backendVideoCategory);\n        // Log the final values\n        console.log(\"UPLOAD MANAGER - Selected category:\", category);\n        console.log(\"UPLOAD MANAGER - Backend video category set to:\", backendVideoCategory);\n        console.log(\"UPLOAD MANAGER - Media subtype set to:\", mediaSubtype);\n        // Proceed to file upload after setting the category\n        if (currentType === 'photos') {\n            // For photos, use the photo-specific upload handler\n            handlePhotoUpload();\n        } else {\n            // For videos, use the standard file upload handler\n            handleFileUpload();\n        }\n    };\n    // Handle thumbnail upload\n    const handleThumbnailUpload = ()=>{\n        // Create a file input element\n        const input = document.createElement('input');\n        input.type = 'file';\n        input.accept = 'image/*';\n        // Handle file selection\n        input.onchange = (e)=>{\n            const files = e.target.files;\n            if (files && files.length > 0) {\n                const file = files[0];\n                // Store the thumbnail\n                setThumbnailImage(file);\n                setThumbnail(file);\n                console.log(\"Thumbnail selected:\", file.name);\n                // Show a preview if needed\n                const reader = new FileReader();\n                reader.onload = (e)=>{\n                    var _e_target;\n                    if ((_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result) {\n                        // You could set a thumbnail preview here if needed\n                        console.log(\"Thumbnail preview ready\");\n                    }\n                };\n                reader.readAsDataURL(file);\n            }\n        };\n        // Trigger the file dialog\n        input.click();\n    };\n    // Get user-friendly display name for a category\n    const getCategoryDisplayName = (category)=>{\n        switch(category){\n            case 'flash':\n                return 'Flash';\n            case 'glimpse':\n                return 'Glimpse';\n            case 'movie':\n                return 'Movie';\n            case 'story':\n                return 'Story';\n            case 'post':\n                return 'Photo';\n            default:\n                return category.charAt(0).toUpperCase() + category.slice(1);\n        }\n    };\n    // Get appropriate category based on duration\n    const getAppropriateCategory = (duration)=>{\n        // For very short videos (1 minute or less), use flash instead of story/moments\n        if (duration <= 60) {\n            return 'flash'; // Very short videos (1 minute or less) - changed from 'story' to 'flash'\n        } else if (duration <= 90) {\n            return 'flash'; // Short videos (1.5 minutes or less)\n        } else if (duration <= 420) {\n            return 'glimpse'; // Medium videos (7 minutes or less)\n        } else {\n            return 'movie'; // Long videos (over 7 minutes)\n        }\n    };\n    // Special handler for photo uploads that strictly enforces image-only files\n    const handlePhotoUpload = ()=>{\n        console.log('handlePhotoUpload called - strict image-only upload');\n        // Create a file input element specifically for photos\n        const input = document.createElement('input');\n        input.type = 'file';\n        // Reset the input value\n        input.value = '';\n        // Only accept image files - explicitly list allowed types\n        input.accept = 'image/jpeg,image/png,image/gif,image/webp';\n        // Handle file selection with strict validation\n        input.onchange = async (e)=>{\n            const files = e.target.files;\n            if (!files || files.length === 0) return;\n            const file = files[0];\n            console.log('Photo file selected:', file.name, file.type, file.size);\n            // Strict validation - must be an image file\n            const validImageTypes = [\n                'image/jpeg',\n                'image/png',\n                'image/gif',\n                'image/webp'\n            ];\n            if (!validImageTypes.includes(file.type)) {\n                console.error('Invalid file type for photos:', file.type);\n                alert('Only JPEG, PNG, GIF, or WebP images can be uploaded as photos. Videos are not allowed.');\n                return;\n            }\n            // Additional check - reject any file that might be a video\n            if (file.type.startsWith('video/')) {\n                console.error('Attempted to upload a video file as photo');\n                alert('Videos cannot be uploaded as photos. Please select an image file.');\n                return;\n            }\n            // For photos, we need to be more careful with state management\n            // First, set the media type and subtype\n            setMediaType('photo');\n            setMediaSubtype('post');\n            // Then set the file in the state\n            setFile(file);\n            console.log('Photo file set in state:', file.name);\n            // Create a local reference to the file for use in the timeout\n            const currentFile = file;\n            // Double-check that the file is set in the state before proceeding\n            setTimeout(()=>{\n                // Check if the file is in the state\n                if (!state.file) {\n                    console.log('File not found in state after setting, trying again');\n                    // Try setting the file again\n                    setFile(currentFile);\n                    // Add another timeout to ensure the file is set\n                    setTimeout(()=>{\n                        if (!state.file) {\n                            console.log('File still not in state, setting it one more time');\n                            setFile(currentFile);\n                        } else {\n                            console.log('File confirmed in state after second attempt:', state.file.name);\n                        }\n                        // New flow logic: For moments (stories), skip personal details and go directly to face verification\n                        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                            console.log('Moments photo upload: skipping personal details, going directly to face verification');\n                            setPhase('faceVerification');\n                        } else {\n                            console.log('Moving to personalDetails phase for photo');\n                            setPhase('personalDetails');\n                        }\n                    }, 100);\n                } else {\n                    console.log('File confirmed in state:', state.file.name);\n                    // New flow logic: For moments (stories), skip personal details and go directly to face verification\n                    if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                        console.log('Moments photo upload: skipping personal details, going directly to face verification');\n                        setPhase('faceVerification');\n                    } else {\n                        console.log('Moving to personalDetails phase for photo');\n                        setPhase('personalDetails');\n                    }\n                }\n            }, 100);\n            // Handle image preview\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                var _e_target;\n                if ((_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result) {\n                    setPreviewImage(e.target.result);\n                    console.log('Preview image set for photo');\n                }\n            };\n            reader.readAsDataURL(file);\n        // Note: We don't set the phase here anymore - it's handled in the timeout above\n        };\n        // Trigger the file dialog\n        input.click();\n    };\n    // This function was previously used but is now replaced by getAppropriateCategory\n    // Keeping a comment here for reference in case it needs to be restored\n    // Handle manual upload button click\n    const handleFileUpload = async (category)=>{\n        console.log('handleFileUpload called with category:', category || 'none');\n        // Create a file input element\n        const input = document.createElement('input');\n        input.type = 'file';\n        // Reset the input value to ensure we get a new file selection event even if the same file is selected\n        input.value = '';\n        if (selectedType === 'moments') {\n            input.accept = 'image/*,video/*';\n        } else {\n            input.accept = selectedType === 'photo' || selectedType === 'photos' ? 'image/jpeg,image/png,image/gif,image/webp' // Explicitly list image types only\n             : 'video/*';\n        }\n        // Handle file selection\n        input.onchange = async (e)=>{\n            const files = e.target.files;\n            if (!files || files.length === 0) return;\n            const file = files[0];\n            console.log('File selected:', file.name, file.type, file.size);\n            // Strict validation for photo uploads - must be an image file\n            if (selectedType === 'photo' || selectedType === 'photos') {\n                const validImageTypes = [\n                    'image/jpeg',\n                    'image/png',\n                    'image/gif',\n                    'image/webp'\n                ];\n                // Check if file is a video or not a valid image type\n                if (file.type.startsWith('video/') || !validImageTypes.includes(file.type)) {\n                    console.error('Invalid file type for photos:', file.type);\n                    (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Invalid File Type', 'Only JPEG, PNG, GIF, or WebP images can be uploaded as photos. Videos are not allowed.');\n                    return;\n                }\n            }\n            // Reset the upload context before setting the new file\n            resetUpload();\n            // Set the file in the state\n            setFile(file);\n            console.log('File set in state:', file.name);\n            // If it's a video, calculate and set the duration\n            // Double-check that we're not trying to upload a video as a photo\n            if (file.type.startsWith('video/')) {\n                // Safety check - don't process videos for photo uploads\n                if (selectedType === 'photo' || selectedType === 'photos') {\n                    console.error('Attempted to process a video file for photo upload');\n                    (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Invalid File Type', 'Videos cannot be uploaded as photos. Please select an image file.');\n                    resetUpload();\n                    return;\n                }\n                try {\n                    const duration = await (0,_utils_uploadUtils__WEBPACK_IMPORTED_MODULE_10__.getVideoDuration)(file);\n                    console.log('Video duration calculated:', duration);\n                    setDuration(duration);\n                    // For moments, check if it's a video and validate the duration (max 1 minute)\n                    if (selectedType === 'moments') {\n                        console.log('Validating moments video duration...');\n                        setMediaType('video');\n                        // Check if the video is longer than 1 minute (60 seconds)\n                        if (duration > 60) {\n                            console.log(\"Moments video too long: \".concat(duration, \" seconds (max: 60 seconds)\"));\n                            // Show a more detailed error message with custom alert\n                            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showWarningAlert)('Moments Video Too Long', \"Moments videos must be 1 minute or less.\\n\\nYour video is \".concat(formatTime(duration), \" long.\\n\\nPlease select a shorter video or trim this video to 1 minute or less.\"));\n                            // Reset the upload context but preserve the selected type and category\n                            const currentType = selectedType;\n                            const currentCategory = selectedCategory;\n                            // First set the phase back to category selection\n                            setPhase('categorySelection');\n                            // Then reset the upload state\n                            setTimeout(()=>{\n                                resetUpload();\n                                setSelectedType(currentType);\n                                setSelectedCategory(currentCategory);\n                                console.log('Reset upload state after moments video duration validation failure');\n                            }, 100);\n                            // Return early to prevent further processing\n                            return;\n                        }\n                        console.log(\"Moments video duration valid: \".concat(duration, \" seconds (max: 60 seconds)\"));\n                        // For moments, we always use 'story' as the media subtype\n                        console.log('Setting media subtype for moments video to story');\n                        setMediaSubtype('story');\n                    }\n                    // If we have a category, validate the duration for that category\n                    if (selectedType && [\n                        'flashes',\n                        'glimpses',\n                        'movies'\n                    ].includes(selectedType)) {\n                        const mediaSubtype = getMediaSubtypeFromSelectedType(selectedType);\n                        const validationResult = (0,_utils_uploadUtils__WEBPACK_IMPORTED_MODULE_10__.validateVideoDuration)(duration, mediaSubtype);\n                        if (!validationResult.isValid) {\n                            // If there's a suggested category, automatically switch to it\n                            if (validationResult.suggestedCategory) {\n                                // For videos that exceed the maximum duration, automatically switch without asking\n                                console.log(\"Video exceeds maximum duration for \".concat(mediaSubtype, \". Automatically switching to \").concat(validationResult.suggestedCategory));\n                                (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showWarningAlert)('Video Duration Notice', \"Your video is too long for the \".concat(getCategoryDisplayName(mediaSubtype), \" category. It will be uploaded as a \").concat(getCategoryDisplayName(validationResult.suggestedCategory), \" instead.\"));\n                                // Switch to the suggested category\n                                console.log(\"Switching to suggested category: \".concat(validationResult.suggestedCategory));\n                                // Make sure we keep a reference to the file\n                                const currentFile = file;\n                                // Update the media subtype\n                                setMediaSubtype(validationResult.suggestedCategory);\n                                // Update the selected type to match the new category\n                                // Never suggest 'story' (moments) for other categories\n                                if (validationResult.suggestedCategory === 'flash') {\n                                    setSelectedType('flashes');\n                                } else if (validationResult.suggestedCategory === 'glimpse') {\n                                    setSelectedType('glimpses');\n                                } else if (validationResult.suggestedCategory === 'movie') {\n                                    setSelectedType('movies');\n                                }\n                                // Removed the 'story' suggestion for short videos\n                                // Make sure the file is still set in the state\n                                setTimeout(()=>{\n                                    if (!state.file) {\n                                        console.log('Re-setting file after category change:', currentFile.name);\n                                        setFile(currentFile);\n                                    }\n                                }, 50);\n                            } else {\n                                // No suggested category, just show the error\n                                (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Video Duration Error', validationResult.error || 'The video duration is not valid for this category.');\n                            }\n                        } else if (validationResult.suggestedCategory && validationResult.suggestedCategory !== mediaSubtype) {\n                            // Video is valid for current category but there's a better category\n                            // For this case, we still give the user a choice since the video is valid for the current category\n                            // Use our custom confirm dialog instead of window.confirm\n                            const confirmSwitch = await (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showAlert)({\n                                title: 'Category Suggestion',\n                                message: \"\".concat(validationResult.error, \"\\n\\nWould you like to switch to the suggested category?\"),\n                                type: 'warning',\n                                confirmText: 'Yes, Switch Category',\n                                cancelText: 'No, Keep Current',\n                                onConfirm: ()=>{}\n                            });\n                            if (confirmSwitch) {\n                                // Switch to the suggested category\n                                console.log(\"Switching to suggested category: \".concat(validationResult.suggestedCategory));\n                                // Make sure we keep a reference to the file\n                                const currentFile = file;\n                                // Update the media subtype\n                                setMediaSubtype(validationResult.suggestedCategory);\n                                // Update the selected type to match the new category\n                                // Never suggest 'story' (moments) for other categories\n                                if (validationResult.suggestedCategory === 'flash') {\n                                    setSelectedType('flashes');\n                                } else if (validationResult.suggestedCategory === 'glimpse') {\n                                    setSelectedType('glimpses');\n                                } else if (validationResult.suggestedCategory === 'movie') {\n                                    setSelectedType('movies');\n                                }\n                                // Removed the 'story' suggestion for short videos\n                                // Make sure the file is still set in the state\n                                setTimeout(()=>{\n                                    if (!state.file) {\n                                        console.log('Re-setting file after category change:', currentFile.name);\n                                        setFile(currentFile);\n                                    }\n                                }, 50);\n                            }\n                        }\n                    }\n                    // Always go to thumbnail selection for videos\n                    console.log('Moving to thumbnailSelection phase');\n                    // Keep a reference to the current file\n                    const currentFile = file;\n                    // Double-check that the file is set in the state before proceeding\n                    if (state.file) {\n                        console.log('File confirmed in state before phase change:', state.file.name);\n                        setPhase('thumbnailSelection');\n                    } else {\n                        console.log('File not found in state before phase change, setting it again');\n                        // Try setting the file again\n                        setFile(currentFile);\n                        // Add a small delay to ensure the state is updated\n                        setTimeout(()=>{\n                            // Double-check again\n                            if (!state.file) {\n                                console.log('File still not in state, setting it one more time');\n                                setFile(currentFile);\n                            }\n                            console.log('Delayed phase change to thumbnailSelection');\n                            setPhase('thumbnailSelection');\n                        }, 100);\n                    }\n                } catch (error) {\n                    console.error('Error calculating video duration:', error);\n                    // For moments videos, we need to enforce the duration check\n                    // If we can't calculate duration, we can't validate it, so we should reject the upload\n                    if (selectedType === 'moments') {\n                        (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Video Error', 'Unable to determine video duration. Please try a different video file.');\n                        resetUpload();\n                        return;\n                    }\n                    console.log('Moving to thumbnailSelection phase despite error');\n                    // Keep a reference to the current file\n                    const currentFile = file;\n                    // Double-check that the file is set in the state before proceeding\n                    if (state.file) {\n                        console.log('File confirmed in state before phase change (error case):', state.file.name);\n                        setPhase('thumbnailSelection');\n                    } else {\n                        console.log('File not found in state before phase change (error case), setting it again');\n                        // Try setting the file again\n                        setFile(currentFile);\n                        // Add a small delay to ensure the state is updated\n                        setTimeout(()=>{\n                            // Double-check again\n                            if (!state.file) {\n                                console.log('File still not in state (error case), setting it one more time');\n                                setFile(currentFile);\n                            }\n                            console.log('Delayed phase change to thumbnailSelection (error case)');\n                            setPhase('thumbnailSelection');\n                        }, 100);\n                    }\n                }\n            } else {\n                // For photos or moments images\n                if (selectedType === 'moments') {\n                    // For moments, we need to set the media type based on the file type\n                    if (file.type.startsWith('image/')) {\n                        console.log('Moments image detected');\n                        setMediaType('photo');\n                        // For moments images, we always use 'story' as the media subtype\n                        setMediaSubtype('story');\n                        // Create a local reference to the file for use in the timeout\n                        const currentFile = file;\n                        // Double-check that the file is set in the state before proceeding\n                        setTimeout(()=>{\n                            // Check if the file is in the state\n                            if (!state.file) {\n                                console.log('Moments photo not found in state after setting, trying again');\n                                // Try setting the file again\n                                setFile(currentFile);\n                            } else {\n                                console.log('Moments photo confirmed in state:', state.file.name);\n                            }\n                        }, 50);\n                    } else {\n                        console.log('Invalid file type for moments. Only images and videos less than 1 minute are allowed.');\n                        (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Invalid File Type', 'Invalid file type for moments. Only images and videos less than 1 minute are allowed.');\n                        // Reset the upload context but preserve the selected type and category\n                        const currentType = selectedType;\n                        const currentCategory = selectedCategory;\n                        // First set the phase back to category selection\n                        setPhase('categorySelection');\n                        // Then reset the upload state\n                        setTimeout(()=>{\n                            resetUpload();\n                            setSelectedType(currentType);\n                            setSelectedCategory(currentCategory);\n                            console.log('Reset upload state after invalid file type for moments');\n                        }, 100);\n                        return;\n                    }\n                }\n                // Handle image preview and set phase\n                const reader = new FileReader();\n                reader.onload = (e)=>{\n                    var _e_target;\n                    if ((_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result) {\n                        setPreviewImage(e.target.result);\n                        console.log('Preview image set for file:', file.name);\n                    }\n                };\n                reader.readAsDataURL(file);\n                // Create a local reference to the file for use in the timeout\n                const currentFile = file;\n                // Double-check that the file is set in the state before proceeding\n                setTimeout(()=>{\n                    // Check if the file is in the state\n                    if (!state.file) {\n                        console.log('File not found in state before moving to personalDetails, setting it again');\n                        // Try setting the file again\n                        setFile(currentFile);\n                        // Add another timeout to ensure the file is set\n                        setTimeout(()=>{\n                            if (!state.file) {\n                                console.log('File still not in state, setting it one more time');\n                                setFile(currentFile);\n                            } else {\n                                console.log('File confirmed in state after second attempt:', state.file.name);\n                            }\n                            // New flow logic: For moments (stories), skip personal details and go directly to face verification\n                            if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                                console.log('Moments image upload: skipping personal details, going directly to face verification');\n                                setPhase('faceVerification');\n                            } else {\n                                console.log('Moving to personalDetails phase for photo/image');\n                                setPhase('personalDetails');\n                            }\n                        }, 100);\n                    } else {\n                        console.log('File confirmed in state:', state.file.name);\n                        // New flow logic: For moments (stories), skip personal details and go directly to face verification\n                        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                            console.log('Moments image upload: skipping personal details, going directly to face verification');\n                            setPhase('faceVerification');\n                        } else {\n                            console.log('Moving to personalDetails phase for photo/image');\n                            setPhase('personalDetails');\n                        }\n                    }\n                }, 100);\n            }\n        };\n        // Trigger the file dialog\n        input.click();\n    };\n    // Handle personal details completed\n    const handlePersonalDetailsCompleted = (details)=>{\n        console.log('Personal details completed:', details);\n        // Store the personal details in local state for component persistence\n        setLocalPersonalDetails(details);\n        // Validate that we have a title\n        if (!details.caption || !details.caption.trim()) {\n            console.error('Caption/title is empty, this should not happen');\n            // Go back to personal details to fix this\n            setPhase('personalDetails');\n            return;\n        }\n        // Set the title in the upload context\n        setTitle(details.caption.trim());\n        // Also store in global context for persistence (this is the upload context function)\n        setPersonalDetails(details);\n        // Check if we have a file in the state\n        if (!state.file) {\n            console.error('No file found in state after personal details');\n            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Upload Error', 'Something went wrong with your file upload. Please try again.');\n            setPhase('typeSelection');\n            return;\n        }\n        console.log('File confirmed in state after personal details:', state.file.name);\n        console.log('Personal details set successfully');\n        console.log('Title set to:', details.caption.trim());\n        console.log('Current selectedType:', selectedType);\n        console.log('Current mediaSubtype:', state.mediaSubtype);\n        // New flow logic based on backend requirements:\n        // - Moments (stories): Skip personal details, go directly to face verification\n        // - Photos: Go to face verification after personal details (no vendor details)\n        // - Videos: Go to vendor details after personal details\n        if (state.mediaType === 'photo') {\n            console.log('Photo upload: proceeding to face verification (no vendor details needed)');\n            setPhase('faceVerification');\n        } else if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n            console.log('Moments upload: proceeding to face verification (no vendor details needed)');\n            setPhase('faceVerification');\n        } else {\n            // For videos (flashes, glimpses, movies), proceed to vendor details\n            console.log('Video upload: proceeding to vendor details');\n            setPhase('vendorDetails');\n        }\n    };\n    // Handle vendor details completed\n    const handleVendorDetailsCompleted = (vendorDetails)=>{\n        // console.log('Vendor details completed:', vendorDetails);\n        // Normalize vendor details to ensure consistent field names\n        const normalizedVendorDetails = {\n            ...vendorDetails\n        };\n        // Ensure we have both frontend and backend field names for makeup artist and decorations\n        if (vendorDetails.makeupArtist) {\n            normalizedVendorDetails.makeup_artist = vendorDetails.makeupArtist;\n        } else if (vendorDetails.makeup_artist) {\n            normalizedVendorDetails.makeupArtist = vendorDetails.makeup_artist;\n        }\n        if (vendorDetails.decorations) {\n            normalizedVendorDetails.decoration = vendorDetails.decorations;\n        } else if (vendorDetails.decoration) {\n            normalizedVendorDetails.decorations = vendorDetails.decoration;\n        }\n        // Store the normalized vendor details for persistence between screens\n        setVendorDetailsData(normalizedVendorDetails);\n        // Also store in the ref for Edge browser compatibility\n        vendorDetailsRef.current = normalizedVendorDetails;\n        // Store vendor details in localStorage for persistence\n        try {\n            localStorage.setItem('wedzat_vendor_details', JSON.stringify(normalizedVendorDetails));\n            console.log('UPLOAD MANAGER - Stored vendor details in localStorage');\n        } catch (error) {\n            console.error('UPLOAD MANAGER - Failed to store vendor details in localStorage:', error);\n        }\n        // Save the current video_category before setting vendor details\n        const currentVideoCategory = state.detailFields.video_category;\n        console.log('UPLOAD MANAGER - Saving video_category before vendor details:', currentVideoCategory);\n        // Store video_category in localStorage\n        if (currentVideoCategory) {\n            try {\n                localStorage.setItem('wedzat_video_category', currentVideoCategory);\n                console.log('UPLOAD MANAGER - Stored video_category in localStorage:', currentVideoCategory);\n            } catch (error) {\n                console.error('UPLOAD MANAGER - Failed to store video_category in localStorage:', error);\n            }\n        }\n        // Store in global context for persistence\n        setVendorDetails(normalizedVendorDetails);\n        // Explicitly set each vendor detail field\n        Object.entries(normalizedVendorDetails).forEach((param)=>{\n            let [vendorType, details] = param;\n            if (details && details.name && details.mobileNumber) {\n                setDetailField(\"vendor_\".concat(vendorType, \"_name\"), details.name);\n                setDetailField(\"vendor_\".concat(vendorType, \"_contact\"), details.mobileNumber);\n            }\n        });\n        // Re-set the video_category after vendor details to ensure it's preserved\n        if (currentVideoCategory) {\n            console.log('UPLOAD MANAGER - Re-setting video_category after vendor details:', currentVideoCategory);\n            setTimeout(()=>{\n                setDetailField('video_category', currentVideoCategory);\n            }, 100);\n        }\n        // Log all detail fields after setting vendor details\n        setTimeout(()=>{\n            console.log('All detail fields after vendor details:', state.detailFields);\n            console.log('Detail fields count:', Object.keys(state.detailFields).length);\n            console.log('Normalized vendor details:', normalizedVendorDetails);\n        }, 200);\n        // Add a small delay to ensure the state is updated before proceeding\n        // This helps with cross-browser compatibility, especially in Edge\n        setTimeout(()=>{\n            // Double-check that we have at least 4 vendor details before proceeding\n            const vendorNameFields = Object.keys(state.detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_name'));\n            const vendorContactFields = Object.keys(state.detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_contact'));\n            console.log('UPLOAD MANAGER - Vendor name fields:', vendorNameFields.length);\n            console.log('UPLOAD MANAGER - Vendor contact fields:', vendorContactFields.length);\n            // Edge browser workaround - directly set vendor details in the state\n            if ( true && /Edge|Edg/.test(window.navigator.userAgent)) {\n                console.log('UPLOAD MANAGER - Edge browser detected, applying direct vendor details workaround');\n                // Create vendor detail fields directly in the state\n                // This is a workaround for Edge browser where the state update doesn't properly preserve vendor details\n                Object.entries(normalizedVendorDetails).forEach((param)=>{\n                    let [vendorType, details] = param;\n                    if (details && details.name && details.mobileNumber) {\n                        // Set the vendor details directly in the state\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                        // Also set the normalized version\n                        const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                        if (normalizedType !== vendorType) {\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                        }\n                        console.log(\"UPLOAD MANAGER - Edge workaround: Added vendor \".concat(vendorType, \" directly to state\"));\n                    }\n                });\n                // Re-set the video_category directly\n                if (currentVideoCategory) {\n                    state.detailFields.video_category = currentVideoCategory;\n                    console.log('UPLOAD MANAGER - Edge workaround: Re-set video_category directly:', currentVideoCategory);\n                }\n            }\n            // Proceed to face verification\n            setPhase('faceVerification');\n        }, 300);\n    };\n    // Handle thumbnail selection\n    const handleThumbnailSelected = (thumbnailFile)=>{\n        if (thumbnailFile) {\n            // Set the thumbnail in the context\n            setThumbnail(thumbnailFile);\n            console.log('Thumbnail selected:', thumbnailFile.name);\n        } else {\n            console.log('No thumbnail selected, using auto-generated thumbnail');\n        }\n        // New flow logic: For moments (stories), skip personal details and go directly to face verification\n        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n            console.log('Moments upload: skipping personal details, going directly to face verification');\n            setPhase('faceVerification');\n        } else {\n            // For photos and videos, go to personal details\n            console.log('Photo/Video upload: proceeding to personal details');\n            setPhase('personalDetails');\n        }\n    };\n    // Function to proceed with upload after vendor details are applied\n    const proceedWithUpload = (videoCategory)=>{\n        // For moments (stories), this function should not be called, but add safety check\n        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n            console.log('UPLOAD MANAGER - Moments detected in proceedWithUpload, calling startUpload directly');\n            startUpload();\n            return;\n        }\n        // Double-check that we have a title before changing to uploading phase\n        if (!state.title || !state.title.trim()) {\n            console.error('Title is missing before upload, setting it from personal details');\n            // Try to set the title from personal details\n            if (personalDetails.caption && personalDetails.caption.trim()) {\n                // console.log('Setting personal details from local state:', personalDetails);\n                // Use the global context function to set all personal details at once\n                setPersonalDetails(personalDetails);\n                // Explicitly set the title as well\n                setTitle(personalDetails.caption.trim());\n            } else {\n                console.error('No title in personal details either, going back to personal details');\n                setPhase('personalDetails');\n                return;\n            }\n        }\n        // Edge browser workaround - directly set vendor details in the state\n        if ( true && /Edge|Edg/.test(window.navigator.userAgent)) {\n            console.log('UPLOAD MANAGER - Edge browser detected in face verification, applying vendor details workaround');\n            // Get the vendor details from the vendor details data\n            const vendorDetailsData = vendorDetailsRef.current;\n            if (vendorDetailsData) {\n                console.log('UPLOAD MANAGER - Edge workaround: Using vendor details from ref:', vendorDetailsData);\n                // Create vendor detail fields directly in the state\n                Object.entries(vendorDetailsData).forEach((param)=>{\n                    let [vendorType, details] = param;\n                    if (details && details.name && details.mobileNumber) {\n                        // Set the vendor details directly in the state\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                        // Also set the normalized version\n                        const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                        if (normalizedType !== vendorType) {\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                        }\n                        console.log(\"UPLOAD MANAGER - Edge workaround: Added vendor \".concat(vendorType, \" directly to state\"));\n                    }\n                });\n            }\n        }\n        // For videos, check if we have a video_category\n        if (state.mediaType === 'video') {\n            console.log(\"UPLOAD MANAGER - Checking video_category before upload\");\n            console.log(\"UPLOAD MANAGER - Current video_category: \".concat(state.detailFields.video_category || 'Not set'));\n            console.log(\"UPLOAD MANAGER - Current mediaSubtype: \".concat(state.mediaSubtype));\n            console.log(\"UPLOAD MANAGER - Selected category: \".concat(selectedCategory || 'Not set'));\n            // Special handling for glimpses\n            if (state.mediaSubtype === 'glimpse') {\n                console.log(\"UPLOAD MANAGER - Special handling for glimpses\");\n                // If we don't have a video_category yet, try to set it from selectedCategory\n                if (!state.detailFields.video_category && selectedCategory) {\n                    // Map the UI category to the backend video_category\n                    let videoCategory = '';\n                    if (selectedCategory === 'my_wedding_videos') {\n                        videoCategory = 'my_wedding';\n                    } else if (selectedCategory === 'wedding_vlog') {\n                        videoCategory = 'wedding_vlog';\n                    } else if (selectedCategory === 'friends_family_videos') {\n                        videoCategory = 'friends_family_video';\n                    }\n                    if (videoCategory) {\n                        console.log(\"UPLOAD MANAGER - Setting video_category for glimpse: \".concat(videoCategory));\n                        setDetailField('video_category', videoCategory);\n                    }\n                } else {\n                    console.log(\"UPLOAD MANAGER - Glimpse already has video_category: \".concat(state.detailFields.video_category));\n                }\n            }\n            // If we still don't have a video_category, use a default based on selectedCategory\n            if (!state.detailFields.video_category && selectedCategory) {\n                console.log(\"UPLOAD MANAGER - No video_category set, using selectedCategory: \".concat(selectedCategory));\n                // Map the UI category to the backend video_category\n                let videoCategory = '';\n                if (selectedCategory === 'my_wedding_videos') {\n                    videoCategory = 'my_wedding';\n                } else if (selectedCategory === 'wedding_vlog') {\n                    videoCategory = 'wedding_vlog';\n                } else if (selectedCategory === 'friends_family_videos') {\n                    videoCategory = 'friends_family_video';\n                }\n                if (videoCategory) {\n                    console.log(\"UPLOAD MANAGER - Setting video_category from selectedCategory: \".concat(videoCategory));\n                    setDetailField('video_category', videoCategory);\n                }\n            }\n            // Final check - if we still don't have a video_category, use a default\n            if (!state.detailFields.video_category) {\n                console.log('No video_category found, using a default one');\n                // Use 'my_wedding' as a default category instead of asking the user again\n                setDetailField('video_category', 'my_wedding');\n                console.log('Set default video_category to my_wedding');\n            }\n        }\n        // Edge browser workaround - directly set vendor details in the state before upload\n        if ( true && /Edge|Edg/.test(window.navigator.userAgent)) {\n            console.log('UPLOAD MANAGER - Edge browser detected before upload, applying vendor details workaround');\n            // Get the vendor details from the vendor details data\n            const vendorDetailsData = vendorDetailsRef.current;\n            if (vendorDetailsData && Object.keys(vendorDetailsData).length > 0) {\n                console.log('UPLOAD MANAGER - Edge workaround: Using vendor details from ref before upload:', vendorDetailsData);\n                // Create vendor detail fields directly in the state\n                Object.entries(vendorDetailsData).forEach((param)=>{\n                    let [vendorType, details] = param;\n                    if (details && details.name && details.mobileNumber) {\n                        // Set the vendor details directly in the state\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                        // Also set the normalized version\n                        const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                        if (normalizedType !== vendorType) {\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                        }\n                        console.log(\"UPLOAD MANAGER - Edge workaround: Added vendor \".concat(vendorType, \" directly to state before upload\"));\n                    }\n                });\n            }\n        }\n        // Check if we have a file before proceeding\n        if (!state.file) {\n            console.error('No file found in state before upload');\n            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Upload Error', 'No file selected. Please select a file to upload.');\n            // Go back to type selection to start over\n            setPhase('typeSelection');\n            return;\n        }\n        // Now we can proceed to uploading phase\n        setPhase('uploading');\n        // Log the current state before starting upload\n        console.log('Current state before upload:', {\n            file: state.file ? state.file.name : 'No file',\n            mediaType: state.mediaType,\n            mediaSubtype: state.mediaSubtype,\n            title: state.title,\n            description: state.description,\n            detailFields: state.detailFields,\n            detailFieldsCount: Object.keys(state.detailFields).length\n        });\n        // Double-check that we're using the correct category\n        console.log(\"UPLOAD MANAGER - Final check - Selected type: \".concat(selectedType));\n        console.log(\"UPLOAD MANAGER - Final check - MediaSubtype in state: \".concat(state.mediaSubtype));\n        // If the mediaSubtype doesn't match what we expect based on the selected type, fix it\n        if (selectedType && state.mediaSubtype !== getMediaSubtypeFromSelectedType(selectedType)) {\n            console.log(\"UPLOAD MANAGER - WARNING: MediaSubtype mismatch detected!\");\n            console.log(\"UPLOAD MANAGER - Expected mediaSubtype based on selected type: \".concat(getMediaSubtypeFromSelectedType(selectedType)));\n            console.log(\"UPLOAD MANAGER - Actual mediaSubtype in state: \".concat(state.mediaSubtype));\n            console.log(\"UPLOAD MANAGER - Correcting category before upload...\");\n            // Get the corrected category\n            const correctedCategory = getMediaSubtypeFromSelectedType(selectedType);\n            console.log(\"UPLOAD MANAGER - Category corrected to: \".concat(correctedCategory));\n            // Get the video_category from the original selection\n            // We need to map it to the correct backend value\n            let videoCategory = '';\n            if (selectedCategory === 'my_wedding_videos') {\n                videoCategory = 'my_wedding';\n            } else if (selectedCategory === 'wedding_vlog') {\n                videoCategory = 'wedding_vlog';\n            } else if (selectedCategory === 'friends_family_videos') {\n                videoCategory = 'friends_family_video';\n            }\n            console.log(\"UPLOAD MANAGER - Original selected category: \".concat(selectedCategory));\n            console.log(\"UPLOAD MANAGER - Mapped to backend video_category: \".concat(videoCategory));\n            // Start the upload process with the corrected category and video_category\n            startUploadWithCategory(correctedCategory, videoCategory);\n        } else {\n            // Get the video_category from the state\n            const finalVideoCategory = videoCategory || state.detailFields.video_category || 'my_wedding';\n            console.log(\"UPLOAD MANAGER - Using video_category for upload: \".concat(finalVideoCategory));\n            // Start the upload process with the current category and video_category\n            startUploadWithCategory(state.mediaSubtype, finalVideoCategory).then(()=>{\n                // Upload completed successfully\n                console.log('Upload completed successfully');\n            }).catch((error)=>{\n                console.error('Upload failed:', error);\n            });\n        }\n    };\n    // Handle face verification completed and start upload\n    const handleFaceVerificationCompleted = ()=>{\n        console.log('Face verification completed, starting upload process');\n        // Check if we have a file in the state\n        if (!state.file) {\n            console.error('No file found in state after face verification');\n            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Upload Error', 'Something went wrong with your file upload. Please try again.');\n            setPhase('typeSelection');\n            return;\n        }\n        console.log('File confirmed in state after face verification:', state.file.name);\n        // For moments (stories), skip all vendor processing and go directly to upload\n        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n            console.log('UPLOAD MANAGER - Moments detected after face verification, proceeding directly to upload');\n            console.log('UPLOAD MANAGER - Current selectedType:', selectedType);\n            console.log('UPLOAD MANAGER - Current state.mediaSubtype:', state.mediaSubtype);\n            // Ensure mediaSubtype is set to 'story' for moments\n            if (state.mediaSubtype !== 'story') {\n                console.log('UPLOAD MANAGER - Setting mediaSubtype to story for moments');\n                setMediaSubtype('story');\n            }\n            // Set a default title if not already set (using filename without extension)\n            if (!state.title || !state.title.trim()) {\n                const defaultTitle = state.file.name.replace(/\\.[^/.]+$/, \"\"); // Remove file extension\n                setTitle(defaultTitle);\n                console.log('UPLOAD MANAGER - Set default title for moments:', defaultTitle);\n            }\n            // Proceed directly to upload without vendor details processing\n            setTimeout(()=>{\n                console.log('UPLOAD MANAGER - About to call startUpload for moments');\n                console.log('UPLOAD MANAGER - Final check - state.mediaSubtype:', state.mediaSubtype);\n                startUpload();\n            }, 200); // Increased timeout to allow state updates\n            return;\n        }\n        // Try to get vendor details from localStorage first\n        let vendorDetailsData = vendorDetailsRef.current;\n        // If not in ref, try localStorage\n        if (!vendorDetailsData || Object.keys(vendorDetailsData).length === 0) {\n            try {\n                const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n                if (storedVendorDetails) {\n                    vendorDetailsData = JSON.parse(storedVendorDetails);\n                    console.log('UPLOAD MANAGER - Retrieved vendor details from localStorage:', storedVendorDetails);\n                    // Update the ref with the localStorage data\n                    vendorDetailsRef.current = vendorDetailsData;\n                    // Log the vendor details we found\n                    console.log(\"UPLOAD MANAGER - Found \".concat(Object.keys(vendorDetailsData).length, \" vendor details in localStorage\"));\n                    Object.entries(vendorDetailsData).forEach((param)=>{\n                        let [vendorType, details] = param;\n                        if (details && details.name && details.mobileNumber) {\n                            console.log(\"UPLOAD MANAGER - Vendor \".concat(vendorType, \": \").concat(details.name, \" (\").concat(details.mobileNumber, \")\"));\n                        }\n                    });\n                } else {\n                    console.log('UPLOAD MANAGER - No vendor details found in localStorage');\n                }\n            } catch (error) {\n                console.error('UPLOAD MANAGER - Failed to retrieve vendor details from localStorage:', error);\n            }\n        } else {\n            console.log(\"UPLOAD MANAGER - Using \".concat(Object.keys(vendorDetailsData).length, \" vendor details from ref\"));\n        }\n        // Try to get video_category from localStorage\n        let videoCategory = state.detailFields.video_category;\n        if (!videoCategory) {\n            try {\n                const storedVideoCategory = localStorage.getItem('wedzat_video_category');\n                if (storedVideoCategory) {\n                    videoCategory = storedVideoCategory;\n                    console.log('UPLOAD MANAGER - Retrieved video_category from localStorage:', videoCategory);\n                    // Set it in the state\n                    setDetailField('video_category', videoCategory);\n                }\n            } catch (error) {\n                console.error('UPLOAD MANAGER - Failed to retrieve video_category from localStorage:', error);\n            }\n        }\n        // Ensure vendor details are present\n        if (vendorDetailsData) {\n            console.log('UPLOAD MANAGER - Applying vendor details to state');\n            // Create a batch of all detail fields to update at once\n            const detailFieldUpdates = {};\n            let completeVendorCount = 0;\n            // Re-apply vendor details to ensure they're in the state\n            Object.entries(vendorDetailsData).forEach((param)=>{\n                let [vendorType, details] = param;\n                if (details && details.name && details.mobileNumber) {\n                    // Add to the batch\n                    detailFieldUpdates[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                    detailFieldUpdates[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                    completeVendorCount++;\n                    // Also set normalized versions\n                    const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                    if (normalizedType !== vendorType) {\n                        detailFieldUpdates[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                        detailFieldUpdates[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                    }\n                }\n            });\n            // Apply all updates at once\n            console.log(\"UPLOAD MANAGER - Applying \".concat(completeVendorCount, \" complete vendor details to state\"));\n            console.log('UPLOAD MANAGER - Detail field updates:', JSON.stringify(detailFieldUpdates));\n            // Apply each update individually to ensure they're all set\n            Object.entries(detailFieldUpdates).forEach((param)=>{\n                let [field, value] = param;\n                setDetailField(field, value);\n            });\n            // Add a delay before proceeding to ensure state updates are applied\n            setTimeout(()=>{\n                console.log('UPLOAD MANAGER - Vendor details applied to state, proceeding with upload');\n                proceedWithUpload(videoCategory);\n            }, 500);\n        } else {\n            console.log('UPLOAD MANAGER - No vendor details found, proceeding with upload');\n            proceedWithUpload(videoCategory);\n        }\n    // This code has been moved to the proceedWithUpload function\n    };\n    // Handle going back to personal details from upload error\n    const handleBackToPersonalDetails = ()=>{\n        // console.log('Going back to personal details with stored data:', personalDetails);\n        // Make sure the personal details are set in the context\n        if (personalDetails.caption && personalDetails.caption.trim()) {\n            // Use the global context function to set all personal details at once\n            setPersonalDetails(personalDetails);\n        }\n        setPhase('personalDetails');\n    };\n    // Handle close modal\n    const handleClose = ()=>{\n        // Check if upload was successful and call onUploadComplete\n        if (state.step === 'complete' && onUploadComplete) {\n            console.log('Upload completed successfully, calling onUploadComplete callback');\n            onUploadComplete();\n        }\n        // Reset the phase first\n        setPhase('closed');\n        // Call the onClose callback if provided\n        if (onClose) {\n            onClose();\n        }\n        // Reset the upload state after a short delay to ensure the modal is closed first\n        setTimeout(()=>{\n            resetUpload();\n            console.log('Upload state reset after modal close');\n        }, 100);\n    };\n    // Render selected phase component\n    if (phase === 'closed') {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            phase === 'typeSelection' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UploadTypeSelection__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                onNext: handleTypeSelected,\n                onClose: handleClose\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1383,\n                columnNumber: 9\n            }, undefined),\n            phase === 'categorySelection' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VideoCategorySelection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onNext: handleCategorySelected,\n                onBack: ()=>setPhase('typeSelection'),\n                onUpload: handleCategorySelected,\n                onThumbnailUpload: handleThumbnailUpload,\n                onClose: handleClose,\n                mediaType: state.mediaType,\n                selectedType: selectedType\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1390,\n                columnNumber: 9\n            }, undefined),\n            phase === 'thumbnailSelection' && state.file && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ThumbnailSelection__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                videoFile: state.file,\n                onNext: handleThumbnailSelected,\n                onBack: ()=>{\n                    // Go back to category selection instead of triggering file upload again\n                    if ([\n                        'flashes',\n                        'glimpses',\n                        'movies'\n                    ].includes(selectedType)) {\n                        setPhase('categorySelection');\n                    } else {\n                        // For moments, go back to type selection\n                        setPhase('typeSelection');\n                    }\n                },\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1402,\n                columnNumber: 9\n            }, undefined),\n            phase === 'personalDetails' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PersonalDetails__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onNext: handlePersonalDetailsCompleted,\n                onBack: ()=>{\n                    // Go back to thumbnail selection for videos\n                    if (state.mediaType === 'video' && state.file) {\n                        setPhase('thumbnailSelection');\n                    } else {\n                        // For photos, go back to type selection\n                        setPhase('typeSelection');\n                    }\n                },\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                },\n                previewImage: previewImage,\n                videoFile: state.mediaType === 'video' ? state.file : null,\n                mediaType: state.mediaType,\n                contentType: getContentType(),\n                initialDetails: personalDetails\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1423,\n                columnNumber: 9\n            }, undefined),\n            phase === 'vendorDetails' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VendorDetails__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                onNext: handleVendorDetailsCompleted,\n                onBack: ()=>setPhase('personalDetails'),\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                },\n                initialVendorDetails: vendorDetailsData,\n                videoCategory: (()=>{\n                    const category = state.detailFields.video_category || 'my_wedding';\n                    console.log('UPLOAD MANAGER - Passing video category to VendorDetails:', category);\n                    return category;\n                })()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1448,\n                columnNumber: 9\n            }, undefined),\n            phase === 'faceVerification' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FaceVerification__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                onUpload: handleFaceVerificationCompleted,\n                onBack: ()=>{\n                    // New flow logic for back navigation:\n                    // - Moments: Go back to thumbnail selection (or type selection for images)\n                    // - Photos: Go back to personal details\n                    // - Videos: Go back to vendor details\n                    if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                        // For moments, go back to thumbnail selection for videos, or type selection for images\n                        if (state.mediaType === 'video') {\n                            setPhase('thumbnailSelection');\n                        } else {\n                            setPhase('typeSelection');\n                        }\n                    } else if (state.mediaType === 'photo') {\n                        setPhase('personalDetails');\n                    } else {\n                        setPhase('vendorDetails');\n                    }\n                },\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1466,\n                columnNumber: 9\n            }, undefined),\n            (phase === 'uploading' || phase === 'complete') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UploadProgress__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                },\n                onGoBack: handleBackToPersonalDetails\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1495,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(UploadManager, \"LQsMODnAAL1BJd6LrYNCqh7ZCEM=\", false, function() {\n    return [\n        _contexts_UploadContexts__WEBPACK_IMPORTED_MODULE_9__.useUpload,\n        _hooks_useMedia__WEBPACK_IMPORTED_MODULE_12__.useMediaUpload\n    ];\n});\n_c = UploadManager;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UploadManager);\nvar _c;\n$RefreshReg$(_c, \"UploadManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/upload/UploadManager.tsx\n"));

/***/ })

});