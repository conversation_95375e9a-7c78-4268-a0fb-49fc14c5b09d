'use client';

import React from 'react';
import { createRoot } from 'react-dom/client';
import CustomAlert from '../components/ui/CustomAlert';

interface AlertOptions {
  title: string;
  message: string;
  type?: 'error' | 'warning' | 'success' | 'info';
  confirmText?: string;
  cancelText?: string;
  onConfirm?: () => void;
}

export const showAlert = (options: AlertOptions): Promise<boolean> => {
  return new Promise((resolve) => {
    // Create a div element to mount the alert
    const alertContainer = document.createElement('div');
    alertContainer.id = 'custom-alert-container';
    document.body.appendChild(alertContainer);

    // Create a root for the alert
    const root = createRoot(alertContainer);

    // Function to remove the alert from the DOM
    const removeAlert = () => {
      root.unmount();
      if (alertContainer.parentNode) {
        document.body.removeChild(alertContainer);
      }
    };

    // Render the alert
    root.render(
      <CustomAlert
        title={options.title}
        message={options.message}
        type={options.type || 'info'}
        confirmText={options.confirmText}
        cancelText={options.cancelText}
        onClose={() => {
          removeAlert();
          resolve(false);
        }}
        onConfirm={options.onConfirm ? () => {
          if (options.onConfirm) options.onConfirm();
          removeAlert();
          resolve(true);
        } : undefined}
      />
    );
  });
};

// Helper functions for common alert types
export const showErrorAlert = (title: string, message: string, confirmText = 'OK') => {
  return showAlert({ title, message, type: 'error', confirmText });
};

export const showWarningAlert = (title: string, message: string, confirmText = 'OK') => {
  return showAlert({ title, message, type: 'warning', confirmText });
};

export const showSuccessAlert = (title: string, message: string, confirmText = 'OK') => {
  return showAlert({ title, message, type: 'success', confirmText });
};

export const showInfoAlert = (title: string, message: string, confirmText = 'OK') => {
  return showAlert({ title, message, type: 'info', confirmText });
};

export const showConfirmAlert = (title: string, message: string, confirmText = 'Yes', cancelText = 'No') => {
  return showAlert({
    title,
    message,
    type: 'warning',
    confirmText,
    cancelText,
    onConfirm: () => {}
  });
};
