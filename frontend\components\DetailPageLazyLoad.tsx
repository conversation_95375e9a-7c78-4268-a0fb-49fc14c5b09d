import React, { useEffect, useRef, useState } from 'react';

interface DetailPageLazyLoadProps {
  children: ((props: { isVisible: boolean, shouldLoad: boolean }) => React.ReactNode) | React.ReactNode;
  placeholder?: React.ReactNode;
  threshold?: number;
  rootMargin?: string;
  delayLoadUntilVisible?: boolean;
  id?: string;
  index?: number; // Position index for staggered loading
}

// Keep track of which components have already been loaded to prevent duplicate loading
const loadedComponents = new Set<string>();

const DetailPageLazyLoad: React.FC<DetailPageLazyLoadProps> = ({
  children,
  placeholder = <div className="h-40 w-full bg-gray-100 animate-pulse rounded-lg"></div>,
  threshold = 0.1,
  rootMargin = '0px 0px 200px 0px',
  delayLoadUntilVisible = true,
  id = 'component-' + Math.random().toString(36).substr(2, 9),
  index = 0,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [shouldLoad, setShouldLoad] = useState(!delayLoadUntilVisible);
  const [hasLoaded, setHasLoaded] = useState(loadedComponents.has(id));
  const ref = useRef<HTMLDivElement>(null);
  
  // Add staggered delay based on index
  const staggerDelay = index * 300; // 300ms delay between each component
  
  // Check if this component has already been loaded in a previous render
  useEffect(() => {
    if (loadedComponents.has(id)) {
      setIsVisible(true);
      setShouldLoad(true);
      setHasLoaded(true);
    }
  }, [id]);

  useEffect(() => {
    // Skip if already loaded
    if (hasLoaded) return;
    
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasLoaded) {
          console.log(`Detail component ${id} is now visible, loading content...`);
          setIsVisible(true);
          
          // Add staggered delay for loading
          setTimeout(() => {
            setShouldLoad(true); // Allow data loading when visible
            setHasLoaded(true);
            
            // Mark this component as loaded to prevent duplicate loading
            loadedComponents.add(id);
          }, staggerDelay);
          
          // Once loaded, we can disconnect the observer
          observer.disconnect();
        }
      },
      {
        threshold,
        rootMargin,
      }
    );

    const currentRef = ref.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [hasLoaded, threshold, rootMargin, id, staggerDelay]);

  return (
    <div ref={ref} className="w-full">
      {isVisible
        ? typeof children === 'function'
          ? children({ isVisible, shouldLoad })
          : children
        : placeholder}
    </div>
  );
};

export default DetailPageLazyLoad;
