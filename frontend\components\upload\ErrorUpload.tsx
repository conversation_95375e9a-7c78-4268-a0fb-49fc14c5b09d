// components/upload/ErrorUpload.tsx
'use client';

import React from 'react';
import { X } from 'lucide-react';
import { useUpload } from '../../contexts/UploadContexts';

const ErrorUpload: React.FC = () => {
  const { state, goToStep, resetUpload } = useUpload();
  
  const handleTryAgain = () => {
    goToStep(state.file ? "details" : "selecting");
  };
  
  return (
    <div className="text-center py-8">
      <div className="mx-auto w-24 h-24 mb-4 flex items-center justify-center rounded-full bg-red-50">
        <X className="h-12 w-12 text-red-500" />
      </div>
      <h3 className="text-lg font-medium text-gray-900">
        Upload Failed
      </h3>
      <p className="mt-1 text-sm text-red-500">
        {state.error || "Something went wrong. Please try again."}
      </p>
      
      <div className="mt-6 flex justify-center space-x-4">
        <button
          type="button"
          onClick={handleTryAgain}
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
        >
          Try Again
        </button>
        <button
          type="button"
          onClick={resetUpload}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
        >
          Cancel
        </button>
      </div>
    </div>
  );
};

export default ErrorUpload;