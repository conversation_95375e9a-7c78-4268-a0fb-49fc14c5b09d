"use client";

import React, {
  createContext,
  useState,
  useContext,
  ReactNode,
  useEffect,
} from "react";
import { useRouter } from "next/navigation";
import { useUser } from "@clerk/nextjs";
import { authService } from "../services/api";
import { userService } from "../services/api";
import { UserProfile } from "../utils/auth";

interface AuthContextType {
  isAuthenticated: boolean;
  userProfile: UserProfile | null;
  isLoading: boolean;
  login: (token: string) => void;
  logout: () => void;
  updateProfile: (profile: UserProfile) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const router = useRouter();
  const { isSignedIn, isLoaded: isClerkLoaded, user: clerkUser } = useUser();
  const [authInitialized, setAuthInitialized] = useState<boolean>(false);

  useEffect(() => {
    // Function to initialize authentication
    const initAuth = async () => {
      setIsLoading(true);
      try {
        // Check if we have a token (including vendor token)
        const token =
          localStorage.getItem("token") || localStorage.getItem("jwt_token");

        console.log(
          `Auth context initialization: Token ${token ? "found" : "not found"}`
        );
        console.log(
          `Clerk authentication: ${isClerkLoaded
            ? isSignedIn
              ? "signed in"
              : "not signed in"
            : "loading"
          }`
        );

        // Check if authenticated with Clerk
        if (isClerkLoaded && isSignedIn) {
          console.log("User is authenticated with Clerk");
          setIsAuthenticated(true);

          // If we don't have a token but have Clerk auth, try to get one
          if (!token && clerkUser) {
            try {
              // This assumes you have an endpoint to convert Clerk session to JWT
              console.log("Getting token from Clerk session");
              const sessions = await clerkUser.getSessions();
              const clerkToken = sessions[0]?.id;

              if (clerkToken) {
                // Exchange Clerk token for your JWT
                // Extract user info to send along with the token
                const userEmail =
                  clerkUser?.primaryEmailAddress?.emailAddress || "";
                const userName = clerkUser?.fullName || "";
                const userId = clerkUser?.id || "";

                console.log("Sending user info to backend:", {
                  email: userEmail,
                  name: userName,
                  id: userId,
                });

                const response = await authService.clerkAuth({
                  clerk_token: clerkToken,
                  user_type: "customer",
                  user_email: userEmail,
                  user_name: userName,
                  user_id: userId,
                });

                if (response && response.token) {
                  localStorage.setItem("token", response.token);
                  localStorage.setItem("wedzat_token", response.token); // Also save as wedzat_token for consistency
                  console.log("Token obtained from Clerk session");
                } else {
                  // If backend auth fails, store the Clerk token as a fallback
                  console.log(
                    "No token received from backend, using Clerk token as fallback"
                  );
                  // Use the token we already have
                  localStorage.setItem("token", clerkToken);
                  localStorage.setItem("wedzat_token", clerkToken);
                }
              }
            } catch (error) {
              console.error("Error getting token from Clerk session:", error);

              // If there's an error with the backend, use the Clerk token as a fallback
              // Get the token again if needed
              try {
                const sessions = await clerkUser.getSessions();
                const fallbackToken = sessions[0]?.id;

                if (fallbackToken) {
                  console.log(
                    "Using Clerk token as fallback due to backend error"
                  );
                  localStorage.setItem("token", fallbackToken);
                  localStorage.setItem("wedzat_token", fallbackToken);
                }
              } catch (tokenError) {
                console.error("Error getting fallback token:", tokenError);
              }
            }
          }

          setIsLoading(false);
          setAuthInitialized(true);
          return;
        }

        if (!token) {
          setIsAuthenticated(false);
          setUserProfile(null);
          setIsLoading(false);
          setAuthInitialized(true);

          // If we're on a protected route, redirect to login
          if (typeof window !== "undefined") {
            const pathname = window.location.pathname;
            const protectedRoutes = [
              "/home",
              "/dashboard",
              "/profile",
              "/protected",
            ];

            if (
              protectedRoutes.some(
                (route) =>
                  pathname === route || pathname.startsWith(`${route}/`)
              )
            ) {
              console.log(
                "No token found on protected route, redirecting to login"
              );
              router.push("/");
            }
          }
          return;
        }

        // Attempt to get user profile
        try {
          console.log("Fetching user profile");

          // Check if this is a vendor token
          const isVendorToken = localStorage.getItem("is_vendor") === "true";

          if (isVendorToken) {
            console.log("Detected vendor token, setting authenticated state");
            // For vendors, we don't need to fetch a profile, just set authenticated
            setIsAuthenticated(true);
            setUserProfile(null); // No regular user profile for vendors
          } else {
            // For regular users, fetch the profile
            const profile = await userService.getUserDetails();
            setUserProfile(profile);
            setIsAuthenticated(true);
          }
        } catch (error) {
          console.error("Error fetching user profile:", error);

          // Check if this might be a vendor token
          try {
            // If we're on a vendor page, this might be a vendor token
            const pathname = window.location.pathname;
            if (pathname.includes("/vendor/")) {
              console.log("On vendor page with possible vendor token, setting authenticated");
              localStorage.setItem("is_vendor", "true");
              setIsAuthenticated(true);
              setUserProfile(null);
              return;
            }
          } catch (vendorCheckError) {
            console.error("Error checking for vendor token:", vendorCheckError);
          }

          // Invalid or expired token
          authService.logout();
          setIsAuthenticated(false);
          setUserProfile(null);

          // Redirect to login if on a protected page
          if (typeof window !== "undefined") {
            const pathname = window.location.pathname;
            if (pathname !== "/" && !pathname.includes("/auth/")) {
              router.push("/");
            }
          }
        }
      } catch (error) {
        console.error("Auth initialization error:", error);
        setIsAuthenticated(false);
        setUserProfile(null);
      } finally {
        setIsLoading(false);
        setAuthInitialized(true);
      }
    };

    // Only run initAuth when Clerk has loaded
    if (isClerkLoaded) {
      initAuth();
    }
  }, [isClerkLoaded, isSignedIn, clerkUser, router]);

  // Add a backup timeout to prevent infinite loading state
  useEffect(() => {
    const loadingTimeoutId = setTimeout(() => {
      if (isLoading && !authInitialized) {
        console.log("Auth initialization timed out, resetting loading state");
        setIsLoading(false);
        setAuthInitialized(true);

        // Check if we have a token before redirecting
        const token = localStorage.getItem("token") || 
                      localStorage.getItem("jwt_token") || 
                      localStorage.getItem("wedzat_token");
        
        // Only redirect if no token is found
        if (!token && typeof window !== "undefined") {
          const pathname = window.location.pathname;
          const protectedRoutes = [
            "/home",
            "/dashboard",
            "/profile",
            "/protected",
          ];

          if (
            protectedRoutes.some(
              (route) => pathname === route || pathname.startsWith(`${route}/`)
            )
          ) {
            console.log("No token found, redirecting to login");
            router.push("/");
          }
        }
      }
    }, 10000); // Increased to 10 seconds for more time to initialize

    return () => clearTimeout(loadingTimeoutId);
  }, [isLoading, authInitialized, router]);

  // Add listener for storage events to handle token removal in other tabs
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "token" || e.key === "jwt_token") {
        // If token was removed
        if (!e.newValue) {
          console.log("Token removed in another tab/window");
          setIsAuthenticated(false);
          setUserProfile(null);

          // If on a protected route, redirect to login
          if (typeof window !== "undefined") {
            const pathname = window.location.pathname;
            const protectedRoutes = [
              "/home",
              "/dashboard",
              "/profile",
              "/protected",
            ];

            if (
              protectedRoutes.some(
                (route) =>
                  pathname === route || pathname.startsWith(`${route}/`)
              )
            ) {
              router.push("/");
            }
          }
        }
      }
    };

    window.addEventListener("storage", handleStorageChange);
    return () => window.removeEventListener("storage", handleStorageChange);
  }, [router]);

  const login = (token: string) => {
    console.log("Storing token and setting authenticated state");
    localStorage.setItem("token", token);
    localStorage.setItem("jwt_token", token); // Store in both keys for compatibility
    setIsAuthenticated(true);

    // Fetch user profile after login
    userService
      .getUserDetails()
      .then((profile) => {
        console.log("User profile fetched:", profile);
        setUserProfile(profile);
      })
      .catch((error) => console.error("Error fetching user profile:", error));
  };

  const logout = () => {
    console.log("Logging out");
    authService.logout();
    setIsAuthenticated(false);
    setUserProfile(null);
    router.push("/");
  };

  const updateProfile = (profile: UserProfile) => {
    console.log("Updating user profile", profile);
    setUserProfile(profile);
  };

  // Refresh user profile data
  useEffect(() => {
    // Only refresh if already authenticated
    if (isAuthenticated && !isLoading) {
      const refreshInterval = setInterval(() => {
        console.log("Refreshing user profile data");
        userService.getUserDetails()
          .then(profile => {
            console.log("Refreshed user profile:", profile);
            setUserProfile(profile);
          })
          .catch(error => {
            console.error("Error refreshing user profile:", error);
            // Don't log out on profile refresh errors
            // This prevents unnecessary redirects
          });
      }, 300000); // Refresh every 5 minutes instead of every minute

      return () => clearInterval(refreshInterval);
    }
  }, [isAuthenticated, isLoading]);

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        userProfile,
        isLoading,
        login,
        logout,
        updateProfile,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
