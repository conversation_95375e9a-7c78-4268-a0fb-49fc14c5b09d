"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./services/api.ts":
/*!*************************!*\
  !*** ./services/api.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authService: () => (/* binding */ authService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   uploadService: () => (/* binding */ uploadService),\n/* harmony export */   userService: () => (/* binding */ userService)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n// services/api.ts\n\n// Use environment variable or fallback to localhost for development\nconst BASE_URL = \"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub\" || 0;\n// Log the API URL being used\n// console.log(`API Service using base URL: ${BASE_URL}`);\n// For development, use a CORS proxy if needed\nconst API_URL = BASE_URL;\nif (true) {\n    // Uncomment the line below to use a CORS proxy in development if needed\n    // API_URL = `https://cors-anywhere.herokuapp.com/${BASE_URL}`;\n    // Log the API URL for debugging\n    console.log('Development mode detected, using API URL:', API_URL);\n}\n// Log the API URL being used\nconsole.log(\"API Service using base URL: \".concat(API_URL));\nconst TOKEN_KEY = 'token'; // Keep using your existing token key\nconst JWT_TOKEN_KEY = 'jwt_token'; // Alternative key for compatibility\n// Create axios instance\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_URL,\n    headers: {\n        'Content-Type': 'application/json'\n    },\n    timeout: 60000,\n    withCredentials: false // Helps with CORS issues\n});\n// Helper to get token from storage (checking both keys)\nconst getToken = ()=>{\n    if (false) {}\n    return localStorage.getItem(TOKEN_KEY);\n};\n// Helper to save token to storage (using both keys for compatibility)\nconst saveToken = (token)=>{\n    if (false) {}\n    console.log('Saving token to localStorage:', token.substring(0, 15) + '...');\n    localStorage.setItem(TOKEN_KEY, token);\n    localStorage.setItem(JWT_TOKEN_KEY, token); // For compatibility with other components\n};\n// Request interceptor to add auth token to all requests\napi.interceptors.request.use((config)=>{\n    const token = getToken();\n    if (token && config.headers) {\n        config.headers.Authorization = \"Bearer \".concat(token);\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Authentication services\nconst authService = {\n    // Register a new user\n    signup: async (signupData)=>{\n        try {\n            console.log('Attempting signup with data:', {\n                ...signupData,\n                password: signupData.password ? '********' : undefined\n            });\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"\".concat(BASE_URL, \"/signup\"), signupData, {\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            console.log('Signup response received:', {\n                success: true,\n                hasToken: !!response.data.token\n            });\n            if (response.data.token) {\n                saveToken(response.data.token);\n            } else {\n                console.warn('No token received in signup response');\n            }\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            console.error(\"Signup error:\", error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: \"An error occurred during signup\"\n            };\n        }\n    },\n    // Register a new vendor\n    registerVendor: async (vendorData)=>{\n        try {\n            console.log('Attempting vendor registration with data:', {\n                ...vendorData,\n                password: vendorData.password ? '********' : undefined\n            });\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"\".concat(BASE_URL, \"/register-vendor\"), vendorData, {\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            console.log('Vendor registration response received:', {\n                success: true,\n                hasToken: !!response.data.token\n            });\n            if (response.data.token) {\n                saveToken(response.data.token);\n            } else {\n                console.warn('No token received in vendor registration response');\n            }\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            console.error(\"Vendor registration error:\", error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: \"An error occurred during vendor registration\"\n            };\n        }\n    },\n    // Get business types\n    getBusinessTypes: async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(BASE_URL, \"/business-types\"));\n            return response.data.business_types;\n        } catch (error) {\n            var _error_response;\n            console.error(\"Get business types error:\", error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: \"An error occurred while fetching business types\"\n            };\n        }\n    },\n    // Get vendor profile\n    getVendorProfile: async ()=>{\n        try {\n            const token = getToken();\n            if (!token) {\n                throw {\n                    error: 'No authentication token found'\n                };\n            }\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(BASE_URL, \"/vendor-profile\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            return response.data.profile;\n        } catch (error) {\n            var _error_response;\n            console.error(\"Get vendor profile error:\", error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: \"An error occurred while fetching vendor profile\"\n            };\n        }\n    },\n    // Login with email/mobile and password\n    // In services/api.ts - login function\n    login: async (credentials)=>{\n        try {\n            console.log('Attempting login with:', {\n                email: credentials.email,\n                mobile_number: credentials.mobile_number,\n                password: '********'\n            });\n            const response = await api.post('/login', credentials);\n            console.log('Login response received:', {\n                success: true,\n                hasToken: !!response.data.token,\n                tokenPreview: response.data.token ? \"\".concat(response.data.token.substring(0, 10), \"...\") : 'none'\n            });\n            // Save token to localStorage with explicit console logs\n            if (response.data.token) {\n                console.log('Saving token to localStorage...');\n                localStorage.setItem('token', response.data.token);\n                // Verify token was saved\n                const savedToken = localStorage.getItem('token');\n                console.log(\"Token verification: \".concat(savedToken ? 'Successfully saved' : 'Failed to save'));\n            } else {\n                console.warn('No token received in login response');\n            }\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            console.error('Login error:', error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: 'Invalid credentials'\n            };\n        }\n    },\n    // Authenticate with Clerk\n    clerkAuth: async (clerkData)=>{\n        try {\n            console.log('Attempting Clerk authentication');\n            const response = await api.post('/clerk_auth', clerkData);\n            console.log('Clerk auth response received:', {\n                success: true,\n                hasToken: !!response.data.token\n            });\n            // Save token to localStorage\n            if (response.data.token) {\n                saveToken(response.data.token);\n            } else {\n                console.warn('No token received in Clerk auth response');\n            }\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            console.error('Clerk authentication error:', error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: 'Clerk authentication failed'\n            };\n        }\n    },\n    // Check if user profile is complete\n    checkProfile: async ()=>{\n        try {\n            const token = getToken();\n            if (!token) {\n                throw {\n                    error: 'No authentication token found'\n                };\n            }\n            console.log('Checking user profile');\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(BASE_URL, \"/check-profile\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            console.error('Profile check error:', error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: 'Failed to check profile'\n            };\n        }\n    },\n    // Get the current token\n    getToken: ()=>{\n        return getToken();\n    },\n    // Check if the user is authenticated\n    isAuthenticated: ()=>{\n        return !!getToken();\n    },\n    // Logout - clear token from localStorage\n    logout: ()=>{\n        console.log('Logging out and removing tokens');\n        localStorage.removeItem(TOKEN_KEY);\n        localStorage.removeItem(JWT_TOKEN_KEY);\n    }\n};\n// User services\nconst userService = {\n    // Get user details - uses GET method with explicit token in header\n    getUserDetails: async ()=>{\n        try {\n            const token = getToken();\n            if (!token) {\n                throw {\n                    error: 'No authentication token found'\n                };\n            }\n            console.log('Fetching user details');\n            // Set the correct Authorization format (Bearer + token)\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(BASE_URL, \"/user-details\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            console.error(\"Error fetching user details:\", error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: 'Failed to fetch user details'\n            };\n        }\n    },\n    // Update user details\n    updateUser: async (userData)=>{\n        try {\n            console.log('Updating user details');\n            const response = await api.put('/update-user', userData);\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            console.error('Update user error:', error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: 'Failed to update user'\n            };\n        }\n    }\n};\n// Helper to check if user is authenticated\nconst isAuthenticated = ()=>{\n    if (false) {}\n    const token = getToken();\n    return !!token; // Return true if token exists, false otherwise\n};\n// Upload services\nconst uploadService = {\n    /**\r\n   * Verify user's face using captured image\r\n   * @param faceImage Base64 encoded image data (without data URL prefix)\r\n   */ verifyFace: async (faceImage)=>{\n        try {\n            console.log('Sending face verification request');\n            const token = getToken();\n            // Create the request payload\n            const payload = {\n                face_image: faceImage\n            };\n            const payloadSize = JSON.stringify(payload).length;\n            console.log('Request payload size:', payloadSize);\n            // Check if payload is too large\n            if (payloadSize > 1000000) {\n                console.warn('Warning: Payload is very large, which may cause issues with the API');\n            }\n            // No mock implementation - always use the real API\n            console.log('Using real face verification API');\n            // Make the real API call\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"\".concat(API_URL, \"/verify-face\"), payload, {\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': token ? \"Bearer \".concat(token) : ''\n                },\n                timeout: 60000,\n                withCredentials: false\n            });\n            console.log('Face verification response:', response.data);\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            console.error('Error verifying face:', error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: 'Face verification failed'\n            };\n        }\n    },\n    /**\r\n   * Get pre-signed URLs for uploading media to S3\r\n   */ getPresignedUrl: async (request)=>{\n        try {\n            console.log('Getting presigned URL with request:', request);\n            // Ensure we have a valid token\n            const token = getToken();\n            if (!token) {\n                console.warn('No authentication token found when getting presigned URL');\n            }\n            // Make the API call with explicit headers\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"\".concat(API_URL, \"/get-upload-url\"), request, {\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': token ? \"Bearer \".concat(token) : ''\n                },\n                timeout: 30000 // 30 seconds timeout\n            });\n            console.log('Presigned URL response:', response.data);\n            // Validate the response\n            if (!response.data.media_id || !response.data.upload_urls || !response.data.upload_urls.main) {\n                throw new Error('Invalid response from get-upload-url API');\n            }\n            return response.data;\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error('Error getting presigned URL:', error);\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                throw {\n                    error: 'Authentication failed. Please log in again.'\n                };\n            }\n            throw ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data) || {\n                error: 'Failed to get upload URL'\n            };\n        }\n    },\n    /**\r\n   * Complete the upload process by notifying the backend\r\n   */ completeUpload: async (request)=>{\n        try {\n            console.log('Completing upload with request:', JSON.stringify(request, null, 2));\n            // Log the media_subtype for debugging\n            console.log(\"API SERVICE - Complete upload - Media subtype: \".concat(request.media_subtype || 'Not set'));\n            console.log(\"API SERVICE - Complete upload - Media type: \".concat(request.media_type));\n            console.log(\"API SERVICE - Complete upload - Media ID: \".concat(request.media_id));\n            // Validate the request\n            if (!request.media_id) {\n                console.error('Missing media_id in completeUpload request');\n                throw new Error('Missing media_id in request');\n            }\n            if (!request.title || !request.title.trim()) {\n                console.error('Missing title in completeUpload request');\n                throw new Error('Please provide a title for your upload');\n            }\n            // Ensure we have a valid token\n            const token = getToken();\n            if (!token) {\n                console.warn('No authentication token found when completing upload');\n            }\n            // Make the API call with explicit headers\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"\".concat(API_URL, \"/complete-upload\"), request, {\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': token ? \"Bearer \".concat(token) : ''\n                },\n                timeout: 60000 // 60 seconds timeout for completion\n            });\n            console.log('Upload completion response:', response.data);\n            // Validate the response\n            if (!response.data.message) {\n                throw new Error('Invalid response from complete-upload API');\n            }\n            return response.data;\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error('Error completing upload:', error);\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                throw {\n                    error: 'Authentication failed. Please log in again.'\n                };\n            }\n            throw ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data) || {\n                error: 'Failed to complete upload'\n            };\n        }\n    },\n    /**\r\n   * Upload a file to a presigned URL with optimized performance\r\n   */ uploadToPresignedUrl: async (url, file, onProgress)=>{\n        try {\n            console.log('Starting simple direct upload for file:', file.name);\n            console.log('File type:', file.type);\n            console.log('File size:', (file.size / (1024 * 1024)).toFixed(2) + ' MB');\n            console.log('Upload URL:', url);\n            // Report initial progress\n            if (onProgress) onProgress(10);\n            // Start timing the upload\n            const startTime = Date.now();\n            let lastProgressUpdate = 0;\n            // Use simple direct upload for all files\n            console.log('Using simple direct upload');\n            // Create simple headers for direct upload\n            const headers = {\n                'Content-Type': file.type,\n                'Content-Length': file.size.toString()\n            };\n            // Perform simple direct upload to the presigned URL\n            await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(url, file, {\n                headers,\n                timeout: 3600000,\n                maxBodyLength: Infinity,\n                maxContentLength: Infinity,\n                onUploadProgress: (progressEvent)=>{\n                    if (onProgress && progressEvent.total) {\n                        // Calculate raw percentage\n                        const rawPercent = Math.round(progressEvent.loaded * 100 / progressEvent.total);\n                        // Update UI in 5% increments for smoother progress\n                        if (rawPercent >= lastProgressUpdate + 5 || rawPercent === 100) {\n                            lastProgressUpdate = rawPercent;\n                            // Map to 10-95% range for UI\n                            const percentCompleted = 10 + Math.floor(rawPercent * 85 / 100);\n                            onProgress(percentCompleted);\n                            // Calculate and log upload speed\n                            const elapsedSeconds = (Date.now() - startTime) / 1000;\n                            if (elapsedSeconds > 0) {\n                                const speedMBps = (progressEvent.loaded / elapsedSeconds / (1024 * 1024)).toFixed(2);\n                                console.log(\"Upload progress: \".concat(percentCompleted, \"% at \").concat(speedMBps, \"MB/s\"));\n                            }\n                        }\n                    }\n                }\n            });\n            // Calculate final stats\n            const endTime = Date.now();\n            const elapsedSeconds = (endTime - startTime) / 1000;\n            const uploadSpeed = (file.size / elapsedSeconds / (1024 * 1024)).toFixed(2);\n            console.log(\"Upload completed in \".concat(elapsedSeconds.toFixed(2), \"s at \").concat(uploadSpeed, \"MB/s\"));\n            console.log('Response status: 200 (success)');\n            // Report completion\n            if (onProgress) onProgress(100);\n            console.log('File uploaded successfully');\n        } catch (error) {\n            console.error('Error uploading file:', error);\n            // Provide more detailed error information\n            let errorMessage = 'Failed to upload file';\n            if (error.message) {\n                if (error.message.includes('Network Error') || error.message.includes('CORS')) {\n                    errorMessage = 'Network error or CORS issue. Please try again or contact support.';\n                } else {\n                    errorMessage = \"Upload error: \".concat(error.message);\n                }\n            }\n            if (error.response) {\n                console.error('Response status:', error.response.status);\n                console.error('Response headers:', error.response.headers);\n                console.error('Response data:', error.response.data);\n                if (error.response.status === 403) {\n                    errorMessage = 'Permission denied. The upload URL may have expired.';\n                }\n            }\n            throw {\n                error: errorMessage\n            };\n        }\n    },\n    /**\r\n   * Handle the complete upload process\r\n   */ handleUpload: async (file, mediaType, category, title, description, tags, details, duration, thumbnail, onProgress)=>{\n        try {\n            console.log('API SERVICE - handleUpload called with params:', {\n                fileName: file === null || file === void 0 ? void 0 : file.name,\n                fileSize: Math.round(file.size / (1024 * 1024) * 100) / 100 + ' MB',\n                mediaType,\n                category,\n                title,\n                description,\n                tagsCount: tags === null || tags === void 0 ? void 0 : tags.length,\n                detailsCount: details ? Object.keys(details).length : 0,\n                videoCategory: (details === null || details === void 0 ? void 0 : details.video_category) || 'Not set',\n                duration,\n                hasThumbnail: !!thumbnail\n            });\n            // Log the video_category from details\n            if (mediaType === 'video') {\n                console.log('API SERVICE - Video category from details:', details === null || details === void 0 ? void 0 : details.video_category);\n                console.log('API SERVICE - All detail fields:', details ? JSON.stringify(details) : 'No details');\n            }\n            if (!file) {\n                throw new Error('No file provided');\n            }\n            // Log the file size and selected category without overriding the user's selection\n            if (mediaType === 'video') {\n                const fileSizeMB = Math.round(file.size / (1024 * 1024) * 100) / 100;\n                const categoryLimits = {\n                    'story': 50,\n                    'flash': 100,\n                    'glimpse': 250,\n                    'movie': 2000\n                };\n                const sizeLimit = categoryLimits[category] || 250;\n                console.log(\"API SERVICE - File size: \".concat(fileSizeMB, \" MB, Selected category: \").concat(category));\n                console.log(\"API SERVICE - Size limit for \".concat(category, \": \").concat(sizeLimit, \" MB\"));\n            // Warning is now handled in the presignedUrlRequest section\n            }\n            console.log('API SERVICE - Starting upload process for:', file.name);\n            // Update progress to 10%\n            if (onProgress) onProgress(10);\n            // Step 1: Get presigned URLs\n            console.log('API SERVICE - Creating presigned URL request with category:', category);\n            // Ensure we're using the correct backend category names\n            // Frontend: flashes, glimpses, movies, moments\n            // Backend: flash, glimpse, movie, story\n            let backendCategory = mediaType === 'photo' ? category === 'story' ? 'story' : 'post' // For photos: either 'story' or 'post'\n             : category; // For videos: keep the original category\n            // Double-check that we have a valid category\n            const validCategories = [\n                'story',\n                'flash',\n                'glimpse',\n                'movie',\n                'post'\n            ];\n            if (!validCategories.includes(backendCategory)) {\n                console.log(\"API SERVICE - WARNING: Invalid category '\".concat(backendCategory, \"'. Using 'flash' as fallback.\"));\n                console.log(\"API SERVICE - Valid categories are: \".concat(validCategories.join(', ')));\n                console.log(\"API SERVICE - Category type: \".concat(typeof backendCategory));\n                console.log(\"API SERVICE - Category value: '\".concat(backendCategory, \"'\"));\n                // Use 'flash' as the default for videos instead of 'glimpse'\n                backendCategory = 'flash';\n            }\n            console.log(\"API SERVICE - Original category from context: \".concat(category));\n            console.log(\"API SERVICE - Using backend category: \".concat(backendCategory));\n            // Log the file size to help with debugging\n            const fileSizeMB = Math.round(file.size / (1024 * 1024) * 100) / 100;\n            console.log(\"API SERVICE - File size: \".concat(fileSizeMB, \" MB\"));\n            // Log the category limits\n            const categoryLimits = {\n                'story': 50,\n                'flash': 100,\n                'glimpse': 250,\n                'movie': 2000\n            };\n            const sizeLimit = categoryLimits[backendCategory] || 250;\n            console.log(\"API SERVICE - Size limit for \".concat(backendCategory, \": \").concat(sizeLimit, \" MB\"));\n            // Log a warning if the file size exceeds the limit\n            if (fileSizeMB > sizeLimit) {\n                console.log(\"API SERVICE - WARNING: File size (\".concat(fileSizeMB, \" MB) exceeds the limit for \").concat(backendCategory, \" (\").concat(sizeLimit, \" MB).\"));\n                console.log(\"API SERVICE - The upload may fail. Consider selecting a category with a higher size limit.\");\n            }\n            const presignedUrlRequest = {\n                media_type: mediaType,\n                media_subtype: backendCategory,\n                filename: file.name,\n                content_type: file.type,\n                file_size: file.size\n            };\n            // Log the presigned URL request\n            console.log(\"API SERVICE - Sending presigned URL request with media_subtype: \".concat(backendCategory));\n            // Add video_category for videos\n            if (mediaType === 'video') {\n                // Get the video_category from details - no default value\n                console.log('API SERVICE - Details object:', details);\n                console.log('API SERVICE - Details keys:', details ? Object.keys(details) : 'No details');\n                const videoCategory = details === null || details === void 0 ? void 0 : details.video_category;\n                console.log('API SERVICE - Video category from details:', videoCategory);\n                // Make sure we're using a valid video_category\n                const allowedCategories = [\n                    'my_wedding',\n                    'wedding_vlog'\n                ];\n                // If no video_category is provided, use a default one\n                if (!videoCategory) {\n                    console.error(\"API SERVICE - Missing video_category. Using default 'my_wedding'.\");\n                    // Use 'my_wedding' as the default video_category\n                    presignedUrlRequest.video_category = 'my_wedding';\n                    console.log('API SERVICE - Using default video_category: my_wedding');\n                } else {\n                    // Map the UI category to the backend category if needed\n                    let backendVideoCategory = videoCategory;\n                    // If the category is in UI format, map it to backend format\n                    if (videoCategory === 'friends_family_videos') {\n                        backendVideoCategory = 'friends_family_video';\n                        console.log('API SERVICE - Mapped UI category to backend category:', backendVideoCategory);\n                    } else if (videoCategory === 'my_wedding_videos') {\n                        backendVideoCategory = 'my_wedding';\n                        console.log('API SERVICE - Mapped UI category to backend category:', backendVideoCategory);\n                    }\n                    // If the video_category is not allowed, throw an error\n                    if (!allowedCategories.includes(backendVideoCategory)) {\n                        console.error(\"API SERVICE - Invalid video_category: \".concat(backendVideoCategory, \". Must be one of \").concat(JSON.stringify(allowedCategories)));\n                        throw new Error(\"Invalid video category. Must be one of \".concat(JSON.stringify(allowedCategories)));\n                    }\n                    // Use the provided video_category\n                    presignedUrlRequest.video_category = backendVideoCategory;\n                    console.log('API SERVICE - Using video_category:', backendVideoCategory);\n                    console.log(\"API SERVICE - Final video category: \".concat(backendVideoCategory));\n                    console.log(\"API SERVICE - Complete presigned URL request:\", JSON.stringify(presignedUrlRequest, null, 2));\n                }\n                // Log the category and file size limits\n                console.log(\"API SERVICE - Original category from UI: \".concat(category));\n                console.log(\"API SERVICE - Using backend media subtype: \".concat(backendCategory));\n                // Log size limits based on category without overriding the user's selection\n                const categoryLimits = {\n                    'story': 50,\n                    'flash': 100,\n                    'glimpse': 250,\n                    'movie': 2000\n                };\n                const sizeLimit = categoryLimits[backendCategory] || 250;\n                console.log(\"API SERVICE - Size limit for \".concat(backendCategory, \": \").concat(sizeLimit, \" MB (file size: \").concat(fileSizeMB, \" MB)\"));\n                // Log a warning if the file size exceeds the limit for the selected category\n                if (fileSizeMB > sizeLimit) {\n                    console.log(\"API SERVICE - WARNING: File size (\".concat(fileSizeMB, \" MB) exceeds the limit for \").concat(backendCategory, \" (\").concat(sizeLimit, \" MB).\"));\n                    console.log(\"API SERVICE - The upload may fail. Consider selecting a category with a higher size limit.\");\n                }\n                // Add duration if available\n                if (duration) {\n                    console.log(\"Video duration: \".concat(duration, \" seconds\"));\n                // You could add this to the request if the API supports it\n                // presignedUrlRequest.duration = duration;\n                }\n            }\n            const presignedUrlResponse = await uploadService.getPresignedUrl(presignedUrlRequest);\n            // Update progress to 20%\n            if (onProgress) onProgress(20);\n            // Step 2: Upload the file to the presigned URL (20-70% of progress)\n            await uploadService.uploadToPresignedUrl(presignedUrlResponse.upload_urls.main, file, (uploadProgress)=>{\n                // Map the upload progress from 0-100 to 20-70 in our overall progress\n                if (onProgress) onProgress(20 + uploadProgress * 0.5);\n            });\n            // Step 3: Upload thumbnail if available (70-90% of progress)\n            if (thumbnail && presignedUrlResponse.upload_urls.thumbnail) {\n                console.log('Uploading thumbnail:', thumbnail.name);\n                // Update progress to 70%\n                if (onProgress) onProgress(70);\n                await uploadService.uploadToPresignedUrl(presignedUrlResponse.upload_urls.thumbnail, thumbnail, (uploadProgress)=>{\n                    // Map the upload progress from 0-100 to 70-90 in our overall progress\n                    if (onProgress) onProgress(70 + uploadProgress * 0.2);\n                });\n            }\n            // Update progress to 90%\n            if (onProgress) onProgress(90);\n            // Step 4: Complete the upload (90-100% of progress)\n            try {\n                // Validate title\n                if (!title || !title.trim()) {\n                    console.error('Title is empty or missing');\n                    throw new Error('Please provide a title for your upload');\n                }\n                console.log('Title is valid:', title);\n                // Prepare the complete upload request\n                const completeRequest = {\n                    media_id: presignedUrlResponse.media_id,\n                    media_type: mediaType,\n                    media_subtype: backendCategory,\n                    title: title.trim(),\n                    description: description || '',\n                    tags: tags || []\n                };\n                console.log(\"API SERVICE - Setting media_subtype in completeRequest: \".concat(backendCategory));\n                // Log the description being sent\n                console.log('Sending description:', description || '');\n                // Add duration for videos if available\n                if (mediaType === 'video' && duration) {\n                    completeRequest.duration = duration;\n                }\n                // Add backend-compatible fields directly to the request\n                completeRequest.caption = title.trim();\n                completeRequest.place = (details === null || details === void 0 ? void 0 : details.personal_place) || (details === null || details === void 0 ? void 0 : details.place) || '';\n                // Add content-type specific fields based on backend requirements\n                if (mediaType === 'photo' && backendCategory !== 'story') {\n                    // Photos (non-stories) require: caption, place, event_type\n                    completeRequest.event_type = (details === null || details === void 0 ? void 0 : details.personal_event_type) || (details === null || details === void 0 ? void 0 : details.eventType) || '';\n                    console.log('API SERVICE - Photo upload: Added event_type =', completeRequest.event_type);\n                } else if (mediaType === 'video') {\n                    // Videos require: caption, place, partner, budget, wedding_style, video_category\n                    completeRequest.partner = (details === null || details === void 0 ? void 0 : details.personal_life_partner) || (details === null || details === void 0 ? void 0 : details.lifePartner) || '';\n                    completeRequest.budget = (details === null || details === void 0 ? void 0 : details.personal_budget) || (details === null || details === void 0 ? void 0 : details.budget) || '';\n                    completeRequest.wedding_style = (details === null || details === void 0 ? void 0 : details.personal_wedding_style) || (details === null || details === void 0 ? void 0 : details.weddingStyle) || '';\n                    completeRequest.event_type = (details === null || details === void 0 ? void 0 : details.personal_event_type) || (details === null || details === void 0 ? void 0 : details.eventType) || '';\n                    completeRequest.video_category = (details === null || details === void 0 ? void 0 : details.video_category) || backendCategory;\n                    console.log('API SERVICE - Video upload: Added partner, budget, wedding_style, event_type, video_category');\n                } else if (mediaType === 'photo' && backendCategory === 'story') {\n                    // Moments (stories) only require caption and face verification - no additional fields\n                    console.log('API SERVICE - Moments upload: Only caption required, skipping additional fields');\n                }\n                // Extract vendor details from the details object\n                const vendorDetails = {};\n                // Process vendor details from the details object\n                if (details) {\n                    console.log('API SERVICE - Processing vendor details from details object');\n                    console.log('API SERVICE - Raw details object:', JSON.stringify(details, null, 2));\n                    // Count vendor fields in the details object\n                    let vendorFieldCount = 0;\n                    Object.keys(details).forEach((key)=>{\n                        if (key.startsWith('vendor_')) {\n                            vendorFieldCount++;\n                        }\n                    });\n                    console.log(\"API SERVICE - Found \".concat(vendorFieldCount, \" vendor-related fields in details object\"));\n                    // Keep track of which vendor types we've already processed\n                    const processedVendors = new Set();\n                    Object.entries(details).forEach((param)=>{\n                        let [key, value] = param;\n                        if (key.startsWith('vendor_') && value) {\n                            const parts = key.split('_');\n                            if (parts.length >= 3) {\n                                const vendorType = parts[1];\n                                const fieldType = parts.slice(2).join('_');\n                                // Normalize vendor type to avoid duplicates\n                                const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                                console.log(\"API SERVICE - Processing vendor field: \".concat(key, \" = \").concat(value), {\n                                    vendorType,\n                                    fieldType,\n                                    normalizedType,\n                                    alreadyProcessed: processedVendors.has(normalizedType)\n                                });\n                                // Skip if we've already processed this vendor type\n                                if (processedVendors.has(normalizedType)) {\n                                    console.log(\"API SERVICE - Skipping \".concat(key, \" as \").concat(normalizedType, \" is already processed\"));\n                                    return;\n                                }\n                                if (fieldType === 'name') {\n                                    vendorDetails[\"\".concat(normalizedType, \"_name\")] = value;\n                                    console.log(\"API SERVICE - Set \".concat(normalizedType, \"_name = \").concat(value));\n                                    // Also set the contact if available\n                                    const contactKey = \"vendor_\".concat(vendorType, \"_contact\");\n                                    if (details[contactKey]) {\n                                        vendorDetails[\"\".concat(normalizedType, \"_contact\")] = details[contactKey];\n                                        console.log(\"API SERVICE - Also set \".concat(normalizedType, \"_contact = \").concat(details[contactKey]));\n                                        processedVendors.add(normalizedType);\n                                        console.log(\"API SERVICE - Marked \".concat(normalizedType, \" as processed (has both name and contact)\"));\n                                    }\n                                } else if (fieldType === 'contact') {\n                                    vendorDetails[\"\".concat(normalizedType, \"_contact\")] = value;\n                                    console.log(\"API SERVICE - Set \".concat(normalizedType, \"_contact = \").concat(value));\n                                    // Also set the name if available\n                                    const nameKey = \"vendor_\".concat(vendorType, \"_name\");\n                                    if (details[nameKey]) {\n                                        vendorDetails[\"\".concat(normalizedType, \"_name\")] = details[nameKey];\n                                        console.log(\"API SERVICE - Also set \".concat(normalizedType, \"_name = \").concat(details[nameKey]));\n                                        processedVendors.add(normalizedType);\n                                        console.log(\"API SERVICE - Marked \".concat(normalizedType, \" as processed (has both name and contact)\"));\n                                    }\n                                }\n                            }\n                        }\n                    });\n                    // Log the processed vendor details\n                    // console.log('API SERVICE - Processed vendor details:', JSON.stringify(vendorDetails, null, 2));\n                    // console.log(`API SERVICE - Processed ${processedVendors.size} complete vendor types: ${Array.from(processedVendors).join(', ')}`);\n                    // Final check to ensure we have both name and contact for each vendor\n                    const vendorNames = new Set();\n                    const vendorContacts = new Set();\n                    let completeVendorCount = 0;\n                    Object.keys(vendorDetails).forEach((key)=>{\n                        if (key.endsWith('_name')) {\n                            vendorNames.add(key.replace('_name', ''));\n                        } else if (key.endsWith('_contact')) {\n                            vendorContacts.add(key.replace('_contact', ''));\n                        }\n                    });\n                    // Count complete pairs\n                    vendorNames.forEach((name)=>{\n                        if (vendorContacts.has(name)) {\n                            completeVendorCount++;\n                        } else {\n                            // If we have a name but no contact, add a default contact\n                            console.log(\"API SERVICE - WARNING: Vendor \".concat(name, \" has name but no contact, adding default contact\"));\n                            vendorDetails[\"\".concat(name, \"_contact\")] = '0000000000';\n                            completeVendorCount++;\n                        }\n                    });\n                    // Check for contacts without names\n                    vendorContacts.forEach((contact)=>{\n                        if (!vendorNames.has(contact)) {\n                            // If we have a contact but no name, add a default name\n                            console.log(\"API SERVICE - WARNING: Vendor \".concat(contact, \" has contact but no name, adding default name\"));\n                            if (typeof contact === 'string') {\n                                vendorDetails[\"\".concat(contact, \"_name\")] = \"Vendor \".concat(contact.charAt(0).toUpperCase() + contact.slice(1));\n                            }\n                            completeVendorCount++;\n                        }\n                    });\n                    console.log(\"API SERVICE - Final check: Found \".concat(completeVendorCount, \" complete vendor pairs\"));\n                    console.log('API SERVICE - Final vendor details:', JSON.stringify(vendorDetails, null, 2));\n                }\n                // Ensure we have the required vendor fields for wedding videos\n                if (mediaType === 'video' && (details === null || details === void 0 ? void 0 : details.video_category) && [\n                    'my_wedding',\n                    'wedding_vlog'\n                ].includes(details.video_category)) {\n                    // Try to get vendor details from localStorage if they're missing from the details object\n                    const initialVendorKeys = Object.keys(vendorDetails);\n                    const vendorCount = initialVendorKeys.filter((key)=>key.endsWith('_name')).length;\n                    console.log(\"API SERVICE - Initial vendor count: \".concat(vendorCount, \" name fields found\"));\n                    console.log('API SERVICE - Initial vendor details:', JSON.stringify(vendorDetails, null, 2));\n                    // Always check localStorage for vendor details to ensure we have the most complete set\n                    console.log('API SERVICE - Checking localStorage for vendor details');\n                    try {\n                        const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n                        if (storedVendorDetails) {\n                            const parsedVendorDetails = JSON.parse(storedVendorDetails);\n                            console.log('API SERVICE - Retrieved vendor details from localStorage:', storedVendorDetails);\n                            // Track how many complete vendor details we've added\n                            let completeVendorCount = 0;\n                            // Process vendor details from localStorage\n                            Object.entries(parsedVendorDetails).forEach((param)=>{\n                                let [vendorType, details] = param;\n                                if (details && details.name && details.mobileNumber) {\n                                    // Add to vendorDetails\n                                    const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                                    vendorDetails[\"\".concat(normalizedType, \"_name\")] = details.name;\n                                    vendorDetails[\"\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                                    console.log(\"API SERVICE - Added vendor \".concat(normalizedType, \" with name: \").concat(details.name, \" and contact: \").concat(details.mobileNumber));\n                                    completeVendorCount++;\n                                    // Also add the original type if it's different\n                                    if (normalizedType !== vendorType) {\n                                        vendorDetails[\"\".concat(vendorType, \"_name\")] = details.name;\n                                        vendorDetails[\"\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                                        console.log(\"API SERVICE - Also added original vendor \".concat(vendorType));\n                                    }\n                                }\n                            });\n                            console.log(\"API SERVICE - Added \".concat(completeVendorCount, \" complete vendor details from localStorage\"));\n                            console.log('API SERVICE - Updated vendor details:', JSON.stringify(vendorDetails, null, 2));\n                            // Force update the state with these vendor details\n                            try {\n                                // This is a direct state update to ensure the vendor details are available for validation\n                                if (window && window.dispatchEvent) {\n                                    const vendorUpdateEvent = new CustomEvent('vendor-details-update', {\n                                        detail: parsedVendorDetails\n                                    });\n                                    window.dispatchEvent(vendorUpdateEvent);\n                                    console.log('API SERVICE - Dispatched vendor-details-update event');\n                                }\n                            } catch (eventError) {\n                                console.error('API SERVICE - Failed to dispatch vendor-details-update event:', eventError);\n                            }\n                        } else {\n                            console.log('API SERVICE - No vendor details found in localStorage');\n                        }\n                    } catch (error) {\n                        console.error('API SERVICE - Failed to retrieve vendor details from localStorage:', error);\n                    }\n                    // Make sure we have at least 4 vendor details with both name and contact\n                    // Define required vendor types for fallback if needed\n                    const requiredVendorTypes = [\n                        'venue',\n                        'photographer',\n                        'makeup_artist',\n                        'decoration',\n                        'caterer'\n                    ];\n                    // Count all vendor details, including additional ones\n                    let validVendorCount = 0;\n                    // Count all vendor details where both name and contact are provided\n                    console.log('API SERVICE - Checking vendor details for validation:', JSON.stringify(vendorDetails, null, 2));\n                    const allVendorKeys = Object.keys(vendorDetails);\n                    console.log('API SERVICE - Vendor keys:', allVendorKeys.join(', '));\n                    // Keep track of which vendors we've already counted to avoid duplicates\n                    const countedVendors = new Set();\n                    // First pass: Check for standard vendor types\n                    for(let i = 0; i < allVendorKeys.length; i++){\n                        const key = allVendorKeys[i];\n                        if (key.endsWith('_name')) {\n                            const baseKey = key.replace('_name', '');\n                            const contactKey = \"\".concat(baseKey, \"_contact\");\n                            // Normalize the key to handle both frontend and backend naming\n                            const normalizedKey = baseKey === 'makeupArtist' ? 'makeup_artist' : baseKey === 'decorations' ? 'decoration' : baseKey;\n                            // Skip if we've already counted this vendor\n                            if (countedVendors.has(normalizedKey)) {\n                                continue;\n                            }\n                            console.log(\"Checking vendor \".concat(baseKey, \":\"), {\n                                name: vendorDetails[key],\n                                contact: vendorDetails[contactKey]\n                            });\n                            if (vendorDetails[key] && vendorDetails[contactKey]) {\n                                validVendorCount++;\n                                countedVendors.add(normalizedKey);\n                                console.log(\"Valid vendor found: \".concat(baseKey, \" with name: \").concat(vendorDetails[key], \" and contact: \").concat(vendorDetails[contactKey]));\n                            }\n                        }\n                    }\n                    console.log(\"Total valid vendor count after first pass: \".concat(validVendorCount));\n                    // Second pass: Check for additional vendors with different naming patterns\n                    for(let i = 1; i <= 10; i++){\n                        const nameKey = \"additional\".concat(i, \"_name\");\n                        const contactKey = \"additional\".concat(i, \"_contact\");\n                        // Skip if we've already counted this vendor\n                        if (countedVendors.has(\"additional\".concat(i))) {\n                            continue;\n                        }\n                        if (vendorDetails[nameKey] && vendorDetails[contactKey]) {\n                            validVendorCount++;\n                            countedVendors.add(\"additional\".concat(i));\n                            console.log(\"Valid additional vendor found: additional\".concat(i, \" with name: \").concat(vendorDetails[nameKey], \" and contact: \").concat(vendorDetails[contactKey]));\n                        }\n                    }\n                    // Third pass: Check for additionalVendor pattern (used in some parts of the code)\n                    for(let i = 1; i <= 10; i++){\n                        const nameKey = \"additionalVendor\".concat(i, \"_name\");\n                        const contactKey = \"additionalVendor\".concat(i, \"_contact\");\n                        // Skip if we've already counted this vendor\n                        if (countedVendors.has(\"additionalVendor\".concat(i))) {\n                            continue;\n                        }\n                        if (vendorDetails[nameKey] && vendorDetails[contactKey]) {\n                            validVendorCount++;\n                            countedVendors.add(\"additionalVendor\".concat(i));\n                            console.log(\"Valid additionalVendor found: additionalVendor\".concat(i, \" with name: \").concat(vendorDetails[nameKey], \" and contact: \").concat(vendorDetails[contactKey]));\n                        }\n                    }\n                    console.log(\"Total valid vendor count after all passes: \".concat(validVendorCount));\n                    // Map additional vendors to the predefined vendor types if needed\n                    // This ensures the backend will count them correctly\n                    if (validVendorCount < 4) {\n                        console.log('Need to map additional vendors to predefined types');\n                        // First, collect all additional vendors from all patterns\n                        const additionalVendors = [];\n                        // Collect all additional vendors with 'additional' prefix\n                        for(let i = 1; i <= 10; i++){\n                            const nameKey = \"additional\".concat(i, \"_name\");\n                            const contactKey = \"additional\".concat(i, \"_contact\");\n                            if (vendorDetails[nameKey] && vendorDetails[contactKey] && !countedVendors.has(\"additional\".concat(i))) {\n                                additionalVendors.push({\n                                    name: vendorDetails[nameKey],\n                                    contact: vendorDetails[contactKey],\n                                    key: \"additional\".concat(i)\n                                });\n                                countedVendors.add(\"additional\".concat(i));\n                                console.log(\"Found additional vendor \".concat(i, \": \").concat(vendorDetails[nameKey]));\n                            }\n                        }\n                        // Collect all additional vendors with 'additionalVendor' prefix\n                        for(let i = 1; i <= 10; i++){\n                            const nameKey = \"additionalVendor\".concat(i, \"_name\");\n                            const contactKey = \"additionalVendor\".concat(i, \"_contact\");\n                            if (vendorDetails[nameKey] && vendorDetails[contactKey] && !countedVendors.has(\"additionalVendor\".concat(i))) {\n                                additionalVendors.push({\n                                    name: vendorDetails[nameKey],\n                                    contact: vendorDetails[contactKey],\n                                    key: \"additionalVendor\".concat(i)\n                                });\n                                countedVendors.add(\"additionalVendor\".concat(i));\n                                console.log(\"Found additionalVendor \".concat(i, \": \").concat(vendorDetails[nameKey]));\n                            }\n                        }\n                        console.log(\"Found \".concat(additionalVendors.length, \" additional vendors to map\"));\n                        // Map additional vendors to empty predefined vendor slots\n                        let additionalIndex = 0;\n                        for (const type of requiredVendorTypes){\n                            // Check if this type is already filled\n                            const normalizedType = type === 'makeup_artist' ? 'makeupArtist' : type === 'decoration' ? 'decorations' : type;\n                            // Skip if we've already counted this vendor type\n                            if (countedVendors.has(type) || countedVendors.has(normalizedType)) {\n                                console.log(\"Skipping \".concat(type, \" as it's already counted\"));\n                                continue;\n                            }\n                            if (additionalIndex < additionalVendors.length) {\n                                // Map this additional vendor to this predefined type\n                                vendorDetails[\"\".concat(type, \"_name\")] = additionalVendors[additionalIndex].name;\n                                vendorDetails[\"\".concat(type, \"_contact\")] = additionalVendors[additionalIndex].contact;\n                                console.log(\"Mapped additional vendor \".concat(additionalVendors[additionalIndex].key, \" to \").concat(type, \": \").concat(additionalVendors[additionalIndex].name));\n                                additionalIndex++;\n                                validVendorCount++;\n                                countedVendors.add(type);\n                                if (validVendorCount >= 4) {\n                                    console.log(\"Reached 4 valid vendors after mapping, no need to continue\");\n                                    break;\n                                }\n                            }\n                        }\n                        // If we still don't have enough, add placeholders\n                        if (validVendorCount < 4) {\n                            console.log('Still need more vendors, adding placeholders');\n                            for (const type of requiredVendorTypes){\n                                // Check if this type is already filled\n                                const normalizedType = type === 'makeup_artist' ? 'makeupArtist' : type === 'decoration' ? 'decorations' : type;\n                                // Skip if we've already counted this vendor type\n                                if (countedVendors.has(type) || countedVendors.has(normalizedType)) {\n                                    console.log(\"Skipping \".concat(type, \" for placeholder as it's already counted\"));\n                                    continue;\n                                }\n                                // Add placeholder for this vendor type\n                                vendorDetails[\"\".concat(type, \"_name\")] = vendorDetails[\"\".concat(type, \"_name\")] || \"\".concat(type.charAt(0).toUpperCase() + type.slice(1));\n                                vendorDetails[\"\".concat(type, \"_contact\")] = vendorDetails[\"\".concat(type, \"_contact\")] || '0000000000';\n                                validVendorCount++;\n                                countedVendors.add(type);\n                                console.log(\"Added placeholder for \".concat(type, \": \").concat(vendorDetails[\"\".concat(type, \"_name\")]));\n                                if (validVendorCount >= 4) {\n                                    console.log(\"Reached 4 valid vendors after adding placeholders\");\n                                    break;\n                                }\n                            }\n                            // Final check - if we still don't have 4, force add the remaining required types\n                            if (validVendorCount < 4) {\n                                console.log('CRITICAL: Still don\\'t have 4 vendors, forcing placeholders for all required types');\n                                for (const type of requiredVendorTypes){\n                                    if (!vendorDetails[\"\".concat(type, \"_name\")] || !vendorDetails[\"\".concat(type, \"_contact\")]) {\n                                        vendorDetails[\"\".concat(type, \"_name\")] = \"\".concat(type.charAt(0).toUpperCase() + type.slice(1));\n                                        vendorDetails[\"\".concat(type, \"_contact\")] = '0000000000';\n                                        validVendorCount++;\n                                        console.log(\"Force added placeholder for \".concat(type));\n                                        if (validVendorCount >= 4) break;\n                                    }\n                                }\n                            }\n                        }\n                        // Final log of vendor count\n                        console.log(\"Final valid vendor count: \".concat(validVendorCount));\n                    }\n                }\n                // Add vendor details to the request\n                completeRequest.vendor_details = vendorDetails;\n                // Final check before sending - ensure we have at least 4 complete vendor details\n                if (mediaType === 'video') {\n                    // Count complete vendor details (with both name and contact)\n                    const vendorNames = new Set();\n                    const vendorContacts = new Set();\n                    let completeVendorCount = 0;\n                    if (vendorDetails) {\n                        Object.keys(vendorDetails).forEach((key)=>{\n                            if (key.endsWith('_name')) {\n                                vendorNames.add(key.replace('_name', ''));\n                            } else if (key.endsWith('_contact')) {\n                                vendorContacts.add(key.replace('_contact', ''));\n                            }\n                        });\n                        // Count complete pairs\n                        vendorNames.forEach((name)=>{\n                            if (vendorContacts.has(name)) {\n                                completeVendorCount++;\n                            }\n                        });\n                    }\n                    console.log(\"API SERVICE - FINAL CHECK: Found \".concat(completeVendorCount, \" complete vendor pairs before sending request\"));\n                    // If we don't have enough vendors, add placeholders to reach 4\n                    if (completeVendorCount < 4) {\n                        console.log(\"API SERVICE - FINAL CHECK: Adding placeholders to reach 4 vendors\");\n                        const requiredVendorTypes = [\n                            'venue',\n                            'photographer',\n                            'makeup_artist',\n                            'decoration',\n                            'caterer'\n                        ];\n                        for (const type of requiredVendorTypes){\n                            // Skip if this vendor is already complete\n                            if (vendorDetails[\"\".concat(type, \"_name\")] && vendorDetails[\"\".concat(type, \"_contact\")]) {\n                                continue;\n                            }\n                            // Add placeholder for this vendor\n                            vendorDetails[\"\".concat(type, \"_name\")] = vendorDetails[\"\".concat(type, \"_name\")] || \"\".concat(type.charAt(0).toUpperCase() + type.slice(1));\n                            vendorDetails[\"\".concat(type, \"_contact\")] = vendorDetails[\"\".concat(type, \"_contact\")] || '0000000000';\n                            completeVendorCount++;\n                            console.log(\"API SERVICE - FINAL CHECK: Added placeholder for \".concat(type));\n                            if (completeVendorCount >= 4) {\n                                break;\n                            }\n                        }\n                        // Update the request with the new vendor details\n                        completeRequest.vendor_details = vendorDetails;\n                        console.log(\"API SERVICE - FINAL CHECK: Now have \".concat(completeVendorCount, \" complete vendor pairs\"));\n                    }\n                }\n                // Log the final request structure for debugging\n                console.log('=== FINAL UPLOAD REQUEST ===');\n                console.log('Media Type:', completeRequest.media_type);\n                console.log('Media Subtype:', completeRequest.media_subtype);\n                console.log('Caption:', completeRequest.caption);\n                console.log('Place:', completeRequest.place);\n                if (completeRequest.event_type) console.log('Event Type:', completeRequest.event_type);\n                if (completeRequest.partner) console.log('Partner:', completeRequest.partner);\n                if (completeRequest.budget) console.log('Budget:', completeRequest.budget);\n                if (completeRequest.wedding_style) console.log('Wedding Style:', completeRequest.wedding_style);\n                if (completeRequest.video_category) console.log('Video Category:', completeRequest.video_category);\n                console.log('Vendor Details Count:', Object.keys(completeRequest.vendor_details || {}).length);\n                console.log('=== END UPLOAD REQUEST ===');\n                console.log('Sending complete upload request:', JSON.stringify(completeRequest, null, 2));\n                // Complete the upload\n                const completeResponse = await uploadService.completeUpload(completeRequest);\n                // Log the complete response\n                console.log('Complete upload response:', JSON.stringify(completeResponse, null, 2));\n                // Update progress to 100%\n                if (onProgress) onProgress(100);\n                console.log('Upload process completed successfully');\n                return completeResponse;\n            } catch (completeError) {\n                var _completeError_response_data, _completeError_response;\n                console.error('Error in complete upload step:', completeError);\n                // Check if the error is related to the title\n                if (completeError.message && completeError.message.includes('title')) {\n                    console.error('Title-related error detected:', completeError.message);\n                    throw {\n                        error: 'Please provide a title for your upload'\n                    };\n                }\n                // Handle other errors\n                const errorMessage = ((_completeError_response = completeError.response) === null || _completeError_response === void 0 ? void 0 : (_completeError_response_data = _completeError_response.data) === null || _completeError_response_data === void 0 ? void 0 : _completeError_response_data.error) || completeError.message || 'Failed to complete the upload process';\n                console.error('Throwing error:', errorMessage);\n                throw {\n                    error: errorMessage\n                };\n            }\n        } catch (error) {\n            var _error_response;\n            console.error('Error in upload process:', error);\n            // Check if the error is related to the title\n            if (error.error && typeof error.error === 'string' && error.error.includes('title')) {\n                console.error('Title-related error detected in main catch block');\n                throw {\n                    error: 'Please provide a title for your upload'\n                };\n            }\n            // If error is already formatted correctly, just pass it through\n            if (error.error) {\n                throw error;\n            }\n            // Otherwise, format the error\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: 'Upload process failed'\n            };\n        }\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    authService,\n    userService,\n    uploadService,\n    isAuthenticated\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./services/api.ts\n"));

/***/ })

});