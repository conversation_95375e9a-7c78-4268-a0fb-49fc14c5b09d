import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  const resolvedParams = await context.params;
  const photoId = resolvedParams.id;

  try {
    // Get token from request headers (passed from client)
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication token required' },
        { status: 401 }
      );
    }

    console.log(`Direct API: Fetching photo details for ID: ${photoId}`);

    // Make direct API call to backend with proper Bearer format
    console.log('Direct API token being sent:', token.substring(0, 20) + '...');
    console.log('Direct API Authorization header:', `Bearer ${token}`.substring(0, 50) + '...');

    const response = await axios.get(
      `https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/photo/${photoId}`,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        timeout: 15000,
      }
    );

    console.log('Direct photo API response received:', response.status);
    return NextResponse.json(response.data);
  } catch (error: any) {
    console.error('Error in direct photo API:', error);

    // Detailed error response
    let errorMessage = 'Failed to fetch photo details';
    let statusCode = 500;

    if (error.response) {
      console.error('Direct API response error:', error.response.data);
      statusCode = error.response.status;
      errorMessage = `Server error: ${error.response.data?.error || error.response.data?.message || error.message}`;
    } else if (error.request) {
      console.error('Direct API request error:', error.request);
      errorMessage = 'No response received from server';
    } else {
      console.error('Direct API error message:', error.message);
      errorMessage = error.message;
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}
