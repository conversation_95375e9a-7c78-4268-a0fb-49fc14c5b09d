"use client";
import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import axios from 'axios';
import { getAuthToken } from '../../utils';
import { Guest, Group, MenuOption } from './GuestList';
import Image from "next/image";

interface AddGuestModalProps {
  onClose: () => void;
  onSave: () => void;
  guest: Guest | null;
  groups: Group[];
  menuOptions: MenuOption[];
  defaultGroupId?: string;
  setError: (error: string | null) => void;
  setSuccessMessage: (message: string | null) => void;
}

const AddGuestModal: React.FC<AddGuestModalProps> = ({
  onClose,
  onSave,
  guest,
  groups,
  menuOptions,
  defaultGroupId,
  setError,
  setSuccessMessage
}) => {

  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [groupId, setGroupId] = useState('');
  const [menuId, setMenuId] = useState('');
  const [attendanceStatus, setAttendanceStatus] = useState<'attending' | 'pending' | 'declined'>('pending');
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);

  // Initialize form with guest data if editing
  useEffect(() => {
    if (guest) {
      setFirstName(guest.first_name || '');
      setLastName(guest.last_name || '');
      setEmail(guest.email || '');
      setPhone(guest.phone || '');
      setGroupId(guest.group_id || '');
      setMenuId(guest.menu_id || '');
      setAttendanceStatus(guest.attendance_status || 'pending');
      setNotes(guest.notes || '');
    } else if (defaultGroupId) {
      setGroupId(defaultGroupId);
    }
  }, [guest, defaultGroupId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!firstName || !lastName || !groupId) {
      setError('First name, last name, and group are required');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const token = getAuthToken();

      if (!token) {
        console.warn('No authentication token found');
        setError('Authentication required');
        setLoading(false);
        return;
      }

      const guestData = {
        first_name: firstName,
        last_name: lastName,
        email,
        phone,
        group_id: groupId,
        menu_id: menuId || null,
        attendance_status: attendanceStatus,
        notes
      };

      if (guest) {
        // Update existing guest
        const response = await axios.put(
          'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/update-guest',
          {
            guest_id: guest.guest_id,
            ...guestData
          },
          { headers: { Authorization: `Bearer ${token}` } }
        );

        console.log('Guest updated successfully:', response.data);
        setSuccessMessage('Guest updated successfully');
      } else {
        // Add new guest
        const response = await axios.post(
          'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/add-guest',
          guestData,
          { headers: { Authorization: `Bearer ${token}` } }
        );

        console.log('Guest added successfully:', response.data);
        setSuccessMessage('Guest added successfully');
      }

      // Directly fetch the guest list from the API to verify database state
      try {
        console.log('Directly fetching guest list from API to verify database state...');
        const directGuestListResponse = await axios.get(
          'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/guest-list',
          {
            headers: { Authorization: `Bearer ${token}` },
            params: { _t: new Date().getTime() } // Cache busting
          }
        );
        console.log('DIRECT API CALL - All guests in database:', directGuestListResponse.data.guests);

        // Also fetch groups to verify
        const directGroupsResponse = await axios.get(
          'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/guest-groups',
          {
            headers: { Authorization: `Bearer ${token}` },
            params: { _t: new Date().getTime() } // Cache busting
          }
        );
        console.log('DIRECT API CALL - All groups in database:', directGroupsResponse.data.groups);

        // Now call the regular onSave function
        await onSave();

        // Close the modal immediately
        onClose();
      } catch (error) {
        console.error('Error during direct API verification:', error);
        // Still try the regular onSave as fallback
        try {
          await onSave();
        } catch (saveError) {
          console.error('Error in fallback onSave:', saveError);
        }
        onClose();
      }

    } catch (err: any) {
      console.error('Error saving guest:', err);
      setError(err.response?.data?.error || 'Failed to save guest');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 backdrop-brightness-30 flex items-center justify-center z-50 p-4">
      <div
        className="rounded-lg shadow-lg w-full max-w-md relative overflow-hidden max-h-[90vh] overflow-y-auto"
        style={{
          background: "linear-gradient(to bottom, #FAE6C4, #FFFFFF)",
          boxShadow: "0 10px 25px rgba(0, 0, 0, 0.2)",
        }}
      >
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 z-10"
        >
          <X size={20} />
        </button>

        {/* Logo */}
        <div className="flex justify-center pt-6">
          <div className="text-red-600">
            <Image
              src="/pics/logo.png"
              alt="Wedzat logo"
              width={40}
              height={40}
              className="object-cover"
            />
          </div>
        </div>

        <div className="px-6 py-4">
          <h3 className="text-2xl font-bold mb-2 text-center" style={{ color: "#B31B1E" }}>
            {guest ? 'Edit Guest' : 'Add Guest'}
          </h3>

        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            {/* Guest Name */}
            <div className="flex space-x-2">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  First Name *
                </label>
                <input
                  type="text"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
                  required
                />
              </div>
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Last Name *
                </label>
                <input
                  type="text"
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
                  required
                />
              </div>
            </div>

            {/* Contact Information */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email
              </label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
              />
              <p className="text-xs text-gray-500 mt-1">
                Email is required to send invitations
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Phone
              </label>
              <input
                type="tel"
                value={phone}
                onChange={(e) => setPhone(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
              />
            </div>

            {/* Group */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Group *
              </label>
              <select
                value={groupId}
                onChange={(e) => setGroupId(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
                required
              >
                <option value="">Select a group</option>
                {groups.map(group => (
                  <option key={group.group_id} value={group.group_id}>
                    {group.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Menu */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Menu
              </label>
              <select
                value={menuId}
                onChange={(e) => setMenuId(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
              >
                <option value="">Select a menu</option>
                {menuOptions.map(menu => (
                  <option key={menu.menu_id} value={menu.menu_id}>
                    {menu.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Attendance Status */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Attendance Status
              </label>
              <select
                value={attendanceStatus}
                onChange={(e) => setAttendanceStatus(e.target.value as 'attending' | 'pending' | 'declined')}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
              >
                <option value="attending">Attending</option>
                <option value="pending">Pending</option>
                <option value="declined">Declined</option>
              </select>
            </div>

            {/* Notes */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Notes
              </label>
              <textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
                rows={3}
              />
            </div>
          </div>

          <div className="mt-6 flex justify-end space-x-2">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-[#B31B1E] text-white rounded hover:bg-red-700 disabled:bg-red-300"
              disabled={loading}
            >
              {loading ? 'Saving...' : (guest ? 'Update' : 'Add')}
            </button>
          </div>
        </form>
        </div>
      </div>
    </div>
  );
};

export default AddGuestModal;
