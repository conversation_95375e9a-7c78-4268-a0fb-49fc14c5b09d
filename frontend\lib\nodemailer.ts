// lib/nodemailer.ts
import nodemailer from 'nodemailer';

// Create reusable transporter
export async function createTransporter() {
  // Log the email configuration for debugging
  console.log("Email credentials:", {
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT,
    user: process.env.EMAIL_USER ? process.env.EMAIL_USER.substring(0, 5) + '...' : 'missing',
    pass: process.env.EMAIL_PASS ? 'configured' : 'missing',
    from: process.env.EMAIL_FROM
  });

  // Create the transporter with credentials
  const transporter = nodemailer.createTransport({
    host: process.env.EMAIL_HOST,
    port: parseInt(process.env.EMAIL_PORT || '587'),
    secure: process.env.EMAIL_SECURE === 'true',
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
  });

  return transporter;
}

// Send OTP via email
export async function sendOtpEmail(email: string, otp: string): Promise<void> {
  try {
    console.log("Attempting to verify email connection...");

    const transporter = await createTransporter();

    // Define email content
    const mailOptions = {
      from: process.env.EMAIL_FROM,
      to: email,
      subject: 'Wedzat Email Verification',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 5px;">
          <div style="text-align: center; margin-bottom: 20px;">
            <h1 style="color: #e53e3e;">Wedzat</h1>
          </div>
          <h2 style="text-align: center; color: #333;">Email Verification</h2>
          <p>Thank you for signing up with Wedzat. To complete your registration, please use the verification code below:</p>
          <div style="text-align: center; margin: 20px 0;">
            <div style="font-size: 24px; font-weight: bold; letter-spacing: 8px; padding: 10px; background-color: #f5f5f5; border-radius: 5px; display: inline-block;">${otp}</div>
          </div>
          <p>This code will expire in 10 minutes.</p>
          <p>If you did not request this verification, please ignore this email.</p>
          <div style="text-align: center; margin-top: 20px; padding-top: 20px; border-top: 1px solid #eee; color: #777; font-size: 12px;">
            <p>&copy; ${new Date().getFullYear()} Wedzat. All rights reserved.</p>
          </div>
        </div>
      `,
    };

    // Send email
    await transporter.sendMail(mailOptions);
    console.log(`Email verification sent to ${email}`);
  } catch (error) {
    console.error('Error sending email:', error);
    throw error;
  }
}

// Send wedding invitation email
export async function sendWeddingInvitation(
  email: string,
  guestName: string,
  coupleName: string,
  websiteUrl: string,
  responseUrl: string
): Promise<void> {
  try {
    console.log("Attempting to send wedding invitation...");

    const transporter = await createTransporter();

    // Define email content with wedding invitation template
    const mailOptions = {
      from: process.env.EMAIL_FROM,
      to: email,
      subject: `You're Invited to ${coupleName}'s Wedding!`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 5px;">
          <div style="text-align: center; margin-bottom: 20px;">
            <h1 style="color: #B31B1E;">Wedding Invitation</h1>
          </div>
          <h2 style="text-align: center; color: #333;">You're Invited!</h2>
          <p>Dear ${guestName},</p>
          <p>We are delighted to invite you to our wedding celebration!</p>
          <p>Please visit our wedding website to view all the details:</p>
          <div style="text-align: center; margin: 20px 0;">
            <a href="${websiteUrl}" style="display: inline-block; padding: 10px 20px; background-color: #B31B1E; color: white; text-decoration: none; border-radius: 5px;">View Wedding Website</a>
          </div>
          <p>Will you be able to join us? Please let us know by clicking one of the buttons below:</p>
          <div style="text-align: center; margin: 20px 0;">
            <a href="${responseUrl}&response=attending" style="display: inline-block; padding: 10px 20px; margin: 0 10px; background-color: #B31B1E; color: white; text-decoration: none; border-radius: 5px;">Yes, I'll be there!</a>
            <a href="${responseUrl}&response=declined" style="display: inline-block; padding: 10px 20px; margin: 0 10px; background-color: #f8f9fa; color: #333; text-decoration: none; border-radius: 5px; border: 1px solid #ddd;">Sorry, I can't make it</a>
          </div>
          <p style="font-size: 12px; color: #777; text-align: center;">If the buttons above don't work, you can also respond by visiting our wedding website.</p>
          <div style="text-align: center; margin-top: 20px; padding-top: 20px; border-top: 1px solid #eee; color: #777; font-size: 12px;">
            <p>This is an automated message. Please do not reply to this email.</p>
            <p>&copy; ${new Date().getFullYear()} Wedzat. All rights reserved.</p>
          </div>
        </div>
      `,
    };

    // Send email
    await transporter.sendMail(mailOptions);
    console.log(`Wedding invitation sent to ${email}`);
  } catch (error) {
    console.error('Error sending wedding invitation:', error);
    throw error;
  }
}

// Send RSVP confirmation email
export async function sendRsvpConfirmation(
  email: string,
  guestName: string,
  coupleName: string,
  response: string
): Promise<void> {
  try {
    console.log("Sending RSVP confirmation...");

    const transporter = await createTransporter();

    // Create message based on response
    let message = "Thank you for your response!";
    if (response === 'attending') {
      message = `Thank you, ${guestName.split(' ')[0]}! ${coupleName} look forward to celebrating with you!`;
    } else {
      message = `Thank you for letting us know, ${guestName.split(' ')[0]}. ${coupleName} are sorry you can't make it.`;
    }

    // Define email content
    const mailOptions = {
      from: process.env.EMAIL_FROM,
      to: email,
      subject: 'Wedding RSVP Confirmation',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 5px;">
          <div style="text-align: center; margin-bottom: 20px;">
            <h1 style="color: #B31B1E;">RSVP Confirmation</h1>
          </div>
          <div>
            <p>Dear ${guestName},</p>
            <p>Thank you for responding to the wedding invitation from ${coupleName}.</p>
            <div style="padding: 10px; text-align: center; font-weight: bold; margin: 20px 0; ${response === 'attending' ? 'background-color: #e8f5e9; color: #2e7d32; border: 1px solid #2e7d32;' : 'background-color: #ffebee; color: #c62828; border: 1px solid #c62828;'}">
              Your response: <strong>${response.toUpperCase()}</strong>
            </div>
            <p>${message}</p>
          </div>
          <div style="text-align: center; margin-top: 20px; padding-top: 20px; border-top: 1px solid #eee; color: #777; font-size: 12px;">
            <p>This is an automated confirmation. Please do not reply to this email.</p>
            <p>&copy; ${new Date().getFullYear()} Wedzat. All rights reserved.</p>
          </div>
        </div>
      `,
    };

    // Send email
    await transporter.sendMail(mailOptions);
    console.log(`RSVP confirmation sent to ${email}`);
  } catch (error) {
    console.error('Error sending RSVP confirmation:', error);
    throw error;
  }
}