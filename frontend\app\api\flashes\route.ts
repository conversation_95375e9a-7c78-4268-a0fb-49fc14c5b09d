import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

export async function GET(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');

    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization header is required' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = searchParams.get('page') || '1';
    const limit = searchParams.get('limit') || '10';
    const location = searchParams.get('location');

    console.log(`Fetching flashes: page=${page}, limit=${limit}, location=${location}`);

    // Build query parameters for backend API
    const queryParams = new URLSearchParams({
      page,
      limit
    });

    if (location) {
      queryParams.append('location', location);
    }

    // Make API call to backend
    const response = await axios.get(
      `https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/flashes?${queryParams.toString()}`,
      {
        headers: {
          'Authorization': authHeader,
          'Content-Type': 'application/json',
        },
        timeout: 15000,
      }
    );

    console.log('Flashes API response received:', response.status);
    const res = NextResponse.json(response.data);
    res.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    return res;
  } catch (error: any) {
    console.error('Error fetching flashes:', error);

    // Detailed error response
    let errorMessage = 'Failed to fetch flashes';
    let statusCode = 500;

    if (error.response) {
      console.error('Flashes API response error:', error.response.data);
      statusCode = error.response.status;
      errorMessage = `Server error: ${error.response.data?.error || error.response.data?.message || error.message}`;
    } else if (error.request) {
      console.error('Flashes API request error:', error.request);
      errorMessage = 'No response received from server';
    } else {
      console.error('Flashes API error message:', error.message);
      errorMessage = error.message;
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}
