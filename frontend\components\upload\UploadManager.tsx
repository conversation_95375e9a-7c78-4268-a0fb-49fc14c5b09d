// components/upload/UploadManager.tsx
'use client';

import React, { useState, useRef, useEffect } from 'react';
import UploadTypeSelection from './UploadTypeSelection';
import VideoCategorySelection from './VideoCategorySelection';
import ThumbnailSelection from './ThumbnailSelection';
import PersonalDetails from './PersonalDetails';
import VendorDetails from './VendorDetails';
import FaceVerification from './FaceVerification';
import UploadProgress from './UploadProgress';
import { useUpload } from '../../contexts/UploadContexts';
import { getVideoDuration, validateVideoDuration } from '../../utils/uploadUtils';
import { showWarningAlert, showErrorAlert, showAlert } from '../../utils/alertUtils';
import { useMediaUpload, usePreloadMedia } from '../../hooks/useMedia';

export type UploadPhase =
  | 'typeSelection'
  | 'categorySelection'
  | 'fileUpload'
  | 'thumbnailSelection'
  | 'personalDetails'
  | 'vendorDetails'
  | 'faceVerification'
  | 'uploading'
  | 'complete'
  | 'closed';

interface PersonalDetailsData {
  caption: string;
  lifePartner: string;
  weddingStyle: string;
  place: string;
  eventType?: string;
  budget?: string;
}

interface VendorDetailItem {
  name: string;
  mobileNumber: string;
}

interface UploadManagerProps {
  onClose?: () => void;
  initialType?: string;
  onUploadComplete?: () => void;
}

// Helper function to format time in minutes and seconds
const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  if (minutes === 0) {
    return `${remainingSeconds} seconds`;
  } else if (minutes === 1 && remainingSeconds === 0) {
    return '1 minute';
  } else if (remainingSeconds === 0) {
    return `${minutes} minutes`;
  } else {
    return `${minutes} minute${minutes > 1 ? 's' : ''} and ${remainingSeconds} second${remainingSeconds !== 1 ? 's' : ''}`;
  }
};

const UploadManager: React.FC<UploadManagerProps> = ({ onClose, initialType, onUploadComplete }) => {
  const { state, setFile, setMediaType, setCategory, setMediaSubtype, setTitle, setDescription, setDetailField, startUpload, startUploadWithCategory, setThumbnail, setVendorDetails, setPersonalDetails, setDuration, resetUpload } = useUpload();
  const [phase, setPhase] = useState<UploadPhase>('typeSelection');
  const [selectedType, setSelectedType] = useState<string>(initialType || '');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [thumbnailImage, setThumbnailImage] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const vendorDetailsRef = useRef<Record<string, VendorDetailItem>>({});

  // Helper function to determine content type for PersonalDetails
  const getContentType = (): 'photo' | 'video' | 'moment' => {
    if (selectedType === 'moments' || state.mediaSubtype === 'story') {
      return 'moment';
    } else if (state.mediaType === 'video') {
      return 'video';
    } else {
      return 'photo';
    }
  };

  // Store personal details to persist between screens
  const [personalDetails, setLocalPersonalDetails] = useState<PersonalDetailsData>({
    caption: '',
    lifePartner: '',
    weddingStyle: '',
    place: '',
    eventType: '',
    budget: ''
  });

  // Auto-select the type if initialType is provided
  useEffect(() => {
    if (initialType) {
      console.log('Auto-selecting type from initialType:', initialType);
      handleTypeSelected(initialType);
    }
  }, []);

  // Store vendor details to persist between screens
  const [vendorDetailsData, setVendorDetailsData] = useState<Record<string, VendorDetailItem>>({
    venue: { name: '', mobileNumber: '' },
    photographer: { name: '', mobileNumber: '' },
    makeupArtist: { name: '', mobileNumber: '' },
    decorations: { name: '', mobileNumber: '' },
    caterer: { name: '', mobileNumber: '' }
  });

  // Use the new media upload hook
  const { mutate: uploadMedia, isPending: isUploading } = useMediaUpload();

  // Handle media type selection
  const handleTypeSelected = (type: string) => {
    // First, completely reset everything
    resetUpload();
    setPreviewImage(null);
    setThumbnailImage(null);

    // Then set the new type
    setSelectedType(type);
    console.log("Selected type:", type);

    if (['flashes', 'glimpses', 'movies', 'photos', 'moments'].includes(type)) {
      // For explicit video types, photos, and moments, set the appropriate media type and go to category selection
      if (type === 'photos') {
        console.log('Setting media type to photo for:', type);
        setMediaType('photo');
        setMediaSubtype('post');
      } else if (type === 'moments') {
        console.log('Setting media type for moments (will be determined by file type)');
        // For moments, we'll set the media type later based on the file type (photo or video)
        setMediaSubtype('story');
      } else {
        setMediaType('video');
        setMediaSubtype(getMediaSubtypeFromSelectedType(type));
      }
      // Go to category selection for all media types
      setPhase('categorySelection');
    } else if (type === 'photo') {
      // For single photo type (if it exists)
      console.log('Setting media type to photo for:', type);
      setMediaType('photo');
      setMediaSubtype('post');
      // Use a special photo-only upload handler for photos
      handlePhotoUpload();
    }
  };

  // Helper function to get the backend media subtype from the selected UI type
  const getMediaSubtypeFromSelectedType = (type: string): string => {
    // Map UI category to backend category for media_subtype
    switch (type) {
      // Photo types
      case 'moments':
        return 'story';  // Backend expects 'story' for moments
      case 'photos':
        return 'post';   // Backend expects 'post' for regular photos

      // Video types
      case 'flashes':
        return 'flash';  // Backend expects 'flash'
      case 'glimpses':
        return 'glimpse';  // Backend expects 'glimpse'
      case 'movies':
        return 'movie';  // Backend expects 'movie'

      // Default fallback
      default:
        return type === 'moments' ? 'story' : 'post';  // Default based on type
    }
  };

  // Handle category selection for both videos and photos
  const handleCategorySelected = (category: string) => {
    // First, make sure we have a clean state for the new upload
    // but preserve the selected type and media type
    const currentType = selectedType;
    const currentMediaType = state.mediaType;
    resetUpload();
    setSelectedType(currentType);
    setMediaType(currentMediaType);
    setPreviewImage(null);
    setThumbnailImage(null);

    // Now set the new category
    setSelectedCategory(category);

    // Get the media subtype based on the selected type
    let mediaSubtype;
    if (currentType === 'photos') {
      // For photos, always use 'post' as the media subtype
      mediaSubtype = 'post';
      console.log(`UPLOAD MANAGER - Using media subtype 'post' for photos`);
    } else {
      // For videos, use the subtype based on the selected type
      mediaSubtype = getMediaSubtypeFromSelectedType(selectedType);
      console.log(`UPLOAD MANAGER - Using media subtype ${mediaSubtype} based on selected type ${selectedType}`);
      console.log(`UPLOAD MANAGER - Backend expects: flashes→flash, glimpses→glimpse, movies→movie, moments→story`);
    }

    console.log("UPLOAD MANAGER - Selected category:", category);
    console.log("UPLOAD MANAGER - Setting media subtype to:", mediaSubtype);

    // Set the media subtype in the context
    setMediaSubtype(mediaSubtype);

    // Map the selected category to a valid backend video_category
    let backendVideoCategory = '';

    if (category === 'my_wedding_videos') {
      backendVideoCategory = 'my_wedding';
    } else if (category === 'wedding_vlog') {
      backendVideoCategory = 'wedding_vlog';
    }

    // Make sure we have a valid video_category
    if (!backendVideoCategory) {
      console.error('Invalid video category selected:', category);
      alert('Please select a valid video category');
      return;
    }

    // Set video category in the context for the backend
    console.log("UPLOAD MANAGER - Setting video_category to:", backendVideoCategory);
    setDetailField('video_category', backendVideoCategory);

    // Log the final values
    console.log("UPLOAD MANAGER - Selected category:", category);
    console.log("UPLOAD MANAGER - Backend video category set to:", backendVideoCategory);
    console.log("UPLOAD MANAGER - Media subtype set to:", mediaSubtype);

    // Proceed to file upload after setting the category
    if (currentType === 'photos') {
      // For photos, use the photo-specific upload handler
      handlePhotoUpload();
    } else {
      // For videos, use the standard file upload handler
      handleFileUpload();
    }
  };

  // Handle thumbnail upload
  const handleThumbnailUpload = () => {
    // Create a file input element
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';

    // Handle file selection
    input.onchange = (e) => {
      const files = (e.target as HTMLInputElement).files;
      if (files && files.length > 0) {
        const file = files[0];

        // Store the thumbnail
        setThumbnailImage(file);
        setThumbnail(file);

        console.log("Thumbnail selected:", file.name);

        // Show a preview if needed
        const reader = new FileReader();
        reader.onload = (e) => {
          if (e.target?.result) {
            // You could set a thumbnail preview here if needed
            console.log("Thumbnail preview ready");
          }
        };
        reader.readAsDataURL(file);
      }
    };

    // Trigger the file dialog
    input.click();
  };

  // Get user-friendly display name for a category
  const getCategoryDisplayName = (category: string): string => {
    switch (category) {
      case 'flash':
        return 'Flash';
      case 'glimpse':
        return 'Glimpse';
      case 'movie':
        return 'Movie';
      case 'story':
        return 'Story';
      case 'post':
        return 'Photo';
      default:
        return category.charAt(0).toUpperCase() + category.slice(1);
    }
  };

  // Get appropriate category based on duration
  const getAppropriateCategory = (duration: number): string => {
    // For very short videos (1 minute or less), use flash instead of story/moments
    if (duration <= 60) {
      return 'flash'; // Very short videos (1 minute or less) - changed from 'story' to 'flash'
    } else if (duration <= 90) {
      return 'flash'; // Short videos (1.5 minutes or less)
    } else if (duration <= 420) {
      return 'glimpse'; // Medium videos (7 minutes or less)
    } else {
      return 'movie'; // Long videos (over 7 minutes)
    }
  };

  // Special handler for photo uploads that strictly enforces image-only files
  const handlePhotoUpload = () => {
    console.log('handlePhotoUpload called - strict image-only upload');

    // Create a file input element specifically for photos
    const input = document.createElement('input');
    input.type = 'file';

    // Reset the input value
    input.value = '';

    // Only accept image files - explicitly list allowed types
    input.accept = 'image/jpeg,image/png,image/gif,image/webp';

    // Handle file selection with strict validation
    input.onchange = async (e) => {
      const files = (e.target as HTMLInputElement).files;
      if (!files || files.length === 0) return;

      const file = files[0];
      console.log('Photo file selected:', file.name, file.type, file.size);

      // Strict validation - must be an image file
      const validImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

      if (!validImageTypes.includes(file.type)) {
        console.error('Invalid file type for photos:', file.type);
        alert('Only JPEG, PNG, GIF, or WebP images can be uploaded as photos. Videos are not allowed.');
        return;
      }

      // Additional check - reject any file that might be a video
      if (file.type.startsWith('video/')) {
        console.error('Attempted to upload a video file as photo');
        alert('Videos cannot be uploaded as photos. Please select an image file.');
        return;
      }

      // For photos, we need to be more careful with state management
      // First, set the media type and subtype
      setMediaType('photo');
      setMediaSubtype('post');

      // Then set the file in the state
      setFile(file);
      console.log('Photo file set in state:', file.name);

      // Create a local reference to the file for use in the timeout
      const currentFile = file;

      // Double-check that the file is set in the state before proceeding
      setTimeout(() => {
        // Check if the file is in the state
        if (!state.file) {
          console.log('File not found in state after setting, trying again');
          // Try setting the file again
          setFile(currentFile);

          // Add another timeout to ensure the file is set
          setTimeout(() => {
            if (!state.file) {
              console.log('File still not in state, setting it one more time');
              setFile(currentFile);
            } else {
              console.log('File confirmed in state after second attempt:', state.file.name);
            }
            // New flow logic: For moments (stories), skip personal details and go directly to face verification
            if (selectedType === 'moments' || state.mediaSubtype === 'story') {
              console.log('Moments photo upload: skipping personal details, going directly to face verification');
              setPhase('faceVerification');
            } else {
              console.log('Moving to personalDetails phase for photo');
              setPhase('personalDetails');
            }
          }, 100);
        } else {
          console.log('File confirmed in state:', state.file.name);
          // New flow logic: For moments (stories), skip personal details and go directly to face verification
          if (selectedType === 'moments' || state.mediaSubtype === 'story') {
            console.log('Moments photo upload: skipping personal details, going directly to face verification');
            setPhase('faceVerification');
          } else {
            console.log('Moving to personalDetails phase for photo');
            setPhase('personalDetails');
          }
        }
      }, 100);

      // Handle image preview
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          setPreviewImage(e.target.result as string);
          console.log('Preview image set for photo');
        }
      };
      reader.readAsDataURL(file);

      // Note: We don't set the phase here anymore - it's handled in the timeout above
    };

    // Trigger the file dialog
    input.click();
  };

  // This function was previously used but is now replaced by getAppropriateCategory
  // Keeping a comment here for reference in case it needs to be restored

  // Handle manual upload button click
  const handleFileUpload = async (category?: string) => {
    console.log('handleFileUpload called with category:', category || 'none');

    // Create a file input element
    const input = document.createElement('input');
    input.type = 'file';

    // Reset the input value to ensure we get a new file selection event even if the same file is selected
    input.value = '';

    if (selectedType === 'moments') {
      input.accept = 'image/*,video/*';
    } else {
      input.accept = selectedType === 'photo' || selectedType === 'photos'
        ? 'image/jpeg,image/png,image/gif,image/webp' // Explicitly list image types only
        : 'video/*';
    }

    // Handle file selection
    input.onchange = async (e) => {
      const files = (e.target as HTMLInputElement).files;
      if (!files || files.length === 0) return;

      const file = files[0];
      console.log('File selected:', file.name, file.type, file.size);

      // Strict validation for photo uploads - must be an image file
      if (selectedType === 'photo' || selectedType === 'photos') {
        const validImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

        // Check if file is a video or not a valid image type
        if (file.type.startsWith('video/') || !validImageTypes.includes(file.type)) {
          console.error('Invalid file type for photos:', file.type);
          showErrorAlert('Invalid File Type', 'Only JPEG, PNG, GIF, or WebP images can be uploaded as photos. Videos are not allowed.');
          return;
        }
      }

      // Reset the upload context before setting the new file
      resetUpload();

      // Set the file in the state
      setFile(file);
      console.log('File set in state:', file.name);

      // If it's a video, calculate and set the duration
      // Double-check that we're not trying to upload a video as a photo
      if (file.type.startsWith('video/')) {
        // Safety check - don't process videos for photo uploads
        if (selectedType === 'photo' || selectedType === 'photos') {
          console.error('Attempted to process a video file for photo upload');
          showErrorAlert('Invalid File Type', 'Videos cannot be uploaded as photos. Please select an image file.');
          resetUpload();
          return;
        }
        try {
          const duration = await getVideoDuration(file);
          console.log('Video duration calculated:', duration);
          setDuration(duration);

          // For moments, check if it's a video and validate the duration (max 1 minute)
          if (selectedType === 'moments') {
            console.log('Validating moments video duration...');
            setMediaType('video');

            // Check if the video is longer than 1 minute (60 seconds)
            if (duration > 60) {
              console.log(`Moments video too long: ${duration} seconds (max: 60 seconds)`);

              // Show a more detailed error message with custom alert
              showWarningAlert(
                'Moments Video Too Long',
                `Moments videos must be 1 minute or less.\n\nYour video is ${formatTime(duration)} long.\n\nPlease select a shorter video or trim this video to 1 minute or less.`
              );

              // Reset the upload context but preserve the selected type and category
              const currentType = selectedType;
              const currentCategory = selectedCategory;

              // First set the phase back to category selection
              setPhase('categorySelection');

              // Then reset the upload state
              setTimeout(() => {
                resetUpload();
                setSelectedType(currentType);
                setSelectedCategory(currentCategory);
                console.log('Reset upload state after moments video duration validation failure');
              }, 100);

              // Return early to prevent further processing
              return;
            }

            console.log(`Moments video duration valid: ${duration} seconds (max: 60 seconds)`);
            // For moments, we always use 'story' as the media subtype
            console.log('Setting media subtype for moments video to story');
            setMediaSubtype('story');
          }

          // If we have a category, validate the duration for that category
          if (selectedType && ['flashes', 'glimpses', 'movies'].includes(selectedType)) {
            const mediaSubtype = getMediaSubtypeFromSelectedType(selectedType);
            const validationResult = validateVideoDuration(duration, mediaSubtype);

            if (!validationResult.isValid) {
              // If there's a suggested category, automatically switch to it
              if (validationResult.suggestedCategory) {
                // For videos that exceed the maximum duration, automatically switch without asking
                console.log(`Video exceeds maximum duration for ${mediaSubtype}. Automatically switching to ${validationResult.suggestedCategory}`);
                showWarningAlert(
                  'Video Duration Notice',
                  `Your video is too long for the ${getCategoryDisplayName(mediaSubtype)} category. It will be uploaded as a ${getCategoryDisplayName(validationResult.suggestedCategory)} instead.`
                );

                // Switch to the suggested category
                console.log(`Switching to suggested category: ${validationResult.suggestedCategory}`);

                // Make sure we keep a reference to the file
                const currentFile = file;

                // Update the media subtype
                setMediaSubtype(validationResult.suggestedCategory);

                // Update the selected type to match the new category
                // Never suggest 'story' (moments) for other categories
                if (validationResult.suggestedCategory === 'flash') {
                  setSelectedType('flashes');
                } else if (validationResult.suggestedCategory === 'glimpse') {
                  setSelectedType('glimpses');
                } else if (validationResult.suggestedCategory === 'movie') {
                  setSelectedType('movies');
                }
                // Removed the 'story' suggestion for short videos

                // Make sure the file is still set in the state
                setTimeout(() => {
                  if (!state.file) {
                    console.log('Re-setting file after category change:', currentFile.name);
                    setFile(currentFile);
                  }
                }, 50);
              } else {
                // No suggested category, just show the error
                showErrorAlert('Video Duration Error', validationResult.error || 'The video duration is not valid for this category.');
              }
            } else if (validationResult.suggestedCategory && validationResult.suggestedCategory !== mediaSubtype) {
              // Video is valid for current category but there's a better category
              // For this case, we still give the user a choice since the video is valid for the current category
              // Use our custom confirm dialog instead of window.confirm
              const confirmSwitch = await showAlert({
                title: 'Category Suggestion',
                message: `${validationResult.error}\n\nWould you like to switch to the suggested category?`,
                type: 'warning',
                confirmText: 'Yes, Switch Category',
                cancelText: 'No, Keep Current',
                onConfirm: () => { }
              });

              if (confirmSwitch) {
                // Switch to the suggested category
                console.log(`Switching to suggested category: ${validationResult.suggestedCategory}`);

                // Make sure we keep a reference to the file
                const currentFile = file;

                // Update the media subtype
                setMediaSubtype(validationResult.suggestedCategory);

                // Update the selected type to match the new category
                // Never suggest 'story' (moments) for other categories
                if (validationResult.suggestedCategory === 'flash') {
                  setSelectedType('flashes');
                } else if (validationResult.suggestedCategory === 'glimpse') {
                  setSelectedType('glimpses');
                } else if (validationResult.suggestedCategory === 'movie') {
                  setSelectedType('movies');
                }
                // Removed the 'story' suggestion for short videos

                // Make sure the file is still set in the state
                setTimeout(() => {
                  if (!state.file) {
                    console.log('Re-setting file after category change:', currentFile.name);
                    setFile(currentFile);
                  }
                }, 50);
              }
            }
          }

          // Always go to thumbnail selection for videos
          console.log('Moving to thumbnailSelection phase');

          // Keep a reference to the current file
          const currentFile = file;

          // Double-check that the file is set in the state before proceeding
          if (state.file) {
            console.log('File confirmed in state before phase change:', state.file.name);
            setPhase('thumbnailSelection');
          } else {
            console.log('File not found in state before phase change, setting it again');
            // Try setting the file again
            setFile(currentFile);
            // Add a small delay to ensure the state is updated
            setTimeout(() => {
              // Double-check again
              if (!state.file) {
                console.log('File still not in state, setting it one more time');
                setFile(currentFile);
              }
              console.log('Delayed phase change to thumbnailSelection');
              setPhase('thumbnailSelection');
            }, 100);
          }
        } catch (error) {
          console.error('Error calculating video duration:', error);

          // For moments videos, we need to enforce the duration check
          // If we can't calculate duration, we can't validate it, so we should reject the upload
          if (selectedType === 'moments') {
            showErrorAlert('Video Error', 'Unable to determine video duration. Please try a different video file.');
            resetUpload();
            return;
          }
          console.log('Moving to thumbnailSelection phase despite error');

          // Keep a reference to the current file
          const currentFile = file;

          // Double-check that the file is set in the state before proceeding
          if (state.file) {
            console.log('File confirmed in state before phase change (error case):', state.file.name);
            setPhase('thumbnailSelection');
          } else {
            console.log('File not found in state before phase change (error case), setting it again');
            // Try setting the file again
            setFile(currentFile);
            // Add a small delay to ensure the state is updated
            setTimeout(() => {
              // Double-check again
              if (!state.file) {
                console.log('File still not in state (error case), setting it one more time');
                setFile(currentFile);
              }
              console.log('Delayed phase change to thumbnailSelection (error case)');
              setPhase('thumbnailSelection');
            }, 100);
          }
        }
      } else {
        // For photos or moments images
        if (selectedType === 'moments') {
          // For moments, we need to set the media type based on the file type
          if (file.type.startsWith('image/')) {
            console.log('Moments image detected');
            setMediaType('photo');
            // For moments images, we always use 'story' as the media subtype
            setMediaSubtype('story');

            // Create a local reference to the file for use in the timeout
            const currentFile = file;

            // Double-check that the file is set in the state before proceeding
            setTimeout(() => {
              // Check if the file is in the state
              if (!state.file) {
                console.log('Moments photo not found in state after setting, trying again');
                // Try setting the file again
                setFile(currentFile);
              } else {
                console.log('Moments photo confirmed in state:', state.file.name);
              }
            }, 50);
          } else {
            console.log('Invalid file type for moments. Only images and videos less than 1 minute are allowed.');
            showErrorAlert('Invalid File Type', 'Invalid file type for moments. Only images and videos less than 1 minute are allowed.');

            // Reset the upload context but preserve the selected type and category
            const currentType = selectedType;
            const currentCategory = selectedCategory;

            // First set the phase back to category selection
            setPhase('categorySelection');

            // Then reset the upload state
            setTimeout(() => {
              resetUpload();
              setSelectedType(currentType);
              setSelectedCategory(currentCategory);
              console.log('Reset upload state after invalid file type for moments');
            }, 100);
            return;
          }
        }

        // Handle image preview and set phase
        const reader = new FileReader();
        reader.onload = (e) => {
          if (e.target?.result) {
            setPreviewImage(e.target.result as string);
            console.log('Preview image set for file:', file.name);
          }
        };
        reader.readAsDataURL(file);

        // Create a local reference to the file for use in the timeout
        const currentFile = file;

        // Double-check that the file is set in the state before proceeding
        setTimeout(() => {
          // Check if the file is in the state
          if (!state.file) {
            console.log('File not found in state before moving to personalDetails, setting it again');
            // Try setting the file again
            setFile(currentFile);

            // Add another timeout to ensure the file is set
            setTimeout(() => {
              if (!state.file) {
                console.log('File still not in state, setting it one more time');
                setFile(currentFile);
              } else {
                console.log('File confirmed in state after second attempt:', state.file.name);
              }
              // New flow logic: For moments (stories), skip personal details and go directly to face verification
              if (selectedType === 'moments' || state.mediaSubtype === 'story') {
                console.log('Moments image upload: skipping personal details, going directly to face verification');
                setPhase('faceVerification');
              } else {
                console.log('Moving to personalDetails phase for photo/image');
                setPhase('personalDetails');
              }
            }, 100);
          } else {
            console.log('File confirmed in state:', state.file.name);
            // New flow logic: For moments (stories), skip personal details and go directly to face verification
            if (selectedType === 'moments' || state.mediaSubtype === 'story') {
              console.log('Moments image upload: skipping personal details, going directly to face verification');
              setPhase('faceVerification');
            } else {
              console.log('Moving to personalDetails phase for photo/image');
              setPhase('personalDetails');
            }
          }
        }, 100);
      }
    };

    // Trigger the file dialog
    input.click();
  };

  // Handle personal details completed
  const handlePersonalDetailsCompleted = (details: PersonalDetailsData) => {
    console.log('Personal details completed:', details);

    // Store the personal details in local state for component persistence
    setLocalPersonalDetails(details);

    // Validate that we have a title
    if (!details.caption || !details.caption.trim()) {
      console.error('Caption/title is empty, this should not happen');
      // Go back to personal details to fix this
      setPhase('personalDetails');
      return;
    }

    // Set the title in the upload context
    setTitle(details.caption.trim());

    // Also store in global context for persistence (this is the upload context function)
    setPersonalDetails(details);

    // Check if we have a file in the state
    if (!state.file) {
      console.error('No file found in state after personal details');
      showErrorAlert('Upload Error', 'Something went wrong with your file upload. Please try again.');
      setPhase('typeSelection');
      return;
    }

    console.log('File confirmed in state after personal details:', state.file.name);
    console.log('Personal details set successfully');
    console.log('Title set to:', details.caption.trim());
    console.log('Current selectedType:', selectedType);
    console.log('Current mediaSubtype:', state.mediaSubtype);

    // New flow logic based on backend requirements:
    // - Moments (stories): Skip personal details, go directly to face verification
    // - Photos: Go to face verification after personal details (no vendor details)
    // - Videos: Go to vendor details after personal details

    if (state.mediaType === 'photo') {
      console.log('Photo upload: proceeding to face verification (no vendor details needed)');
      setPhase('faceVerification');
    } else if (selectedType === 'moments' || state.mediaSubtype === 'story') {
      console.log('Moments upload: proceeding to face verification (no vendor details needed)');
      setPhase('faceVerification');
    } else {
      // For videos (flashes, glimpses, movies), proceed to vendor details
      console.log('Video upload: proceeding to vendor details');
      setPhase('vendorDetails');
    }
  };

  // Handle vendor details completed
  const handleVendorDetailsCompleted = (vendorDetails: Record<string, VendorDetailItem>) => {
    // console.log('Vendor details completed:', vendorDetails);

    // Normalize vendor details to ensure consistent field names
    const normalizedVendorDetails = { ...vendorDetails };

    // Ensure we have both frontend and backend field names for makeup artist and decorations
    if (vendorDetails.makeupArtist) {
      normalizedVendorDetails.makeup_artist = vendorDetails.makeupArtist;
    } else if (vendorDetails.makeup_artist) {
      normalizedVendorDetails.makeupArtist = vendorDetails.makeup_artist;
    }

    if (vendorDetails.decorations) {
      normalizedVendorDetails.decoration = vendorDetails.decorations;
    } else if (vendorDetails.decoration) {
      normalizedVendorDetails.decorations = vendorDetails.decoration;
    }

    // Store the normalized vendor details for persistence between screens
    setVendorDetailsData(normalizedVendorDetails);

    // Also store in the ref for Edge browser compatibility
    vendorDetailsRef.current = normalizedVendorDetails;

    // Store vendor details in localStorage for persistence
    try {
      localStorage.setItem('wedzat_vendor_details', JSON.stringify(normalizedVendorDetails));
      console.log('UPLOAD MANAGER - Stored vendor details in localStorage');
    } catch (error) {
      console.error('UPLOAD MANAGER - Failed to store vendor details in localStorage:', error);
    }

    // Save the current video_category before setting vendor details
    const currentVideoCategory = state.detailFields.video_category;
    console.log('UPLOAD MANAGER - Saving video_category before vendor details:', currentVideoCategory);

    // Store video_category in localStorage
    if (currentVideoCategory) {
      try {
        localStorage.setItem('wedzat_video_category', currentVideoCategory);
        console.log('UPLOAD MANAGER - Stored video_category in localStorage:', currentVideoCategory);
      } catch (error) {
        console.error('UPLOAD MANAGER - Failed to store video_category in localStorage:', error);
      }
    }

    // Store in global context for persistence
    setVendorDetails(normalizedVendorDetails);

    // Explicitly set each vendor detail field
    Object.entries(normalizedVendorDetails).forEach(([vendorType, details]) => {
      if (details && details.name && details.mobileNumber) {
        setDetailField(`vendor_${vendorType}_name`, details.name);
        setDetailField(`vendor_${vendorType}_contact`, details.mobileNumber);
      }
    });

    // Re-set the video_category after vendor details to ensure it's preserved
    if (currentVideoCategory) {
      console.log('UPLOAD MANAGER - Re-setting video_category after vendor details:', currentVideoCategory);
      setTimeout(() => {
        setDetailField('video_category', currentVideoCategory);
      }, 100);
    }

    // Log all detail fields after setting vendor details
    setTimeout(() => {
      console.log('All detail fields after vendor details:', state.detailFields);
      console.log('Detail fields count:', Object.keys(state.detailFields).length);
      console.log('Normalized vendor details:', normalizedVendorDetails);
    }, 200);

    // Add a small delay to ensure the state is updated before proceeding
    // This helps with cross-browser compatibility, especially in Edge
    setTimeout(() => {
      // Double-check that we have at least 4 vendor details before proceeding
      const vendorNameFields = Object.keys(state.detailFields).filter(key => key.startsWith('vendor_') && key.endsWith('_name'));
      const vendorContactFields = Object.keys(state.detailFields).filter(key => key.startsWith('vendor_') && key.endsWith('_contact'));

      console.log('UPLOAD MANAGER - Vendor name fields:', vendorNameFields.length);
      console.log('UPLOAD MANAGER - Vendor contact fields:', vendorContactFields.length);

      // Edge browser workaround - directly set vendor details in the state
      if (typeof window !== 'undefined' && /Edge|Edg/.test(window.navigator.userAgent)) {
        console.log('UPLOAD MANAGER - Edge browser detected, applying direct vendor details workaround');

        // Create vendor detail fields directly in the state
        // This is a workaround for Edge browser where the state update doesn't properly preserve vendor details
        Object.entries(normalizedVendorDetails).forEach(([vendorType, details]) => {
          if (details && details.name && details.mobileNumber) {
            // Set the vendor details directly in the state
            state.detailFields[`vendor_${vendorType}_name`] = details.name;
            state.detailFields[`vendor_${vendorType}_contact`] = details.mobileNumber;

            // Also set the normalized version
            const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' :
              vendorType === 'decorations' ? 'decoration' : vendorType;

            if (normalizedType !== vendorType) {
              state.detailFields[`vendor_${normalizedType}_name`] = details.name;
              state.detailFields[`vendor_${normalizedType}_contact`] = details.mobileNumber;
            }

            console.log(`UPLOAD MANAGER - Edge workaround: Added vendor ${vendorType} directly to state`);
          }
        });

        // Re-set the video_category directly
        if (currentVideoCategory) {
          state.detailFields.video_category = currentVideoCategory;
          console.log('UPLOAD MANAGER - Edge workaround: Re-set video_category directly:', currentVideoCategory);
        }
      }

      // Proceed to face verification
      setPhase('faceVerification');
    }, 300);
  };

  // Handle thumbnail selection
  const handleThumbnailSelected = (thumbnailFile?: File) => {
    if (thumbnailFile) {
      // Set the thumbnail in the context
      setThumbnail(thumbnailFile);
      console.log('Thumbnail selected:', thumbnailFile.name);
    } else {
      console.log('No thumbnail selected, using auto-generated thumbnail');
    }

    // New flow logic: For moments (stories), skip personal details and go directly to face verification
    if (selectedType === 'moments' || state.mediaSubtype === 'story') {
      console.log('Moments upload: skipping personal details, going directly to face verification');
      setPhase('faceVerification');
    } else {
      // For photos and videos, go to personal details
      console.log('Photo/Video upload: proceeding to personal details');
      setPhase('personalDetails');
    }
  };

  // Function to proceed with upload after vendor details are applied
  const proceedWithUpload = (videoCategory: string | undefined) => {
    // Double-check that we have a title before changing to uploading phase
    if (!state.title || !state.title.trim()) {
      console.error('Title is missing before upload, setting it from personal details');

      // Try to set the title from personal details
      if (personalDetails.caption && personalDetails.caption.trim()) {
        // console.log('Setting personal details from local state:', personalDetails);
        // Use the global context function to set all personal details at once
        setPersonalDetails(personalDetails);
        // Explicitly set the title as well
        setTitle(personalDetails.caption.trim());
      } else {
        console.error('No title in personal details either, going back to personal details');
        setPhase('personalDetails');
        return;
      }
    }

    // Edge browser workaround - directly set vendor details in the state
    if (typeof window !== 'undefined' && /Edge|Edg/.test(window.navigator.userAgent)) {
      console.log('UPLOAD MANAGER - Edge browser detected in face verification, applying vendor details workaround');

      // Get the vendor details from the vendor details data
      const vendorDetailsData = vendorDetailsRef.current;
      if (vendorDetailsData) {
        console.log('UPLOAD MANAGER - Edge workaround: Using vendor details from ref:', vendorDetailsData);

        // Create vendor detail fields directly in the state
        Object.entries(vendorDetailsData).forEach(([vendorType, details]) => {
          if (details && details.name && details.mobileNumber) {
            // Set the vendor details directly in the state
            state.detailFields[`vendor_${vendorType}_name`] = details.name;
            state.detailFields[`vendor_${vendorType}_contact`] = details.mobileNumber;

            // Also set the normalized version
            const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' :
              vendorType === 'decorations' ? 'decoration' : vendorType;

            if (normalizedType !== vendorType) {
              state.detailFields[`vendor_${normalizedType}_name`] = details.name;
              state.detailFields[`vendor_${normalizedType}_contact`] = details.mobileNumber;
            }

            console.log(`UPLOAD MANAGER - Edge workaround: Added vendor ${vendorType} directly to state`);
          }
        });
      }
    }

    // For videos, check if we have a video_category
    if (state.mediaType === 'video') {
      console.log(`UPLOAD MANAGER - Checking video_category before upload`);
      console.log(`UPLOAD MANAGER - Current video_category: ${state.detailFields.video_category || 'Not set'}`);
      console.log(`UPLOAD MANAGER - Current mediaSubtype: ${state.mediaSubtype}`);
      console.log(`UPLOAD MANAGER - Selected category: ${selectedCategory || 'Not set'}`);

      // Special handling for glimpses
      if (state.mediaSubtype === 'glimpse') {
        console.log(`UPLOAD MANAGER - Special handling for glimpses`);

        // If we don't have a video_category yet, try to set it from selectedCategory
        if (!state.detailFields.video_category && selectedCategory) {
          // Map the UI category to the backend video_category
          let videoCategory = '';

          if (selectedCategory === 'my_wedding_videos') {
            videoCategory = 'my_wedding';
          } else if (selectedCategory === 'wedding_vlog') {
            videoCategory = 'wedding_vlog';
          } else if (selectedCategory === 'friends_family_videos') {
            videoCategory = 'friends_family_video';
          }

          if (videoCategory) {
            console.log(`UPLOAD MANAGER - Setting video_category for glimpse: ${videoCategory}`);
            setDetailField('video_category', videoCategory);
          }
        } else {
          console.log(`UPLOAD MANAGER - Glimpse already has video_category: ${state.detailFields.video_category}`);
        }
      }

      // If we still don't have a video_category, use a default based on selectedCategory
      if (!state.detailFields.video_category && selectedCategory) {
        console.log(`UPLOAD MANAGER - No video_category set, using selectedCategory: ${selectedCategory}`);

        // Map the UI category to the backend video_category
        let videoCategory = '';

        if (selectedCategory === 'my_wedding_videos') {
          videoCategory = 'my_wedding';
        } else if (selectedCategory === 'wedding_vlog') {
          videoCategory = 'wedding_vlog';
        } else if (selectedCategory === 'friends_family_videos') {
          videoCategory = 'friends_family_video';
        }

        if (videoCategory) {
          console.log(`UPLOAD MANAGER - Setting video_category from selectedCategory: ${videoCategory}`);
          setDetailField('video_category', videoCategory);
        }
      }

      // Final check - if we still don't have a video_category, use a default
      if (!state.detailFields.video_category) {
        console.log('No video_category found, using a default one');
        // Use 'my_wedding' as a default category instead of asking the user again
        setDetailField('video_category', 'my_wedding');
        console.log('Set default video_category to my_wedding');
      }
    }

    // Edge browser workaround - directly set vendor details in the state before upload
    if (typeof window !== 'undefined' && /Edge|Edg/.test(window.navigator.userAgent)) {
      console.log('UPLOAD MANAGER - Edge browser detected before upload, applying vendor details workaround');

      // Get the vendor details from the vendor details data
      const vendorDetailsData = vendorDetailsRef.current;
      if (vendorDetailsData && Object.keys(vendorDetailsData).length > 0) {
        console.log('UPLOAD MANAGER - Edge workaround: Using vendor details from ref before upload:', vendorDetailsData);

        // Create vendor detail fields directly in the state
        Object.entries(vendorDetailsData).forEach(([vendorType, details]) => {
          if (details && details.name && details.mobileNumber) {
            // Set the vendor details directly in the state
            state.detailFields[`vendor_${vendorType}_name`] = details.name;
            state.detailFields[`vendor_${vendorType}_contact`] = details.mobileNumber;

            // Also set the normalized version
            const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' :
              vendorType === 'decorations' ? 'decoration' : vendorType;

            if (normalizedType !== vendorType) {
              state.detailFields[`vendor_${normalizedType}_name`] = details.name;
              state.detailFields[`vendor_${normalizedType}_contact`] = details.mobileNumber;
            }

            console.log(`UPLOAD MANAGER - Edge workaround: Added vendor ${vendorType} directly to state before upload`);
          }
        });
      }
    }

    // Check if we have a file before proceeding
    if (!state.file) {
      console.error('No file found in state before upload');
      showErrorAlert('Upload Error', 'No file selected. Please select a file to upload.');

      // Go back to type selection to start over
      setPhase('typeSelection');
      return;
    }

    // Now we can proceed to uploading phase
    setPhase('uploading');

    // Log the current state before starting upload
    console.log('Current state before upload:', {
      file: state.file ? state.file.name : 'No file',
      mediaType: state.mediaType,
      mediaSubtype: state.mediaSubtype,
      title: state.title,
      description: state.description,
      detailFields: state.detailFields,
      detailFieldsCount: Object.keys(state.detailFields).length
    });

    // Double-check that we're using the correct category
    console.log(`UPLOAD MANAGER - Final check - Selected type: ${selectedType}`);
    console.log(`UPLOAD MANAGER - Final check - MediaSubtype in state: ${state.mediaSubtype}`);

    // If the mediaSubtype doesn't match what we expect based on the selected type, fix it
    if (selectedType && state.mediaSubtype !== getMediaSubtypeFromSelectedType(selectedType)) {
      console.log(`UPLOAD MANAGER - WARNING: MediaSubtype mismatch detected!`);
      console.log(`UPLOAD MANAGER - Expected mediaSubtype based on selected type: ${getMediaSubtypeFromSelectedType(selectedType)}`);
      console.log(`UPLOAD MANAGER - Actual mediaSubtype in state: ${state.mediaSubtype}`);
      console.log(`UPLOAD MANAGER - Correcting category before upload...`);

      // Get the corrected category
      const correctedCategory = getMediaSubtypeFromSelectedType(selectedType);
      console.log(`UPLOAD MANAGER - Category corrected to: ${correctedCategory}`);

      // Get the video_category from the original selection
      // We need to map it to the correct backend value
      let videoCategory = '';

      if (selectedCategory === 'my_wedding_videos') {
        videoCategory = 'my_wedding';
      } else if (selectedCategory === 'wedding_vlog') {
        videoCategory = 'wedding_vlog';
      } else if (selectedCategory === 'friends_family_videos') {
        videoCategory = 'friends_family_video';
      }

      console.log(`UPLOAD MANAGER - Original selected category: ${selectedCategory}`);
      console.log(`UPLOAD MANAGER - Mapped to backend video_category: ${videoCategory}`);

      // Start the upload process with the corrected category and video_category
      startUploadWithCategory(correctedCategory, videoCategory);
    } else {
      // Get the video_category from the state
      const finalVideoCategory = videoCategory || state.detailFields.video_category || 'my_wedding';
      console.log(`UPLOAD MANAGER - Using video_category for upload: ${finalVideoCategory}`);

      // Start the upload process with the current category and video_category
      startUploadWithCategory(state.mediaSubtype, finalVideoCategory).then(() => {
        // Upload completed successfully
        console.log('Upload completed successfully');
      }).catch((error) => {
        console.error('Upload failed:', error);
      });
    }
  };

  // Handle face verification completed and start upload
  const handleFaceVerificationCompleted = () => {
    console.log('Face verification completed, starting upload process');

    // Check if we have a file in the state
    if (!state.file) {
      console.error('No file found in state after face verification');
      showErrorAlert('Upload Error', 'Something went wrong with your file upload. Please try again.');
      setPhase('typeSelection');
      return;
    }

    console.log('File confirmed in state after face verification:', state.file.name);

    // Try to get vendor details from localStorage first
    let vendorDetailsData = vendorDetailsRef.current;

    // If not in ref, try localStorage
    if (!vendorDetailsData || Object.keys(vendorDetailsData).length === 0) {
      try {
        const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');
        if (storedVendorDetails) {
          vendorDetailsData = JSON.parse(storedVendorDetails);
          console.log('UPLOAD MANAGER - Retrieved vendor details from localStorage:', storedVendorDetails);

          // Update the ref with the localStorage data
          vendorDetailsRef.current = vendorDetailsData;

          // Log the vendor details we found
          console.log(`UPLOAD MANAGER - Found ${Object.keys(vendorDetailsData).length} vendor details in localStorage`);
          Object.entries(vendorDetailsData).forEach(([vendorType, details]: [string, any]) => {
            if (details && details.name && details.mobileNumber) {
              console.log(`UPLOAD MANAGER - Vendor ${vendorType}: ${details.name} (${details.mobileNumber})`);
            }
          });
        } else {
          console.log('UPLOAD MANAGER - No vendor details found in localStorage');
        }
      } catch (error) {
        console.error('UPLOAD MANAGER - Failed to retrieve vendor details from localStorage:', error);
      }
    } else {
      console.log(`UPLOAD MANAGER - Using ${Object.keys(vendorDetailsData).length} vendor details from ref`);
    }

    // Try to get video_category from localStorage
    let videoCategory = state.detailFields.video_category;
    if (!videoCategory) {
      try {
        const storedVideoCategory = localStorage.getItem('wedzat_video_category');
        if (storedVideoCategory) {
          videoCategory = storedVideoCategory;
          console.log('UPLOAD MANAGER - Retrieved video_category from localStorage:', videoCategory);

          // Set it in the state
          setDetailField('video_category', videoCategory);
        }
      } catch (error) {
        console.error('UPLOAD MANAGER - Failed to retrieve video_category from localStorage:', error);
      }
    }

    // Ensure vendor details are present
    if (vendorDetailsData) {
      console.log('UPLOAD MANAGER - Applying vendor details to state');

      // Create a batch of all detail fields to update at once
      const detailFieldUpdates: Record<string, string> = {};
      let completeVendorCount = 0;

      // Re-apply vendor details to ensure they're in the state
      Object.entries(vendorDetailsData).forEach(([vendorType, details]) => {
        if (details && details.name && details.mobileNumber) {
          // Add to the batch
          detailFieldUpdates[`vendor_${vendorType}_name`] = details.name;
          detailFieldUpdates[`vendor_${vendorType}_contact`] = details.mobileNumber;
          completeVendorCount++;

          // Also set normalized versions
          const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' :
            vendorType === 'decorations' ? 'decoration' : vendorType;

          if (normalizedType !== vendorType) {
            detailFieldUpdates[`vendor_${normalizedType}_name`] = details.name;
            detailFieldUpdates[`vendor_${normalizedType}_contact`] = details.mobileNumber;
          }
        }
      });

      // Apply all updates at once
      console.log(`UPLOAD MANAGER - Applying ${completeVendorCount} complete vendor details to state`);
      console.log('UPLOAD MANAGER - Detail field updates:', JSON.stringify(detailFieldUpdates));

      // Apply each update individually to ensure they're all set
      Object.entries(detailFieldUpdates).forEach(([field, value]) => {
        setDetailField(field, value);
      });

      // Add a delay before proceeding to ensure state updates are applied
      setTimeout(() => {
        console.log('UPLOAD MANAGER - Vendor details applied to state, proceeding with upload');
        proceedWithUpload(videoCategory);
      }, 500);
    } else {
      console.log('UPLOAD MANAGER - No vendor details found, proceeding with upload');
      proceedWithUpload(videoCategory);
    }

    // This code has been moved to the proceedWithUpload function
  };

  // Handle going back to personal details from upload error
  const handleBackToPersonalDetails = () => {
    // console.log('Going back to personal details with stored data:', personalDetails);

    // Make sure the personal details are set in the context
    if (personalDetails.caption && personalDetails.caption.trim()) {
      // Use the global context function to set all personal details at once
      setPersonalDetails(personalDetails);
    }

    setPhase('personalDetails');
  };

  // Handle close modal
  const handleClose = () => {
    // Check if upload was successful and call onUploadComplete
    if (state.step === 'complete' && onUploadComplete) {
      console.log('Upload completed successfully, calling onUploadComplete callback');
      onUploadComplete();
    }

    // Reset the phase first
    setPhase('closed');

    // Call the onClose callback if provided
    if (onClose) {
      onClose();
    }

    // Reset the upload state after a short delay to ensure the modal is closed first
    setTimeout(() => {
      resetUpload();
      console.log('Upload state reset after modal close');
    }, 100);
  };

  // Render selected phase component
  if (phase === 'closed') {
    return null;
  }

  return (
    <>
      {phase === 'typeSelection' && (
        <UploadTypeSelection
          onNext={handleTypeSelected}
          onClose={handleClose}
        />
      )}

      {phase === 'categorySelection' && (
        <VideoCategorySelection
          onNext={handleCategorySelected}
          onBack={() => setPhase('typeSelection')}
          onUpload={handleCategorySelected} // This is now redundant but kept for compatibility
          onThumbnailUpload={handleThumbnailUpload}
          onClose={handleClose}
          mediaType={state.mediaType as 'photo' | 'video'} // Pass the current media type
          selectedType={selectedType} // Pass the selected type (moments, flashes, etc.)
        />
      )}

      {phase === 'thumbnailSelection' && state.file && (
        <ThumbnailSelection
          videoFile={state.file}
          onNext={handleThumbnailSelected}
          onBack={() => {
            // Go back to category selection instead of triggering file upload again
            if (['flashes', 'glimpses', 'movies'].includes(selectedType)) {
              setPhase('categorySelection');
            } else {
              // For moments, go back to type selection
              setPhase('typeSelection');
            }
          }}
          onClose={() => {
            // Completely reset the state before closing
            resetUpload();
            handleClose();
          }}
        />
      )}

      {phase === 'personalDetails' && (
        <PersonalDetails
          onNext={handlePersonalDetailsCompleted}
          onBack={() => {
            // Go back to thumbnail selection for videos
            if (state.mediaType === 'video' && state.file) {
              setPhase('thumbnailSelection');
            } else {
              // For photos, go back to type selection
              setPhase('typeSelection');
            }
          }}
          onClose={() => {
            // Completely reset the state before closing
            resetUpload();
            handleClose();
          }}
          previewImage={previewImage}
          videoFile={state.mediaType === 'video' ? state.file : null}
          mediaType={state.mediaType}
          contentType={getContentType()} // Pass the content type to determine which fields to show
          initialDetails={personalDetails} // Pass the stored personal details
        />
      )}

      {phase === 'vendorDetails' && (
        <VendorDetails
          onNext={handleVendorDetailsCompleted}
          onBack={() => setPhase('personalDetails')}
          onClose={() => {
            // Completely reset the state before closing
            resetUpload();
            handleClose();
          }}
          initialVendorDetails={vendorDetailsData} // Pass the stored vendor details
          videoCategory={(() => {
            const category = state.detailFields.video_category || 'my_wedding';
            console.log('UPLOAD MANAGER - Passing video category to VendorDetails:', category);
            return category;
          })()} // Pass the backend video category with debugging
        />
      )}

      {phase === 'faceVerification' && (
        <FaceVerification
          onUpload={handleFaceVerificationCompleted}
          onBack={() => {
            // New flow logic for back navigation:
            // - Moments: Go back to thumbnail selection (or type selection for images)
            // - Photos: Go back to personal details
            // - Videos: Go back to vendor details
            if (selectedType === 'moments' || state.mediaSubtype === 'story') {
              // For moments, go back to thumbnail selection for videos, or type selection for images
              if (state.mediaType === 'video') {
                setPhase('thumbnailSelection');
              } else {
                setPhase('typeSelection');
              }
            } else if (state.mediaType === 'photo') {
              setPhase('personalDetails');
            } else {
              setPhase('vendorDetails');
            }
          }}
          onClose={() => {
            // Completely reset the state before closing
            resetUpload();
            handleClose();
          }}
        />
      )}

      {(phase === 'uploading' || phase === 'complete') && (
        <UploadProgress
          onClose={() => {
            // Completely reset the state before closing
            resetUpload();
            handleClose();
          }}
          onGoBack={handleBackToPersonalDetails}
        />
      )}
    </>
  );
};

export default UploadManager;








