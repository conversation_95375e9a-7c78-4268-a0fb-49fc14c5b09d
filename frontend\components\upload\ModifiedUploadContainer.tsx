// components/upload/ModifiedUploadContainer.tsx
'use client';

import React, { useState } from 'react';
import { PlusCircle } from 'lucide-react';
import { useUpload } from '../../contexts/UploadContexts';
import UploadManager from './UploadManager';
import UploadContainer from './UploadContainer';

const ModifiedUploadContainer: React.FC = () => {
  const { state } = useUpload();
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);

  const handleCreateNew = () => {
    setIsUploadModalOpen(true);
  };

  // If the upload modal is open, render the UploadManager
  // Otherwise, render the normal container with a create new button
  return (
    <div className="p-6">
      {isUploadModalOpen ? (
        <UploadManager />
      ) : (
        <div>
          <button
            onClick={handleCreateNew}
            className="flex items-center justify-center px-4 py-2 bg-red-600 text-white rounded-md mb-6 hover:bg-red-700 transition duration-200"
          >
            <PlusCircle className="mr-2 h-5 w-5" />
            Create New
          </button>
          
          <UploadContainer />
        </div>
      )}
    </div>
  );
};

export default ModifiedUploadContainer;