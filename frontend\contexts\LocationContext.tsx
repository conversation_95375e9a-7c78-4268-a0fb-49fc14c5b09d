"use client";

import React, { createContext, useContext, useState, ReactNode } from 'react';

interface LocationContextType {
  selectedLocation: string;
  setSelectedLocation: (location: string) => void;
  reloadHomepage: () => void;
}

const LocationContext = createContext<LocationContextType | undefined>(undefined);

export const LocationProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [selectedLocation, setSelectedLocationState] = useState<string>('');
  const [reloadKey, setReloadKey] = useState<number>(0);

  const setSelectedLocation = (location: string) => {
    setSelectedLocationState(location);
    // Trigger homepage reload when location changes
    setReloadKey(prev => prev + 1);
  };

  const reloadHomepage = () => {
    setReloadKey(prev => prev + 1);
  };

  return (
    <LocationContext.Provider
      value={{
        selectedLocation,
        setSelectedLocation,
        reloadHomepage,
      }}
    >
      <div key={reloadKey}>
        {children}
      </div>
    </LocationContext.Provider>
  );
};

export const useLocation = (): LocationContextType => {
  const context = useContext(LocationContext);
  if (context === undefined) {
    throw new Error('useLocation must be used within a LocationProvider');
  }
  return context;
};
