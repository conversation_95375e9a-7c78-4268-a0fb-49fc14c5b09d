"use client";
import React from "react";
import Image from "next/image";
import UserAvatar from "../HomeDashboard/UserAvatar";
import { SearchResultItem } from "../../services/search-api";
import { useRouter } from "next/navigation";

interface SearchResultsDisplayProps {
  title: string;
  items: SearchResultItem[];
  contentType: 'flashes' | 'glimpses' | 'movies' | 'photos';
  loading?: boolean;
  hasNextPage?: boolean;
  searchParams?: any;
  isGridView?: boolean; // For category page grid layout
}

const SearchResultsDisplay: React.FC<SearchResultsDisplayProps> = ({
  title,
  items,
  contentType,
  loading = false,
  hasNextPage = false,
  searchParams = {},
  isGridView = false
}) => {
  const router = useRouter();

  // Function to navigate to a user's profile
  const navigateToUserProfile = (userId?: string, username?: string, event?: React.MouseEvent) => {
    if (event) {
      event.stopPropagation();
    }

    if (userId) {
      router.push(`/profile/${userId}`);
    } else if (username) {
      router.push(`/profile/${username}`);
    } else {
      console.warn('Cannot navigate to profile: missing both userId and username');
    }
  };

  // Get appropriate image source
  const getImageSource = (item: SearchResultItem): string => {
    if (contentType === 'photos') {
      return item.photo_url || item.content_url || '/pics/placeholder.svg';
    } else {
      // For videos (flashes, glimpses, movies)
      return item.video_thumbnail || item.thumbnail_url || '/pics/placeholder.svg';
    }
  };

  // Get content name
  const getContentName = (item: SearchResultItem): string => {
    return item.content_name || item.video_name || item.photo_name || 'Untitled';
  };

  // Get content ID for navigation
  const getContentId = (item: SearchResultItem): string => {
    return item.content_id || item.video_id || item.photo_id || '';
  };

  // Handle item click navigation
  const handleItemClick = (item: SearchResultItem) => {
    const contentId = getContentId(item);
    if (!contentId) return;

    switch (contentType) {
      case 'photos':
        router.push(`/home/<USER>/${contentId}`);
        break;
      case 'flashes':
        router.push(`/home/<USER>/shorts?index=0&id=${contentId}`);
        break;
      case 'glimpses':
        router.push(`/home/<USER>/${contentId}`);
        break;
      case 'movies':
        router.push(`/home/<USER>/${contentId}`);
        break;
      default:
        console.warn('Unknown content type:', contentType);
    }
  };

  if (loading) {
    return (
      <section className="mb-8">
        <h2 className="font-inter text-[20px] leading-[18px] font-semibold text-black mb-4">
          {title}
        </h2>
        <div className="py-10 text-center">
          <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
          <span className="text-gray-600">Loading {title.toLowerCase()}...</span>
        </div>
      </section>
    );
  }

  if (items.length === 0) {
    return null; // Don't render empty sections
  }

  return (
    <section className="mb-8">
      {title && (
        <div className="flex items-center justify-between mb-4">
          <h2 className="font-inter text-[20px] leading-[18px] font-semibold text-black">
            {title}
          </h2>
          {hasNextPage && (
            <button
              onClick={() => {
                const params = new URLSearchParams();
                if (searchParams.q) params.set('q', searchParams.q);
                if (searchParams.username) params.set('username', searchParams.username);
                if (searchParams.tags) params.set('tags', searchParams.tags);
                if (searchParams.place) params.set('place', searchParams.place);
                if (searchParams.sort_by) params.set('sort_by', searchParams.sort_by);
                if (searchParams.sort_order) params.set('sort_order', searchParams.sort_order);
                params.set('types', contentType);
                router.push(`/api/search/category?${params.toString()}`);
              }}
              className="text-red-600 hover:text-red-700 font-medium text-sm transition-colors"
            >
              Show More →
            </button>
          )}
        </div>
      )}

      {/* Container - either horizontal scroll or grid */}
      <div className={isGridView ? "" : "overflow-x-auto scrollbar-hide"}>
        <div className={isGridView
          ? `grid ${
              contentType === 'photos'
                ? 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4'
                : contentType === 'movies'
                ? 'grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 md:gap-8'
                : contentType === 'glimpses'
                ? 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6'
                : 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4'
            }`
          : "flex gap-3 pb-4 flex-nowrap"
        }>
          {items.map((item, index) => {
            const contentId = getContentId(item);
            const contentName = getContentName(item);
            const imageSource = getImageSource(item);

            if (contentType === 'photos') {
              // Photos display (rectangular cards)
              return (
                <div
                  key={`${contentId}-${index}`}
                  className={`rounded-lg overflow-hidden shadow-md relative hover:scale-105 transition-transform cursor-pointer ${
                    isGridView ? 'w-full aspect-[4/3]' : 'flex-shrink-0'
                  }`}
                  style={!isGridView ? { width: '250px', height: '200px' } : {}}
                  onClick={() => handleItemClick(item)}
                >
                  {/* User avatar */}
                  <div
                    className="absolute top-2 left-2 z-10"
                    onClick={(e) => navigateToUserProfile(item.user_id, item.user_name, e)}
                  >
                    <UserAvatar
                      username={item.user_name || "user"}
                      size="sm"
                      isGradientBorder={true}
                    />
                  </div>

                  {/* Photo */}
                  <div className="relative w-full h-full">
                    <Image
                      src={imageSource}
                      alt={contentName}
                      fill
                      sizes="250px"
                      className="object-cover"
                      priority={index < 4}
                      placeholder="blur"
                      blurDataURL="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImciPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iMjAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNmZmYiIG9mZnNldD0iNTAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iNzAlIiAvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHdpZHRoPSI3MDAiIGhlaWdodD0iNDc1IiBmaWxsPSIjZWVlIiAvPjxyZWN0IGlkPSJyIiB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgZmlsbD0idXJsKCNnKSIgLz48YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTcwMCIgdG89IjcwMCIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPjwvc3ZnPg=="
                      onError={(e) => {
                        const imgElement = e.target as HTMLImageElement;
                        if (imgElement) {
                          imgElement.src = '/pics/placeholder.svg';
                        }
                      }}
                    />
                  </div>

                  {/* Photo info */}
                  <div className="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/80 to-transparent text-white">
                    <div className="text-sm font-medium truncate">{contentName}</div>
                    <div className="text-xs flex justify-between">
                      <span
                        className="cursor-pointer hover:underline"
                        onClick={(e) => navigateToUserProfile(item.user_id, item.user_name, e)}
                      >
                        {item.user_name}
                      </span>
                      <span>
                        {item.photo_likes ? `${item.photo_likes} likes` : ''}
                      </span>
                    </div>
                  </div>
                </div>
              );
            } else {
              // Video content display (flashes, glimpses, movies)
              const isFlash = contentType === 'flashes';
              const isMovie = contentType === 'movies';
              const isGlimpse = contentType === 'glimpses';

              if (isMovie && isGridView) {
                // Movies use a special layout similar to Instagram posts
                return (
                  <div
                    key={`${contentId}-${index}`}
                    className="flex-shrink-0 border-b pb-8 border-gray-200"
                    onClick={() => handleItemClick(item)}
                  >
                    {/* User Info */}
                    <div className="flex justify-between items-center p-2 mb-4">
                      <div className="flex items-center gap-3">
                        <UserAvatar
                          username={item.user_name || "user"}
                          size="sm"
                        />
                        <span className="font-semibold">
                          {item.user_name || "user"}
                        </span>
                      </div>
                      <button>•••</button>
                    </div>

                    {/* Video */}
                    <div className="relative w-full aspect-video rounded-lg overflow-hidden border border-gray-200 mb-4">
                      <Image
                        src={imageSource}
                        alt={contentName}
                        fill
                        sizes="(max-width: 768px) 100vw, 800px"
                        className="object-cover"
                        priority={index < 2}
                        unoptimized={true}
                        onError={(e) => {
                          const imgElement = e.target as HTMLImageElement;
                          if (imgElement) {
                            imgElement.src = '/pics/placeholder.svg';
                          }
                        }}
                      />

                      {/* Play Button Overlay */}
                      <div className="absolute inset-0 flex items-center justify-center bg-black/20 hover:bg-black/30 transition-colors cursor-pointer">
                        <div className="w-14 h-14 rounded-full bg-red-500 bg-opacity-80 flex items-center justify-center">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="28"
                            height="28"
                            viewBox="0 0 24 24"
                            fill="white"
                          >
                            <path d="M8 5v14l11-7z" />
                          </svg>
                        </div>
                      </div>
                    </div>

                    {/* Interactions */}
                    <div className="flex flex-col gap-3">
                      <div className="flex gap-4 text-sm text-gray-600">
                        <span>{item.video_likes ? `${(item.video_likes / 1000).toFixed(1)}K` : '0'} likes</span>
                        <span>{item.video_views ? `${(item.video_views / 1000).toFixed(1)}K` : '0'} views</span>
                      </div>
                      <div className="text-sm">
                        <span>{item.video_description || contentName}</span>
                      </div>
                    </div>
                  </div>
                );
              } else if (isGlimpse && isGridView) {
                // Glimpses use a special layout similar to home/glimpses
                return (
                  <div
                    key={`${contentId}-${index}`}
                    className="rounded-[10px] overflow-hidden shadow-md relative hover:scale-105 transition-transform cursor-pointer border border-[#4D0C0D]"
                    style={{
                      height: "220px",
                      background: "linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.8) 100.01%)",
                    }}
                    onClick={() => handleItemClick(item)}
                  >
                    {/* User info with avatar and name */}
                    <div className="absolute top-4 left-4 flex items-center space-x-2 z-10">
                      <UserAvatar
                        username={item.user_name || "user"}
                        size="sm"
                        isGradientBorder={true}
                      />
                      <span className="text-white text-sm font-medium drop-shadow-md">
                        {item.user_name || "user"}
                      </span>
                    </div>

                    {/* Video Thumbnail */}
                    <div className="h-full relative">
                      <div
                        style={{
                          position: "absolute",
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          zIndex: 1,
                        }}
                      >
                        <div className="relative w-full h-full rounded-[4px] overflow-hidden">
                          <Image
                            src={imageSource}
                            alt={contentName}
                            fill
                            sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                            className="object-cover"
                            priority={index < 6}
                            unoptimized={true}
                            onError={(e) => {
                              const imgElement = e.target as HTMLImageElement;
                              if (imgElement) {
                                imgElement.src = '/pics/placeholder.svg';
                              }
                            }}
                          />
                        </div>
                      </div>

                      {/* Play Button Overlay */}
                      <div
                        style={{
                          position: "absolute",
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          zIndex: 2,
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          cursor: "pointer",
                          backgroundColor: "rgba(0, 0, 0, 0.2)",
                        }}
                      >
                        <div className="w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 rounded-full bg-red-500 bg-opacity-80 flex items-center justify-center">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="28"
                            height="28"
                            viewBox="0 0 24 24"
                            fill="white"
                          >
                            <path d="M8 5v14l11-7z" />
                          </svg>
                        </div>
                      </div>
                    </div>

                    {/* Content info */}
                    <div className="absolute bottom-0 left-0 right-0 p-2 sm:p-3 md:p-4 bg-gradient-to-t from-black/80 to-transparent text-white">
                      <div className="font-medium text-xs sm:text-sm md:text-base line-clamp-1">{contentName}</div>
                      <div className="text-[10px] sm:text-xs">
                        {item.video_views ? `${(item.video_views / 1000).toFixed(1)}K views` : ''}
                        {item.video_likes ? ` • ${(item.video_likes / 1000).toFixed(1)}K likes` : ''}
                      </div>
                    </div>
                  </div>
                );
              } else {
                // Flashes use card layout
                return (
                  <div
                    key={`${contentId}-${index}`}
                    className={`overflow-hidden shadow-md relative hover:scale-105 transition-transform cursor-pointer ${
                      isGridView
                        ? 'w-full h-[308px] rounded-[10px] border border-[#B31B1E] p-[10px]'
                        : isMovie
                        ? 'flex-shrink-0 w-[85vw] sm:w-[75vw] md:w-[65vw] lg:w-[55vw] h-[180px] sm:h-[250px] md:h-[300px] lg:h-[350px] max-w-[676px] rounded-[10px] border border-[#4D0C0D]'
                        : isFlash
                        ? 'flex-shrink-0 w-[40vw] sm:w-[30vw] md:w-[22vw] lg:w-[18vw] h-[220px] sm:h-[280px] md:h-[308px] max-w-[200px] rounded-[10px] border border-[#B31B1E]'
                        : 'flex-shrink-0 w-[80vw] sm:w-[40vw] md:w-[30vw] lg:w-[22vw] h-[160px] sm:h-[180px] md:h-[200px] lg:h-[220px] max-w-[365px] rounded-[10px] border border-[#4D0C0D]'
                    }`}
                    style={{
                      background: "linear-gradient(180deg, rgba(113, 113, 113, 0) 71.27%, #393939 100%)",
                    }}
                    onClick={() => handleItemClick(item)}
                  >
                    {/* User info */}
                    <div className={`absolute ${isFlash && isGridView ? 'top-[10px] left-[10px]' : 'top-4 left-4'} z-10`}>
                      <UserAvatar
                        username={item.user_name || "user"}
                        size="sm"
                        isGradientBorder={true}
                      />
                    </div>

                    {/* Video thumbnail */}
                    <div className="absolute top-0 left-0 right-0 bottom-0 z-[1]">
                      <div className="relative w-full h-full">
                        <Image
                          src={imageSource}
                          alt={contentName}
                          fill
                          sizes="(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 16vw"
                          className="object-cover"
                          priority={index < 6}
                          unoptimized={true}
                          onError={(e) => {
                            const imgElement = e.target as HTMLImageElement;
                            if (imgElement) {
                              imgElement.src = '/pics/placeholder.svg';
                            }
                          }}
                        />
                      </div>
                    </div>

                    {/* Content info */}
                    <div className={`absolute ${isFlash && isGridView ? 'bottom-[10px] left-[10px] right-[10px]' : 'bottom-0 left-0 right-0 p-3'} z-[2] text-white`}>
                      <div className="text-sm font-medium truncate">{contentName}</div>
                      <div className="text-xs">
                        {item.video_views ? `${(item.video_views / 1000).toFixed(1)}K views` : ''}
                        {item.video_likes ? ` • ${(item.video_likes / 1000).toFixed(1)}K likes` : ''}
                      </div>
                    </div>
                  </div>
                );
              }
            }
          })}
        </div>
      </div>

      <style jsx>{`
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
      `}</style>
    </section>
  );
};

export default SearchResultsDisplay;
