"use client";
import React, { useState } from "react";
import { X, Search } from "lucide-react";
import { useRouter } from "next/navigation";

interface AdvancedSearchDropdownProps {
  isOpen: boolean;
  onClose: () => void;
  initialQuery?: string;
}

const AdvancedSearchDropdown: React.FC<AdvancedSearchDropdownProps> = ({
  isOpen,
  onClose,
  initialQuery = ""
}) => {
  const router = useRouter();
  const [formData, setFormData] = useState({
    q: initialQuery,
    username: "",
    tags: "",
    place: "",
    types: [] as string[],
    sort_by: "created_at",
    sort_order: "desc" as "asc" | "desc"
  });

  const contentTypes = [
    { id: 'flashes', label: 'Flashes', description: 'Short video clips' },
    { id: 'glimpses', label: 'Glimpses', description: 'Quick video previews' },
    { id: 'movies', label: 'Movies', description: 'Full wedding videos' },
    { id: 'photos', label: 'Photos', description: 'Wedding photographs' }
  ];

  const sortOptions = [
    { value: 'created_at', label: 'Date Created' },
    { value: 'views', label: 'Views' },
    { value: 'likes', label: 'Likes' }
  ];

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleTypeToggle = (typeId: string) => {
    setFormData(prev => ({
      ...prev,
      types: prev.types.includes(typeId)
        ? prev.types.filter(t => t !== typeId)
        : [...prev.types, typeId]
    }));
  };

  const handleSearch = () => {
    const params = new URLSearchParams();
    
    if (formData.q.trim()) params.set('q', formData.q.trim());
    if (formData.username.trim()) params.set('username', formData.username.trim());
    if (formData.tags.trim()) params.set('tags', formData.tags.trim());
    if (formData.place.trim()) params.set('place', formData.place.trim());
    if (formData.types.length > 0) params.set('types', formData.types.join(','));
    if (formData.sort_by !== 'created_at') params.set('sort_by', formData.sort_by);
    if (formData.sort_order !== 'desc') params.set('sort_order', formData.sort_order);
    
    router.push(`/api/search?${params.toString()}`);
    onClose();
  };

  const clearForm = () => {
    setFormData({
      q: "",
      username: "",
      tags: "",
      place: "",
      types: [],
      sort_by: "created_at",
      sort_order: "desc"
    });
  };

  if (!isOpen) return null;

  return (
    <div className="absolute top-full left-0 right-0 mt-2 bg-white rounded-lg shadow-xl border border-gray-200 z-50 max-w-4xl mx-auto transform transition-all duration-200 ease-out opacity-100 scale-100">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-800">Advanced Search</h3>
        <button
          onClick={onClose}
          className="p-1 rounded-full hover:bg-gray-100 transition-colors"
        >
          <X size={18} className="text-gray-500" />
        </button>
      </div>

      {/* Content */}
      <div className="p-4 max-h-96 overflow-y-auto">
        <div className="space-y-4">
          {/* Search Fields Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* General Search */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                General Search
              </label>
              <input
                type="text"
                value={formData.q}
                onChange={(e) => handleInputChange('q', e.target.value)}
                placeholder="Search for anything..."
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-sm"
              />
            </div>

            {/* Username Search */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Search by Username
              </label>
              <input
                type="text"
                value={formData.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                placeholder="Enter username..."
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-sm"
              />
            </div>

            {/* Tags Search */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Search by Tags
              </label>
              <input
                type="text"
                value={formData.tags}
                onChange={(e) => handleInputChange('tags', e.target.value)}
                placeholder="e.g., wedding, ceremony..."
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-sm"
              />
            </div>

            {/* Place Search */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Search by Place
              </label>
              <input
                type="text"
                value={formData.place}
                onChange={(e) => handleInputChange('place', e.target.value)}
                placeholder="e.g., Mumbai, Delhi..."
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-sm"
              />
            </div>
          </div>

          {/* Content Types */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Content Types
            </label>
            <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-4 gap-2">
              {contentTypes.map((type) => (
                <label
                  key={type.id}
                  className="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 p-2 rounded border border-gray-200"
                >
                  <input
                    type="checkbox"
                    checked={formData.types.includes(type.id)}
                    onChange={() => handleTypeToggle(type.id)}
                    className="w-4 h-4 text-red-600 border-gray-300 rounded focus:ring-red-500"
                  />
                  <div>
                    <div className="text-sm font-medium text-gray-900">{type.label}</div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* Sort Options */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Sort By
              </label>
              <select
                value={formData.sort_by}
                onChange={(e) => handleInputChange('sort_by', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-sm"
              >
                {sortOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Sort Order
              </label>
              <select
                value={formData.sort_order}
                onChange={(e) => handleInputChange('sort_order', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-sm"
              >
                <option value="desc">Newest First</option>
                <option value="asc">Oldest First</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200 flex space-x-3">
        <button
          onClick={clearForm}
          className="flex-1 px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
        >
          Clear All
        </button>
        <button
          onClick={handleSearch}
          className="flex-1 px-3 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 transition-colors flex items-center justify-center space-x-2"
        >
          <Search size={16} />
          <span>Search</span>
        </button>
      </div>
    </div>
  );
};

export default AdvancedSearchDropdown;
