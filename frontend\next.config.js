/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: false, // Disabled to prevent duplicate API calls
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    // Add this to ignore TypeScript errors during build
    ignoreBuildErrors: true,
  },
  images: {
    domains: [
      'upload-wedzat-1.s3.amazonaws.com',
      'wedzat-uploads.s3.amazonaws.com',
      'd32sv0f8c41dk.cloudfront.net',  // Your specific CloudFront domain
      'd32sx0if8c41dk.cloudfront.net', // Additional CloudFront domain from API
      'cloudfront.net',
      'images.unsplash.com',  // Added for unsplash images
      'img.youtube.com'       // For YouTube thumbnails
    ],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**.cloudfront.net',
        port: '',
        pathname: '/**',
      },
    ],
    unoptimized: true, // Disable image optimization for external images
    minimumCacheTTL: 60 * 60 * 24 * 30, // 30 days
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
  serverRuntimeConfig: {
    // Will only be available on the server side
  },
  publicRuntimeConfig: {
    // Will be available on both server and client
    apiResponseLimit: '5gb', // Increased from 50mb to 5gb
  },
  // Add CORS headers to all API responses
  async headers() {
    // Disable caching in development
    const cacheControl = process.env.NODE_ENV === 'development' 
      ? 'no-store, no-cache, must-revalidate, proxy-revalidate'
      : 'public, max-age=31536000, immutable';

    return [
      {
        source: '/api/:path*',
        headers: [
          { key: 'Access-Control-Allow-Credentials', value: 'true' },
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET,OPTIONS,PATCH,DELETE,POST,PUT' },
          { key: 'Access-Control-Allow-Headers', value: 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Content-Disposition' },
          { key: 'Cache-Control', value: process.env.NODE_ENV === 'development' ? 'no-store' : 'public, max-age=60, stale-while-revalidate=120' },
        ],
      },
      {
        source: '/_next/static/:static*',
        headers: [
          { key: 'Cache-Control', value: cacheControl },
        ],
      },
      {
        source: '/public/:all*',
        headers: [
          { key: 'Cache-Control', value: cacheControl },
        ],
      },
    ];
  },
}

module.exports = nextConfig