"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./contexts/UploadContexts.tsx":
/*!*************************************!*\
  !*** ./contexts/UploadContexts.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UploadProvider: () => (/* binding */ UploadProvider),\n/* harmony export */   useUpload: () => (/* binding */ useUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/api */ \"(app-pages-browser)/./services/api.ts\");\n/* harmony import */ var _utils_uploadUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/uploadUtils */ \"(app-pages-browser)/./utils/uploadUtils.ts\");\n// contexts/UploadContext.tsx\n/* __next_internal_client_entry_do_not_use__ UploadProvider,useUpload auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Initial state\nconst initialState = {\n    file: null,\n    thumbnail: null,\n    mediaType: 'photo',\n    mediaSubtype: 'story',\n    category: '',\n    title: '',\n    description: '',\n    tags: [],\n    detailFields: {},\n    step: 'selecting',\n    progress: 0,\n    isUploading: false\n};\n// Create context\nconst UploadContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Provider component\nconst UploadProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialState);\n    // Set file and automatically determine media type\n    const setFile = async (file)=>{\n        if (!file) {\n            setState({\n                ...state,\n                file: null\n            });\n            return;\n        }\n        const isVideo = file.type.startsWith('video/');\n        const mediaType = isVideo ? 'video' : 'photo';\n        // Default media subtypes based on media type\n        const mediaSubtype = isVideo ? 'glimpse' : 'post'; // Default to 'post' for photos\n        setState({\n            ...state,\n            file,\n            mediaType,\n            mediaSubtype,\n            step: 'details'\n        });\n    };\n    // Set thumbnail image\n    const setThumbnail = (thumbnail)=>{\n        setState({\n            ...state,\n            thumbnail\n        });\n    };\n    const setMediaType = (type)=>{\n        // Don't set a default category - let the user's selection flow through the process\n        // Just update the media type\n        setState({\n            ...state,\n            mediaType: type\n        });\n    };\n    const setMediaSubtype = (mediaSubtype)=>{\n        setState({\n            ...state,\n            mediaSubtype\n        });\n    };\n    // Keep the old function for backward compatibility\n    const setCategory = (category)=>{\n        console.log('UPLOAD CONTEXT - setCategory is deprecated, use setMediaSubtype instead');\n        setState({\n            ...state,\n            mediaSubtype: category\n        });\n    };\n    const setTitle = (title)=>{\n        // Ensure title is not empty\n        if (!title || !title.trim()) {\n            console.warn('Attempted to set empty title');\n            return;\n        }\n        console.log('Setting title to:', title.trim());\n        setState({\n            ...state,\n            title: title.trim()\n        });\n    };\n    const setDescription = (description)=>{\n        setState({\n            ...state,\n            description\n        });\n    };\n    const addTag = (tag)=>{\n        if (tag.trim() && !state.tags.includes(tag.trim())) {\n            setState({\n                ...state,\n                tags: [\n                    ...state.tags,\n                    tag.trim()\n                ]\n            });\n        }\n    };\n    const removeTag = (tag)=>{\n        setState({\n            ...state,\n            tags: state.tags.filter((t)=>t !== tag)\n        });\n    };\n    const setDetailField = (field, value)=>{\n        // For moments (stories), skip all localStorage operations and vendor field handling\n        if (state.mediaSubtype === 'story') {\n            console.log(\"UPLOAD CONTEXT - Moments detected, setting field \".concat(field, \" without localStorage operations\"));\n            setState((prevState)=>({\n                    ...prevState,\n                    detailFields: {\n                        ...prevState.detailFields,\n                        [field]: value\n                    }\n                }));\n            return;\n        }\n        // Special handling for video_category to ensure it's properly set\n        if (field === 'video_category') {\n            console.log(\"UPLOAD CONTEXT - Setting video_category to: \".concat(value));\n            // Store video_category in localStorage\n            try {\n                localStorage.setItem('wedzat_video_category', value);\n                console.log(\"UPLOAD CONTEXT - Stored video_category in localStorage: \".concat(value));\n            } catch (error) {\n                console.error('UPLOAD CONTEXT - Failed to store video_category in localStorage:', error);\n            }\n        }\n        // Special handling for vendor fields to ensure they're properly set\n        if (field.startsWith('vendor_')) {\n            // If this is a vendor field, update the vendor details in localStorage\n            try {\n                // Get existing vendor details from localStorage\n                const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n                let vendorDetails = storedVendorDetails ? JSON.parse(storedVendorDetails) : {};\n                // If this is a vendor name field, extract the vendor type and update the name\n                if (field.endsWith('_name')) {\n                    const vendorType = field.replace('vendor_', '').replace('_name', '');\n                    // Check if we already have this vendor in the details\n                    if (!vendorDetails[vendorType]) {\n                        vendorDetails[vendorType] = {\n                            name: value,\n                            mobileNumber: ''\n                        };\n                    } else {\n                        vendorDetails[vendorType].name = value;\n                    }\n                }\n                // If this is a vendor contact field, extract the vendor type and update the contact\n                if (field.endsWith('_contact')) {\n                    const vendorType = field.replace('vendor_', '').replace('_contact', '');\n                    // Check if we already have this vendor in the details\n                    if (!vendorDetails[vendorType]) {\n                        vendorDetails[vendorType] = {\n                            name: '',\n                            mobileNumber: value\n                        };\n                    } else {\n                        vendorDetails[vendorType].mobileNumber = value;\n                    }\n                }\n                // Store the updated vendor details in localStorage\n                localStorage.setItem('wedzat_vendor_details', JSON.stringify(vendorDetails));\n            } catch (error) {\n                console.error('UPLOAD CONTEXT - Failed to update vendor details in localStorage:', error);\n            }\n        }\n        // Create a new detailFields object with the updated field\n        const updatedDetailFields = {\n            ...state.detailFields,\n            [field]: value\n        };\n        // Update the state with the new detailFields\n        setState((prevState)=>({\n                ...prevState,\n                detailFields: updatedDetailFields\n            }));\n        // For video_category, log the updated state after a short delay\n        if (field === 'video_category') {\n            setTimeout(()=>{\n                console.log(\"UPLOAD CONTEXT - Verified video_category is set to: \".concat(state.detailFields.video_category || 'Not set'));\n            }, 100);\n        }\n    };\n    // Set all personal details at once and update the title and related detail fields\n    const setPersonalDetails = (details)=>{\n        // console.log('Setting all personal details:', details);\n        // Validate caption/title\n        if (!details.caption || !details.caption.trim()) {\n            console.warn('Attempted to set personal details with empty caption/title');\n            return;\n        }\n        // Update title\n        const title = details.caption.trim();\n        // console.log('Setting title from personal details:', title);\n        // Update detail fields with backend-compatible field names\n        const updatedDetailFields = {\n            ...state.detailFields,\n            'personal_caption': title,\n            'personal_life_partner': details.lifePartner || '',\n            'personal_wedding_style': details.weddingStyle || '',\n            'personal_place': details.place || '',\n            'personal_event_type': details.eventType || '',\n            'personal_budget': details.budget || '',\n            // Keep legacy field names for compatibility\n            'lifePartner': details.lifePartner || '',\n            'location': details.place || '',\n            'place': details.place || '',\n            'eventType': details.eventType || '',\n            'budget': details.budget || '',\n            'weddingStyle': details.weddingStyle || ''\n        };\n        // Update state with all changes at once\n        setState({\n            ...state,\n            title,\n            description: details.weddingStyle || '',\n            detailFields: updatedDetailFields\n        });\n        // Log the description being set\n        console.log('Setting description to:', details.weddingStyle || '');\n        // Log the updated state after a short delay to ensure state has updated\n        setTimeout(()=>{\n            console.log('Personal details set successfully');\n            console.log('Title after update:', title);\n        }, 0);\n    };\n    // Set all vendor details at once and update the related detail fields\n    const setVendorDetails = (vendorDetails)=>{\n        console.log('UPLOAD CONTEXT - Setting all vendor details:', JSON.stringify(vendorDetails, null, 2));\n        // Create a copy of the current detail fields\n        const updatedDetailFields = {\n            ...state.detailFields\n        };\n        // Save the video_category if it exists\n        const videoCategory = state.detailFields.video_category;\n        console.log('UPLOAD CONTEXT - Preserving video_category:', videoCategory);\n        // Count how many complete vendor details we're receiving\n        const completeVendorCount = Object.entries(vendorDetails).filter((param)=>{\n            let [_, detail] = param;\n            return detail && detail.name && detail.mobileNumber && detail.name.trim() !== '' && detail.mobileNumber.trim() !== '';\n        }).length;\n        console.log(\"UPLOAD CONTEXT - Received \".concat(completeVendorCount, \" complete vendor details\"));\n        // Process vendor details\n        Object.entries(vendorDetails).forEach((param)=>{\n            let [vendorType, details] = param;\n            if (details) {\n                // Only include vendors that have BOTH name AND mobile number\n                if (details.name && details.mobileNumber && details.name.trim() !== '' && details.mobileNumber.trim() !== '') {\n                    // Handle special mappings for makeup_artist and decoration\n                    let backendVendorType = vendorType;\n                    // Map frontend field names to backend field names\n                    if (vendorType === 'makeupArtist') {\n                        backendVendorType = 'makeup_artist';\n                    } else if (vendorType === 'decorations') {\n                        backendVendorType = 'decoration';\n                    }\n                    // Store vendor details in the format expected by the backend\n                    updatedDetailFields[\"vendor_\".concat(backendVendorType, \"_name\")] = details.name || '';\n                    updatedDetailFields[\"vendor_\".concat(backendVendorType, \"_contact\")] = details.mobileNumber || '';\n                    // Always store with the original vendorType to ensure we count it correctly\n                    // This ensures both frontend and backend field names are present\n                    // This is especially important for Edge browser compatibility\n                    if (vendorType !== backendVendorType) {\n                        updatedDetailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name || '';\n                        updatedDetailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber || '';\n                    }\n                    // Also store with common vendor types to ensure cross-browser compatibility\n                    if (vendorType === 'makeupArtist' || vendorType === 'makeup_artist') {\n                        // Ensure both makeupArtist and makeup_artist are present\n                        updatedDetailFields[\"vendor_makeupArtist_name\"] = details.name || '';\n                        updatedDetailFields[\"vendor_makeupArtist_contact\"] = details.mobileNumber || '';\n                        updatedDetailFields[\"vendor_makeup_artist_name\"] = details.name || '';\n                        updatedDetailFields[\"vendor_makeup_artist_contact\"] = details.mobileNumber || '';\n                    } else if (vendorType === 'decorations' || vendorType === 'decoration') {\n                        // Ensure both decorations and decoration are present\n                        updatedDetailFields[\"vendor_decorations_name\"] = details.name || '';\n                        updatedDetailFields[\"vendor_decorations_contact\"] = details.mobileNumber || '';\n                        updatedDetailFields[\"vendor_decoration_name\"] = details.name || '';\n                        updatedDetailFields[\"vendor_decoration_contact\"] = details.mobileNumber || '';\n                    }\n                // console.log(`UPLOAD CONTEXT - Setting vendor detail: ${vendorType} (backend: ${backendVendorType})`, {\n                //   name: details.name || '',\n                //   contact: details.mobileNumber || ''\n                // });\n                } else {\n                    console.log(\"UPLOAD CONTEXT - Skipping incomplete vendor detail: \".concat(vendorType));\n                }\n            }\n        });\n        // Don't update state here - we'll do it after restoring the video_category\n        // console.log('UPLOAD CONTEXT - Vendor details set successfully');\n        // console.log('UPLOAD CONTEXT - Total detail fields count:', Object.keys(updatedDetailFields).length);\n        // Count how many complete vendor details we have after processing\n        let completeVendorPairs = 0;\n        const vendorNames = new Set();\n        const vendorContacts = new Set();\n        // Log all vendor details for debugging\n        Object.keys(updatedDetailFields).forEach((key)=>{\n            if (key.startsWith('vendor_')) {\n                // console.log(`UPLOAD CONTEXT - Vendor field: ${key} = ${updatedDetailFields[key]}`);\n                if (key.endsWith('_name')) {\n                    vendorNames.add(key.replace('vendor_', '').replace('_name', ''));\n                } else if (key.endsWith('_contact')) {\n                    vendorContacts.add(key.replace('vendor_', '').replace('_contact', ''));\n                }\n            }\n        });\n        // Count complete pairs (both name and contact)\n        vendorNames.forEach((name)=>{\n            if (vendorContacts.has(name)) {\n                completeVendorPairs++;\n            }\n        });\n        // console.log(`UPLOAD CONTEXT - Found ${completeVendorPairs} complete vendor pairs (name + contact)`);\n        // console.log(`UPLOAD CONTEXT - Vendor names: ${Array.from(vendorNames).join(', ')}`);\n        // console.log(`UPLOAD CONTEXT - Vendor contacts: ${Array.from(vendorContacts).join(', ')}`);\n        // Restore the video_category if it exists\n        if (videoCategory) {\n            console.log('UPLOAD CONTEXT - Restoring video_category:', videoCategory);\n            updatedDetailFields.video_category = videoCategory;\n        }\n        // Log the detail fields before updating state\n        console.log('UPLOAD CONTEXT - Detail fields before update:', JSON.stringify(state.detailFields, null, 2));\n        console.log('UPLOAD CONTEXT - Updated detail fields to be set:', JSON.stringify(updatedDetailFields, null, 2));\n        // Create a completely new state object to ensure Edge updates correctly\n        const newState = {\n            ...state,\n            detailFields: {\n                ...updatedDetailFields\n            }\n        };\n        // For Edge browser compatibility, directly set the vendor fields in the state\n        // This is a workaround for Edge where the state update doesn't properly preserve vendor details\n        if ( true && /Edge|Edg/.test(window.navigator.userAgent)) {\n            console.log('UPLOAD CONTEXT - Edge browser detected, using direct state update');\n            // Create a direct reference to the state object\n            const directState = state;\n            // Directly set the detailFields\n            directState.detailFields = {\n                ...updatedDetailFields\n            };\n            // Log the direct update\n            console.log('UPLOAD CONTEXT - Direct state update:', JSON.stringify(directState.detailFields, null, 2));\n        }\n        // Update the state with the updated detail fields\n        setState(newState);\n        // Force a re-render to ensure the state is updated\n        setTimeout(()=>{\n            console.log('UPLOAD CONTEXT - Vendor details set successfully');\n            console.log('UPLOAD CONTEXT - Total detail fields count:', Object.keys(state.detailFields).length);\n            console.log('UPLOAD CONTEXT - Detail fields after update:', JSON.stringify(state.detailFields, null, 2));\n            // Double-check that the vendor details were set correctly\n            const vendorNameFields = Object.keys(state.detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_name'));\n            const vendorContactFields = Object.keys(state.detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_contact'));\n            console.log('UPLOAD CONTEXT - Vendor name fields count:', vendorNameFields.length);\n            console.log('UPLOAD CONTEXT - Vendor contact fields count:', vendorContactFields.length);\n        }, 100);\n    };\n    const resetUpload = ()=>{\n        console.log('UPLOAD CONTEXT - Completely resetting upload state');\n        // Create a fresh copy of the initial state\n        const freshState = {\n            file: null,\n            thumbnail: null,\n            mediaType: '',\n            mediaSubtype: '',\n            title: '',\n            description: '',\n            tags: [],\n            detailFields: {},\n            step: 'select',\n            duration: 0\n        };\n        // Set the state to the fresh state\n        setState(freshState);\n        // Log the reset\n        console.log('UPLOAD CONTEXT - Upload state reset to:', JSON.stringify(freshState, null, 2));\n    };\n    // Helper function to detect Edge browser\n    const isEdgeBrowser = ()=>{\n        if (true) {\n            return /Edge|Edg/.test(window.navigator.userAgent);\n        }\n        return false;\n    };\n    const validateForm = ()=>{\n        // For moments (stories), only validate file and title - skip all other validations\n        if (state.mediaSubtype === 'story') {\n            console.log('VALIDATE FORM - Moments/Stories detected, using simplified validation');\n            // Check if file is selected\n            if (!state.file) {\n                console.log('Validation failed: No file selected for moments');\n                return {\n                    isValid: false,\n                    error: 'Please select a file to upload'\n                };\n            }\n            // Validate file type and size\n            const fileValidation = (0,_utils_uploadUtils__WEBPACK_IMPORTED_MODULE_3__.validateFile)(state.file, state.mediaType);\n            if (!fileValidation.isValid) {\n                console.log('Validation failed: File validation failed for moments', fileValidation);\n                return fileValidation;\n            }\n            // Check if title is provided\n            if (!state.title || !state.title.trim()) {\n                console.log('Validation failed: Title is empty for moments');\n                return {\n                    isValid: false,\n                    error: 'Please provide a title for your upload'\n                };\n            }\n            console.log('VALIDATE FORM - Moments validation passed');\n            return {\n                isValid: true\n            };\n        }\n        // Check if we're running in Edge browser\n        const isEdge = isEdgeBrowser();\n        if (isEdge) {\n            console.log('VALIDATE FORM - Running in Edge browser, applying special handling');\n        }\n        // console.log('VALIDATE FORM - Validating form with state:', {\n        //   file: state.file ? state.file.name : 'No file',\n        //   mediaType: state.mediaType,\n        //   title: state.title,\n        //   description: state.description,\n        //   detailFieldsCount: Object.keys(state.detailFields).length,\n        //   tags: state.tags\n        // });\n        // Log all detail fields for debugging\n        // console.log('VALIDATE FORM - All detail fields:', JSON.stringify(state.detailFields, null, 2));\n        // Check if file is selected\n        if (!state.file) {\n            // console.log('Validation failed: No file selected');\n            return {\n                isValid: false,\n                error: 'Please select a file to upload'\n            };\n        }\n        // Validate file type and size\n        const fileValidation = (0,_utils_uploadUtils__WEBPACK_IMPORTED_MODULE_3__.validateFile)(state.file, state.mediaType);\n        if (!fileValidation.isValid) {\n            console.log('Validation failed: File validation failed', fileValidation);\n            return fileValidation;\n        }\n        // Check if title is provided\n        if (!state.title || !state.title.trim()) {\n            console.log('Validation failed: Title is empty');\n            return {\n                isValid: false,\n                error: 'Please provide a title for your upload'\n            };\n        }\n        // First, try to get vendor details from localStorage\n        let detailFields = {\n            ...state.detailFields\n        };\n        let vendorDetailsFromStorage = null;\n        try {\n            const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n            if (storedVendorDetails) {\n                vendorDetailsFromStorage = JSON.parse(storedVendorDetails);\n                console.log('VALIDATE FORM - Retrieved vendor details from localStorage:', storedVendorDetails);\n                // Process vendor details from localStorage\n                if (vendorDetailsFromStorage && Object.keys(vendorDetailsFromStorage).length > 0) {\n                    Object.entries(vendorDetailsFromStorage).forEach((param)=>{\n                        let [vendorType, details] = param;\n                        if (details && details.name && details.mobileNumber) {\n                            // Add to detailFields\n                            detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                            detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                            console.log(\"VALIDATE FORM - Added vendor \".concat(vendorType, \" with name: \").concat(details.name, \" and contact: \").concat(details.mobileNumber));\n                            // Also add normalized versions\n                            const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                            if (normalizedType !== vendorType) {\n                                detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                                detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                                console.log(\"VALIDATE FORM - Also added normalized vendor \".concat(normalizedType));\n                            }\n                        }\n                    });\n                    console.log('VALIDATE FORM - Updated detail fields with vendor details from localStorage');\n                }\n            }\n        } catch (error) {\n            console.error('VALIDATE FORM - Failed to retrieve vendor details from localStorage:', error);\n        }\n        // Now use the updated detailFields for validation\n        console.log('Detail fields count:', Object.keys(detailFields).length);\n        console.log('Detail fields present:', Object.keys(detailFields));\n        console.log('Detail fields values:', detailFields);\n        // For videos, check if required vendor details are present based on video category\n        if (state.mediaType === 'video') {\n            // Determine required vendor count based on video category\n            const videoCategory = detailFields.video_category || 'my_wedding';\n            const requiredVendorCount = videoCategory === 'wedding_vlog' ? 1 : 4;\n            console.log(\"VALIDATE FORM - Video category: \".concat(videoCategory, \", Required vendors: \").concat(requiredVendorCount));\n            // Special handling for Edge browser\n            if (isEdge) {\n                console.log('VALIDATE FORM - Edge browser detected, checking vendor details directly');\n                // In Edge, we'll count vendor details directly from the detailFields\n                const vendorNameFields = Object.keys(detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_name'));\n                const vendorContactFields = Object.keys(detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_contact'));\n                console.log('VALIDATE FORM - Edge browser - Vendor name fields:', vendorNameFields);\n                console.log('VALIDATE FORM - Edge browser - Vendor contact fields:', vendorContactFields);\n                // If we have at least 4 vendor name fields and 4 vendor contact fields, assume we have enough vendors\n                if (vendorNameFields.length >= 4 && vendorContactFields.length >= 4) {\n                    console.log('VALIDATE FORM - Edge browser - Found enough vendor fields, validation passed');\n                    return {\n                        isValid: true\n                    };\n                }\n                // Edge browser workaround - if we're uploading a video, assume vendor details are valid\n                // This is a temporary workaround for Edge browser compatibility\n                if (state.mediaType === 'video') {\n                    console.log('VALIDATE FORM - Edge browser workaround: Assuming vendor details are valid for video upload');\n                    return {\n                        isValid: true\n                    };\n                }\n            }\n            console.log('VALIDATE FORM - Checking vendor details for video upload');\n            console.log('VALIDATE FORM - Detail fields:', JSON.stringify(detailFields, null, 2));\n            // Count how many complete vendor details we have (where BOTH name AND contact are provided)\n            let validVendorCount = 0;\n            // Include both frontend and backend field names to ensure we count all vendor details\n            const vendorPrefixes = [\n                'venue',\n                'photographer',\n                'makeup_artist',\n                'makeupArtist',\n                'decoration',\n                'decorations',\n                'caterer',\n                'additional1',\n                'additional2',\n                'additionalVendor1',\n                'additionalVendor2'\n            ];\n            console.log('VALIDATE FORM - Checking these vendor prefixes:', vendorPrefixes.join(', '));\n            // Keep track of which vendors we've already counted to avoid duplicates\n            const countedVendors = new Set();\n            // First, log all vendor-related fields for debugging\n            const vendorFields = Object.keys(detailFields).filter((key)=>key.startsWith('vendor_'));\n            console.log('VALIDATE FORM - All vendor fields:', vendorFields.join(', '));\n            for (const prefix of vendorPrefixes){\n                // Skip if we've already counted this vendor (to avoid counting both makeupArtist and makeup_artist)\n                const normalizedPrefix = prefix === 'makeupArtist' ? 'makeup_artist' : prefix === 'decorations' ? 'decoration' : prefix;\n                if (countedVendors.has(normalizedPrefix)) {\n                    console.log(\"VALIDATE FORM - Skipping \".concat(prefix, \" as we already counted \").concat(normalizedPrefix));\n                    continue;\n                }\n                const nameField = \"vendor_\".concat(prefix, \"_name\");\n                const contactField = \"vendor_\".concat(prefix, \"_contact\");\n                console.log(\"VALIDATE FORM - Checking vendor \".concat(prefix, \":\"), {\n                    nameField,\n                    nameValue: detailFields[nameField],\n                    contactField,\n                    contactValue: detailFields[contactField],\n                    hasName: !!detailFields[nameField],\n                    hasContact: !!detailFields[contactField]\n                });\n                if (detailFields[nameField] && detailFields[contactField]) {\n                    validVendorCount++;\n                    countedVendors.add(normalizedPrefix);\n                    console.log(\"VALIDATE FORM - Found valid vendor: \".concat(prefix, \" with name: \").concat(detailFields[nameField], \" and contact: \").concat(detailFields[contactField]));\n                }\n            }\n            // Also check for any other vendor_ fields that might have been added\n            console.log('VALIDATE FORM - Checking for additional vendor fields');\n            Object.keys(detailFields).forEach((key)=>{\n                if (key.startsWith('vendor_') && key.endsWith('_name')) {\n                    const baseKey = key.replace('vendor_', '').replace('_name', '');\n                    const contactKey = \"vendor_\".concat(baseKey, \"_contact\");\n                    // Skip if we've already counted this vendor\n                    const normalizedPrefix = baseKey === 'makeupArtist' ? 'makeup_artist' : baseKey === 'decorations' ? 'decoration' : baseKey;\n                    console.log(\"VALIDATE FORM - Checking additional vendor \".concat(baseKey, \":\"), {\n                        normalizedPrefix,\n                        alreadyCounted: countedVendors.has(normalizedPrefix),\n                        hasName: !!detailFields[key],\n                        hasContact: !!detailFields[contactKey]\n                    });\n                    if (!countedVendors.has(normalizedPrefix) && detailFields[key] && detailFields[contactKey]) {\n                        validVendorCount++;\n                        countedVendors.add(normalizedPrefix);\n                        console.log(\"VALIDATE FORM - Found additional valid vendor: \".concat(baseKey, \" with name: \").concat(detailFields[key], \" and contact: \").concat(detailFields[contactKey]));\n                    }\n                }\n            });\n            console.log(\"VALIDATE FORM - Total valid vendor count: \".concat(validVendorCount));\n            console.log(\"VALIDATE FORM - Counted vendors: \".concat(Array.from(countedVendors).join(', ')));\n            // Special handling for Edge browser - if we have vendor fields but they're not being counted correctly\n            let edgeVendorNameFields = Object.keys(detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_name'));\n            let edgeVendorContactFields = Object.keys(detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_contact'));\n            console.log('VALIDATE FORM - Edge browser check - Vendor name fields:', edgeVendorNameFields);\n            console.log('VALIDATE FORM - Edge browser check - Vendor contact fields:', edgeVendorContactFields);\n            // If we have at least required vendor name fields and contact fields, but validVendorCount is less than required,\n            // this is likely an Edge browser issue where the fields aren't being properly counted\n            if (validVendorCount < requiredVendorCount && edgeVendorNameFields.length >= requiredVendorCount && edgeVendorContactFields.length >= requiredVendorCount) {\n                console.log('VALIDATE FORM - Edge browser workaround: Found enough vendor fields but they were not counted correctly');\n                console.log('VALIDATE FORM - Vendor name fields:', edgeVendorNameFields.join(', '));\n                console.log('VALIDATE FORM - Vendor contact fields:', edgeVendorContactFields.join(', '));\n                // Count unique vendor prefixes (excluding the _name/_contact suffix)\n                const vendorPrefixSet = new Set();\n                edgeVendorNameFields.forEach((field)=>{\n                    const prefix = field.replace('vendor_', '').replace('_name', '');\n                    if (edgeVendorContactFields.includes(\"vendor_\".concat(prefix, \"_contact\"))) {\n                        vendorPrefixSet.add(prefix);\n                    }\n                });\n                const uniqueVendorCount = vendorPrefixSet.size;\n                console.log(\"VALIDATE FORM - Unique vendor count: \".concat(uniqueVendorCount));\n                if (uniqueVendorCount >= requiredVendorCount) {\n                    console.log(\"VALIDATE FORM - Edge browser workaround: Found at least \".concat(requiredVendorCount, \" unique vendors with both name and contact\"));\n                    validVendorCount = uniqueVendorCount;\n                }\n            }\n            // Log the vendor field counts\n            console.log('VALIDATE FORM - Vendor name fields count:', edgeVendorNameFields.length);\n            console.log('VALIDATE FORM - Vendor contact fields count:', edgeVendorContactFields.length);\n            if (validVendorCount < requiredVendorCount) {\n                console.log('VALIDATE FORM - Validation failed: Not enough vendor details', validVendorCount);\n                const categoryText = videoCategory === 'wedding_vlog' ? 'wedding vlog' : 'my wedding';\n                return {\n                    isValid: false,\n                    error: \"At least \".concat(requiredVendorCount, \" complete vendor detail\").concat(requiredVendorCount > 1 ? 's' : '', \" (with both name and contact) \").concat(requiredVendorCount > 1 ? 'are' : 'is', \" required for \").concat(categoryText, \" videos. You provided \").concat(validVendorCount, \"/\").concat(requiredVendorCount, \".\")\n                };\n            } else {\n                console.log('VALIDATE FORM - Vendor validation passed with', validVendorCount, 'vendors');\n            }\n        }\n        // Just log the detail fields count for now\n        console.log('Detail fields count:', Object.keys(state.detailFields).length);\n        // Log the detail fields that are present\n        console.log('Detail fields present:', Object.keys(state.detailFields));\n        console.log('Detail fields values:', state.detailFields);\n        console.log('Form validation passed');\n        return {\n            isValid: true\n        };\n    };\n    // Start upload with a specific category and video_category (used when correcting the category)\n    const startUploadWithCategory = async (category, videoCategory)=>{\n        console.log(\"Starting upload process with corrected category: \".concat(category));\n        console.log(\"Using video_category: \".concat(videoCategory || 'Not provided'));\n        // Try to get vendor details from localStorage\n        let vendorDetails = {};\n        try {\n            const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n            if (storedVendorDetails) {\n                vendorDetails = JSON.parse(storedVendorDetails);\n                console.log('UPLOAD CONTEXT - Retrieved vendor details from localStorage');\n            }\n        } catch (error) {\n            console.error('UPLOAD CONTEXT - Failed to retrieve vendor details from localStorage:', error);\n        }\n        // Create detail fields from vendor details\n        const detailFields = {\n            ...state.detailFields\n        };\n        // Process vendor details to create detail fields\n        if (Object.keys(vendorDetails).length > 0) {\n            console.log('UPLOAD CONTEXT - Processing vendor details from localStorage:', JSON.stringify(vendorDetails));\n            // Track how many complete vendor details we've added\n            let completeVendorCount = 0;\n            Object.entries(vendorDetails).forEach((param)=>{\n                let [vendorType, details] = param;\n                if (details && details.name && details.mobileNumber) {\n                    detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                    detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                    console.log(\"UPLOAD CONTEXT - Added vendor \".concat(vendorType, \" with name: \").concat(details.name, \" and contact: \").concat(details.mobileNumber));\n                    completeVendorCount++;\n                    // Also set normalized versions\n                    const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                    if (normalizedType !== vendorType) {\n                        detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                        detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                        console.log(\"UPLOAD CONTEXT - Also added normalized vendor \".concat(normalizedType));\n                    }\n                }\n            });\n            console.log(\"UPLOAD CONTEXT - Added \".concat(completeVendorCount, \" complete vendor details from localStorage to detailFields\"));\n            console.log('UPLOAD CONTEXT - Detail fields after adding vendors:', JSON.stringify(detailFields));\n        }\n        // Update the state with the corrected category and video_category if provided\n        const updatedState = {\n            ...state,\n            mediaSubtype: category,\n            category: category,\n            detailFields: detailFields\n        };\n        // If videoCategory is provided, update the detailFields\n        if (videoCategory) {\n            updatedState.detailFields.video_category = videoCategory;\n            console.log(\"Setting video_category in state to: \".concat(videoCategory));\n            // Also store in localStorage\n            try {\n                localStorage.setItem('wedzat_video_category', videoCategory);\n                console.log('UPLOAD CONTEXT - Stored video_category in localStorage:', videoCategory);\n            } catch (error) {\n                console.error('UPLOAD CONTEXT - Failed to store video_category in localStorage:', error);\n            }\n        }\n        // Apply the state update immediately\n        setState(updatedState);\n        // Then start the upload process with the updated category\n        const validation = validateForm();\n        if (!validation.isValid) {\n            console.log('Upload validation failed:', validation.error);\n            setState({\n                ...state,\n                mediaSubtype: category,\n                error: validation.error,\n                step: 'error'\n            });\n            return;\n        }\n        if (!state.file) {\n            console.log('No file to upload');\n            return;\n        }\n        setState({\n            ...state,\n            mediaSubtype: category,\n            isUploading: true,\n            progress: 0,\n            step: 'uploading',\n            error: undefined\n        });\n        try {\n            console.log('UPLOAD CONTEXT - Starting upload process with corrected category...');\n            console.log('UPLOAD CONTEXT - Upload details:', {\n                file: state.file ? state.file.name : 'No file',\n                fileSize: state.file ? Math.round(state.file.size / (1024 * 1024) * 100) / 100 + ' MB' : 'N/A',\n                mediaType: state.mediaType,\n                category: category,\n                title: state.title,\n                videoCategory: videoCategory || 'Not set'\n            });\n            // Log the video_category that will be used\n            console.log('UPLOAD CONTEXT - Using explicit video_category:', videoCategory);\n            console.log('UPLOAD CONTEXT - Video category from detail fields:', state.detailFields.video_category);\n            // Create a copy of the detail fields with the explicit video_category\n            const updatedDetailFields = {\n                ...state.detailFields\n            };\n            // If videoCategory is provided, use it\n            if (videoCategory) {\n                updatedDetailFields.video_category = videoCategory;\n                console.log('UPLOAD CONTEXT - Setting video_category in details to:', videoCategory);\n            }\n            // Use the upload service to handle the complete upload process with the corrected category\n            const result = await _services_api__WEBPACK_IMPORTED_MODULE_2__.uploadService.handleUpload(state.file, state.mediaType, category, state.title, state.description, state.tags, updatedDetailFields, state.duration, state.thumbnail, (progress)=>{\n                setState({\n                    ...state,\n                    mediaSubtype: category,\n                    progress\n                });\n            });\n            // Update the state with the upload result\n            setState({\n                ...state,\n                mediaSubtype: category,\n                isUploading: false,\n                progress: 100,\n                step: 'complete',\n                uploadResult: result\n            });\n        // Upload completed successfully\n        } catch (error) {\n            console.error('Upload failed:', error);\n            setState({\n                ...state,\n                mediaSubtype: category,\n                isUploading: false,\n                error: error instanceof Error ? error.message : 'Unknown error'\n            });\n            throw error;\n        }\n    };\n    // Perform the actual upload without vendor processing\n    const performUpload = async ()=>{\n        console.log('UPLOAD CONTEXT - Starting direct upload for moments');\n        // Simple validation for moments - only check file and title\n        if (!state.file) {\n            console.log('No file to upload');\n            setState({\n                ...state,\n                error: 'No file selected',\n                step: 'error'\n            });\n            return;\n        }\n        if (!state.title || !state.title.trim()) {\n            console.log('No title provided for moments');\n            setState({\n                ...state,\n                error: 'Please provide a title for your upload',\n                step: 'error'\n            });\n            return;\n        }\n        setState({\n            ...state,\n            isUploading: true,\n            progress: 0,\n            step: 'uploading',\n            error: undefined\n        });\n        try {\n            console.log('UPLOAD CONTEXT - Starting upload process for moments...');\n            console.log('UPLOAD CONTEXT - Upload details:', {\n                file: state.file ? state.file.name : 'No file',\n                fileSize: state.file ? Math.round(state.file.size / (1024 * 1024) * 100) / 100 + ' MB' : 'N/A',\n                mediaType: state.mediaType,\n                mediaSubtype: state.mediaSubtype,\n                title: state.title\n            });\n            // Use the upload service to handle the complete upload process\n            const result = await _services_api__WEBPACK_IMPORTED_MODULE_2__.uploadService.handleUpload(state.file, state.mediaType, state.mediaSubtype, state.title, state.description, state.tags, {}, state.duration, state.thumbnail, (progress)=>{\n                console.log(\"Upload progress: \".concat(progress, \"%\"));\n                setState((prevState)=>{\n                    // Only update if the new progress is greater\n                    if (progress > prevState.progress) {\n                        return {\n                            ...prevState,\n                            progress,\n                            // Change to processing state when we reach 80%\n                            step: progress >= 80 && progress < 100 ? 'processing' : prevState.step\n                        };\n                    }\n                    return prevState;\n                });\n            });\n            console.log('UPLOAD CONTEXT - Upload completed successfully for moments:', result);\n            setState({\n                ...state,\n                isUploading: false,\n                progress: 100,\n                step: 'success',\n                error: undefined\n            });\n        // Upload completed successfully\n        } catch (error) {\n            console.error('Upload failed for moments:', error);\n            setState({\n                ...state,\n                isUploading: false,\n                error: error instanceof Error ? error.message : 'Unknown error',\n                step: 'error'\n            });\n            throw error;\n        }\n    };\n    const startUpload = async ()=>{\n        console.log('Starting upload process...');\n        // For moments (stories), skip vendor details processing and go directly to upload\n        if (state.mediaSubtype === 'story') {\n            console.log('UPLOAD CONTEXT - Moments/Stories detected, skipping vendor details processing');\n            await performUpload();\n            return;\n        }\n        // Try to get vendor details from localStorage\n        try {\n            const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n            if (storedVendorDetails) {\n                const vendorDetails = JSON.parse(storedVendorDetails);\n                console.log('UPLOAD CONTEXT - Retrieved vendor details from localStorage for startUpload:', storedVendorDetails);\n                // Create a new detailFields object to hold all the vendor details\n                const updatedDetailFields = {\n                    ...state.detailFields\n                };\n                let completeVendorCount = 0;\n                // Process vendor details to create detail fields\n                if (Object.keys(vendorDetails).length > 0) {\n                    Object.entries(vendorDetails).forEach((param)=>{\n                        let [vendorType, details] = param;\n                        if (details && details.name && details.mobileNumber) {\n                            // Add to the updated detail fields\n                            updatedDetailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                            updatedDetailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                            console.log(\"UPLOAD CONTEXT - Added vendor \".concat(vendorType, \" with name: \").concat(details.name, \" and contact: \").concat(details.mobileNumber));\n                            completeVendorCount++;\n                            // Also set normalized versions\n                            const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                            if (normalizedType !== vendorType) {\n                                updatedDetailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                                updatedDetailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                                console.log(\"UPLOAD CONTEXT - Also added normalized vendor \".concat(normalizedType));\n                            }\n                        }\n                    });\n                    // Update the state with all vendor details at once\n                    setState((prevState)=>({\n                            ...prevState,\n                            detailFields: updatedDetailFields\n                        }));\n                    console.log(\"UPLOAD CONTEXT - Added \".concat(completeVendorCount, \" complete vendor details from localStorage to state\"));\n                    console.log('UPLOAD CONTEXT - Updated detail fields:', JSON.stringify(updatedDetailFields));\n                }\n            }\n        } catch (error) {\n            console.error('UPLOAD CONTEXT - Failed to retrieve vendor details from localStorage:', error);\n        }\n        // Check if we have a video_category for videos\n        if (state.mediaType === 'video') {\n            // Try to get video_category from localStorage if not in state\n            let videoCategory = state.detailFields.video_category;\n            if (!videoCategory) {\n                try {\n                    const storedVideoCategory = localStorage.getItem('wedzat_video_category');\n                    if (storedVideoCategory) {\n                        videoCategory = storedVideoCategory;\n                        console.log('UPLOAD CONTEXT - Retrieved video_category from localStorage:', videoCategory);\n                        setDetailField('video_category', videoCategory);\n                    }\n                } catch (error) {\n                    console.error('UPLOAD CONTEXT - Failed to retrieve video_category from localStorage:', error);\n                }\n            }\n            console.log(\"UPLOAD CONTEXT - Current video_category: \".concat(videoCategory || 'Not set'));\n            // If we don't have a video_category, use a default one\n            if (!videoCategory) {\n                console.log('UPLOAD CONTEXT - No video_category found, using default: my_wedding');\n                // Use startUploadWithCategory to ensure the video_category is properly set\n                return startUploadWithCategory(state.mediaSubtype, 'my_wedding');\n            } else {\n                // Use startUploadWithCategory to ensure the video_category is properly passed\n                console.log(\"UPLOAD CONTEXT - Using existing video_category: \".concat(videoCategory));\n                return startUploadWithCategory(state.mediaSubtype, videoCategory);\n            }\n        }\n        // For photos, just use the regular upload flow\n        const validation = validateForm();\n        if (!validation.isValid) {\n            console.log('Upload validation failed:', validation.error);\n            setState({\n                ...state,\n                error: validation.error,\n                step: 'error'\n            });\n            return;\n        }\n        if (!state.file) {\n            console.log('No file to upload');\n            return;\n        }\n        setState({\n            ...state,\n            isUploading: true,\n            progress: 0,\n            step: 'uploading',\n            error: undefined\n        });\n        try {\n            console.log('UPLOAD CONTEXT - Starting upload process...');\n            console.log('UPLOAD CONTEXT - Upload details:', {\n                file: state.file ? state.file.name : 'No file',\n                fileSize: state.file ? Math.round(state.file.size / (1024 * 1024) * 100) / 100 + ' MB' : 'N/A',\n                mediaType: state.mediaType,\n                mediaSubtype: state.mediaSubtype,\n                title: state.title,\n                videoCategory: state.detailFields.video_category || 'Not set'\n            });\n            // Use the upload service to handle the complete upload process\n            const result = await _services_api__WEBPACK_IMPORTED_MODULE_2__.uploadService.handleUpload(state.file, state.mediaType, state.mediaSubtype, state.title, state.description, state.tags, state.detailFields, state.duration, state.thumbnail, (progress)=>{\n                console.log(\"Upload progress: \".concat(progress, \"%\"));\n                setState((prevState)=>{\n                    // Only update if the new progress is greater\n                    if (progress > prevState.progress) {\n                        return {\n                            ...prevState,\n                            progress,\n                            // Change to processing state when we reach 80%\n                            step: progress >= 80 && progress < 100 ? 'processing' : prevState.step\n                        };\n                    }\n                    return prevState;\n                });\n            });\n            console.log('Upload completed successfully:', result);\n            // Upload complete\n            setState({\n                ...state,\n                isUploading: false,\n                progress: 100,\n                step: 'complete',\n                response: result\n            });\n        } catch (error) {\n            console.error('Upload failed:', error);\n            setState({\n                ...state,\n                isUploading: false,\n                progress: 0,\n                step: 'error',\n                error: error instanceof Error ? error.message : 'Upload failed. Please try again.'\n            });\n        }\n    };\n    const goToStep = (step)=>{\n        setState({\n            ...state,\n            step\n        });\n    };\n    // Set video duration\n    const setDuration = (duration)=>{\n        setState({\n            ...state,\n            duration\n        });\n        console.log(\"Duration set to \".concat(duration, \" seconds\"));\n    };\n    // Effect to initialize the upload context and listen for vendor details updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UploadProvider.useEffect\": ()=>{\n            // For moments (stories), skip localStorage initialization\n            if (state.mediaSubtype === 'story') {\n                console.log('UPLOAD CONTEXT - Moments detected, skipping localStorage initialization');\n                return;\n            }\n            // Check if we have a video_category in localStorage\n            try {\n                const storedVideoCategory = localStorage.getItem('wedzat_video_category');\n                if (storedVideoCategory) {\n                    console.log('UPLOAD CONTEXT - Retrieved video_category from localStorage:', storedVideoCategory);\n                    setDetailField('video_category', storedVideoCategory);\n                }\n            } catch (error) {\n                console.error('UPLOAD CONTEXT - Failed to retrieve video_category from localStorage:', error);\n            }\n            // Add event listener for vendor details updates from API service\n            const handleVendorDetailsUpdate = {\n                \"UploadProvider.useEffect.handleVendorDetailsUpdate\": (event)=>{\n                    if (event.detail) {\n                        console.log('UPLOAD CONTEXT - Received vendor-details-update event:', event.detail);\n                        // Process vendor details from event\n                        const vendorDetails = event.detail;\n                        const updatedDetailFields = {\n                            ...state.detailFields\n                        };\n                        let completeVendorCount = 0;\n                        Object.entries(vendorDetails).forEach({\n                            \"UploadProvider.useEffect.handleVendorDetailsUpdate\": (param)=>{\n                                let [vendorType, details] = param;\n                                if (details && details.name && details.mobileNumber) {\n                                    // Add to detailFields\n                                    updatedDetailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                                    updatedDetailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                                    console.log(\"UPLOAD CONTEXT - Event handler added vendor \".concat(vendorType, \" with name: \").concat(details.name, \" and contact: \").concat(details.mobileNumber));\n                                    completeVendorCount++;\n                                    // Also add normalized versions\n                                    const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                                    if (normalizedType !== vendorType) {\n                                        updatedDetailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                                        updatedDetailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                                        console.log(\"UPLOAD CONTEXT - Event handler also added normalized vendor \".concat(normalizedType));\n                                    }\n                                }\n                            }\n                        }[\"UploadProvider.useEffect.handleVendorDetailsUpdate\"]);\n                        // Update the state with all vendor details at once\n                        setState({\n                            \"UploadProvider.useEffect.handleVendorDetailsUpdate\": (prevState)=>({\n                                    ...prevState,\n                                    detailFields: updatedDetailFields\n                                })\n                        }[\"UploadProvider.useEffect.handleVendorDetailsUpdate\"]);\n                        console.log(\"UPLOAD CONTEXT - Event handler added \".concat(completeVendorCount, \" complete vendor details to state\"));\n                        console.log('UPLOAD CONTEXT - Event handler updated detail fields:', JSON.stringify(updatedDetailFields));\n                    }\n                }\n            }[\"UploadProvider.useEffect.handleVendorDetailsUpdate\"];\n            // Add event listener\n            window.addEventListener('vendor-details-update', handleVendorDetailsUpdate);\n            // Remove event listener on cleanup\n            return ({\n                \"UploadProvider.useEffect\": ()=>{\n                    window.removeEventListener('vendor-details-update', handleVendorDetailsUpdate);\n                }\n            })[\"UploadProvider.useEffect\"];\n        }\n    }[\"UploadProvider.useEffect\"], []);\n    // Create the context value\n    const contextValue = {\n        state,\n        setFile,\n        setThumbnail,\n        setMediaType,\n        setMediaSubtype,\n        setCategory,\n        setTitle,\n        setDescription,\n        addTag,\n        removeTag,\n        setDetailField,\n        setPersonalDetails,\n        setVendorDetails,\n        setDuration,\n        resetUpload,\n        startUpload,\n        startUploadWithCategory,\n        validateForm,\n        goToStep\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UploadContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\contexts\\\\UploadContexts.tsx\",\n        lineNumber: 1313,\n        columnNumber: 5\n    }, undefined);\n};\n_s(UploadProvider, \"g9yWDQF6ixWa1r5sfsm7YAeGJG4=\");\n_c = UploadProvider;\n// Custom hook to use the context\nconst useUpload = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(UploadContext);\n    if (context === undefined) {\n        throw new Error('useUpload must be used within an UploadProvider');\n    }\n    return context;\n};\n_s1(useUpload, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"UploadProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/UploadContexts.tsx\n"));

/***/ })

});