"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9437c8c19ebd\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xca29tbWlcXE9uZURyaXZlXFxEZXNrdG9wXFxXRURaQVRfXFxmcm9udGVuZFxcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjk0MzdjOGMxOWViZFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/upload/UploadManager.tsx":
/*!*********************************************!*\
  !*** ./components/upload/UploadManager.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _UploadTypeSelection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./UploadTypeSelection */ \"(app-pages-browser)/./components/upload/UploadTypeSelection.tsx\");\n/* harmony import */ var _VideoCategorySelection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./VideoCategorySelection */ \"(app-pages-browser)/./components/upload/VideoCategorySelection.tsx\");\n/* harmony import */ var _ThumbnailSelection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ThumbnailSelection */ \"(app-pages-browser)/./components/upload/ThumbnailSelection.tsx\");\n/* harmony import */ var _PersonalDetails__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./PersonalDetails */ \"(app-pages-browser)/./components/upload/PersonalDetails.tsx\");\n/* harmony import */ var _VendorDetails__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./VendorDetails */ \"(app-pages-browser)/./components/upload/VendorDetails.tsx\");\n/* harmony import */ var _FaceVerification__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./FaceVerification */ \"(app-pages-browser)/./components/upload/FaceVerification.tsx\");\n/* harmony import */ var _UploadProgress__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./UploadProgress */ \"(app-pages-browser)/./components/upload/UploadProgress.tsx\");\n/* harmony import */ var _contexts_UploadContexts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../contexts/UploadContexts */ \"(app-pages-browser)/./contexts/UploadContexts.tsx\");\n/* harmony import */ var _utils_uploadUtils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../utils/uploadUtils */ \"(app-pages-browser)/./utils/uploadUtils.ts\");\n/* harmony import */ var _utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../utils/alertUtils */ \"(app-pages-browser)/./utils/alertUtils.tsx\");\n/* harmony import */ var _hooks_useMedia__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../hooks/useMedia */ \"(app-pages-browser)/./hooks/useMedia.ts\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../services/api */ \"(app-pages-browser)/./services/api.ts\");\n// components/upload/UploadManager.tsx\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function to format time in minutes and seconds\nconst formatTime = (seconds)=>{\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    if (minutes === 0) {\n        return \"\".concat(remainingSeconds, \" seconds\");\n    } else if (minutes === 1 && remainingSeconds === 0) {\n        return '1 minute';\n    } else if (remainingSeconds === 0) {\n        return \"\".concat(minutes, \" minutes\");\n    } else {\n        return \"\".concat(minutes, \" minute\").concat(minutes > 1 ? 's' : '', \" and \").concat(remainingSeconds, \" second\").concat(remainingSeconds !== 1 ? 's' : '');\n    }\n};\nconst UploadManager = (param)=>{\n    let { onClose, initialType, onUploadComplete } = param;\n    _s();\n    const { state, setFile, setMediaType, setCategory, setMediaSubtype, setTitle, setDescription, setDetailField, startUpload, startUploadWithCategory, setThumbnail, setVendorDetails, setPersonalDetails, setDuration, setIsMoments, resetUpload } = (0,_contexts_UploadContexts__WEBPACK_IMPORTED_MODULE_9__.useUpload)();\n    const [phase, setPhase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('typeSelection');\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialType || '');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [previewImage, setPreviewImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [thumbnailImage, setThumbnailImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const vendorDetailsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    // Helper function to determine content type for PersonalDetails\n    const getContentType = ()=>{\n        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n            return 'moment';\n        } else if (state.mediaType === 'video') {\n            return 'video';\n        } else {\n            return 'photo';\n        }\n    };\n    // Store personal details to persist between screens\n    const [personalDetails, setLocalPersonalDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        caption: '',\n        lifePartner: '',\n        weddingStyle: '',\n        place: '',\n        eventType: '',\n        budget: ''\n    });\n    // Auto-select the type if initialType is provided\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UploadManager.useEffect\": ()=>{\n            if (initialType) {\n                console.log('Auto-selecting type from initialType:', initialType);\n                handleTypeSelected(initialType);\n            }\n        }\n    }[\"UploadManager.useEffect\"], []);\n    // Store vendor details to persist between screens\n    const [vendorDetailsData, setVendorDetailsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        venue: {\n            name: '',\n            mobileNumber: ''\n        },\n        photographer: {\n            name: '',\n            mobileNumber: ''\n        },\n        makeupArtist: {\n            name: '',\n            mobileNumber: ''\n        },\n        decorations: {\n            name: '',\n            mobileNumber: ''\n        },\n        caterer: {\n            name: '',\n            mobileNumber: ''\n        }\n    });\n    // Use the new media upload hook\n    const { mutate: uploadMedia, isPending: isUploading } = (0,_hooks_useMedia__WEBPACK_IMPORTED_MODULE_12__.useMediaUpload)();\n    // Handle media type selection\n    const handleTypeSelected = (type)=>{\n        // First, completely reset everything\n        resetUpload();\n        setPreviewImage(null);\n        setThumbnailImage(null);\n        // Then set the new type\n        setSelectedType(type);\n        console.log(\"Selected type:\", type);\n        // Set the moments flag in the upload context\n        if (type === 'moments') {\n            setIsMoments(true);\n            console.log(\"UPLOAD MANAGER - Setting isMoments to true\");\n        } else {\n            setIsMoments(false);\n        }\n        if ([\n            'flashes',\n            'glimpses',\n            'movies',\n            'photos',\n            'moments'\n        ].includes(type)) {\n            // For explicit video types, photos, and moments, set the appropriate media type\n            if (type === 'photos') {\n                console.log('Setting media type to photo for:', type);\n                setMediaType('photo');\n                setMediaSubtype('post');\n                // Go to category selection for photos\n                setPhase('categorySelection');\n            } else if (type === 'moments') {\n                console.log('Setting media type for moments (will be determined by file type)');\n                // For moments, we'll set the media type later based on the file type (photo or video)\n                setMediaSubtype('story');\n                // For moments, skip category selection and go directly to file upload\n                console.log('Moments selected: skipping category selection, going directly to file upload');\n                handleFileUpload();\n                return; // Early return to prevent further processing\n            } else {\n                setMediaType('video');\n                setMediaSubtype(getMediaSubtypeFromSelectedType(type));\n                // Go to category selection for videos\n                setPhase('categorySelection');\n            }\n        } else if (type === 'photo') {\n            // For single photo type (if it exists)\n            console.log('Setting media type to photo for:', type);\n            setMediaType('photo');\n            setMediaSubtype('post');\n            // Use a special photo-only upload handler for photos\n            handlePhotoUpload();\n        }\n    };\n    // Helper function to get the backend media subtype from the selected UI type\n    const getMediaSubtypeFromSelectedType = (type)=>{\n        // Map UI category to backend category for media_subtype\n        switch(type){\n            // Photo types\n            case 'moments':\n                return 'story'; // Backend expects 'story' for moments\n            case 'photos':\n                return 'post'; // Backend expects 'post' for regular photos\n            // Video types\n            case 'flashes':\n                return 'flash'; // Backend expects 'flash'\n            case 'glimpses':\n                return 'glimpse'; // Backend expects 'glimpse'\n            case 'movies':\n                return 'movie'; // Backend expects 'movie'\n            // Default fallback\n            default:\n                return type === 'moments' ? 'story' : 'post'; // Default based on type\n        }\n    };\n    // Handle category selection for both videos and photos\n    const handleCategorySelected = (category)=>{\n        // First, make sure we have a clean state for the new upload\n        // but preserve the selected type and media type\n        const currentType = selectedType;\n        const currentMediaType = state.mediaType;\n        resetUpload();\n        setSelectedType(currentType);\n        setMediaType(currentMediaType);\n        setPreviewImage(null);\n        setThumbnailImage(null);\n        // Now set the new category\n        setSelectedCategory(category);\n        // Get the media subtype based on the selected type\n        let mediaSubtype;\n        if (currentType === 'photos') {\n            // For photos, always use 'post' as the media subtype\n            mediaSubtype = 'post';\n            console.log(\"UPLOAD MANAGER - Using media subtype 'post' for photos\");\n        } else {\n            // For videos, use the subtype based on the selected type\n            mediaSubtype = getMediaSubtypeFromSelectedType(selectedType);\n            console.log(\"UPLOAD MANAGER - Using media subtype \".concat(mediaSubtype, \" based on selected type \").concat(selectedType));\n            console.log(\"UPLOAD MANAGER - Backend expects: flashes→flash, glimpses→glimpse, movies→movie, moments→story\");\n        }\n        console.log(\"UPLOAD MANAGER - Selected category:\", category);\n        console.log(\"UPLOAD MANAGER - Setting media subtype to:\", mediaSubtype);\n        // Set the media subtype in the context\n        setMediaSubtype(mediaSubtype);\n        // Map the selected category to a valid backend video_category\n        let backendVideoCategory = '';\n        if (category === 'my_wedding_videos') {\n            backendVideoCategory = 'my_wedding';\n        } else if (category === 'wedding_vlog') {\n            backendVideoCategory = 'wedding_vlog';\n        }\n        // Make sure we have a valid video_category\n        if (!backendVideoCategory) {\n            console.error('Invalid video category selected:', category);\n            alert('Please select a valid video category');\n            return;\n        }\n        // Set video category in the context for the backend\n        console.log(\"UPLOAD MANAGER - Setting video_category to:\", backendVideoCategory);\n        setDetailField('video_category', backendVideoCategory);\n        // Log the final values\n        console.log(\"UPLOAD MANAGER - Selected category:\", category);\n        console.log(\"UPLOAD MANAGER - Backend video category set to:\", backendVideoCategory);\n        console.log(\"UPLOAD MANAGER - Media subtype set to:\", mediaSubtype);\n        // Proceed to file upload after setting the category\n        if (currentType === 'photos') {\n            // For photos, use the photo-specific upload handler\n            handlePhotoUpload();\n        } else {\n            // For videos, use the standard file upload handler\n            handleFileUpload();\n        }\n    };\n    // Handle thumbnail upload\n    const handleThumbnailUpload = ()=>{\n        // Create a file input element\n        const input = document.createElement('input');\n        input.type = 'file';\n        input.accept = 'image/*';\n        // Handle file selection\n        input.onchange = (e)=>{\n            const files = e.target.files;\n            if (files && files.length > 0) {\n                const file = files[0];\n                // Store the thumbnail\n                setThumbnailImage(file);\n                setThumbnail(file);\n                console.log(\"Thumbnail selected:\", file.name);\n                // Show a preview if needed\n                const reader = new FileReader();\n                reader.onload = (e)=>{\n                    var _e_target;\n                    if ((_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result) {\n                        // You could set a thumbnail preview here if needed\n                        console.log(\"Thumbnail preview ready\");\n                    }\n                };\n                reader.readAsDataURL(file);\n            }\n        };\n        // Trigger the file dialog\n        input.click();\n    };\n    // Get user-friendly display name for a category\n    const getCategoryDisplayName = (category)=>{\n        switch(category){\n            case 'flash':\n                return 'Flash';\n            case 'glimpse':\n                return 'Glimpse';\n            case 'movie':\n                return 'Movie';\n            case 'story':\n                return 'Story';\n            case 'post':\n                return 'Photo';\n            default:\n                return category.charAt(0).toUpperCase() + category.slice(1);\n        }\n    };\n    // Get appropriate category based on duration\n    const getAppropriateCategory = (duration)=>{\n        // For very short videos (1 minute or less), use flash instead of story/moments\n        if (duration <= 60) {\n            return 'flash'; // Very short videos (1 minute or less) - changed from 'story' to 'flash'\n        } else if (duration <= 90) {\n            return 'flash'; // Short videos (1.5 minutes or less)\n        } else if (duration <= 420) {\n            return 'glimpse'; // Medium videos (7 minutes or less)\n        } else {\n            return 'movie'; // Long videos (over 7 minutes)\n        }\n    };\n    // Special handler for photo uploads that strictly enforces image-only files\n    const handlePhotoUpload = ()=>{\n        console.log('handlePhotoUpload called - strict image-only upload');\n        // Create a file input element specifically for photos\n        const input = document.createElement('input');\n        input.type = 'file';\n        // Reset the input value\n        input.value = '';\n        // Only accept image files - explicitly list allowed types\n        input.accept = 'image/jpeg,image/png,image/gif,image/webp';\n        // Handle file selection with strict validation\n        input.onchange = async (e)=>{\n            const files = e.target.files;\n            if (!files || files.length === 0) return;\n            const file = files[0];\n            console.log('Photo file selected:', file.name, file.type, file.size);\n            // Strict validation - must be an image file\n            const validImageTypes = [\n                'image/jpeg',\n                'image/png',\n                'image/gif',\n                'image/webp'\n            ];\n            if (!validImageTypes.includes(file.type)) {\n                console.error('Invalid file type for photos:', file.type);\n                alert('Only JPEG, PNG, GIF, or WebP images can be uploaded as photos. Videos are not allowed.');\n                return;\n            }\n            // Additional check - reject any file that might be a video\n            if (file.type.startsWith('video/')) {\n                console.error('Attempted to upload a video file as photo');\n                alert('Videos cannot be uploaded as photos. Please select an image file.');\n                return;\n            }\n            // For photos, we need to be more careful with state management\n            // First, set the media type and subtype\n            setMediaType('photo');\n            setMediaSubtype('post');\n            // Then set the file in the state\n            setFile(file);\n            console.log('Photo file set in state:', file.name);\n            // Create a local reference to the file for use in the timeout\n            const currentFile = file;\n            // Double-check that the file is set in the state before proceeding\n            setTimeout(()=>{\n                // Check if the file is in the state\n                if (!state.file) {\n                    console.log('File not found in state after setting, trying again');\n                    // Try setting the file again\n                    setFile(currentFile);\n                    // Add another timeout to ensure the file is set\n                    setTimeout(()=>{\n                        if (!state.file) {\n                            console.log('File still not in state, setting it one more time');\n                            setFile(currentFile);\n                        } else {\n                            console.log('File confirmed in state after second attempt:', state.file.name);\n                        }\n                        // New flow logic: For moments (stories), skip personal details and go directly to face verification\n                        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                            console.log('Moments photo upload: skipping personal details, going directly to face verification');\n                            setPhase('faceVerification');\n                        } else {\n                            console.log('Moving to personalDetails phase for photo');\n                            setPhase('personalDetails');\n                        }\n                    }, 100);\n                } else {\n                    console.log('File confirmed in state:', state.file.name);\n                    // New flow logic: For moments (stories), skip personal details and go directly to face verification\n                    if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                        console.log('Moments photo upload: skipping personal details, going directly to face verification');\n                        setPhase('faceVerification');\n                    } else {\n                        console.log('Moving to personalDetails phase for photo');\n                        setPhase('personalDetails');\n                    }\n                }\n            }, 100);\n            // Handle image preview\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                var _e_target;\n                if ((_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result) {\n                    setPreviewImage(e.target.result);\n                    console.log('Preview image set for photo');\n                }\n            };\n            reader.readAsDataURL(file);\n        // Note: We don't set the phase here anymore - it's handled in the timeout above\n        };\n        // Trigger the file dialog\n        input.click();\n    };\n    // This function was previously used but is now replaced by getAppropriateCategory\n    // Keeping a comment here for reference in case it needs to be restored\n    // Handle manual upload button click\n    const handleFileUpload = async (category)=>{\n        console.log('handleFileUpload called with category:', category || 'none');\n        // Create a file input element\n        const input = document.createElement('input');\n        input.type = 'file';\n        // Reset the input value to ensure we get a new file selection event even if the same file is selected\n        input.value = '';\n        if (selectedType === 'moments') {\n            input.accept = 'image/*,video/*';\n        } else {\n            input.accept = selectedType === 'photo' || selectedType === 'photos' ? 'image/jpeg,image/png,image/gif,image/webp' // Explicitly list image types only\n             : 'video/*';\n        }\n        // Handle file selection\n        input.onchange = async (e)=>{\n            const files = e.target.files;\n            if (!files || files.length === 0) return;\n            const file = files[0];\n            console.log('File selected:', file.name, file.type, file.size);\n            console.log('UPLOAD MANAGER - selectedType before resetUpload:', selectedType);\n            // Strict validation for photo uploads - must be an image file\n            if (selectedType === 'photo' || selectedType === 'photos') {\n                const validImageTypes = [\n                    'image/jpeg',\n                    'image/png',\n                    'image/gif',\n                    'image/webp'\n                ];\n                // Check if file is a video or not a valid image type\n                if (file.type.startsWith('video/') || !validImageTypes.includes(file.type)) {\n                    console.error('Invalid file type for photos:', file.type);\n                    (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Invalid File Type', 'Only JPEG, PNG, GIF, or WebP images can be uploaded as photos. Videos are not allowed.');\n                    return;\n                }\n            }\n            // Reset the upload context before setting the new file\n            // Preserve the isMoments flag before reset\n            const preserveIsMoments = state.isMoments;\n            console.log('UPLOAD MANAGER - Preserving isMoments:', preserveIsMoments);\n            resetUpload();\n            console.log('UPLOAD MANAGER - selectedType after resetUpload:', selectedType);\n            // Restore the isMoments flag after reset\n            if (preserveIsMoments) {\n                setIsMoments(true);\n                console.log('UPLOAD MANAGER - Restored isMoments to true after reset');\n            }\n            // Set the file in the state\n            setFile(file);\n            console.log('File set in state:', file.name);\n            // If it's a video, calculate and set the duration\n            // Double-check that we're not trying to upload a video as a photo\n            if (file.type.startsWith('video/')) {\n                // Safety check - don't process videos for photo uploads\n                if (selectedType === 'photo' || selectedType === 'photos') {\n                    console.error('Attempted to process a video file for photo upload');\n                    (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Invalid File Type', 'Videos cannot be uploaded as photos. Please select an image file.');\n                    resetUpload();\n                    return;\n                }\n                try {\n                    const duration = await (0,_utils_uploadUtils__WEBPACK_IMPORTED_MODULE_10__.getVideoDuration)(file);\n                    console.log('Video duration calculated:', duration);\n                    setDuration(duration);\n                    // For moments, check if it's a video and validate the duration (max 1 minute)\n                    if (selectedType === 'moments') {\n                        console.log('Validating moments video duration...');\n                        setMediaType('video');\n                        // Check if the video is longer than 1 minute (60 seconds)\n                        if (duration > 60) {\n                            console.log(\"Moments video too long: \".concat(duration, \" seconds (max: 60 seconds)\"));\n                            // Show a more detailed error message with custom alert\n                            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showWarningAlert)('Moments Video Too Long', \"Moments videos must be 1 minute or less.\\n\\nYour video is \".concat(formatTime(duration), \" long.\\n\\nPlease select a shorter video or trim this video to 1 minute or less.\"));\n                            // Reset the upload context but preserve the selected type and category\n                            const currentType = selectedType;\n                            const currentCategory = selectedCategory;\n                            // First set the phase back to category selection\n                            setPhase('categorySelection');\n                            // Then reset the upload state\n                            setTimeout(()=>{\n                                resetUpload();\n                                setSelectedType(currentType);\n                                setSelectedCategory(currentCategory);\n                                console.log('Reset upload state after moments video duration validation failure');\n                            }, 100);\n                            // Return early to prevent further processing\n                            return;\n                        }\n                        console.log(\"Moments video duration valid: \".concat(duration, \" seconds (max: 60 seconds)\"));\n                        // For moments, we always use 'story' as the media subtype\n                        console.log('Setting media subtype for moments video to story');\n                        setMediaSubtype('story');\n                    }\n                    // If we have a category, validate the duration for that category\n                    if (selectedType && [\n                        'flashes',\n                        'glimpses',\n                        'movies'\n                    ].includes(selectedType)) {\n                        const mediaSubtype = getMediaSubtypeFromSelectedType(selectedType);\n                        const validationResult = (0,_utils_uploadUtils__WEBPACK_IMPORTED_MODULE_10__.validateVideoDuration)(duration, mediaSubtype);\n                        if (!validationResult.isValid) {\n                            // If there's a suggested category, automatically switch to it\n                            if (validationResult.suggestedCategory) {\n                                // For videos that exceed the maximum duration, automatically switch without asking\n                                console.log(\"Video exceeds maximum duration for \".concat(mediaSubtype, \". Automatically switching to \").concat(validationResult.suggestedCategory));\n                                (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showWarningAlert)('Video Duration Notice', \"Your video is too long for the \".concat(getCategoryDisplayName(mediaSubtype), \" category. It will be uploaded as a \").concat(getCategoryDisplayName(validationResult.suggestedCategory), \" instead.\"));\n                                // Switch to the suggested category\n                                console.log(\"Switching to suggested category: \".concat(validationResult.suggestedCategory));\n                                // Make sure we keep a reference to the file\n                                const currentFile = file;\n                                // Update the media subtype\n                                setMediaSubtype(validationResult.suggestedCategory);\n                                // Update the selected type to match the new category\n                                // Never suggest 'story' (moments) for other categories\n                                if (validationResult.suggestedCategory === 'flash') {\n                                    setSelectedType('flashes');\n                                } else if (validationResult.suggestedCategory === 'glimpse') {\n                                    setSelectedType('glimpses');\n                                } else if (validationResult.suggestedCategory === 'movie') {\n                                    setSelectedType('movies');\n                                }\n                                // Removed the 'story' suggestion for short videos\n                                // Make sure the file is still set in the state\n                                setTimeout(()=>{\n                                    if (!state.file) {\n                                        console.log('Re-setting file after category change:', currentFile.name);\n                                        setFile(currentFile);\n                                    }\n                                }, 50);\n                            } else {\n                                // No suggested category, just show the error\n                                (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Video Duration Error', validationResult.error || 'The video duration is not valid for this category.');\n                            }\n                        } else if (validationResult.suggestedCategory && validationResult.suggestedCategory !== mediaSubtype) {\n                            // Video is valid for current category but there's a better category\n                            // For this case, we still give the user a choice since the video is valid for the current category\n                            // Use our custom confirm dialog instead of window.confirm\n                            const confirmSwitch = await (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showAlert)({\n                                title: 'Category Suggestion',\n                                message: \"\".concat(validationResult.error, \"\\n\\nWould you like to switch to the suggested category?\"),\n                                type: 'warning',\n                                confirmText: 'Yes, Switch Category',\n                                cancelText: 'No, Keep Current',\n                                onConfirm: ()=>{}\n                            });\n                            if (confirmSwitch) {\n                                // Switch to the suggested category\n                                console.log(\"Switching to suggested category: \".concat(validationResult.suggestedCategory));\n                                // Make sure we keep a reference to the file\n                                const currentFile = file;\n                                // Update the media subtype\n                                setMediaSubtype(validationResult.suggestedCategory);\n                                // Update the selected type to match the new category\n                                // Never suggest 'story' (moments) for other categories\n                                if (validationResult.suggestedCategory === 'flash') {\n                                    setSelectedType('flashes');\n                                } else if (validationResult.suggestedCategory === 'glimpse') {\n                                    setSelectedType('glimpses');\n                                } else if (validationResult.suggestedCategory === 'movie') {\n                                    setSelectedType('movies');\n                                }\n                                // Removed the 'story' suggestion for short videos\n                                // Make sure the file is still set in the state\n                                setTimeout(()=>{\n                                    if (!state.file) {\n                                        console.log('Re-setting file after category change:', currentFile.name);\n                                        setFile(currentFile);\n                                    }\n                                }, 50);\n                            }\n                        }\n                    }\n                    // Always go to thumbnail selection for videos\n                    console.log('Moving to thumbnailSelection phase');\n                    // Keep a reference to the current file\n                    const currentFile = file;\n                    // Double-check that the file is set in the state before proceeding\n                    if (state.file) {\n                        console.log('File confirmed in state before phase change:', state.file.name);\n                        setPhase('thumbnailSelection');\n                    } else {\n                        console.log('File not found in state before phase change, setting it again');\n                        // Try setting the file again\n                        setFile(currentFile);\n                        // Add a small delay to ensure the state is updated\n                        setTimeout(()=>{\n                            // Double-check again\n                            if (!state.file) {\n                                console.log('File still not in state, setting it one more time');\n                                setFile(currentFile);\n                            }\n                            console.log('Delayed phase change to thumbnailSelection');\n                            setPhase('thumbnailSelection');\n                        }, 100);\n                    }\n                } catch (error) {\n                    console.error('Error calculating video duration:', error);\n                    // For moments videos, we need to enforce the duration check\n                    // If we can't calculate duration, we can't validate it, so we should reject the upload\n                    if (selectedType === 'moments') {\n                        (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Video Error', 'Unable to determine video duration. Please try a different video file.');\n                        resetUpload();\n                        return;\n                    }\n                    console.log('Moving to thumbnailSelection phase despite error');\n                    // Keep a reference to the current file\n                    const currentFile = file;\n                    // Double-check that the file is set in the state before proceeding\n                    if (state.file) {\n                        console.log('File confirmed in state before phase change (error case):', state.file.name);\n                        setPhase('thumbnailSelection');\n                    } else {\n                        console.log('File not found in state before phase change (error case), setting it again');\n                        // Try setting the file again\n                        setFile(currentFile);\n                        // Add a small delay to ensure the state is updated\n                        setTimeout(()=>{\n                            // Double-check again\n                            if (!state.file) {\n                                console.log('File still not in state (error case), setting it one more time');\n                                setFile(currentFile);\n                            }\n                            console.log('Delayed phase change to thumbnailSelection (error case)');\n                            setPhase('thumbnailSelection');\n                        }, 100);\n                    }\n                }\n            } else {\n                // For photos or moments images\n                console.log('UPLOAD MANAGER - selectedType before moments check:', selectedType);\n                if (selectedType === 'moments') {\n                    // For moments, we need to set the media type based on the file type\n                    if (file.type.startsWith('image/')) {\n                        console.log('Moments image detected');\n                        setMediaType('photo');\n                        // For moments images, we always use 'story' as the media subtype\n                        setMediaSubtype('story');\n                        // Create a local reference to the file for use in the timeout\n                        const currentFile = file;\n                        // Double-check that the file is set in the state before proceeding\n                        setTimeout(()=>{\n                            // Check if the file is in the state\n                            if (!state.file) {\n                                console.log('Moments photo not found in state after setting, trying again');\n                                // Try setting the file again\n                                setFile(currentFile);\n                            } else {\n                                console.log('Moments photo confirmed in state:', state.file.name);\n                            }\n                        }, 50);\n                    } else {\n                        console.log('Invalid file type for moments. Only images and videos less than 1 minute are allowed.');\n                        (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Invalid File Type', 'Invalid file type for moments. Only images and videos less than 1 minute are allowed.');\n                        // Reset the upload context but preserve the selected type and category\n                        const currentType = selectedType;\n                        const currentCategory = selectedCategory;\n                        // First set the phase back to category selection\n                        setPhase('categorySelection');\n                        // Then reset the upload state\n                        setTimeout(()=>{\n                            resetUpload();\n                            setSelectedType(currentType);\n                            setSelectedCategory(currentCategory);\n                            console.log('Reset upload state after invalid file type for moments');\n                        }, 100);\n                        return;\n                    }\n                }\n                // Handle image preview and set phase\n                const reader = new FileReader();\n                reader.onload = (e)=>{\n                    var _e_target;\n                    if ((_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result) {\n                        setPreviewImage(e.target.result);\n                        console.log('Preview image set for file:', file.name);\n                    }\n                };\n                reader.readAsDataURL(file);\n                // Create a local reference to the file for use in the timeout\n                const currentFile = file;\n                // Double-check that the file is set in the state before proceeding\n                setTimeout(()=>{\n                    // Check if the file is in the state\n                    if (!state.file) {\n                        console.log('File not found in state before moving to personalDetails, setting it again');\n                        // Try setting the file again\n                        setFile(currentFile);\n                        // Add another timeout to ensure the file is set\n                        setTimeout(()=>{\n                            if (!state.file) {\n                                console.log('File still not in state, setting it one more time');\n                                setFile(currentFile);\n                            } else {\n                                console.log('File confirmed in state after second attempt:', state.file.name);\n                            }\n                            // New flow logic: For moments (stories), skip personal details and go directly to face verification\n                            if (state.isMoments) {\n                                console.log('Moments image upload: skipping personal details, going directly to face verification');\n                                console.log('UPLOAD MANAGER - selectedType:', selectedType);\n                                console.log('UPLOAD MANAGER - state.isMoments:', state.isMoments);\n                                console.log('UPLOAD MANAGER - state.mediaSubtype:', state.mediaSubtype);\n                                setPhase('faceVerification');\n                            } else {\n                                console.log('Moving to personalDetails phase for photo/image');\n                                console.log('UPLOAD MANAGER - selectedType:', selectedType);\n                                console.log('UPLOAD MANAGER - state.isMoments:', state.isMoments);\n                                console.log('UPLOAD MANAGER - state.mediaSubtype:', state.mediaSubtype);\n                                setPhase('personalDetails');\n                            }\n                        }, 100);\n                    } else {\n                        console.log('File confirmed in state:', state.file.name);\n                        // New flow logic: For moments (stories), skip personal details and go directly to face verification\n                        if (state.isMoments) {\n                            console.log('Moments image upload: skipping personal details, going directly to face verification');\n                            console.log('UPLOAD MANAGER - selectedType:', selectedType);\n                            console.log('UPLOAD MANAGER - state.isMoments:', state.isMoments);\n                            console.log('UPLOAD MANAGER - state.mediaSubtype:', state.mediaSubtype);\n                            setPhase('faceVerification');\n                        } else {\n                            console.log('Moving to personalDetails phase for photo/image');\n                            console.log('UPLOAD MANAGER - selectedType:', selectedType);\n                            console.log('UPLOAD MANAGER - state.isMoments:', state.isMoments);\n                            console.log('UPLOAD MANAGER - state.mediaSubtype:', state.mediaSubtype);\n                            setPhase('personalDetails');\n                        }\n                    }\n                }, 100);\n            }\n        };\n        // Trigger the file dialog\n        input.click();\n    };\n    // Handle personal details completed\n    const handlePersonalDetailsCompleted = (details)=>{\n        console.log('Personal details completed:', details);\n        // Store the personal details in local state for component persistence\n        setLocalPersonalDetails(details);\n        // Validate that we have a title\n        if (!details.caption || !details.caption.trim()) {\n            console.error('Caption/title is empty, this should not happen');\n            // Go back to personal details to fix this\n            setPhase('personalDetails');\n            return;\n        }\n        // Set the title in the upload context\n        setTitle(details.caption.trim());\n        // Also store in global context for persistence (this is the upload context function)\n        setPersonalDetails(details);\n        // Check if we have a file in the state\n        if (!state.file) {\n            console.error('No file found in state after personal details');\n            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Upload Error', 'Something went wrong with your file upload. Please try again.');\n            setPhase('typeSelection');\n            return;\n        }\n        console.log('File confirmed in state after personal details:', state.file.name);\n        console.log('Personal details set successfully');\n        console.log('Title set to:', details.caption.trim());\n        console.log('Current selectedType:', selectedType);\n        console.log('Current mediaSubtype:', state.mediaSubtype);\n        // New flow logic based on backend requirements:\n        // - Moments (stories): Skip personal details, go directly to face verification\n        // - Photos: Go to face verification after personal details (no vendor details)\n        // - Videos: Go to vendor details after personal details\n        if (state.mediaType === 'photo') {\n            console.log('Photo upload: proceeding to face verification (no vendor details needed)');\n            setPhase('faceVerification');\n        } else if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n            console.log('Moments upload: proceeding to face verification (no vendor details needed)');\n            setPhase('faceVerification');\n        } else {\n            // For videos (flashes, glimpses, movies), proceed to vendor details\n            console.log('Video upload: proceeding to vendor details');\n            setPhase('vendorDetails');\n        }\n    };\n    // Handle vendor details completed\n    const handleVendorDetailsCompleted = (vendorDetails)=>{\n        // console.log('Vendor details completed:', vendorDetails);\n        // Normalize vendor details to ensure consistent field names\n        const normalizedVendorDetails = {\n            ...vendorDetails\n        };\n        // Ensure we have both frontend and backend field names for makeup artist and decorations\n        if (vendorDetails.makeupArtist) {\n            normalizedVendorDetails.makeup_artist = vendorDetails.makeupArtist;\n        } else if (vendorDetails.makeup_artist) {\n            normalizedVendorDetails.makeupArtist = vendorDetails.makeup_artist;\n        }\n        if (vendorDetails.decorations) {\n            normalizedVendorDetails.decoration = vendorDetails.decorations;\n        } else if (vendorDetails.decoration) {\n            normalizedVendorDetails.decorations = vendorDetails.decoration;\n        }\n        // Store the normalized vendor details for persistence between screens\n        setVendorDetailsData(normalizedVendorDetails);\n        // Also store in the ref for Edge browser compatibility\n        vendorDetailsRef.current = normalizedVendorDetails;\n        // Store vendor details in localStorage for persistence\n        try {\n            localStorage.setItem('wedzat_vendor_details', JSON.stringify(normalizedVendorDetails));\n            console.log('UPLOAD MANAGER - Stored vendor details in localStorage');\n        } catch (error) {\n            console.error('UPLOAD MANAGER - Failed to store vendor details in localStorage:', error);\n        }\n        // Save the current video_category before setting vendor details\n        const currentVideoCategory = state.detailFields.video_category;\n        console.log('UPLOAD MANAGER - Saving video_category before vendor details:', currentVideoCategory);\n        // Store video_category in localStorage\n        if (currentVideoCategory) {\n            try {\n                localStorage.setItem('wedzat_video_category', currentVideoCategory);\n                console.log('UPLOAD MANAGER - Stored video_category in localStorage:', currentVideoCategory);\n            } catch (error) {\n                console.error('UPLOAD MANAGER - Failed to store video_category in localStorage:', error);\n            }\n        }\n        // Store in global context for persistence\n        setVendorDetails(normalizedVendorDetails);\n        // Explicitly set each vendor detail field\n        Object.entries(normalizedVendorDetails).forEach((param)=>{\n            let [vendorType, details] = param;\n            if (details && details.name && details.mobileNumber) {\n                setDetailField(\"vendor_\".concat(vendorType, \"_name\"), details.name);\n                setDetailField(\"vendor_\".concat(vendorType, \"_contact\"), details.mobileNumber);\n            }\n        });\n        // Re-set the video_category after vendor details to ensure it's preserved\n        if (currentVideoCategory) {\n            console.log('UPLOAD MANAGER - Re-setting video_category after vendor details:', currentVideoCategory);\n            setTimeout(()=>{\n                setDetailField('video_category', currentVideoCategory);\n            }, 100);\n        }\n        // Log all detail fields after setting vendor details\n        setTimeout(()=>{\n            console.log('All detail fields after vendor details:', state.detailFields);\n            console.log('Detail fields count:', Object.keys(state.detailFields).length);\n            console.log('Normalized vendor details:', normalizedVendorDetails);\n        }, 200);\n        // Add a small delay to ensure the state is updated before proceeding\n        // This helps with cross-browser compatibility, especially in Edge\n        setTimeout(()=>{\n            // Double-check that we have at least 4 vendor details before proceeding\n            const vendorNameFields = Object.keys(state.detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_name'));\n            const vendorContactFields = Object.keys(state.detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_contact'));\n            console.log('UPLOAD MANAGER - Vendor name fields:', vendorNameFields.length);\n            console.log('UPLOAD MANAGER - Vendor contact fields:', vendorContactFields.length);\n            // Edge browser workaround - directly set vendor details in the state\n            if ( true && /Edge|Edg/.test(window.navigator.userAgent)) {\n                console.log('UPLOAD MANAGER - Edge browser detected, applying direct vendor details workaround');\n                // Create vendor detail fields directly in the state\n                // This is a workaround for Edge browser where the state update doesn't properly preserve vendor details\n                Object.entries(normalizedVendorDetails).forEach((param)=>{\n                    let [vendorType, details] = param;\n                    if (details && details.name && details.mobileNumber) {\n                        // Set the vendor details directly in the state\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                        // Also set the normalized version\n                        const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                        if (normalizedType !== vendorType) {\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                        }\n                        console.log(\"UPLOAD MANAGER - Edge workaround: Added vendor \".concat(vendorType, \" directly to state\"));\n                    }\n                });\n                // Re-set the video_category directly\n                if (currentVideoCategory) {\n                    state.detailFields.video_category = currentVideoCategory;\n                    console.log('UPLOAD MANAGER - Edge workaround: Re-set video_category directly:', currentVideoCategory);\n                }\n            }\n            // Proceed to face verification\n            setPhase('faceVerification');\n        }, 300);\n    };\n    // Handle thumbnail selection\n    const handleThumbnailSelected = (thumbnailFile)=>{\n        if (thumbnailFile) {\n            // Set the thumbnail in the context\n            setThumbnail(thumbnailFile);\n            console.log('Thumbnail selected:', thumbnailFile.name);\n        } else {\n            console.log('No thumbnail selected, using auto-generated thumbnail');\n        }\n        // New flow logic: For moments (stories), skip personal details and go directly to face verification\n        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n            console.log('Moments upload: skipping personal details, going directly to face verification');\n            setPhase('faceVerification');\n        } else {\n            // For photos and videos, go to personal details\n            console.log('Photo/Video upload: proceeding to personal details');\n            setPhase('personalDetails');\n        }\n    };\n    // Function to proceed with upload after vendor details are applied\n    const proceedWithUpload = (videoCategory)=>{\n        // For moments (stories), this function should not be called, but add safety check\n        if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n            console.log('UPLOAD MANAGER - Moments detected in proceedWithUpload, calling startUpload directly');\n            startUpload();\n            return;\n        }\n        // Double-check that we have a title before changing to uploading phase\n        if (!state.title || !state.title.trim()) {\n            console.error('Title is missing before upload, setting it from personal details');\n            // Try to set the title from personal details\n            if (personalDetails.caption && personalDetails.caption.trim()) {\n                // console.log('Setting personal details from local state:', personalDetails);\n                // Use the global context function to set all personal details at once\n                setPersonalDetails(personalDetails);\n                // Explicitly set the title as well\n                setTitle(personalDetails.caption.trim());\n            } else {\n                console.error('No title in personal details either, going back to personal details');\n                setPhase('personalDetails');\n                return;\n            }\n        }\n        // Edge browser workaround - directly set vendor details in the state\n        if ( true && /Edge|Edg/.test(window.navigator.userAgent)) {\n            console.log('UPLOAD MANAGER - Edge browser detected in face verification, applying vendor details workaround');\n            // Get the vendor details from the vendor details data\n            const vendorDetailsData = vendorDetailsRef.current;\n            if (vendorDetailsData) {\n                console.log('UPLOAD MANAGER - Edge workaround: Using vendor details from ref:', vendorDetailsData);\n                // Create vendor detail fields directly in the state\n                Object.entries(vendorDetailsData).forEach((param)=>{\n                    let [vendorType, details] = param;\n                    if (details && details.name && details.mobileNumber) {\n                        // Set the vendor details directly in the state\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                        // Also set the normalized version\n                        const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                        if (normalizedType !== vendorType) {\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                        }\n                        console.log(\"UPLOAD MANAGER - Edge workaround: Added vendor \".concat(vendorType, \" directly to state\"));\n                    }\n                });\n            }\n        }\n        // For videos, check if we have a video_category\n        if (state.mediaType === 'video') {\n            console.log(\"UPLOAD MANAGER - Checking video_category before upload\");\n            console.log(\"UPLOAD MANAGER - Current video_category: \".concat(state.detailFields.video_category || 'Not set'));\n            console.log(\"UPLOAD MANAGER - Current mediaSubtype: \".concat(state.mediaSubtype));\n            console.log(\"UPLOAD MANAGER - Selected category: \".concat(selectedCategory || 'Not set'));\n            // Special handling for glimpses\n            if (state.mediaSubtype === 'glimpse') {\n                console.log(\"UPLOAD MANAGER - Special handling for glimpses\");\n                // If we don't have a video_category yet, try to set it from selectedCategory\n                if (!state.detailFields.video_category && selectedCategory) {\n                    // Map the UI category to the backend video_category\n                    let videoCategory = '';\n                    if (selectedCategory === 'my_wedding_videos') {\n                        videoCategory = 'my_wedding';\n                    } else if (selectedCategory === 'wedding_vlog') {\n                        videoCategory = 'wedding_vlog';\n                    } else if (selectedCategory === 'friends_family_videos') {\n                        videoCategory = 'friends_family_video';\n                    }\n                    if (videoCategory) {\n                        console.log(\"UPLOAD MANAGER - Setting video_category for glimpse: \".concat(videoCategory));\n                        setDetailField('video_category', videoCategory);\n                    }\n                } else {\n                    console.log(\"UPLOAD MANAGER - Glimpse already has video_category: \".concat(state.detailFields.video_category));\n                }\n            }\n            // If we still don't have a video_category, use a default based on selectedCategory\n            if (!state.detailFields.video_category && selectedCategory) {\n                console.log(\"UPLOAD MANAGER - No video_category set, using selectedCategory: \".concat(selectedCategory));\n                // Map the UI category to the backend video_category\n                let videoCategory = '';\n                if (selectedCategory === 'my_wedding_videos') {\n                    videoCategory = 'my_wedding';\n                } else if (selectedCategory === 'wedding_vlog') {\n                    videoCategory = 'wedding_vlog';\n                } else if (selectedCategory === 'friends_family_videos') {\n                    videoCategory = 'friends_family_video';\n                }\n                if (videoCategory) {\n                    console.log(\"UPLOAD MANAGER - Setting video_category from selectedCategory: \".concat(videoCategory));\n                    setDetailField('video_category', videoCategory);\n                }\n            }\n            // Final check - if we still don't have a video_category, use a default\n            if (!state.detailFields.video_category) {\n                console.log('No video_category found, using a default one');\n                // Use 'my_wedding' as a default category instead of asking the user again\n                setDetailField('video_category', 'my_wedding');\n                console.log('Set default video_category to my_wedding');\n            }\n        }\n        // Edge browser workaround - directly set vendor details in the state before upload\n        if ( true && /Edge|Edg/.test(window.navigator.userAgent)) {\n            console.log('UPLOAD MANAGER - Edge browser detected before upload, applying vendor details workaround');\n            // Get the vendor details from the vendor details data\n            const vendorDetailsData = vendorDetailsRef.current;\n            if (vendorDetailsData && Object.keys(vendorDetailsData).length > 0) {\n                console.log('UPLOAD MANAGER - Edge workaround: Using vendor details from ref before upload:', vendorDetailsData);\n                // Create vendor detail fields directly in the state\n                Object.entries(vendorDetailsData).forEach((param)=>{\n                    let [vendorType, details] = param;\n                    if (details && details.name && details.mobileNumber) {\n                        // Set the vendor details directly in the state\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                        state.detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                        // Also set the normalized version\n                        const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                        if (normalizedType !== vendorType) {\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                            state.detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                        }\n                        console.log(\"UPLOAD MANAGER - Edge workaround: Added vendor \".concat(vendorType, \" directly to state before upload\"));\n                    }\n                });\n            }\n        }\n        // Check if we have a file before proceeding\n        if (!state.file) {\n            console.error('No file found in state before upload');\n            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Upload Error', 'No file selected. Please select a file to upload.');\n            // Go back to type selection to start over\n            setPhase('typeSelection');\n            return;\n        }\n        // Now we can proceed to uploading phase\n        setPhase('uploading');\n        // Log the current state before starting upload\n        console.log('Current state before upload:', {\n            file: state.file ? state.file.name : 'No file',\n            mediaType: state.mediaType,\n            mediaSubtype: state.mediaSubtype,\n            title: state.title,\n            description: state.description,\n            detailFields: state.detailFields,\n            detailFieldsCount: Object.keys(state.detailFields).length\n        });\n        // Double-check that we're using the correct category\n        console.log(\"UPLOAD MANAGER - Final check - Selected type: \".concat(selectedType));\n        console.log(\"UPLOAD MANAGER - Final check - MediaSubtype in state: \".concat(state.mediaSubtype));\n        // If the mediaSubtype doesn't match what we expect based on the selected type, fix it\n        if (selectedType && state.mediaSubtype !== getMediaSubtypeFromSelectedType(selectedType)) {\n            console.log(\"UPLOAD MANAGER - WARNING: MediaSubtype mismatch detected!\");\n            console.log(\"UPLOAD MANAGER - Expected mediaSubtype based on selected type: \".concat(getMediaSubtypeFromSelectedType(selectedType)));\n            console.log(\"UPLOAD MANAGER - Actual mediaSubtype in state: \".concat(state.mediaSubtype));\n            console.log(\"UPLOAD MANAGER - Correcting category before upload...\");\n            // Get the corrected category\n            const correctedCategory = getMediaSubtypeFromSelectedType(selectedType);\n            console.log(\"UPLOAD MANAGER - Category corrected to: \".concat(correctedCategory));\n            // Get the video_category from the original selection\n            // We need to map it to the correct backend value\n            let videoCategory = '';\n            if (selectedCategory === 'my_wedding_videos') {\n                videoCategory = 'my_wedding';\n            } else if (selectedCategory === 'wedding_vlog') {\n                videoCategory = 'wedding_vlog';\n            } else if (selectedCategory === 'friends_family_videos') {\n                videoCategory = 'friends_family_video';\n            }\n            console.log(\"UPLOAD MANAGER - Original selected category: \".concat(selectedCategory));\n            console.log(\"UPLOAD MANAGER - Mapped to backend video_category: \".concat(videoCategory));\n            // Start the upload process with the corrected category and video_category\n            startUploadWithCategory(correctedCategory, videoCategory);\n        } else {\n            // Get the video_category from the state\n            const finalVideoCategory = videoCategory || state.detailFields.video_category || 'my_wedding';\n            console.log(\"UPLOAD MANAGER - Using video_category for upload: \".concat(finalVideoCategory));\n            // Start the upload process with the current category and video_category\n            startUploadWithCategory(state.mediaSubtype, finalVideoCategory).then(()=>{\n                // Upload completed successfully\n                console.log('Upload completed successfully');\n            }).catch((error)=>{\n                console.error('Upload failed:', error);\n            });\n        }\n    };\n    // Handle moments upload with dedicated flow\n    const handleMomentsUpload = async ()=>{\n        console.log('UPLOAD MANAGER - Starting dedicated moments upload flow');\n        if (!state.file) {\n            console.error('No file found for moments upload');\n            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Upload Error', 'No file selected for upload.');\n            return;\n        }\n        // Set uploading state\n        setPhase('uploading');\n        try {\n            console.log('UPLOAD MANAGER - Uploading moments file:', state.file.name);\n            // For moments, always use 'story' subtype - backend should set is_story = true\n            const momentsSubtype = 'story';\n            console.log('UPLOAD MANAGER - Using subtype for moments:', momentsSubtype);\n            console.log('UPLOAD MANAGER - Media type:', state.mediaType);\n            console.log('UPLOAD MANAGER - Backend should set is_story = true and handle database constraints properly');\n            // Use upload service directly with minimal data for moments\n            const result = await _services_api__WEBPACK_IMPORTED_MODULE_13__.uploadService.handleUpload(state.file, state.mediaType, momentsSubtype, state.title || state.file.name.replace(/\\.[^/.]+$/, \"\"), '', [], {}, state.duration, state.thumbnail, (progress)=>{\n                console.log(\"Moments upload progress: \".concat(progress, \"%\"));\n            // You can add progress updates here if needed\n            });\n            console.log('UPLOAD MANAGER - Moments upload completed successfully:', result);\n            // Show success and reset\n            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showSuccessAlert)('Upload Successful', 'Your moment has been uploaded successfully!');\n            resetUpload();\n            setPhase('typeSelection');\n        } catch (error) {\n            console.error('UPLOAD MANAGER - Moments upload failed:', error);\n            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Upload Failed', error instanceof Error ? error.message : 'Failed to upload moment. Please try again.');\n            setPhase('faceVerification'); // Go back to face verification\n        }\n    };\n    // Handle face verification completed and start upload\n    const handleFaceVerificationCompleted = ()=>{\n        console.log('Face verification completed, starting upload process');\n        // Check if we have a file in the state\n        if (!state.file) {\n            console.error('No file found in state after face verification');\n            (0,_utils_alertUtils__WEBPACK_IMPORTED_MODULE_11__.showErrorAlert)('Upload Error', 'Something went wrong with your file upload. Please try again.');\n            setPhase('typeSelection');\n            return;\n        }\n        console.log('File confirmed in state after face verification:', state.file.name);\n        // For moments (stories), use completely separate upload flow\n        if (selectedType === 'moments') {\n            console.log('UPLOAD MANAGER - Moments detected after face verification, using dedicated moments upload flow');\n            // Set a default title if not already set (using filename without extension)\n            if (!state.title || !state.title.trim()) {\n                const defaultTitle = state.file.name.replace(/\\.[^/.]+$/, \"\"); // Remove file extension\n                setTitle(defaultTitle);\n                console.log('UPLOAD MANAGER - Set default title for moments:', defaultTitle);\n            }\n            // Call dedicated moments upload function\n            setTimeout(()=>{\n                handleMomentsUpload();\n            }, 100);\n            return;\n        }\n        // Try to get vendor details from localStorage first\n        let vendorDetailsData = vendorDetailsRef.current;\n        // If not in ref, try localStorage\n        if (!vendorDetailsData || Object.keys(vendorDetailsData).length === 0) {\n            try {\n                const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n                if (storedVendorDetails) {\n                    vendorDetailsData = JSON.parse(storedVendorDetails);\n                    console.log('UPLOAD MANAGER - Retrieved vendor details from localStorage:', storedVendorDetails);\n                    // Update the ref with the localStorage data\n                    vendorDetailsRef.current = vendorDetailsData;\n                    // Log the vendor details we found\n                    console.log(\"UPLOAD MANAGER - Found \".concat(Object.keys(vendorDetailsData).length, \" vendor details in localStorage\"));\n                    Object.entries(vendorDetailsData).forEach((param)=>{\n                        let [vendorType, details] = param;\n                        if (details && details.name && details.mobileNumber) {\n                            console.log(\"UPLOAD MANAGER - Vendor \".concat(vendorType, \": \").concat(details.name, \" (\").concat(details.mobileNumber, \")\"));\n                        }\n                    });\n                } else {\n                    console.log('UPLOAD MANAGER - No vendor details found in localStorage');\n                }\n            } catch (error) {\n                console.error('UPLOAD MANAGER - Failed to retrieve vendor details from localStorage:', error);\n            }\n        } else {\n            console.log(\"UPLOAD MANAGER - Using \".concat(Object.keys(vendorDetailsData).length, \" vendor details from ref\"));\n        }\n        // Try to get video_category from localStorage\n        let videoCategory = state.detailFields.video_category;\n        if (!videoCategory) {\n            try {\n                const storedVideoCategory = localStorage.getItem('wedzat_video_category');\n                if (storedVideoCategory) {\n                    videoCategory = storedVideoCategory;\n                    console.log('UPLOAD MANAGER - Retrieved video_category from localStorage:', videoCategory);\n                    // Set it in the state\n                    setDetailField('video_category', videoCategory);\n                }\n            } catch (error) {\n                console.error('UPLOAD MANAGER - Failed to retrieve video_category from localStorage:', error);\n            }\n        }\n        // Ensure vendor details are present\n        if (vendorDetailsData) {\n            console.log('UPLOAD MANAGER - Applying vendor details to state');\n            // Create a batch of all detail fields to update at once\n            const detailFieldUpdates = {};\n            let completeVendorCount = 0;\n            // Re-apply vendor details to ensure they're in the state\n            Object.entries(vendorDetailsData).forEach((param)=>{\n                let [vendorType, details] = param;\n                if (details && details.name && details.mobileNumber) {\n                    // Add to the batch\n                    detailFieldUpdates[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                    detailFieldUpdates[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                    completeVendorCount++;\n                    // Also set normalized versions\n                    const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                    if (normalizedType !== vendorType) {\n                        detailFieldUpdates[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                        detailFieldUpdates[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                    }\n                }\n            });\n            // Apply all updates at once\n            console.log(\"UPLOAD MANAGER - Applying \".concat(completeVendorCount, \" complete vendor details to state\"));\n            console.log('UPLOAD MANAGER - Detail field updates:', JSON.stringify(detailFieldUpdates));\n            // Apply each update individually to ensure they're all set\n            Object.entries(detailFieldUpdates).forEach((param)=>{\n                let [field, value] = param;\n                setDetailField(field, value);\n            });\n            // Add a delay before proceeding to ensure state updates are applied\n            setTimeout(()=>{\n                console.log('UPLOAD MANAGER - Vendor details applied to state, proceeding with upload');\n                proceedWithUpload(videoCategory);\n            }, 500);\n        } else {\n            console.log('UPLOAD MANAGER - No vendor details found, proceeding with upload');\n            proceedWithUpload(videoCategory);\n        }\n    // This code has been moved to the proceedWithUpload function\n    };\n    // Handle going back to personal details from upload error\n    const handleBackToPersonalDetails = ()=>{\n        // console.log('Going back to personal details with stored data:', personalDetails);\n        // Make sure the personal details are set in the context\n        if (personalDetails.caption && personalDetails.caption.trim()) {\n            // Use the global context function to set all personal details at once\n            setPersonalDetails(personalDetails);\n        }\n        setPhase('personalDetails');\n    };\n    // Handle close modal\n    const handleClose = ()=>{\n        // Check if upload was successful and call onUploadComplete\n        if (state.step === 'complete' && onUploadComplete) {\n            console.log('Upload completed successfully, calling onUploadComplete callback');\n            onUploadComplete();\n        }\n        // Reset the phase first\n        setPhase('closed');\n        // Call the onClose callback if provided\n        if (onClose) {\n            onClose();\n        }\n        // Reset the upload state after a short delay to ensure the modal is closed first\n        setTimeout(()=>{\n            resetUpload();\n            console.log('Upload state reset after modal close');\n        }, 100);\n    };\n    // Render selected phase component\n    if (phase === 'closed') {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            phase === 'typeSelection' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UploadTypeSelection__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                onNext: handleTypeSelected,\n                onClose: handleClose\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1461,\n                columnNumber: 9\n            }, undefined),\n            phase === 'categorySelection' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VideoCategorySelection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onNext: handleCategorySelected,\n                onBack: ()=>setPhase('typeSelection'),\n                onUpload: handleCategorySelected,\n                onThumbnailUpload: handleThumbnailUpload,\n                onClose: handleClose,\n                mediaType: state.mediaType,\n                selectedType: selectedType\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1468,\n                columnNumber: 9\n            }, undefined),\n            phase === 'thumbnailSelection' && state.file && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ThumbnailSelection__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                videoFile: state.file,\n                onNext: handleThumbnailSelected,\n                onBack: ()=>{\n                    // Go back to category selection instead of triggering file upload again\n                    if ([\n                        'flashes',\n                        'glimpses',\n                        'movies'\n                    ].includes(selectedType)) {\n                        setPhase('categorySelection');\n                    } else {\n                        // For moments, go back to type selection\n                        setPhase('typeSelection');\n                    }\n                },\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1480,\n                columnNumber: 9\n            }, undefined),\n            phase === 'personalDetails' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PersonalDetails__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onNext: handlePersonalDetailsCompleted,\n                onBack: ()=>{\n                    // Go back to thumbnail selection for videos\n                    if (state.mediaType === 'video' && state.file) {\n                        setPhase('thumbnailSelection');\n                    } else {\n                        // For photos, go back to type selection\n                        setPhase('typeSelection');\n                    }\n                },\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                },\n                previewImage: previewImage,\n                videoFile: state.mediaType === 'video' ? state.file : null,\n                mediaType: state.mediaType,\n                contentType: getContentType(),\n                initialDetails: personalDetails\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1501,\n                columnNumber: 9\n            }, undefined),\n            phase === 'vendorDetails' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VendorDetails__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                onNext: handleVendorDetailsCompleted,\n                onBack: ()=>setPhase('personalDetails'),\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                },\n                initialVendorDetails: vendorDetailsData,\n                videoCategory: (()=>{\n                    const category = state.detailFields.video_category || 'my_wedding';\n                    console.log('UPLOAD MANAGER - Passing video category to VendorDetails:', category);\n                    return category;\n                })()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1526,\n                columnNumber: 9\n            }, undefined),\n            phase === 'faceVerification' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FaceVerification__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                onUpload: handleFaceVerificationCompleted,\n                onBack: ()=>{\n                    // New flow logic for back navigation:\n                    // - Moments: Go back to thumbnail selection (or type selection for images)\n                    // - Photos: Go back to personal details\n                    // - Videos: Go back to vendor details\n                    if (selectedType === 'moments' || state.mediaSubtype === 'story') {\n                        // For moments, go back to thumbnail selection for videos, or type selection for images\n                        if (state.mediaType === 'video') {\n                            setPhase('thumbnailSelection');\n                        } else {\n                            setPhase('typeSelection');\n                        }\n                    } else if (state.mediaType === 'photo') {\n                        setPhase('personalDetails');\n                    } else {\n                        setPhase('vendorDetails');\n                    }\n                },\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1544,\n                columnNumber: 9\n            }, undefined),\n            (phase === 'uploading' || phase === 'complete') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UploadProgress__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                onClose: ()=>{\n                    // Completely reset the state before closing\n                    resetUpload();\n                    handleClose();\n                },\n                onGoBack: handleBackToPersonalDetails\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\components\\\\upload\\\\UploadManager.tsx\",\n                lineNumber: 1573,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(UploadManager, \"idPi3SGLJtrz87d+SNzKuUjYbcw=\", false, function() {\n    return [\n        _contexts_UploadContexts__WEBPACK_IMPORTED_MODULE_9__.useUpload,\n        _hooks_useMedia__WEBPACK_IMPORTED_MODULE_12__.useMediaUpload\n    ];\n});\n_c = UploadManager;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UploadManager);\nvar _c;\n$RefreshReg$(_c, \"UploadManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/upload/UploadManager.tsx\n"));

/***/ })

});