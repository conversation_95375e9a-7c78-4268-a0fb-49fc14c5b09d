"use client";
import React, { useState, useEffect, Suspense } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import {
  TopNavigation,
  SideNavigation,
} from "../../../components/HomeDashboard/Navigation";
import { Search } from "lucide-react";
import searchService, { SearchResults, SearchParams } from "../../../services/search-api";
import SearchResultsDisplay from "../../../components/Search/SearchResultsDisplay";
import SearchFilters from "../../../components/Search/SearchFilters";

function SearchContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const query = searchParams?.get("q") || "";
  const types = searchParams?.get("types") || "";
  const username = searchParams?.get("username") || "";
  const tags = searchParams?.get("tags") || "";
  const place = searchParams?.get("place") || "";
  const sortBy = searchParams?.get("sort_by") || "";
  const sortOrder = searchParams?.get("sort_order") || "";

  const [sidebarExpanded, setSidebarExpanded] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [searchResults, setSearchResults] = useState<SearchResults | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Perform search when component mounts or search parameters change
  useEffect(() => {
    setIsClient(true);

    const performSearch = async () => {
      if (!query.trim() && !username.trim() && !tags.trim() && !place.trim()) {
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const searchParams: SearchParams = {
          q: query || undefined,
          types: types || undefined,
          username: username || undefined,
          tags: tags || undefined,
          place: place || undefined,
          sort_by: sortBy || undefined,
          sort_order: (sortOrder as 'asc' | 'desc') || undefined,
          limit: 10
        };

        console.log('Performing search with params:', searchParams);
        const results = await searchService.searchContent(searchParams);
        console.log('Search results received:', results);

        setSearchResults(results);
      } catch (err: any) {
        console.error('Search failed:', err);
        setError(err.message || 'Failed to search content');
        setSearchResults(null);
      } finally {
        setLoading(false);
      }
    };

    performSearch();
  }, [query, types, username, tags, place, sortBy, sortOrder]);

  if (!isClient) {
    return null; // Don't render anything on the server
  }

  return (
    <div className={isClient ? "flex flex-col min-h-screen bg-white w-full" : "opacity-0"}>
      {/* Top Navigation */}
      <TopNavigation />

      <div className="flex flex-1 bg-white">
        {/* Left Sidebar */}
        <SideNavigation
          expanded={sidebarExpanded}
          onExpand={() => setSidebarExpanded(true)}
          onCollapse={() => setSidebarExpanded(false)}
        />

        {/* Main Content */}
        <main
          className={`flex-1 p-4 bg-white mt-20 ${
            sidebarExpanded ? "md:ml-48" : "md:ml-20"
          }`}
          style={{
            transition: "all 300ms ease-in-out",
            overflowY: "auto",
            overflowX: "hidden",
          }}
        >
          <div className="max-w-7xl mx-auto">
            <div className="mb-8">
              <div className="flex items-center justify-between mb-4">
                <h1 className="text-2xl font-bold">
                  Search Results{query ? ` for "${query}"` : ''}
                </h1>
                <SearchFilters />
              </div>
            </div>

            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-600"></div>
                <span className="ml-3 text-gray-600">Searching...</span>
              </div>
            ) : error ? (
              <div className="text-center py-12">
                <div className="text-5xl mb-4">⚠️</div>
                <h2 className="text-xl font-semibold mb-2 text-red-600">Search Error</h2>
                <p className="text-gray-600 mb-4">{error}</p>
                <button
                  onClick={() => window.location.reload()}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  Try Again
                </button>
              </div>
            ) : !searchResults ? (
              <div className="text-center py-12">
                <div className="text-5xl mb-4">🔍</div>
                <h2 className="text-xl font-semibold mb-2">Start Your Search</h2>
                <p className="text-gray-600">
                  Enter keywords above to search for videos, photos, and more.
                </p>
              </div>
            ) : (
              <div className="space-y-8">
                {/* Check if we have any results at all */}
                {(!searchResults.flashes?.length &&
                  !searchResults.glimpses?.length &&
                  !searchResults.movies?.length &&
                  !searchResults.photos?.length) ? (
                  <div className="text-center py-12">
                    <div className="text-5xl mb-4">🔍</div>
                    <h2 className="text-xl font-semibold mb-2">No results found</h2>
                    <p className="text-gray-600 mb-4">
                      We couldn't find any matches for "{query}". Try different keywords or check your spelling.
                    </p>
                    <div className="text-sm text-gray-500">
                      <p>Try searching for:</p>
                      <ul className="mt-2 space-y-1">
                        <li>• Wedding videos or photos</li>
                        <li>• User names</li>
                        <li>• Tags like "ceremony", "reception", "decoration"</li>
                        <li>• Places like "Mumbai", "Delhi", "Bangalore"</li>
                      </ul>
                    </div>
                  </div>
                ) : (
                  <>
                    {/* Display each category if it has results */}
                    {searchResults.flashes && searchResults.flashes.length > 0 && (
                      <SearchResultsDisplay
                        title="Flashes"
                        items={searchResults.flashes}
                        contentType="flashes"
                        hasNextPage={searchResults.pagination?.flashes?.has_next_page}
                        searchParams={{ q: query, username, tags, place, sort_by: sortBy, sort_order: sortOrder }}
                      />
                    )}

                    {searchResults.glimpses && searchResults.glimpses.length > 0 && (
                      <SearchResultsDisplay
                        title="Glimpses"
                        items={searchResults.glimpses}
                        contentType="glimpses"
                        hasNextPage={searchResults.pagination?.glimpses?.has_next_page}
                        searchParams={{ q: query, username, tags, place, sort_by: sortBy, sort_order: sortOrder }}
                      />
                    )}

                    {searchResults.movies && searchResults.movies.length > 0 && (
                      <SearchResultsDisplay
                        title="Movies"
                        items={searchResults.movies}
                        contentType="movies"
                        hasNextPage={searchResults.pagination?.movies?.has_next_page}
                        searchParams={{ q: query, username, tags, place, sort_by: sortBy, sort_order: sortOrder }}
                      />
                    )}

                    {searchResults.photos && searchResults.photos.length > 0 && (
                      <SearchResultsDisplay
                        title="Photos"
                        items={searchResults.photos}
                        contentType="photos"
                        hasNextPage={searchResults.pagination?.photos?.has_next_page}
                        searchParams={{ q: query, username, tags, place, sort_by: sortBy, sort_order: sortOrder }}
                      />
                    )}
                  </>
                )}
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
}

export default function SearchPage() {
  return (
    <Suspense fallback={
      <div className="flex flex-col min-h-screen bg-white">
        <TopNavigation />
        <div className="flex flex-1 items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-600"></div>
        </div>
      </div>
    }>
      <SearchContent />
    </Suspense>
  );
}
