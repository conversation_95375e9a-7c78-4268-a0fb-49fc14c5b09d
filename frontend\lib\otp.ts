// Helper functions for OTP generation and verification

/**
 * Generates a random numeric OTP of specified length
 * @param length The length of the OTP to generate (default: 6)
 * @returns A string containing the generated OTP
 */
export function generateOTP(length: number = 6): string {
    let otp: string = '';
    for (let i = 0; i < length; i++) {
      otp += Math.floor(Math.random() * 10).toString();
    }
    console.log(`Generated OTP: ${otp}`); // For debugging purposes
    return otp;
  }