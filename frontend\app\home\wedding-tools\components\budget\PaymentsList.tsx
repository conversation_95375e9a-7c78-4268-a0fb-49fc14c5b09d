"use client";
import React, { useState } from "react";
import { Edit } from "lucide-react";
import { BudgetPayment, BudgetExpense } from "../../types";
import { formatCurrency, formatDate } from "../../utils";

interface PaymentsListProps {
  payments: BudgetPayment[];
  expenses: BudgetExpense[];
}

const PaymentsList: React.FC<PaymentsListProps> = ({ payments, expenses }) => {
  const [filter, setFilter] = useState<'all' | 'paid' | 'pending'>('all');
  const [sortBy, setSortBy] = useState<string>('date');

  // Get expense name by ID
  const getExpenseName = (expenseId: string): string => {
    const expense = expenses.find(e => e.expense_id === expenseId);
    return expense ? expense.name : 'Unknown';
  };

  // Get category name by expense ID
  const getCategoryName = (expenseId: string): string => {
    const expense = expenses.find(e => e.expense_id === expenseId);
    return expense ? (expense.category_name || 'Unknown') : 'Unknown';
  };

  // Filter payments based on selected filter
  const filteredPayments = payments.filter(() => {
    if (filter === 'all') return true;
    if (filter === 'paid') return true; // All payments in this list are paid
    return false;
  });

  // Sort payments based on selected sort option
  const sortedPayments = [...filteredPayments].sort((a, b) => {
    if (sortBy === 'date') {
      return new Date(b.payment_date).getTime() - new Date(a.payment_date).getTime();
    }
    return 0;
  });

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div className="flex space-x-4">
          <button
            onClick={() => setFilter('all')}
            className={`px-4 py-1 rounded-md ${
              filter === 'all'
                ? 'bg-gray-200 text-black font-medium'
                : 'bg-white text-gray-600 border border-gray-300'
            }`}
          >
            All
          </button>
          <button
            onClick={() => setFilter('paid')}
            className={`px-4 py-1 rounded-md ${
              filter === 'paid'
                ? 'bg-gray-200 text-black font-medium'
                : 'bg-white text-gray-600 border border-gray-300'
            }`}
          >
            Paid
          </button>
          <button
            onClick={() => setFilter('pending')}
            className={`px-4 py-1 rounded-md ${
              filter === 'pending'
                ? 'bg-gray-200 text-black font-medium'
                : 'bg-white text-gray-600 border border-gray-300'
            }`}
          >
            Pending
          </button>
        </div>

        <div className="flex items-center">
          <span className="text-gray-600 mr-2">Sort by:</span>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="p-1 border border-gray-300 rounded-md text-black"
          >
            <option value="date">Date of payment</option>
            <option value="amount">Amount</option>
          </select>
        </div>
      </div>

      {/* Payment button removed - payments should be added from expense details */}

      {sortedPayments.length === 0 ? (
        <p className="text-gray-500 text-center py-8">No payments recorded yet.</p>
      ) : (
        <div className="border border-gray-200 rounded-lg overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Expense
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {sortedPayments.map((payment) => (
                <tr key={payment.payment_id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                      PAID
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{getExpenseName(payment.expense_id)}</div>
                    <div className="text-sm text-gray-500">{getCategoryName(payment.expense_id)}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">Date paid {formatDate(payment.payment_date)}</div>
                    <div className="text-sm text-gray-500">Paid by {payment.paid_by || 'Unknown'}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatCurrency(payment.amount)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                    <button className="text-indigo-600 hover:text-indigo-900">
                      <Edit size={18} />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default PaymentsList;
