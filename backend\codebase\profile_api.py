import os
import json
import jwt
import psycopg2
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database and JWT configuration
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_PORT = os.getenv('DB_PORT')
DB_NAME = os.getenv('DB_NAME')
JWT_SECRET = os.getenv('JWT_SECRET')

def get_db_connection():
    return psycopg2.connect(
        host=DB_HOST,
        user=DB_USER,
        password=DB_PASSWORD,
        port=DB_PORT,
        dbname=DB_NAME,
        cursor_factory=RealDictCursor  # Return results as dictionaries
    )

def validate_token(headers):
    """Validate the JWT token from the authorization header"""
    token = headers.get('Authorization')

    if not token:
        return None, {
            'statusCode': 401,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": "Token is required"})
        }

    try:
        token = token.split()[1]
        data = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
        return data['user_id'], None
    except jwt.ExpiredSignatureError:
        return None, {
            'statusCode': 401,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": "Token expired"})
        }
    except jwt.InvalidTokenError:
        return None, {
            'statusCode': 401,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": "Invalid token"})
        }

def get_user_stories(event):
    """
    Get a user's stories (both videos and photos) with pagination
    Each page contains 10 items
    """
    try:
        # Validate token
        current_user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Get target user_id and page from query parameters
        query_params = event.get('queryStringParameters', {}) or {}
        target_user_id = query_params.get('user_id', current_user_id)
        page = int(query_params.get('page', 1))
        if page < 1:
            page = 1

        # Calculate offset
        offset = (page - 1) * 10

        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # Check if target user exists
            cursor.execute("SELECT name FROM users WHERE user_id = %s", (target_user_id,))
            user = cursor.fetchone()

            if not user:
                return {
                    'statusCode': 404,
                    'headers': {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, GET",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({"error": "User not found"})
                }

            user_name = user['name']

            # Get total count for pagination
            cursor.execute("""
                SELECT COUNT(*) as total_count
                FROM (
                    SELECT created_at FROM videos WHERE video_subtype = 'story' AND user_id = %s
                    UNION ALL
                    SELECT created_at FROM photos WHERE photo_subtype = 'story' AND user_id = %s
                ) AS combined_stories
            """, (target_user_id, target_user_id))
            total_count = cursor.fetchone()['total_count']

            # Get stories (combine videos and photos)
            stories_query = """
                (
                    SELECT
                        v.video_id AS content_id,
                        v.video_name AS content_name,
                        v.video_url AS content_url,
                        v.video_description AS content_description,
                        v.video_tags,
                        v.video_thumbnail AS thumbnail_url,
                        v.video_duration AS duration,
                        v.created_at,
                        v.user_id,
                        'video' AS content_type,
                        vs.video_views AS views,
                        vs.video_likes AS likes,
                        vs.video_comments AS comments
                    FROM videos v
                    LEFT JOIN video_stats vs ON v.video_id = vs.video_id
                    WHERE v.video_subtype = 'story' AND v.user_id = %s
                )
                UNION ALL
                (
                    SELECT
                        p.photo_id AS content_id,
                        p.photo_name AS content_name,
                        p.photo_url AS content_url,
                        p.photo_description AS content_description,
                        p.photo_tags,
                        NULL AS thumbnail_url,
                        NULL AS duration,
                        p.created_at,
                        p.user_id,
                        'photo' AS content_type,
                        ps.photo_views AS views,
                        ps.photo_likes AS likes,
                        ps.photo_comments AS comments
                    FROM photos p
                    LEFT JOIN photo_stats ps ON p.photo_id = ps.photo_id
                    WHERE p.photo_subtype = 'story' AND p.user_id = %s
                )
                ORDER BY created_at DESC
                LIMIT 10 OFFSET %s
            """

            cursor.execute(stories_query, (target_user_id, target_user_id, offset))
            stories = cursor.fetchall()

            # Prepare final response
            has_next_page = (offset + len(stories)) < total_count

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({
                    "user_id": target_user_id,
                    "user_name": user_name,
                    "stories": stories,
                    "next_page": has_next_page,
                    "total_count": total_count,
                    "current_page": page
                }, cls=CustomJSONEncoder)
            }

        except Exception as e:
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }

def get_user_photos(event):
    """
    Get a user's photos (excluding stories) with pagination
    Each page contains 10 items
    """
    try:
        # Validate token
        current_user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Get target user_id and page from query parameters
        query_params = event.get('queryStringParameters', {}) or {}
        target_user_id = query_params.get('user_id', current_user_id)
        page = int(query_params.get('page', 1))
        if page < 1:
            page = 1

        # Calculate offset
        offset = (page - 1) * 10

        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # Check if target user exists
            cursor.execute("SELECT name FROM users WHERE user_id = %s", (target_user_id,))
            user = cursor.fetchone()

            if not user:
                return {
                    'statusCode': 404,
                    'headers': {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, GET",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({"error": "User not found"})
                }

            user_name = user['name']

            # Get total count for pagination
            cursor.execute("""
                SELECT COUNT(*) as total_count
                FROM photos
                WHERE photo_subtype = 'post' AND user_id = %s
            """, (target_user_id,))
            total_count = cursor.fetchone()['total_count']

            # Get user photos
            photos_query = """
                SELECT
                    p.photo_id,
                    p.photo_name,
                    p.photo_url,
                    p.photo_description,
                    p.photo_tags,
                    p.photo_subtype,
                    p.created_at,
                    p.user_id,
                    ps.photo_views,
                    ps.photo_likes,
                    ps.photo_comments
                FROM photos p
                LEFT JOIN photo_stats ps ON p.photo_id = ps.photo_id
                WHERE p.photo_subtype = 'post' AND p.user_id = %s
                ORDER BY p.created_at DESC
                LIMIT 10 OFFSET %s
            """

            cursor.execute(photos_query, (target_user_id, offset))
            photos = cursor.fetchall()

            # Prepare final response
            has_next_page = (offset + len(photos)) < total_count

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({
                    "user_id": target_user_id,
                    "user_name": user_name,
                    "photos": photos,
                    "next_page": has_next_page,
                    "total_count": total_count,
                    "current_page": page
                }, cls=CustomJSONEncoder)
            }

        except Exception as e:
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }

def get_user_movies(event):
    """
    Get a user's movies (videos only) with pagination
    Each page contains 10 items
    """
    try:
        # Validate token
        current_user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Get target user_id and page from query parameters
        query_params = event.get('queryStringParameters', {}) or {}
        target_user_id = query_params.get('user_id', current_user_id)
        page = int(query_params.get('page', 1))
        if page < 1:
            page = 1

        # Calculate offset
        offset = (page - 1) * 10

        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # Check if target user exists
            cursor.execute("SELECT name FROM users WHERE user_id = %s", (target_user_id,))
            user = cursor.fetchone()

            if not user:
                return {
                    'statusCode': 404,
                    'headers': {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, GET",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({"error": "User not found"})
                }

            user_name = user['name']

            # Get total count for pagination
            cursor.execute("""
                SELECT COUNT(*) as total_count
                FROM videos
                WHERE video_subtype = 'movie' AND user_id = %s
            """, (target_user_id,))
            total_count = cursor.fetchone()['total_count']

            # Get user movies
            movies_query = """
                SELECT
                    v.video_id,
                    v.video_name,
                    v.video_url,
                    v.video_description,
                    v.video_tags,
                    v.video_thumbnail,
                    v.video_duration,
                    v.video_category,
                    v.created_at,
                    v.user_id,
                    vs.video_views,
                    vs.video_likes,
                    vs.video_comments
                FROM videos v
                LEFT JOIN video_stats vs ON v.video_id = vs.video_id
                WHERE v.video_subtype = 'movie' AND v.user_id = %s
                ORDER BY v.created_at DESC
                LIMIT 10 OFFSET %s
            """

            cursor.execute(movies_query, (target_user_id, offset))
            movies = cursor.fetchall()

            # Prepare final response
            has_next_page = (offset + len(movies)) < total_count

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({
                    "user_id": target_user_id,
                    "user_name": user_name,
                    "movies": movies,
                    "next_page": has_next_page,
                    "total_count": total_count,
                    "current_page": page
                }, cls=CustomJSONEncoder)
            }

        except Exception as e:
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }

# Custom JSON encoder to handle date objects
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        from datetime import date, datetime
        if isinstance(obj, (date, datetime)):
            return obj.isoformat()
        return super().default(obj)

def get_user_flashes(event):
    """
    Get a user's flashes (videos only) with pagination
    Each page contains 10 items
    """
    try:
        # Validate token
        current_user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Get target user_id and page from query parameters
        query_params = event.get('queryStringParameters', {}) or {}
        target_user_id = query_params.get('user_id', current_user_id)
        page = int(query_params.get('page', 1))
        if page < 1:
            page = 1

        # Calculate offset
        offset = (page - 1) * 10

        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # Check if target user exists
            cursor.execute("SELECT name FROM users WHERE user_id = %s", (target_user_id,))
            user = cursor.fetchone()

            if not user:
                return {
                    'statusCode': 404,
                    'headers': {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, GET",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({"error": "User not found"})
                }

            user_name = user['name']

            # Get total count for pagination
            cursor.execute("""
                SELECT COUNT(*) as total_count
                FROM videos
                WHERE video_subtype = 'flash' AND user_id = %s
            """, (target_user_id,))
            total_count = cursor.fetchone()['total_count']

            # Get user flashes
            flashes_query = """
                SELECT
                    v.video_id,
                    v.video_name,
                    v.video_url,
                    v.video_description,
                    v.video_tags,
                    v.video_thumbnail,
                    v.video_duration,
                    v.video_category,
                    v.created_at,
                    v.user_id,
                    vs.video_views,
                    vs.video_likes,
                    vs.video_comments
                FROM videos v
                LEFT JOIN video_stats vs ON v.video_id = vs.video_id
                WHERE v.video_subtype = 'flash' AND v.user_id = %s
                ORDER BY v.created_at DESC
                LIMIT 10 OFFSET %s
            """

            cursor.execute(flashes_query, (target_user_id, offset))
            flashes = cursor.fetchall()

            # Prepare final response
            has_next_page = (offset + len(flashes)) < total_count

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({
                    "user_id": target_user_id,
                    "user_name": user_name,
                    "flashes": flashes,
                    "next_page": has_next_page,
                    "total_count": total_count,
                    "current_page": page
                }, cls=CustomJSONEncoder)
            }

        except Exception as e:
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }

def get_user_glimpses(event):
    """
    Get a user's glimpses (videos only) with pagination
    Each page contains 10 items
    """
    try:
        # Validate token
        current_user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Get target user_id and page from query parameters
        query_params = event.get('queryStringParameters', {}) or {}
        target_user_id = query_params.get('user_id', current_user_id)
        page = int(query_params.get('page', 1))
        if page < 1:
            page = 1

        # Calculate offset
        offset = (page - 1) * 10

        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # Check if target user exists
            cursor.execute("SELECT name FROM users WHERE user_id = %s", (target_user_id,))
            user = cursor.fetchone()

            if not user:
                return {
                    'statusCode': 404,
                    'headers': {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, GET",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({"error": "User not found"})
                }

            user_name = user['name']

            # Get total count for pagination
            cursor.execute("""
                SELECT COUNT(*) as total_count
                FROM videos
                WHERE video_subtype = 'glimpse' AND user_id = %s
            """, (target_user_id,))
            total_count = cursor.fetchone()['total_count']

            # Get user glimpses
            glimpses_query = """
                SELECT
                    v.video_id,
                    v.video_name,
                    v.video_url,
                    v.video_description,
                    v.video_tags,
                    v.video_thumbnail,
                    v.video_duration,
                    v.video_category,
                    v.created_at,
                    v.user_id,
                    vs.video_views,
                    vs.video_likes,
                    vs.video_comments
                FROM videos v
                LEFT JOIN video_stats vs ON v.video_id = vs.video_id
                WHERE v.video_subtype = 'glimpse' AND v.user_id = %s
                ORDER BY v.created_at DESC
                LIMIT 10 OFFSET %s
            """

            cursor.execute(glimpses_query, (target_user_id, offset))
            glimpses = cursor.fetchall()

            # Prepare final response
            has_next_page = (offset + len(glimpses)) < total_count

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({
                    "user_id": target_user_id,
                    "user_name": user_name,
                    "glimpses": glimpses,
                    "next_page": has_next_page,
                    "total_count": total_count,
                    "current_page": page
                }, cls=CustomJSONEncoder)
            }

        except Exception as e:
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }


def get_user_all_posts(event):
    """
    Get all posts of a user with pagination
    Each page contains 10 items
    """
    try:
        # Validate token
        current_user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Get target user_id and page from query parameters
        query_params = event.get('queryStringParameters', {}) or {}
        target_user_id = query_params.get('user_id', current_user_id)
        page = int(query_params.get('page', 1))
        if page < 1:
            page = 1

        # Calculate offset
        offset = (page - 1) * 10

        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # Check if target user exists
            cursor.execute("SELECT name FROM users WHERE user_id = %s", (target_user_id,))
            user = cursor.fetchone()
            if not user:
                return {
                    'statusCode': 404,
                    'headers': {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, GET",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({"error": "User not found"})
                }
            user_name = user['name']

            # Get total count for pagination
            cursor.execute("""
                SELECT COUNT(*) as total_count
                FROM (
                    -- Flashes
                    SELECT created_at FROM videos WHERE video_subtype = 'flash' AND user_id = %s
                    UNION ALL
                    -- Glimpses
                    SELECT created_at FROM videos WHERE video_subtype = 'glimpse' AND user_id = %s
                    UNION ALL
                    -- Movies
                    SELECT created_at FROM videos WHERE video_subtype = 'movie' AND user_id = %s
                    UNION ALL
                    -- Photos (excluding stories)
                    SELECT created_at FROM photos WHERE photo_subtype = 'post' AND user_id = %s
                ) AS combined_posts
            """, (target_user_id, target_user_id, target_user_id, target_user_id))
            total_count = cursor.fetchone()['total_count']

            # Get all posts (combine flashes, glimpses, movies, and photos)
            all_posts_query = """
                (
                    -- Flashes
                    SELECT
                        v.video_id AS content_id,
                        v.video_name AS content_name,
                        v.video_url AS content_url,
                        v.video_description AS content_description,
                        v.video_tags,
                        v.video_thumbnail AS thumbnail_url,
                        v.video_duration AS duration,
                        v.created_at,
                        v.user_id,
                        'flash' AS content_type,
                        vs.video_views AS views,
                        vs.video_likes AS likes,
                        vs.video_comments AS comments
                    FROM videos v
                    LEFT JOIN video_stats vs ON v.video_id = vs.video_id
                    WHERE v.video_subtype = 'flash' AND v.user_id = %s
                )
                UNION ALL
                (
                    -- Glimpses
                    SELECT
                        v.video_id AS content_id,
                        v.video_name AS content_name,
                        v.video_url AS content_url,
                        v.video_description AS content_description,
                        v.video_tags,
                        v.video_thumbnail AS thumbnail_url,
                        v.video_duration AS duration,
                        v.created_at,
                        v.user_id,
                        'glimpse' AS content_type,
                        vs.video_views AS views,
                        vs.video_likes AS likes,
                        vs.video_comments AS comments
                    FROM videos v
                    LEFT JOIN video_stats vs ON v.video_id = vs.video_id
                    WHERE v.video_subtype = 'glimpse' AND v.user_id = %s
                )
                UNION ALL
                (
                    -- Movies
                    SELECT
                        v.video_id AS content_id,
                        v.video_name AS content_name,
                        v.video_url AS content_url,
                        v.video_description AS content_description,
                        v.video_tags,
                        v.video_thumbnail AS thumbnail_url,
                        v.video_duration AS duration,
                        v.created_at,
                        v.user_id,
                        'movie' AS content_type,
                        vs.video_views AS views,
                        vs.video_likes AS likes,
                        vs.video_comments AS comments
                    FROM videos v
                    LEFT JOIN video_stats vs ON v.video_id = vs.video_id
                    WHERE v.video_subtype = 'movie' AND v.user_id = %s
                )
                UNION ALL
                (
                    -- Photos (excluding stories)
                    SELECT
                        p.photo_id AS content_id,
                        p.photo_name AS content_name,
                        p.photo_url AS content_url,
                        p.photo_description AS content_description,
                        p.photo_tags,
                        NULL AS thumbnail_url,
                        NULL AS duration,
                        p.created_at,
                        p.user_id,
                        'photo' AS content_type,
                        ps.photo_views AS views,
                        ps.photo_likes AS likes,
                        ps.photo_comments AS comments
                    FROM photos p
                    LEFT JOIN photo_stats ps ON p.photo_id = ps.photo_id
                    WHERE p.photo_subtype = 'post' AND p.user_id = %s
                )
                ORDER BY created_at DESC
                LIMIT 10 OFFSET %s
            """

            cursor.execute(all_posts_query, (target_user_id, target_user_id, target_user_id, target_user_id, offset))
            all_posts = cursor.fetchall()

            # Prepare final response
            has_next_page = (offset + len(all_posts)) < total_count

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({
                    "user_id": target_user_id,
                    "user_name": user_name,
                    "posts": all_posts,
                    "next_page": has_next_page,
                    "total_count": total_count,
                    "current_page": page
                }, cls=CustomJSONEncoder)
            }

        except Exception as e:
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
