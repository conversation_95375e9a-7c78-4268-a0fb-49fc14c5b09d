from codebase.authentication import signup, login, clerk_auth, check_profile, get_user_details
from codebase.presigned_uploads import get_presigned_url, complete_upload, verify_face
from codebase.user_stats import follow_user, unfollow_user, get_user_follow_stats, update_user, get_user_profile
from codebase.video_management import delete_video, get_videos, get_video, search_videos
from codebase.photo_management import delete_photo, search_photos
from codebase.engagement import like_content, unlike_content, view_content
from codebase.chat import get_conversations, create_conversation, get_messages, send_message
from codebase.search_api import search_content, get_search_suggestions

# Import new homepage APIs
from codebase.homepage_api import check_face_verification,get_my_stories, get_stories, get_flashes, get_glimpses, get_movies, get_photos, get_vendor_details, get_photo

# Import new profile APIs
from codebase.profile_api import get_user_stories, get_user_flashes, get_user_glimpses, get_user_movies, get_user_photos, get_user_all_posts

# Import vendor management APIs
from codebase.vendor_management import register_vendor, get_business_types, get_business_subtypes, get_vendor_profile, add_vendor_service, get_vendor_services, update_vendor_service, delete_vendor_service

# Import wedding planning tool APIs
from codebase.toolapi import get_checklist, add_checklist_item, update_checklist_item, delete_checklist_item, \
    get_vendors, add_vendor, get_budget, add_budget_category,add_budget_expense,add_budget_payment,update_budget_expense,delete_budget_expense,delete_budget_category

# Import wedding website builder API
from codebase.website_builder import get_website_templates, get_user_websites, get_website_details, create_website, update_website, delete_website, get_public_website

# Import my wedding videos API
from codebase.my_wedding_videos import save_video_to_my_wedding, get_my_wedding_videos, remove_video_from_my_wedding, check_video_saved_status

# Import guest list management APIs
from codebase.guestlist.group_management import get_guest_groups, create_guest_group, update_guest_group, delete_guest_group
from codebase.guestlist.guest_management import get_guests, get_guest, add_guest, update_guest, delete_guest
from codebase.guestlist.menu_management import get_menu_options, add_menu_option, update_menu_option, delete_menu_option
from codebase.guestlist.invitation_management import send_invitation, track_invitation_response


import json
from flask import Flask, request
from flask_cors import CORS

# Initialize the Flask app
app = Flask(__name__)
CORS(app)

if __name__ == '__main__':
    app.run()

import psycopg2
import os
import json

def create_like_tables(event=None):
    try:
        conn = psycopg2.connect(
            host=os.environ['DB_HOST'],
            port=os.environ['DB_PORT'],
            user=os.environ['DB_USER'],
            password=os.environ['DB_PASSWORD'],
            dbname=os.environ['DB_NAME']
        )
        cursor = conn.cursor()

        cursor.execute("""
            DROP TABLE IF EXISTS content_views CASCADE;
            DROP TABLE IF EXISTS content_view_stats CASCADE;

            CREATE TABLE content_views (
                user_id UUID NOT NULL,
                content_id UUID NOT NULL,
                view_date DATE DEFAULT CURRENT_DATE,
                viewed_at TIMESTAMP DEFAULT NOW(),
                PRIMARY KEY (user_id, content_id, view_date)
            );

            CREATE INDEX idx_content_views_content_id ON content_views(content_id);
            CREATE INDEX idx_content_views_user_id ON content_views(user_id);

            CREATE TABLE content_view_stats (
                content_id UUID PRIMARY KEY,
                view_count BIGINT DEFAULT 0,
                updated_at TIMESTAMP DEFAULT NOW()
            );
        """)

        conn.commit()
        cursor.close()
        conn.close()

        return {
            "statusCode": 200,
            "headers": {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            "body": json.dumps({"message": "View tables created successfully"})
        }

    except Exception as e:
        print("Error while creating view tables:", e)
        return {
            "statusCode": 500,
            "headers": {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            "body": json.dumps({"error": f"Error while creating view tables: {str(e)}"})
        }





def lambda_handler(event, context):
    try:
        method = event['requestContext']['httpMethod']
        resource_path = event['requestContext']['resourcePath']

        # Route to the appropriate function based on method and path
        if method == 'POST':
            # Authentication endpoints
            if resource_path == '/hub/signup':
                return signup(event)
            elif resource_path == '/hub/login':
                return login(event)
            elif resource_path == '/hub/auth-clerk':
                return clerk_auth(event)
            elif resource_path == '/hub/verify-face':
                return verify_face(event)

            # elif resource_path == '/hub/create-like-tables':
            #     return create_like_tables(event)

            # Media upload endpoints
            elif resource_path == '/hub/get-upload-url':
                return get_presigned_url(event)
            elif resource_path == '/hub/complete-upload':
                return complete_upload(event)

            # User stats endpoints
            elif resource_path == '/hub/follow':
                return follow_user(event)
            elif resource_path == '/hub/unfollow':
                return unfollow_user(event)

            # Engagement endpoints
            elif resource_path == '/hub/like':
                return like_content(event)
            elif resource_path == '/hub/unlike':
                return unlike_content(event)
            elif resource_path == '/hub/view':
                return view_content(event)

            # Vendor management endpoints
            elif resource_path == '/hub/register-vendor':
                return register_vendor(event)
            elif resource_path == '/hub/add-vendor-service':
                return add_vendor_service(event)

            # Chat endpoints
            elif resource_path == '/hub/conversations':
                return create_conversation(event)
            elif resource_path == '/hub/messages':
                return send_message(event)

            # Wedding planning tool endpoints
            elif resource_path == '/tool/add-checklist-item':
                return add_checklist_item(event)
            elif resource_path == '/tool/add-vendor':
                return add_vendor(event)
            elif resource_path == '/tool/add-budget-item':
                return add_budget_category(event)
            elif resource_path == '/tool/add-budget-expense':
                return add_budget_expense(event)
            elif resource_path == '/tool/add-budget-payment':
                return add_budget_payment(event)
            elif resource_path == '/tool/add-guest':
                return add_guest(event)
            elif resource_path == '/tool/create-guest-group':
                return create_guest_group(event)
            elif resource_path == '/tool/add-menu-option':
                return add_menu_option(event)
            elif resource_path == '/tool/send-invitation':
                return send_invitation(event)

            # Wedding website endpoints
            elif resource_path == '/tool/create-website':
                return create_website(event)

            # My Wedding endpoints
            elif resource_path == '/hub/save-to-my-wedding':
                return save_video_to_my_wedding(event)

            # Not found
            else:
                return {
                    'statusCode': 404,
                    'body': json.dumps({"error": "Resource not found"})
                }

        elif method == 'GET':
            # User endpoints
            if resource_path == '/hub/check-profile':
                return check_profile(event)
            elif resource_path == '/hub/user':
                return get_user_profile(event)
            elif resource_path == '/hub/user-details':
                return get_user_details(event)
            elif resource_path == '/hub/user-stats':
                return get_user_follow_stats(event)

            # Home page content endpoints (new)
            elif resource_path == '/hub/check-face-verification':
                return check_face_verification(event)
            elif resource_path == '/hub/stories':
                return get_stories(event)
            elif resource_path == '/hub/flashes':
                return get_flashes(event)
            elif resource_path == '/hub/glimpses':
                return get_glimpses(event)
            elif resource_path == '/hub/movies':
                return get_movies(event)
            elif resource_path == '/hub/photos':
                return get_photos(event)

            # Profile page content endpoints (new)
            elif resource_path == '/hub/user-stories':
                return get_user_stories(event)
            elif resource_path == '/hub/mystories':
                return get_my_stories(event)   
            elif resource_path == '/hub/user-flashes':
                return get_user_flashes(event)
            elif resource_path == '/hub/user-glimpses':
                return get_user_glimpses(event)
            elif resource_path == '/hub/user-movies':
                return get_user_movies(event)
            elif resource_path == '/hub/user-photos':
                return get_user_photos(event)
            elif resource_path == '/hub/all-posts':
                return get_user_all_posts(event)

            # Search endpoints
            elif resource_path == '/hub/search':
                return search_content(event)
            elif resource_path == '/hub/search-suggestions':
                return get_search_suggestions(event)

            # Existing video endpoints
            elif resource_path == '/hub/videos':
                return get_videos(event)
            elif resource_path == '/hub/video/{video_id}':
                return get_video(event)
            elif resource_path == '/hub/search-videos':
                return search_videos(event)

            # Individual photo endpoint
            elif resource_path == '/hub/photo/{photo_id}':
                return get_photo(event)
            elif resource_path == '/hub/search-photos':
                return search_photos(event)
            elif resource_path == '/hub/vendor-details/{video_id}':
                return get_vendor_details(event)


            # Chat endpoints
            elif resource_path == '/hub/conversations':
                return get_conversations(event)
            elif resource_path == '/hub/messages':
                return get_messages(event)

            # Vendor management endpoints
            elif resource_path == '/hub/business-types':
                return get_business_types(event)
            elif resource_path == '/hub/vendor-profile':
                return get_vendor_profile(event)
            elif resource_path == '/hub/business-subtypes':
                return get_business_subtypes(event)
            elif resource_path == '/hub/vendor-services':
                return get_vendor_services(event)

            # Wedding planning tool endpoints
            elif resource_path == '/tool/checklist':
                return get_checklist(event)
            elif resource_path == '/tool/vendors':
                return get_vendors(event)
            elif resource_path == '/tool/budget':
                return get_budget(event)
            elif resource_path == '/tool/guest-list':
                return get_guests(event)
            elif resource_path == '/tool/guest-groups':
                return get_guest_groups(event)
            elif resource_path == '/tool/guest':
                return get_guest(event)
            elif resource_path == '/tool/menu-options':
                return get_menu_options(event)
            elif resource_path == '/tool/track-invitation':
                return track_invitation_response(event)

            # Wedding website endpoints
            elif resource_path == '/tool/website-templates':
                return get_website_templates(event)
            elif resource_path == '/tool/user-websites':
                return get_user_websites(event)
            elif resource_path == '/tool/website':
                return get_website_details(event)
            elif resource_path == '/tool/public-website':
                return get_public_website(event)

            # My Wedding endpoints
            elif resource_path == '/hub/my-wedding-videos':
                return get_my_wedding_videos(event)
            elif resource_path == '/hub/check-video-saved':
                return check_video_saved_status(event)

            # Not found
            else:
                return {
                    'statusCode': 404,
                    'body': json.dumps({"error": "Resource not found"})
                }

        elif method == 'PUT':
            # User endpoints
            if resource_path == '/hub/update-user':
                return update_user(event)

            # Wedding planning tool endpoints
            elif resource_path == '/tool/update-checklist-item':
                return update_checklist_item(event)
            elif resource_path == '/tool/update-budget-expense':
                return update_budget_expense(event)
            elif resource_path == '/tool/update-guest':
                return update_guest(event)
            elif resource_path == '/tool/update-guest-group':
                return update_guest_group(event)
            elif resource_path == '/tool/update-menu-option':
                return update_menu_option(event)

            # Vendor management endpoints
            elif resource_path == '/hub/update-vendor-service':
                return update_vendor_service(event)

            # Wedding website endpoints
            elif resource_path == '/tool/update-website':
                return update_website(event)

            # Not found
            else:
                return {
                    'statusCode': 404,
                    'body': json.dumps({"error": "Resource not found"})
                }

        elif method == 'DELETE':
            # Media deletion endpoints
            if resource_path == '/hub/delete-video':
                return delete_video(event)
            elif resource_path == '/hub/delete-photo':
                return delete_photo(event)

            # Wedding planning tool endpoints
            elif resource_path == '/tool/delete-checklist-item':
                return delete_checklist_item(event)
            elif resource_path == '/tool/delete-budget-expense':
                return delete_budget_expense(event)
            elif resource_path == '/tool/delete-budget-category':
                return delete_budget_category(event)
            elif resource_path == '/tool/delete-guest':
                return delete_guest(event)
            elif resource_path == '/tool/delete-guest-group':
                return delete_guest_group(event)
            elif resource_path == '/tool/delete-menu-option':
                return delete_menu_option(event)

            # Vendor management endpoints
            elif resource_path == '/hub/delete-vendor-service':
                return delete_vendor_service(event)

            # Wedding website endpoints
            elif resource_path == '/tool/delete-website':
                return delete_website(event)

            # My Wedding endpoints
            elif resource_path == '/hub/remove-from-my-wedding':
                return remove_video_from_my_wedding(event)

            # Not found
            else:
                return {
                    'statusCode': 404,
                    'body': json.dumps({"error": "Resource not found"})
                }

        else:
            return {
                'statusCode': 405,
                'body': json.dumps({"error": "Method not allowed"})
            }

    except Exception as e:
        print(f"Error in lambda_handler: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }