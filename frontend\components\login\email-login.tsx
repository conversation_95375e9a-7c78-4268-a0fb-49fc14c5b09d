// components/login/email-login.tsx
import React, { useState, ChangeEvent, MouseEvent } from "react";
import { FcGoogle } from "react-icons/fc";
import { FaApple, FaFacebookF } from "react-icons/fa";
import { SiGmail } from "react-icons/si";
import Image from "next/image";
import { useRouter } from 'next/navigation';

type SocialProvider = "Google" | "Apple" | "Facebook" | "Gmail";

interface EmailLoginProps {
  onEmailVerify?: (email: string) => void;
  onSocialLogin?: (provider: SocialProvider) => void;
  onLogin?: () => void;
  onVendorSignup?: () => void;
}

const EmailLogin: React.FC<EmailLoginProps> = ({
  onEmailVerify,
  onSocialLogin,
  onLogin,
  onVendorSignup,
}) => {
  const [email, setEmail] = useState<string>("");
  const [isValidEmail, setIsValidEmail] = useState<boolean>(true);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>("");

  const handleEmailChange = (e: ChangeEvent<HTMLInputElement>): void => {
    setEmail(e.target.value);
    // Reset validation error when typing
    setIsValidEmail(true);
    if (error) setError("");
  };

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleContinue = async (e: MouseEvent<HTMLButtonElement>): Promise<void> => {
    e.preventDefault();
    
    // Validate email before proceeding
    if (!email.trim() || !validateEmail(email)) {
      setIsValidEmail(false);
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Call the API endpoint to send OTP via email
      const response = await fetch('/api/send-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });
      
      const data = await response.json();
      
      if (response.ok && data.success) {
        // Store email in sessionStorage to pass it to registration component after verification
        sessionStorage.setItem('verifiedEmail', email);
        
        // Call the onEmailVerify prop to navigate to email verification screen
        if (onEmailVerify) {
          onEmailVerify(email);
        }
      } else {
        setError(data.message || "Failed to send verification code. Please try again.");
      }
    } catch (error) {
      console.error("Error sending verification code:", error);
      setError("An unexpected error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSocialLogin = (provider: SocialProvider): void => {
    // Call the onSocialLogin prop
    if (onSocialLogin) {
      onSocialLogin(provider);
    }
  };

  const handleLoginClick = (e: MouseEvent<HTMLAnchorElement>): void => {
    e.preventDefault();
    if (onLogin) {
      onLogin();
    }
  };

  const handleVendorSignupClick = (e: MouseEvent<HTMLAnchorElement>): void => {
    e.preventDefault();
    if (onVendorSignup) {
      onVendorSignup();
    }
  };

  return (
    <div className="flex justify-center items-center">
      <div className="w-full px-6 py-6 rounded-lg">
        {/* Logo */}
        <div className="flex justify-center mb-6">
          <div className="text-red-600">
            <Image
              src="/pics/logo.png"
              alt="Wedzat logo"
              width={40}
              height={40}
              className="object-cover"
            />
          </div>
        </div>

        {/* Heading */}
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold">
            Welcome to <span style={{ color: "#B31B1E" }}>Wedzat</span>
          </h1>
          <p className="text-sm mt-1">Register With Email</p>
        </div>

        {/* Form */}
        <div className="flex flex-col gap-2">
          <div className="mb-4">
            <input
              type="email"
              placeholder="Enter Email Address"
              className={`w-full flex-1 p-3 border ${isValidEmail && !error ? 'border-gray-300' : 'border-red-500'} rounded-lg bg-white flex justify-center`}
              value={email}
              onChange={handleEmailChange}
              disabled={isLoading}
            />
            {!isValidEmail && (
              <p className="text-red-500 text-sm mt-1">Please enter a valid email address</p>
            )}
            
            {error && (
              <p className="text-red-500 text-sm mt-1">{error}</p>
            )}
          </div>

          {/* Continue Button */}
          <button
            className="w-full bg-red-700 text-white py-3 rounded-md mb-4 hover:bg-red-800 transition duration-200 disabled:bg-red-300"
            onClick={handleContinue}
            disabled={isLoading}
          >
            {isLoading ? "Sending..." : "Continue"}
          </button>

          {/* Divider */}
          <div className="flex items-center my-2">
            <div className="flex-grow border-t border-gray-300"></div>
            <span className="mx-4 text-gray-500 text-sm">or continue with</span>
            <div className="flex-grow border-t border-gray-300"></div>
          </div>

          {/* Social Login Options */}
          <div className="flex justify-between gap-4 mb-6">
            <button
              onClick={() => handleSocialLogin("Google")}
              className="w-12 h-12 border border-gray-300 rounded-lg bg-white flex items-center justify-center"
              disabled={isLoading}
            >
              <FcGoogle size={20} />
            </button>
            <button
              onClick={() => handleSocialLogin("Apple")}
              className="w-12 h-12 border border-gray-300 rounded-lg bg-white flex items-center justify-center"
              disabled={isLoading}
            >
              <FaApple size={20} />
            </button>
            <button
              onClick={() => handleSocialLogin("Facebook")}
              className="w-12 h-12 border border-gray-300 rounded-lg bg-white flex items-center justify-center"
              disabled={isLoading}
            >
              <FaFacebookF size={20} color="#1877F2" />
            </button>
            <button
              onClick={() => handleSocialLogin("Gmail")}
              className="w-12 h-12 border border-gray-300 rounded-lg bg-white flex items-center justify-center"
              disabled={isLoading}
            >
              <SiGmail size={20} color="#EA4335" />
            </button>
          </div>
        </div>

        {/* Footer Links */}
        <div className="text-center p-2">
          <div className="rounded-lg mb-2 mt-4">
            <span className="text-gray-700 text-sm">Already A member - </span>
            <a
              href="#"
              className="text-[#6A39A4] text-sm font-bold"
              onClick={handleLoginClick}
            >
              Log in
            </a>
          </div>
          <div className="rounded-lg">
            <span className="text-gray-700 text-sm">Are you a Vendor - </span>
            <a
              href="#"
              className="text-blue-600 text-sm font-bold"
              onClick={handleVendorSignupClick}
            >
              Create Account
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmailLogin;