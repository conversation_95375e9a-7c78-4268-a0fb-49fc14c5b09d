'use client';

import React, { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import { X, Eye, EyeOff, Phone } from 'lucide-react';
import { setupRecaptcha, sendOTPWithFirebase, verifyOTPWithFirebase } from '../lib/firebasePhone';
import { RecaptchaVerifier } from 'firebase/auth';

interface VendorDetail {
  name: string;
  mobileNumber: string;
}

interface VendorDetailsVerificationProps {
  vendorDetails: Record<string, VendorDetail>;
  onClose?: () => void;
}

const VendorDetailsVerification: React.FC<VendorDetailsVerificationProps> = ({
  vendorDetails,
  onClose
}) => {
  const [isVerified, setIsVerified] = useState(false);
  const [showVerificationModal, setShowVerificationModal] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [otp, setOtp] = useState('');
  const [step, setStep] = useState<'input' | 'verify'>('input');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [timeLeft, setTimeLeft] = useState(0);
  const recaptchaContainerRef = useRef<HTMLDivElement>(null);
  const recaptchaVerifierRef = useRef<RecaptchaVerifier | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Check if user is already verified from localStorage
  useEffect(() => {
    const verificationStatus = localStorage.getItem('vendorDetailsVerified');
    if (verificationStatus === 'true') {
      setIsVerified(true);
    }

    // For development purposes, auto-verify if in development mode
    if (process.env.NODE_ENV === 'development') {
      setIsVerified(true);
      localStorage.setItem('vendorDetailsVerified', 'true');
    }
  }, []);

  // Clear timer on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  // Start countdown timer
  const startTimer = () => {
    setTimeLeft(30); // 30 seconds countdown

    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    timerRef.current = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          clearInterval(timerRef.current!);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const handleUnlock = () => {
    setShowVerificationModal(true);
  };

  const handleCloseModal = () => {
    setShowVerificationModal(false);
    setStep('input');
    setOtp('');
    setPhoneNumber('');
    setError('');

    // Clear reCAPTCHA if it exists
    if (recaptchaVerifierRef.current) {
      try {
        // @ts-ignore - Firebase doesn't expose clear() in types but it exists
        recaptchaVerifierRef.current.clear();
        recaptchaVerifierRef.current = null;
      } catch (e) {
        console.error('Error clearing reCAPTCHA:', e);
      }
    }
  };

  // Handle phone number submission
  const handleSendOTP = async () => {
    // Basic validation
    if (!phoneNumber || phoneNumber.length < 10) {
      setError('Please enter a valid phone number');
      return;
    }

    try {
      setLoading(true);
      setError('');

      // Setup reCAPTCHA if not already set up
      if (!recaptchaVerifierRef.current && recaptchaContainerRef.current) {
        recaptchaVerifierRef.current = setupRecaptcha('recaptcha-container');
      }

      // Format phone number if needed (add country code if missing)
      let formattedNumber = phoneNumber;
      if (!formattedNumber.startsWith('+')) {
        formattedNumber = `+91${formattedNumber.replace(/^0/, '')}`;
      }

      // Send OTP
      await sendOTPWithFirebase(formattedNumber, recaptchaVerifierRef.current!);

      // Update UI
      setStep('verify');
      startTimer();

    } catch (error) {
      console.error('Error sending OTP:', error);
      setError('Failed to send verification code. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle OTP verification
  const handleVerifyOTP = async () => {
    if (!otp || otp.length < 4) {
      setError('Please enter a valid verification code');
      return;
    }

    try {
      setLoading(true);
      setError('');

      // Verify OTP
      const result = await verifyOTPWithFirebase(otp);

      if (result.success) {
        // Mark as verified
        setIsVerified(true);
        localStorage.setItem('vendorDetailsVerified', 'true');

        // Close modal
        handleCloseModal();
      }
    } catch (error) {
      console.error('Error verifying OTP:', error);
      setError('Invalid verification code. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle resend OTP
  const handleResendOTP = async () => {
    if (timeLeft > 0) return;

    try {
      setLoading(true);
      setError('');

      // Reset reCAPTCHA
      if (recaptchaContainerRef.current) {
        recaptchaVerifierRef.current = setupRecaptcha('recaptcha-container');
      }

      // Format phone number if needed
      let formattedNumber = phoneNumber;
      if (!formattedNumber.startsWith('+')) {
        formattedNumber = `+91${formattedNumber.replace(/^0/, '')}`;
      }

      // Resend OTP
      await sendOTPWithFirebase(formattedNumber, recaptchaVerifierRef.current!);

      // Restart timer
      startTimer();

    } catch (error) {
      console.error('Error resending OTP:', error);
      setError('Failed to resend verification code. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="vendor-details-container w-full max-w-[800px] mx-auto">
      {/* Vendor Details Section */}
      <div className="p-4 mb-4 border-t border-gray-200 pt-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold">Vendor Details</h2>
          {!isVerified && (
            <button
              onClick={handleUnlock}
              className="flex items-center bg-red-600 text-white px-3 py-1 rounded-md hover:bg-red-700 transition-colors"
            >
              <Eye className="w-4 h-4 mr-1" />
              Unlock
            </button>
          )}
        </div>

        <div className={`space-y-3 ${!isVerified ? 'filter blur-sm select-none' : ''}`}>
          {Object.entries(vendorDetails).map(([type, details]) => (
            <div key={type} className="flex justify-between border-b pb-2">
              <div className="font-medium capitalize">{type.replace(/([A-Z])/g, ' $1').trim()}</div>
              <div className="flex space-x-4">
                <div>{details.name}</div>
                <div>{details.mobileNumber}</div>
              </div>
            </div>
          ))}
        </div>

        {!isVerified && (
          <div className="relative mt-4 flex items-center justify-center pointer-events-auto">
            <div className="bg-red-50 text-red-600 p-4 rounded-md w-full">
              <p className="text-center mb-3 font-medium">Failed to load vendor details</p>
              <button
                onClick={handleUnlock}
                className="flex items-center justify-center w-full bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors"
              >
                <Phone className="w-4 h-4 mr-2" />
                Verify Now
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Verification Modal */}
      {showVerificationModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-md p-6 relative">
            {/* Close button */}
            <button
              onClick={handleCloseModal}
              className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
            >
              <X size={20} />
            </button>

            {/* Logo */}
            <div className="flex justify-center mb-4">
              <Image
                src="/pics/logo.png"
                alt="Wedzat logo"
                width={40}
                height={40}
                className="object-cover"
              />
            </div>

            <h2 className="text-xl font-bold text-center mb-6">
              {step === 'input' ? 'Welcome to Wedzat' : 'OTP Verification'}
            </h2>
            <p className="text-center text-gray-600 mb-6">
              {step === 'input'
                ? 'Register With Mobile Number To Unlock Vendor Details'
                : `Enter the OTP sent to ${phoneNumber}`}
            </p>

            {error && (
              <div className="bg-red-50 text-red-600 p-3 rounded-md mb-4">
                {error}
              </div>
            )}

            {step === 'input' ? (
              <div className="space-y-4">
                <div className="relative">
                  <input
                    type="tel"
                    placeholder="Enter Mobile Number"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    disabled={loading}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
                  />
                </div>
                <button
                  onClick={handleSendOTP}
                  disabled={!phoneNumber || loading}
                  className="w-full bg-red-600 text-white py-3 rounded-lg hover:bg-red-700 transition-colors disabled:bg-gray-400"
                >
                  {loading ? 'Sending...' : 'Get OTP'}
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Enter verification code"
                    value={otp}
                    onChange={(e) => setOtp(e.target.value)}
                    disabled={loading}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
                  />
                </div>
                <button
                  onClick={handleVerifyOTP}
                  disabled={!otp || loading}
                  className="w-full bg-red-600 text-white py-3 rounded-lg hover:bg-red-700 transition-colors disabled:bg-gray-400"
                >
                  {loading ? 'Verifying...' : 'Verify & Unlock'}
                </button>
                <div className="text-center">
                  {timeLeft > 0 ? (
                    <p className="text-gray-500">Resend in {timeLeft} seconds</p>
                  ) : (
                    <button
                      onClick={handleResendOTP}
                      disabled={loading}
                      className="text-red-600 hover:underline"
                    >
                      Resend OTP
                    </button>
                  )}
                </div>
                <div className="text-center">
                  <button
                    onClick={() => setStep('input')}
                    className="text-gray-600 hover:underline"
                  >
                    Change Phone Number
                  </button>
                </div>
              </div>
            )}

            {/* reCAPTCHA container - invisible but needed */}
            <div id="recaptcha-container" ref={recaptchaContainerRef} className="hidden"></div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VendorDetailsVerification;
