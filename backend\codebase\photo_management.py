import os
import json
import boto3
import jwt
import psycopg2
from dotenv import load_dotenv
from datetime import datetime

# Load environment variables
load_dotenv()

# Database connection parameters
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_PORT = os.getenv('DB_PORT')
DB_NAME = os.getenv('DB_NAME')

# AWS parameters
AWS_ACCESS_KEY = os.getenv('AWS_ACCESS_KEY')
AWS_SECRET_KEY = os.getenv('AWS_SECRET_KEY')
S3_BUCKET = os.getenv('S3_BUCKET')

# JWT secret
JWT_SECRET = os.getenv('JWT_SECRET')

# S3 client
s3_client = boto3.client(
    's3',
    aws_access_key_id=AWS_ACCESS_KEY,
    aws_secret_access_key=AWS_SECRET_KEY
)

# Create a fresh connection for each function call
def get_db_connection():
    return psycopg2.connect(
        host=DB_HOST,
        user=DB_USER,
        password=DB_PASSWORD,
        port=DB_PORT,
        dbname=DB_NAME
    )

def validate_token(headers):
    token = headers.get('Authorization')
    
    if not token:
        return None, {
            'statusCode': 401,
            'body': json.dumps({"error": "Token is required"})
        }
        
    try:
        data = jwt.decode(token.split()[1], JWT_SECRET, algorithms=['HS256'])
        return data['user_id'], None
    except jwt.ExpiredSignatureError:
        return None, {
            'statusCode': 401,
            'body': json.dumps({"error": "Token expired"})
        }
    except jwt.InvalidTokenError:
        return None, {
            'statusCode': 401,
            'body': json.dumps({"error": "Invalid token"})
        }

def delete_photo(event):
    try:
        # Validate token
        user_id, error = validate_token(event['headers'])
        if error:
            return error
            
        # Parse request body
        data = json.loads(event['body'])
        photo_id = data.get('photo_id')
        
        if not photo_id:
            return {
                'statusCode': 400,
                'body': json.dumps({"error": "Photo ID is required"})
            }
            
        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Check if photo exists and belongs to user
        cursor.execute(
            "SELECT photo_url FROM photos WHERE photo_id = %s AND user_id = %s",
            (photo_id, user_id)
        )
        result = cursor.fetchone()
        
        if not result:
            return {
                'statusCode': 404,
                'body': json.dumps({"error": "Photo not found or not authorized"})
            }
            
        photo_url = result[0]
        
        try:
            cursor.execute("BEGIN")
            
            # Delete from database tables
            cursor.execute("DELETE FROM photo_details WHERE photo_id = %s", (photo_id,))
            cursor.execute("DELETE FROM photo_stats WHERE photo_id = %s", (photo_id,))
            cursor.execute("DELETE FROM comments WHERE content_id = %s AND content_type = 'photo'", (photo_id,))
            cursor.execute("DELETE FROM likes WHERE content_id = %s AND content_type = 'photo'", (photo_id,))
            cursor.execute("DELETE FROM photos WHERE photo_id = %s", (photo_id,))
            
            cursor.execute("COMMIT")
            
            # Delete file from S3
            try:
                # Get S3 key from URL
                if photo_url:
                    key = photo_url.split('/')[-2] + '/' + photo_url.split('/')[-1]
                    s3_client.delete_object(Bucket=S3_BUCKET, Key=key)
            except Exception as e:
                print(f"Error deleting S3 object: {str(e)}")
                # Continue with response, even if S3 deletion fails
            
            return {
                'statusCode': 200,
                'body': json.dumps({"message": "Photo deleted successfully"})
            }
            
        except Exception as e:
            cursor.execute("ROLLBACK")
            return {
                'statusCode': 500,
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
            
    except Exception as e:
        return {
            'statusCode': 500,
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    finally:
        if 'conn' in locals() and conn:
            cursor.close()
            conn.close()

def get_photos(event):
    try:
        # Validate token
        user_id, error = validate_token(event['headers'])
        if error:
            return error
            
        # Get query parameters
        params = event.get('queryStringParameters', {}) or {}
        page = int(params.get('page', 1))
        page_size = min(int(params.get('page_size', 20)), 50)  # Limit to max 50 per page
        category = params.get('category')
        tag = params.get('tag')
        user_filter = params.get('user_id')
        
        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Build the query
        query = """
            SELECT p.photo_id, p.photo_name, p.photo_url, p.photo_description, 
                   p.photo_tags, p.photo_category, p.photo_date, p.user_id, 
                   u.name AS username, ps.photo_views, ps.photo_likes, ps.photo_comments
            FROM photos p
            JOIN users u ON p.user_id = u.user_id
            LEFT JOIN photo_stats ps ON p.photo_id = ps.photo_id
            WHERE 1=1
        """
        query_params = []
        
        # Add filters
        if category:
            query += " AND p.photo_category = %s"
            query_params.append(category)
            
        if tag:
            query += " AND p.photo_tags @> %s"
            query_params.append(json.dumps([tag]))
            
        if user_filter:
            query += " AND p.user_id = %s"
            query_params.append(user_filter)
            
        # Add ordering
        query += " ORDER BY p.photo_date DESC"
        
        # Add pagination
        query += " LIMIT %s OFFSET %s"
        query_params.extend([page_size, (page - 1) * page_size])
        
        # Execute query
        cursor.execute(query, query_params)
        photos = cursor.fetchall()
        
        # Get total count for pagination
        count_query = """
            SELECT COUNT(*)
            FROM photos p
            WHERE 1=1
        """
        count_params = []
        
        if category:
            count_query += " AND p.photo_category = %s"
            count_params.append(category)
            
        if tag:
            count_query += " AND p.photo_tags @> %s"
            count_params.append(json.dumps([tag]))
            
        if user_filter:
            count_query += " AND p.user_id = %s"
            count_params.append(user_filter)
            
        cursor.execute(count_query, count_params)
        total_count = cursor.fetchone()[0]
        
        # Format results
        results = []
        for photo in photos:
            # Check if user has liked the photo
            cursor.execute(
                "SELECT 1 FROM likes WHERE content_id = %s AND content_type = 'photo' AND user_id = %s",
                (photo[0], user_id)
            )
            liked = bool(cursor.fetchone())
            
            results.append({
                "photo_id": photo[0],
                "photo_name": photo[1],
                "photo_url": photo[2],
                "photo_description": photo[3],
                "photo_tags": json.loads(photo[4]),
                "photo_category": photo[5],
                "photo_date": photo[6].isoformat() if photo[6] else None,
                "user_id": photo[7],
                "username": photo[8],
                "views": photo[9] or 0,
                "likes": photo[10] or 0,
                "comments": photo[11] or 0,
                "liked": liked
            })
        
        return {
            'statusCode': 200,
            'body': json.dumps({
                "photos": results,
                "total": total_count,
                "page": page,
                "page_size": page_size,
                "total_pages": (total_count + page_size - 1) // page_size
            })
        }
            
    except Exception as e:
        return {
            'statusCode': 500,
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    finally:
        if 'conn' in locals() and conn:
            cursor.close()
            conn.close()

def get_photo(event):
    try:
        # Validate token
        user_id, error = validate_token(event['headers'])
        if error:
            return error
            
        # Get photo ID from path parameters
        photo_id = event.get('pathParameters', {}).get('photo_id')
        
        if not photo_id:
            return {
                'statusCode': 400,
                'body': json.dumps({"error": "Photo ID is required"})
            }
            
        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Get photo details
        cursor.execute("""
            SELECT p.photo_id, p.photo_name, p.photo_url, p.photo_description, 
                   p.photo_tags, p.photo_category, p.photo_date, p.user_id, 
                   u.name AS username, ps.photo_views, ps.photo_likes, ps.photo_comments
            FROM photos p
            JOIN users u ON p.user_id = u.user_id
            LEFT JOIN photo_stats ps ON p.photo_id = ps.photo_id
            WHERE p.photo_id = %s
        """, (photo_id,))
        
        photo = cursor.fetchone()
        
        if not photo:
            return {
                'statusCode': 404,
                'body': json.dumps({"error": "Photo not found"})
            }
            
        # Check if user has liked the photo
        cursor.execute(
            "SELECT 1 FROM likes WHERE content_id = %s AND content_type = 'photo' AND user_id = %s",
            (photo_id, user_id)
        )
        liked = bool(cursor.fetchone())
        
        # Get photo details
        cursor.execute("SELECT details FROM photo_details WHERE photo_id = %s", (photo_id,))
        details_row = cursor.fetchone()
        details = json.loads(details_row[0]) if details_row else {}
        
        # Get recent comments
        cursor.execute("""
            SELECT c.comment_id, c.user_id, u.name AS username, c.comment_text, c.created_at
            FROM comments c
            JOIN users u ON c.user_id = u.user_id
            WHERE c.content_id = %s AND c.content_type = 'photo' AND c.parent_comment_id IS NULL
            ORDER BY c.created_at DESC
            LIMIT 10
        """, (photo_id,))
        
        comments = [{
            "comment_id": row[0],
            "user_id": row[1],
            "username": row[2],
            "comment_text": row[3],
            "created_at": row[4].isoformat() if row[4] else None
        } for row in cursor.fetchall()]
        
        # Increment view count if this is not the owner viewing
        if user_id != photo[7]:
            try:
                cursor.execute("BEGIN")
                
                # Update view count
                cursor.execute(
                    "UPDATE photo_stats SET photo_views = photo_views + 1 WHERE photo_id = %s",
                    (photo_id,)
                )
                
                # Record view for analytics
                cursor.execute(
                    """INSERT INTO views (view_id, content_id, content_type, user_id, view_timestamp, device_info)
                       VALUES (uuid_generate_v4(), %s, 'photo', %s, NOW(), %s)""",
                    (photo_id, user_id, '{}')
                )
                
                cursor.execute("COMMIT")
            except Exception as e:
                cursor.execute("ROLLBACK")
                print(f"Error incrementing view count: {str(e)}")
        
        # Format result
        result = {
            "photo_id": photo[0],
            "photo_name": photo[1],
            "photo_url": photo[2],
            "photo_description": photo[3],
            "photo_tags": json.loads(photo[4]),
            "photo_category": photo[5],
            "photo_date": photo[6].isoformat() if photo[6] else None,
            "user_id": photo[7],
            "username": photo[8],
            "views": photo[9] or 0,
            "likes": photo[10] or 0,
            "comments_count": photo[11] or 0,
            "liked": liked,
            "details": details,
            "comments": comments,
            "is_owner": user_id == photo[7]
        }
        
        return {
            'statusCode': 200,
            'body': json.dumps(result)
        }
            
    except Exception as e:
        return {
            'statusCode': 500,
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    finally:
        if 'conn' in locals() and conn:
            cursor.close()
            conn.close()

def search_photos(event):
    try:
        # Validate token
        user_id, error = validate_token(event['headers'])
        if error:
            return error
            
        # Get query parameters
        params = event.get('queryStringParameters', {}) or {}
        query = params.get('q', '').strip()
        page = int(params.get('page', 1))
        page_size = min(int(params.get('page_size', 20)), 50)  # Limit to max 50 per page
        
        if not query:
            return {
                'statusCode': 400,
                'body': json.dumps({"error": "Search query is required"})
            }
            
        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Search photos by name, description, and tags
        search_query = """
            SELECT p.photo_id, p.photo_name, p.photo_url, p.photo_description, 
                   p.photo_tags, p.photo_category, p.photo_date, p.user_id, 
                   u.name AS username, ps.photo_views, ps.photo_likes, ps.photo_comments
            FROM photos p
            JOIN users u ON p.user_id = u.user_id
            LEFT JOIN photo_stats ps ON p.photo_id = ps.photo_id
            WHERE (
                p.photo_name ILIKE %s OR
                p.photo_description ILIKE %s OR
                EXISTS (
                    SELECT 1 FROM jsonb_array_elements_text(p.photo_tags) tag
                    WHERE tag ILIKE %s
                )
            )
            ORDER BY 
                CASE 
                    WHEN p.photo_name ILIKE %s THEN 1
                    WHEN p.photo_name ILIKE %s THEN 2
                    ELSE 3
                END,
                p.photo_date DESC
            LIMIT %s OFFSET %s
        """
        
        search_params = [
            f"%{query}%",    # For name ILIKE
            f"%{query}%",    # For description ILIKE
            f"%{query}%",    # For tags ILIKE
            f"{query}%",     # For exact match at beginning (priority 1)
            f"%{query}%",    # For contains match (priority 2)
            page_size,
            (page - 1) * page_size
        ]
        
        cursor.execute(search_query, search_params)
        photos = cursor.fetchall()
        
        # Get total count for pagination
        count_query = """
            SELECT COUNT(*)
            FROM photos p
            WHERE (
                p.photo_name ILIKE %s OR
                p.photo_description ILIKE %s OR
                EXISTS (
                    SELECT 1 FROM jsonb_array_elements_text(p.photo_tags) tag
                    WHERE tag ILIKE %s
                )
            )
        """
        
        count_params = [
            f"%{query}%",    # For name ILIKE
            f"%{query}%",    # For description ILIKE
            f"%{query}%",    # For tags ILIKE
        ]
        
        cursor.execute(count_query, count_params)
        total_count = cursor.fetchone()[0]
        
        # Format results
        results = []
        for photo in photos:
            # Check if user has liked the photo
            cursor.execute(
                "SELECT 1 FROM likes WHERE content_id = %s AND content_type = 'photo' AND user_id = %s",
                (photo[0], user_id)
            )
            liked = bool(cursor.fetchone())
            
            results.append({
                "photo_id": photo[0],
                "photo_name": photo[1],
                "photo_url": photo[2],
                "photo_description": photo[3],
                "photo_tags": json.loads(photo[4]),
                "photo_category": photo[5],
                "photo_date": photo[6].isoformat() if photo[6] else None,
                "user_id": photo[7],
                "username": photo[8],
                "views": photo[9] or 0,
                "likes": photo[10] or 0,
                "comments": photo[11] or 0,
                "liked": liked
            })
        
        return {
            'statusCode': 200,
            'body': json.dumps({
                "photos": results,
                "total": total_count,
                "page": page,
                "page_size": page_size,
                "total_pages": (total_count + page_size - 1) // page_size,
                "query": query
            })
        }
            
    except Exception as e:
        return {
            'statusCode': 500,
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    finally:
        if 'conn' in locals() and conn:
            cursor.close()
            conn.close()