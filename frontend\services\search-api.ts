import axios from './axiosConfig';

// Use environment variable or fallback to localhost for development
const BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:5000/hub';
const TOKEN_KEY = 'token';

// Search parameters interface
export interface SearchParams {
  q?: string;                    // General search query
  username?: string;             // Search by username
  title?: string;               // Search by title
  tags?: string;                // Search by tags
  place?: string;               // Search by place
  wedding_style?: string;       // Search by wedding style
  types?: string;               // Content types (comma-separated: stories,photos,flashes,glimpses,movies)
  page?: number;                // Page number
  limit?: number;               // Items per page
  sort_by?: string;             // Sort field (created_at, video_views, photo_views, etc.)
  sort_order?: 'asc' | 'desc';  // Sort order
}

// Search result interfaces
export interface SearchResultItem {
  // Common fields
  content_id?: string;
  content_name?: string;
  content_url?: string;
  content_description?: string;
  content_type?: 'video' | 'photo';
  created_at: string;
  user_name?: string;
  user_id?: string;
  is_own_content?: boolean;
  
  // Video-specific fields
  video_id?: string;
  video_name?: string;
  video_url?: string;
  video_description?: string;
  video_tags?: string[];
  video_thumbnail?: string;
  video_duration?: number;
  video_category?: string;
  video_views?: number;
  video_likes?: number;
  video_comments?: number;
  
  // Photo-specific fields
  photo_id?: string;
  photo_name?: string;
  photo_url?: string;
  photo_description?: string;
  photo_tags?: string[];
  photo_subtype?: string;
  photo_views?: number;
  photo_likes?: number;
  photo_comments?: number;
  
  // Additional fields
  thumbnail_url?: string;
  duration?: number;
}

export interface SearchResults {
  flashes: SearchResultItem[];
  glimpses: SearchResultItem[];
  movies: SearchResultItem[];
  photos: SearchResultItem[];
  pagination: {
    [key: string]: {
      current_page: number;
      total_pages: number;
      total_count: number;
      has_next_page: boolean;
      has_prev_page: boolean;
      limit: number;
    };
  };
  search_params: SearchParams;
}

export interface SearchSuggestions {
  usernames?: string[];
  tags?: string[];
  places?: string[];
  wedding_styles?: string[];
}

// Search service
export const searchService = {
  // Main search function
  searchContent: async (params: SearchParams): Promise<SearchResults> => {
    try {
      const token = localStorage.getItem(TOKEN_KEY);
      if (!token) {
        throw new Error('Authentication token not found');
      }

      console.log('Searching with params:', params);

      // Build query string
      const queryParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, value.toString());
        }
      });

      const response = await axios.get(`${BASE_URL}/search?${queryParams.toString()}`, {
        headers: {
          "Authorization": `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      console.log('Search API response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Search API error:', error);
      
      if (error.response?.status === 401) {
        throw new Error('Authentication failed. Please log in again.');
      } else if (error.response?.status === 400) {
        throw new Error('Invalid search parameters.');
      } else if (error.response?.data?.error) {
        throw new Error(error.response.data.error);
      } else {
        throw new Error('Failed to search content. Please try again.');
      }
    }
  },

  // Get search suggestions
  getSearchSuggestions: async (query: string, type: 'all' | 'usernames' | 'tags' | 'places' | 'wedding_styles' = 'all'): Promise<SearchSuggestions> => {
    try {
      const token = localStorage.getItem(TOKEN_KEY);
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await axios.get(`${BASE_URL}/search-suggestions`, {
        params: {
          q: query,
          type: type,
          limit: 10
        },
        headers: {
          "Authorization": `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      // The API returns suggestions wrapped in a 'suggestions' object
      return response.data.suggestions || {};
    } catch (error: any) {
      console.error('Search suggestions API error:', error);
      return {};
    }
  },

  // Quick search function for simple queries
  quickSearch: async (query: string): Promise<SearchResults> => {
    return searchService.searchContent({ q: query, limit: 10 });
  },

  // Search by content type
  searchByType: async (types: string[], query?: string): Promise<SearchResults> => {
    return searchService.searchContent({
      types: types.join(','),
      q: query,
      limit: 10
    });
  },

  // Search by user
  searchByUser: async (username: string, types?: string[]): Promise<SearchResults> => {
    return searchService.searchContent({ 
      username: username,
      types: types ? types.join(',') : undefined,
      limit: 10 
    });
  },

  // Search by tags and place
  searchByTagsAndPlace: async (tags: string, place: string, types?: string[]): Promise<SearchResults> => {
    return searchService.searchContent({ 
      tags: tags,
      place: place,
      types: types ? types.join(',') : undefined,
      limit: 10 
    });
  },

  // Search with pagination
  searchWithPagination: async (params: SearchParams, page: number, limit: number = 5): Promise<SearchResults> => {
    return searchService.searchContent({ 
      ...params,
      page: page,
      limit: limit 
    });
  },

  // Search with custom sorting
  searchWithSorting: async (params: SearchParams, sortBy: string, sortOrder: 'asc' | 'desc' = 'desc'): Promise<SearchResults> => {
    return searchService.searchContent({ 
      ...params,
      sort_by: sortBy,
      sort_order: sortOrder 
    });
  }
};

export default searchService;
