// components/upload/PersonalDetails.tsx
'use client';

import React, { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import { X, ChevronDown, Loader, MapPin } from 'lucide-react';
import { useUpload } from '../../contexts/UploadContexts';

interface City {
  id: number;
  name: string;
  region?: string;
  country?: string;
  description?: string;
}

interface PersonalDetailsProps {
  onNext: (details: {
    caption: string;
    lifePartner: string;
    weddingStyle: string;
    place: string;
    eventType?: string;
    budget?: string;
  }) => void;
  onBack: () => void;
  onClose: () => void;
  previewImage: string | null; // URL of the preview image
  videoFile?: File | null; // Video file for preview
  mediaType?: 'photo' | 'video'; // Type of media being uploaded
  contentType?: 'photo' | 'video' | 'moment'; // Content type to determine which fields to show
  initialDetails?: {
    caption: string;
    lifePartner: string;
    weddingStyle: string;
    place: string;
    eventType?: string;
    budget?: string;
  }; // Initial details for persistence between screens
}

const PersonalDetails: React.FC<PersonalDetailsProps> = ({
  onNext,
  onBack,
  onClose,
  previewImage,
  videoFile,
  mediaType = 'photo',
  contentType = 'photo',
  initialDetails
}) => {
  const { state } = useUpload(); // Get the upload context to access thumbnail
  const [videoUrl, setVideoUrl] = useState<string | null>(null);
  const [thumbnailUrl, setThumbnailUrl] = useState<string | null>(null);
  const [details, setDetails] = useState({
    caption: initialDetails?.caption || '',
    lifePartner: initialDetails?.lifePartner || '',
    weddingStyle: initialDetails?.weddingStyle || '',
    place: initialDetails?.place || '',
    eventType: initialDetails?.eventType || '',
    budget: initialDetails?.budget || ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Log the initial details for debugging
  useEffect(() => {
    console.log('PersonalDetails component initialized with:', {
      initialDetails,
      currentDetails: details
    });
  }, []);

  // City search states
  const [showCityDropdown, setShowCityDropdown] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [cities, setCities] = useState<City[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const searchTimeout = useRef<NodeJS.Timeout | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Default cities to show when no search is performed
  const defaultCities: City[] = [
    { id: 1, name: "Mumbai", description: "Financial Capital" },
    { id: 2, name: "Delhi", description: "National Capital" },
    { id: 3, name: "Bangalore", description: "IT Hub" },
    { id: 4, name: "Hyderabad", description: "Pearl City" },
    { id: 5, name: "Chennai", description: "Gateway of South India" },
    { id: 6, name: "Kolkata", description: "City of Joy" },
    { id: 7, name: "Ahmedabad", description: "Manchester of India" },
    { id: 8, name: "Pune", description: "Oxford of the East" },
  ];

  // Use React.useCallback to memoize the handler function
  const handleInputChange = React.useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    // Clear error for this field when user types
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }

    if (name === 'place') {
      setSearchTerm(value);
      setDetails(prev => ({ ...prev, place: value }));

      // Only show dropdown when input is focused and has value
      if (value.length > 0) {
        setShowCityDropdown(true);

        // Debounce search
        if (searchTimeout.current) {
          clearTimeout(searchTimeout.current);
        }

        searchTimeout.current = setTimeout(() => {
          searchCities(value);
        }, 500);
      } else {
        setCities(defaultCities);
      }
    } else {
      setDetails(prev => ({
        ...prev,
        [name]: value
      }));
    }
  }, [errors, defaultCities]); // Remove searchCities from dependencies to avoid circular dependency

  // Function to search cities using API - memoized with useCallback
  const searchCities = React.useCallback(async (query: string) => {
    if (!query) {
      setCities(defaultCities);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch(
        `https://wft-geo-db.p.rapidapi.com/v1/geo/cities?namePrefix=${query}&limit=10&countryIds=IN`,
        {
          method: 'GET',
          headers: {
            'X-RapidAPI-Key': '**************************************************',
            'X-RapidAPI-Host': 'wft-geo-db.p.rapidapi.com'
          }
        }
      );

      if (!response.ok) {
        console.warn('Failed to fetch cities from API, using default cities');
        setCities(defaultCities);
        setIsLoading(false);
        return;
      }

      const data = await response.json();

      const formattedCities: City[] = data.data.map((city: any) => ({
        id: city.id,
        name: city.name,
        region: city.region,
        country: city.country,
        description: city.region || 'India'
      }));

      setCities(formattedCities);
    } catch (err) {
      console.warn('Error fetching cities, using default cities:', err);
      setCities(defaultCities);
    } finally {
      setIsLoading(false);
    }
  }, [defaultCities]); // Add defaultCities as a dependency

  const selectCity = React.useCallback((cityName: string) => {
    setDetails(prev => ({ ...prev, place: cityName }));
    setSearchTerm(cityName); // Update the search term as well
    setShowCityDropdown(false);
  }, []);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowCityDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Initialize with default cities
  useEffect(() => {
    setCities(defaultCities);

    // Cleanup timeout on unmount
    return () => {
      if (searchTimeout.current) {
        clearTimeout(searchTimeout.current);
      }
    };
  }, []);

  // Create thumbnail URL from the thumbnail file
  useEffect(() => {
    if (state.thumbnail) {
      const url = URL.createObjectURL(state.thumbnail);
      setThumbnailUrl(url);
      console.log('PersonalDetails: Thumbnail URL created:', url);

      return () => {
        URL.revokeObjectURL(url);
        console.log('PersonalDetails: Thumbnail URL revoked');
      };
    } else {
      setThumbnailUrl(null);
      console.log('PersonalDetails: No thumbnail available');
    }
  }, [state.thumbnail]);

  // Create and cleanup video object URL - only when videoFile or mediaType changes
  useEffect(() => {
    console.log('PersonalDetails: videoFile changed:', videoFile?.name);
    console.log('PersonalDetails: mediaType:', mediaType);
    console.log('PersonalDetails: Thumbnail available:', !!state.thumbnail);

    // Only create a video URL if we have a video file AND the media type is video
    // AND we don't already have a URL for this file
    if (videoFile && mediaType === 'video' && (!videoUrl || !videoUrl.includes(videoFile.name))) {
      try {
        // Clear previous video URL if it exists
        if (videoUrl) {
          URL.revokeObjectURL(videoUrl);
        }

        const url = URL.createObjectURL(videoFile);
        console.log('PersonalDetails: Created new video URL for:', videoFile.name);
        setVideoUrl(url);

        // Cleanup function
        return () => {
          URL.revokeObjectURL(url);
        };
      } catch (error) {
        console.error('Error creating object URL:', error);
      }
    } else if (videoFile && mediaType !== 'video') {
      console.log('PersonalDetails: Not creating video URL because mediaType is not video');
    } else if (!videoFile && mediaType === 'video') {
      console.log('PersonalDetails: Not creating video URL because videoFile is null');
    }

    return () => {
      // Only revoke the URL when the component unmounts or when videoFile/mediaType changes
      // Not on every re-render
    };
  }, [videoFile, mediaType]); // Only depend on videoFile and mediaType, not videoUrl

  const handleSubmit = React.useCallback(() => {
    // Validate form based on content type
    const newErrors: Record<string, string> = {};

    // Caption/Title is always required
    if (!details.caption.trim()) {
      newErrors.caption = 'Please provide a title for your upload';
    }

    // Place is always required
    if (!details.place.trim()) {
      newErrors.place = 'Please provide a location';
    }

    // Content type specific validation
    if (contentType === 'photo') {
      // Photos require: caption, place, event_type
      if (!details.eventType.trim()) {
        newErrors.eventType = 'Please provide an event type';
      }
    } else if (contentType === 'video') {
      // Videos require: caption, place, partner, budget, wedding_style
      if (!details.lifePartner.trim()) {
        newErrors.lifePartner = 'Please tag your life partner';
      }
      if (!details.budget.trim()) {
        newErrors.budget = 'Please provide a budget';
      }
      if (!details.weddingStyle.trim()) {
        newErrors.weddingStyle = 'Please provide a wedding style';
      }
    }
    // Moments don't require additional validation (only caption and place handled above)

    // Set errors if any
    setErrors(newErrors);

    // Only proceed if there are no errors
    if (Object.keys(newErrors).length === 0) {
      onNext(details);
    }
  }, [details, onNext, contentType]);

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 p-4">
      <div className="bg-[#FFF7E8] rounded-2xl w-full max-w-[50%] p-4 sm:p-6 relative overflow-y-auto max-h-[90vh] md:max-w-[50%] sm:max-w-[70%] xs:max-w-[90%]">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-800 hover:text-red-600"
        >
          <X size={24} />
        </button>

        {/* Header */}
        <div className="flex items-center mb-6">
          <div className="flex items-center mr-2">
            <Image
              src="/pics/logo.png"
              alt="Wedzat logo"
              width={32}
              height={32}
              className="object-cover"
            />
          </div>
          <h2 className="text-xl font-bold">Details</h2>
          <div className="ml-2">
            <Image
              src="/pics/umoments.png"
              alt="Moments"
              width={20}
              height={20}
              className="object-cover"
            />
          </div>
        </div>

        <div className="flex items-center mb-6">
          <Image
            src="/pics/user-profile.png"
            alt="User"
            width={24}
            height={24}
            className="object-cover mr-2"
          />
          <p className="text-base">Add Personal Details</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
          {/* Left side - Form fields */}
          <div className="space-y-4">
            {/* Caption - Always required for all content types */}
            <div>
              <input
                type="text"
                name="caption"
                value={details.caption}
                onChange={handleInputChange}
                placeholder="Add Title (required)"
                className={`w-full p-4 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors.caption ? 'border-red-500' : 'border-gray-300'}`}
              />
              {errors.caption && (
                <p className="text-red-500 text-sm mt-1">{errors.caption}</p>
              )}
            </div>

            {/* Event Type - Only for photos */}
            {contentType === 'photo' && (
              <div>
                <input
                  type="text"
                  name="eventType"
                  value={details.eventType}
                  onChange={handleInputChange}
                  placeholder="Event Type (required)"
                  className={`w-full p-4 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors.eventType ? 'border-red-500' : 'border-gray-300'}`}
                />
                {errors.eventType && (
                  <p className="text-red-500 text-sm mt-1">{errors.eventType}</p>
                )}
              </div>
            )}

            {/* Life Partner - Only for videos */}
            {contentType === 'video' && (
              <div>
                <input
                  type="text"
                  name="lifePartner"
                  value={details.lifePartner}
                  onChange={handleInputChange}
                  placeholder="Tag Life Partner (required)"
                  className={`w-full p-4 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors.lifePartner ? 'border-red-500' : 'border-gray-300'}`}
                />
                {errors.lifePartner && (
                  <p className="text-red-500 text-sm mt-1">{errors.lifePartner}</p>
                )}
              </div>
            )}

            {/* Budget - Only for videos */}
            {contentType === 'video' && (
              <div>
                <input
                  type="text"
                  name="budget"
                  value={details.budget}
                  onChange={handleInputChange}
                  placeholder="Budget (required)"
                  className={`w-full p-4 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors.budget ? 'border-red-500' : 'border-gray-300'}`}
                />
                {errors.budget && (
                  <p className="text-red-500 text-sm mt-1">{errors.budget}</p>
                )}
              </div>
            )}

            {/* Wedding Style - Only for videos */}
            {contentType === 'video' && (
              <div>
                <input
                  type="text"
                  name="weddingStyle"
                  value={details.weddingStyle}
                  onChange={handleInputChange}
                  placeholder="Add style of wedding (required)"
                  className={`w-full p-4 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors.weddingStyle ? 'border-red-500' : 'border-gray-300'}`}
                />
                {errors.weddingStyle && (
                  <p className="text-red-500 text-sm mt-1">{errors.weddingStyle}</p>
                )}
              </div>
            )}

            <div className="relative" ref={dropdownRef}>
              <div className="relative">
                <input
                  type="text"
                  name="place"
                  value={searchTerm}
                  onChange={handleInputChange}
                  onFocus={() => searchTerm.length > 0 && setShowCityDropdown(true)}
                  placeholder="Place (required)"
                  className={`w-full p-4 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 pr-10 ${errors.place ? 'border-red-500' : 'border-gray-300'}`}
                />
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  {isLoading ? (
                    <Loader size={18} className="animate-spin text-gray-400" />
                  ) : (
                    <MapPin size={18} className="text-gray-500" />
                  )}
                </div>
              </div>

              {errors.place && (
                <p className="text-red-500 text-sm mt-1">{errors.place}</p>
              )}

              {/* City dropdown */}
              {showCityDropdown && (
                <div className="absolute z-10 mt-1 w-full bg-white rounded-lg shadow-lg border border-gray-200 max-h-60 overflow-y-auto">
                  {isLoading ? (
                    <div className="px-4 py-3 flex items-center justify-center">
                      <Loader size={18} className="animate-spin text-gray-400 mr-2" />
                      <span className="text-gray-500">Searching...</span>
                    </div>
                  ) : cities.length === 0 ? (
                    <div className="px-4 py-3 text-gray-500">No cities found</div>
                  ) : (
                    cities.map((city) => (
                      <div
                        key={city.id}
                        className="px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                        onClick={() => selectCity(city.name)}
                      >
                        <div className="flex flex-col">
                          <span className="font-medium text-gray-800">{city.name}</span>
                          <span className="text-sm text-gray-500">
                            {city.description || (city.region ? `${city.region}, ${city.country || 'India'}` : 'India')}
                          </span>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Right side - Preview image or video */}
          <div className="rounded-lg overflow-hidden bg-gray-100 h-60">
            {mediaType === 'photo' && previewImage ? (
              <div className="w-full h-full relative">
                <img
                  src={previewImage}
                  alt="Preview"
                  className="w-full h-full object-cover"
                />
                <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs">
                  Photo
                </div>
              </div>
            ) : mediaType === 'video' && videoUrl ? (
              <div className="w-full h-full relative">
                <video
                  key={`video-${videoFile?.name || 'video'}`} // Use stable key based only on file name
                  src={videoUrl}
                  poster={thumbnailUrl || undefined} // Use the selected thumbnail as poster
                  controls
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    console.error('Error loading video:', e);
                  }}
                  autoPlay
                  muted
                  loop
                />
                <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs">
                  Video
                </div>
                {/* Fallback if video fails to load */}
                <div className="absolute inset-0 flex items-center justify-center pointer-events-none opacity-0">
                  <div className="bg-gray-200 p-2 rounded text-gray-600">
                    Video Preview
                  </div>
                </div>
              </div>
            ) : (
              <div className="w-full h-full flex items-center justify-center text-gray-400">
                No media selected
              </div>
            )}
          </div>
        </div>

        {/* Navigation buttons */}
        <div className="flex justify-between mt-8">
          <button
            onClick={onBack}
            className="flex items-center justify-center px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-1"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z"
                clipRule="evenodd"
              />
            </svg>
            Back
          </button>

          <button
            onClick={handleSubmit}
            className="flex items-center justify-center px-6 py-2 rounded-md bg-red-600 text-white hover:bg-red-700 transition duration-200"
          >
            Next
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 ml-1"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default PersonalDetails;