"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"c3745b3ead92\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xca29tbWlcXE9uZURyaXZlXFxEZXNrdG9wXFxXRURaQVRfXFxmcm9udGVuZFxcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImMzNzQ1YjNlYWQ5MlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./contexts/UploadContexts.tsx":
/*!*************************************!*\
  !*** ./contexts/UploadContexts.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UploadProvider: () => (/* binding */ UploadProvider),\n/* harmony export */   useUpload: () => (/* binding */ useUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/api */ \"(app-pages-browser)/./services/api.ts\");\n/* harmony import */ var _utils_uploadUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/uploadUtils */ \"(app-pages-browser)/./utils/uploadUtils.ts\");\n// contexts/UploadContext.tsx\n/* __next_internal_client_entry_do_not_use__ UploadProvider,useUpload auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Initial state\nconst initialState = {\n    file: null,\n    thumbnail: null,\n    mediaType: 'photo',\n    mediaSubtype: 'story',\n    category: '',\n    title: '',\n    description: '',\n    tags: [],\n    detailFields: {},\n    step: 'selecting',\n    progress: 0,\n    isUploading: false\n};\n// Create context\nconst UploadContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Provider component\nconst UploadProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialState);\n    // Set file and automatically determine media type\n    const setFile = async (file)=>{\n        if (!file) {\n            setState({\n                ...state,\n                file: null\n            });\n            return;\n        }\n        const isVideo = file.type.startsWith('video/');\n        const mediaType = isVideo ? 'video' : 'photo';\n        // Default media subtypes based on media type\n        const mediaSubtype = isVideo ? 'glimpse' : 'post'; // Default to 'post' for photos\n        setState({\n            ...state,\n            file,\n            mediaType,\n            mediaSubtype,\n            step: 'details'\n        });\n    };\n    // Set thumbnail image\n    const setThumbnail = (thumbnail)=>{\n        setState({\n            ...state,\n            thumbnail\n        });\n    };\n    const setMediaType = (type)=>{\n        // Don't set a default category - let the user's selection flow through the process\n        // Just update the media type\n        setState({\n            ...state,\n            mediaType: type\n        });\n    };\n    const setMediaSubtype = (mediaSubtype)=>{\n        setState({\n            ...state,\n            mediaSubtype\n        });\n    };\n    // Keep the old function for backward compatibility\n    const setCategory = (category)=>{\n        console.log('UPLOAD CONTEXT - setCategory is deprecated, use setMediaSubtype instead');\n        setState({\n            ...state,\n            mediaSubtype: category\n        });\n    };\n    const setTitle = (title)=>{\n        // Ensure title is not empty\n        if (!title || !title.trim()) {\n            console.warn('Attempted to set empty title');\n            return;\n        }\n        console.log('Setting title to:', title.trim());\n        setState({\n            ...state,\n            title: title.trim()\n        });\n    };\n    const setDescription = (description)=>{\n        setState({\n            ...state,\n            description\n        });\n    };\n    const addTag = (tag)=>{\n        if (tag.trim() && !state.tags.includes(tag.trim())) {\n            setState({\n                ...state,\n                tags: [\n                    ...state.tags,\n                    tag.trim()\n                ]\n            });\n        }\n    };\n    const removeTag = (tag)=>{\n        setState({\n            ...state,\n            tags: state.tags.filter((t)=>t !== tag)\n        });\n    };\n    const setDetailField = (field, value)=>{\n        // Special handling for video_category to ensure it's properly set\n        if (field === 'video_category') {\n            console.log(\"UPLOAD CONTEXT - Setting video_category to: \".concat(value));\n            // Store video_category in localStorage\n            try {\n                localStorage.setItem('wedzat_video_category', value);\n                console.log(\"UPLOAD CONTEXT - Stored video_category in localStorage: \".concat(value));\n            } catch (error) {\n                console.error('UPLOAD CONTEXT - Failed to store video_category in localStorage:', error);\n            }\n        }\n        // Special handling for vendor fields to ensure they're properly set\n        if (field.startsWith('vendor_')) {\n            // If this is a vendor field, update the vendor details in localStorage\n            try {\n                // Get existing vendor details from localStorage\n                const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n                let vendorDetails = storedVendorDetails ? JSON.parse(storedVendorDetails) : {};\n                // If this is a vendor name field, extract the vendor type and update the name\n                if (field.endsWith('_name')) {\n                    const vendorType = field.replace('vendor_', '').replace('_name', '');\n                    // Check if we already have this vendor in the details\n                    if (!vendorDetails[vendorType]) {\n                        vendorDetails[vendorType] = {\n                            name: value,\n                            mobileNumber: ''\n                        };\n                    } else {\n                        vendorDetails[vendorType].name = value;\n                    }\n                }\n                // If this is a vendor contact field, extract the vendor type and update the contact\n                if (field.endsWith('_contact')) {\n                    const vendorType = field.replace('vendor_', '').replace('_contact', '');\n                    // Check if we already have this vendor in the details\n                    if (!vendorDetails[vendorType]) {\n                        vendorDetails[vendorType] = {\n                            name: '',\n                            mobileNumber: value\n                        };\n                    } else {\n                        vendorDetails[vendorType].mobileNumber = value;\n                    }\n                }\n                // Store the updated vendor details in localStorage\n                localStorage.setItem('wedzat_vendor_details', JSON.stringify(vendorDetails));\n            } catch (error) {\n                console.error('UPLOAD CONTEXT - Failed to update vendor details in localStorage:', error);\n            }\n        }\n        // Create a new detailFields object with the updated field\n        const updatedDetailFields = {\n            ...state.detailFields,\n            [field]: value\n        };\n        // Update the state with the new detailFields\n        setState((prevState)=>({\n                ...prevState,\n                detailFields: updatedDetailFields\n            }));\n        // For video_category, log the updated state after a short delay\n        if (field === 'video_category') {\n            setTimeout(()=>{\n                console.log(\"UPLOAD CONTEXT - Verified video_category is set to: \".concat(state.detailFields.video_category || 'Not set'));\n            }, 100);\n        }\n    };\n    // Set all personal details at once and update the title and related detail fields\n    const setPersonalDetails = (details)=>{\n        // console.log('Setting all personal details:', details);\n        // Validate caption/title\n        if (!details.caption || !details.caption.trim()) {\n            console.warn('Attempted to set personal details with empty caption/title');\n            return;\n        }\n        // Update title\n        const title = details.caption.trim();\n        // console.log('Setting title from personal details:', title);\n        // Update detail fields with backend-compatible field names\n        const updatedDetailFields = {\n            ...state.detailFields,\n            'personal_caption': title,\n            'personal_life_partner': details.lifePartner || '',\n            'personal_wedding_style': details.weddingStyle || '',\n            'personal_place': details.place || '',\n            'personal_event_type': details.eventType || '',\n            'personal_budget': details.budget || '',\n            // Keep legacy field names for compatibility\n            'lifePartner': details.lifePartner || '',\n            'location': details.place || '',\n            'place': details.place || '',\n            'eventType': details.eventType || '',\n            'budget': details.budget || '',\n            'weddingStyle': details.weddingStyle || ''\n        };\n        // Update state with all changes at once\n        setState({\n            ...state,\n            title,\n            description: details.weddingStyle || '',\n            detailFields: updatedDetailFields\n        });\n        // Log the description being set\n        console.log('Setting description to:', details.weddingStyle || '');\n        // Log the updated state after a short delay to ensure state has updated\n        setTimeout(()=>{\n            console.log('Personal details set successfully');\n            console.log('Title after update:', title);\n        }, 0);\n    };\n    // Set all vendor details at once and update the related detail fields\n    const setVendorDetails = (vendorDetails)=>{\n        console.log('UPLOAD CONTEXT - Setting all vendor details:', JSON.stringify(vendorDetails, null, 2));\n        // Create a copy of the current detail fields\n        const updatedDetailFields = {\n            ...state.detailFields\n        };\n        // Save the video_category if it exists\n        const videoCategory = state.detailFields.video_category;\n        console.log('UPLOAD CONTEXT - Preserving video_category:', videoCategory);\n        // Count how many complete vendor details we're receiving\n        const completeVendorCount = Object.entries(vendorDetails).filter((param)=>{\n            let [_, detail] = param;\n            return detail && detail.name && detail.mobileNumber && detail.name.trim() !== '' && detail.mobileNumber.trim() !== '';\n        }).length;\n        console.log(\"UPLOAD CONTEXT - Received \".concat(completeVendorCount, \" complete vendor details\"));\n        // Process vendor details\n        Object.entries(vendorDetails).forEach((param)=>{\n            let [vendorType, details] = param;\n            if (details) {\n                // Only include vendors that have BOTH name AND mobile number\n                if (details.name && details.mobileNumber && details.name.trim() !== '' && details.mobileNumber.trim() !== '') {\n                    // Handle special mappings for makeup_artist and decoration\n                    let backendVendorType = vendorType;\n                    // Map frontend field names to backend field names\n                    if (vendorType === 'makeupArtist') {\n                        backendVendorType = 'makeup_artist';\n                    } else if (vendorType === 'decorations') {\n                        backendVendorType = 'decoration';\n                    }\n                    // Store vendor details in the format expected by the backend\n                    updatedDetailFields[\"vendor_\".concat(backendVendorType, \"_name\")] = details.name || '';\n                    updatedDetailFields[\"vendor_\".concat(backendVendorType, \"_contact\")] = details.mobileNumber || '';\n                    // Always store with the original vendorType to ensure we count it correctly\n                    // This ensures both frontend and backend field names are present\n                    // This is especially important for Edge browser compatibility\n                    if (vendorType !== backendVendorType) {\n                        updatedDetailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name || '';\n                        updatedDetailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber || '';\n                    }\n                    // Also store with common vendor types to ensure cross-browser compatibility\n                    if (vendorType === 'makeupArtist' || vendorType === 'makeup_artist') {\n                        // Ensure both makeupArtist and makeup_artist are present\n                        updatedDetailFields[\"vendor_makeupArtist_name\"] = details.name || '';\n                        updatedDetailFields[\"vendor_makeupArtist_contact\"] = details.mobileNumber || '';\n                        updatedDetailFields[\"vendor_makeup_artist_name\"] = details.name || '';\n                        updatedDetailFields[\"vendor_makeup_artist_contact\"] = details.mobileNumber || '';\n                    } else if (vendorType === 'decorations' || vendorType === 'decoration') {\n                        // Ensure both decorations and decoration are present\n                        updatedDetailFields[\"vendor_decorations_name\"] = details.name || '';\n                        updatedDetailFields[\"vendor_decorations_contact\"] = details.mobileNumber || '';\n                        updatedDetailFields[\"vendor_decoration_name\"] = details.name || '';\n                        updatedDetailFields[\"vendor_decoration_contact\"] = details.mobileNumber || '';\n                    }\n                // console.log(`UPLOAD CONTEXT - Setting vendor detail: ${vendorType} (backend: ${backendVendorType})`, {\n                //   name: details.name || '',\n                //   contact: details.mobileNumber || ''\n                // });\n                } else {\n                    console.log(\"UPLOAD CONTEXT - Skipping incomplete vendor detail: \".concat(vendorType));\n                }\n            }\n        });\n        // Don't update state here - we'll do it after restoring the video_category\n        // console.log('UPLOAD CONTEXT - Vendor details set successfully');\n        // console.log('UPLOAD CONTEXT - Total detail fields count:', Object.keys(updatedDetailFields).length);\n        // Count how many complete vendor details we have after processing\n        let completeVendorPairs = 0;\n        const vendorNames = new Set();\n        const vendorContacts = new Set();\n        // Log all vendor details for debugging\n        Object.keys(updatedDetailFields).forEach((key)=>{\n            if (key.startsWith('vendor_')) {\n                // console.log(`UPLOAD CONTEXT - Vendor field: ${key} = ${updatedDetailFields[key]}`);\n                if (key.endsWith('_name')) {\n                    vendorNames.add(key.replace('vendor_', '').replace('_name', ''));\n                } else if (key.endsWith('_contact')) {\n                    vendorContacts.add(key.replace('vendor_', '').replace('_contact', ''));\n                }\n            }\n        });\n        // Count complete pairs (both name and contact)\n        vendorNames.forEach((name)=>{\n            if (vendorContacts.has(name)) {\n                completeVendorPairs++;\n            }\n        });\n        // console.log(`UPLOAD CONTEXT - Found ${completeVendorPairs} complete vendor pairs (name + contact)`);\n        // console.log(`UPLOAD CONTEXT - Vendor names: ${Array.from(vendorNames).join(', ')}`);\n        // console.log(`UPLOAD CONTEXT - Vendor contacts: ${Array.from(vendorContacts).join(', ')}`);\n        // Restore the video_category if it exists\n        if (videoCategory) {\n            console.log('UPLOAD CONTEXT - Restoring video_category:', videoCategory);\n            updatedDetailFields.video_category = videoCategory;\n        }\n        // Log the detail fields before updating state\n        console.log('UPLOAD CONTEXT - Detail fields before update:', JSON.stringify(state.detailFields, null, 2));\n        console.log('UPLOAD CONTEXT - Updated detail fields to be set:', JSON.stringify(updatedDetailFields, null, 2));\n        // Create a completely new state object to ensure Edge updates correctly\n        const newState = {\n            ...state,\n            detailFields: {\n                ...updatedDetailFields\n            }\n        };\n        // For Edge browser compatibility, directly set the vendor fields in the state\n        // This is a workaround for Edge where the state update doesn't properly preserve vendor details\n        if ( true && /Edge|Edg/.test(window.navigator.userAgent)) {\n            console.log('UPLOAD CONTEXT - Edge browser detected, using direct state update');\n            // Create a direct reference to the state object\n            const directState = state;\n            // Directly set the detailFields\n            directState.detailFields = {\n                ...updatedDetailFields\n            };\n            // Log the direct update\n            console.log('UPLOAD CONTEXT - Direct state update:', JSON.stringify(directState.detailFields, null, 2));\n        }\n        // Update the state with the updated detail fields\n        setState(newState);\n        // Force a re-render to ensure the state is updated\n        setTimeout(()=>{\n            console.log('UPLOAD CONTEXT - Vendor details set successfully');\n            console.log('UPLOAD CONTEXT - Total detail fields count:', Object.keys(state.detailFields).length);\n            console.log('UPLOAD CONTEXT - Detail fields after update:', JSON.stringify(state.detailFields, null, 2));\n            // Double-check that the vendor details were set correctly\n            const vendorNameFields = Object.keys(state.detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_name'));\n            const vendorContactFields = Object.keys(state.detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_contact'));\n            console.log('UPLOAD CONTEXT - Vendor name fields count:', vendorNameFields.length);\n            console.log('UPLOAD CONTEXT - Vendor contact fields count:', vendorContactFields.length);\n        }, 100);\n    };\n    const resetUpload = ()=>{\n        console.log('UPLOAD CONTEXT - Completely resetting upload state');\n        // Create a fresh copy of the initial state\n        const freshState = {\n            file: null,\n            thumbnail: null,\n            mediaType: '',\n            mediaSubtype: '',\n            title: '',\n            description: '',\n            tags: [],\n            detailFields: {},\n            step: 'select',\n            duration: 0\n        };\n        // Set the state to the fresh state\n        setState(freshState);\n        // Log the reset\n        console.log('UPLOAD CONTEXT - Upload state reset to:', JSON.stringify(freshState, null, 2));\n    };\n    // Helper function to detect Edge browser\n    const isEdgeBrowser = ()=>{\n        if (true) {\n            return /Edge|Edg/.test(window.navigator.userAgent);\n        }\n        return false;\n    };\n    const validateForm = ()=>{\n        // Check if we're running in Edge browser\n        const isEdge = isEdgeBrowser();\n        if (isEdge) {\n            console.log('VALIDATE FORM - Running in Edge browser, applying special handling');\n        }\n        // console.log('VALIDATE FORM - Validating form with state:', {\n        //   file: state.file ? state.file.name : 'No file',\n        //   mediaType: state.mediaType,\n        //   title: state.title,\n        //   description: state.description,\n        //   detailFieldsCount: Object.keys(state.detailFields).length,\n        //   tags: state.tags\n        // });\n        // Log all detail fields for debugging\n        // console.log('VALIDATE FORM - All detail fields:', JSON.stringify(state.detailFields, null, 2));\n        // Check if file is selected\n        if (!state.file) {\n            // console.log('Validation failed: No file selected');\n            return {\n                isValid: false,\n                error: 'Please select a file to upload'\n            };\n        }\n        // Validate file type and size\n        const fileValidation = (0,_utils_uploadUtils__WEBPACK_IMPORTED_MODULE_3__.validateFile)(state.file, state.mediaType);\n        if (!fileValidation.isValid) {\n            console.log('Validation failed: File validation failed', fileValidation);\n            return fileValidation;\n        }\n        // Check if title is provided\n        if (!state.title || !state.title.trim()) {\n            console.log('Validation failed: Title is empty');\n            return {\n                isValid: false,\n                error: 'Please provide a title for your upload'\n            };\n        }\n        // First, try to get vendor details from localStorage\n        let detailFields = {\n            ...state.detailFields\n        };\n        let vendorDetailsFromStorage = null;\n        try {\n            const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n            if (storedVendorDetails) {\n                vendorDetailsFromStorage = JSON.parse(storedVendorDetails);\n                console.log('VALIDATE FORM - Retrieved vendor details from localStorage:', storedVendorDetails);\n                // Process vendor details from localStorage\n                if (vendorDetailsFromStorage && Object.keys(vendorDetailsFromStorage).length > 0) {\n                    Object.entries(vendorDetailsFromStorage).forEach((param)=>{\n                        let [vendorType, details] = param;\n                        if (details && details.name && details.mobileNumber) {\n                            // Add to detailFields\n                            detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                            detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                            console.log(\"VALIDATE FORM - Added vendor \".concat(vendorType, \" with name: \").concat(details.name, \" and contact: \").concat(details.mobileNumber));\n                            // Also add normalized versions\n                            const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                            if (normalizedType !== vendorType) {\n                                detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                                detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                                console.log(\"VALIDATE FORM - Also added normalized vendor \".concat(normalizedType));\n                            }\n                        }\n                    });\n                    console.log('VALIDATE FORM - Updated detail fields with vendor details from localStorage');\n                }\n            }\n        } catch (error) {\n            console.error('VALIDATE FORM - Failed to retrieve vendor details from localStorage:', error);\n        }\n        // Now use the updated detailFields for validation\n        console.log('Detail fields count:', Object.keys(detailFields).length);\n        console.log('Detail fields present:', Object.keys(detailFields));\n        console.log('Detail fields values:', detailFields);\n        // For videos, check if required vendor details are present based on video category\n        if (state.mediaType === 'video') {\n            // Determine required vendor count based on video category\n            const videoCategory = detailFields.video_category || 'my_wedding';\n            const requiredVendorCount = videoCategory === 'wedding_vlog' ? 1 : 4;\n            console.log(\"VALIDATE FORM - Video category: \".concat(videoCategory, \", Required vendors: \").concat(requiredVendorCount));\n            // Special handling for Edge browser\n            if (isEdge) {\n                console.log('VALIDATE FORM - Edge browser detected, checking vendor details directly');\n                // In Edge, we'll count vendor details directly from the detailFields\n                const vendorNameFields = Object.keys(detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_name'));\n                const vendorContactFields = Object.keys(detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_contact'));\n                console.log('VALIDATE FORM - Edge browser - Vendor name fields:', vendorNameFields);\n                console.log('VALIDATE FORM - Edge browser - Vendor contact fields:', vendorContactFields);\n                // If we have at least 4 vendor name fields and 4 vendor contact fields, assume we have enough vendors\n                if (vendorNameFields.length >= 4 && vendorContactFields.length >= 4) {\n                    console.log('VALIDATE FORM - Edge browser - Found enough vendor fields, validation passed');\n                    return {\n                        isValid: true\n                    };\n                }\n                // Edge browser workaround - if we're uploading a video, assume vendor details are valid\n                // This is a temporary workaround for Edge browser compatibility\n                if (state.mediaType === 'video') {\n                    console.log('VALIDATE FORM - Edge browser workaround: Assuming vendor details are valid for video upload');\n                    return {\n                        isValid: true\n                    };\n                }\n            }\n            console.log('VALIDATE FORM - Checking vendor details for video upload');\n            console.log('VALIDATE FORM - Detail fields:', JSON.stringify(detailFields, null, 2));\n            // Count how many complete vendor details we have (where BOTH name AND contact are provided)\n            let validVendorCount = 0;\n            // Include both frontend and backend field names to ensure we count all vendor details\n            const vendorPrefixes = [\n                'venue',\n                'photographer',\n                'makeup_artist',\n                'makeupArtist',\n                'decoration',\n                'decorations',\n                'caterer',\n                'additional1',\n                'additional2',\n                'additionalVendor1',\n                'additionalVendor2'\n            ];\n            console.log('VALIDATE FORM - Checking these vendor prefixes:', vendorPrefixes.join(', '));\n            // Keep track of which vendors we've already counted to avoid duplicates\n            const countedVendors = new Set();\n            // First, log all vendor-related fields for debugging\n            const vendorFields = Object.keys(detailFields).filter((key)=>key.startsWith('vendor_'));\n            console.log('VALIDATE FORM - All vendor fields:', vendorFields.join(', '));\n            for (const prefix of vendorPrefixes){\n                // Skip if we've already counted this vendor (to avoid counting both makeupArtist and makeup_artist)\n                const normalizedPrefix = prefix === 'makeupArtist' ? 'makeup_artist' : prefix === 'decorations' ? 'decoration' : prefix;\n                if (countedVendors.has(normalizedPrefix)) {\n                    console.log(\"VALIDATE FORM - Skipping \".concat(prefix, \" as we already counted \").concat(normalizedPrefix));\n                    continue;\n                }\n                const nameField = \"vendor_\".concat(prefix, \"_name\");\n                const contactField = \"vendor_\".concat(prefix, \"_contact\");\n                console.log(\"VALIDATE FORM - Checking vendor \".concat(prefix, \":\"), {\n                    nameField,\n                    nameValue: detailFields[nameField],\n                    contactField,\n                    contactValue: detailFields[contactField],\n                    hasName: !!detailFields[nameField],\n                    hasContact: !!detailFields[contactField]\n                });\n                if (detailFields[nameField] && detailFields[contactField]) {\n                    validVendorCount++;\n                    countedVendors.add(normalizedPrefix);\n                    console.log(\"VALIDATE FORM - Found valid vendor: \".concat(prefix, \" with name: \").concat(detailFields[nameField], \" and contact: \").concat(detailFields[contactField]));\n                }\n            }\n            // Also check for any other vendor_ fields that might have been added\n            console.log('VALIDATE FORM - Checking for additional vendor fields');\n            Object.keys(detailFields).forEach((key)=>{\n                if (key.startsWith('vendor_') && key.endsWith('_name')) {\n                    const baseKey = key.replace('vendor_', '').replace('_name', '');\n                    const contactKey = \"vendor_\".concat(baseKey, \"_contact\");\n                    // Skip if we've already counted this vendor\n                    const normalizedPrefix = baseKey === 'makeupArtist' ? 'makeup_artist' : baseKey === 'decorations' ? 'decoration' : baseKey;\n                    console.log(\"VALIDATE FORM - Checking additional vendor \".concat(baseKey, \":\"), {\n                        normalizedPrefix,\n                        alreadyCounted: countedVendors.has(normalizedPrefix),\n                        hasName: !!detailFields[key],\n                        hasContact: !!detailFields[contactKey]\n                    });\n                    if (!countedVendors.has(normalizedPrefix) && detailFields[key] && detailFields[contactKey]) {\n                        validVendorCount++;\n                        countedVendors.add(normalizedPrefix);\n                        console.log(\"VALIDATE FORM - Found additional valid vendor: \".concat(baseKey, \" with name: \").concat(detailFields[key], \" and contact: \").concat(detailFields[contactKey]));\n                    }\n                }\n            });\n            console.log(\"VALIDATE FORM - Total valid vendor count: \".concat(validVendorCount));\n            console.log(\"VALIDATE FORM - Counted vendors: \".concat(Array.from(countedVendors).join(', ')));\n            // Special handling for Edge browser - if we have vendor fields but they're not being counted correctly\n            let edgeVendorNameFields = Object.keys(detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_name'));\n            let edgeVendorContactFields = Object.keys(detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_contact'));\n            console.log('VALIDATE FORM - Edge browser check - Vendor name fields:', edgeVendorNameFields);\n            console.log('VALIDATE FORM - Edge browser check - Vendor contact fields:', edgeVendorContactFields);\n            // If we have at least required vendor name fields and contact fields, but validVendorCount is less than required,\n            // this is likely an Edge browser issue where the fields aren't being properly counted\n            if (validVendorCount < requiredVendorCount && edgeVendorNameFields.length >= requiredVendorCount && edgeVendorContactFields.length >= requiredVendorCount) {\n                console.log('VALIDATE FORM - Edge browser workaround: Found enough vendor fields but they were not counted correctly');\n                console.log('VALIDATE FORM - Vendor name fields:', edgeVendorNameFields.join(', '));\n                console.log('VALIDATE FORM - Vendor contact fields:', edgeVendorContactFields.join(', '));\n                // Count unique vendor prefixes (excluding the _name/_contact suffix)\n                const vendorPrefixSet = new Set();\n                edgeVendorNameFields.forEach((field)=>{\n                    const prefix = field.replace('vendor_', '').replace('_name', '');\n                    if (edgeVendorContactFields.includes(\"vendor_\".concat(prefix, \"_contact\"))) {\n                        vendorPrefixSet.add(prefix);\n                    }\n                });\n                const uniqueVendorCount = vendorPrefixSet.size;\n                console.log(\"VALIDATE FORM - Unique vendor count: \".concat(uniqueVendorCount));\n                if (uniqueVendorCount >= 4) {\n                    console.log('VALIDATE FORM - Edge browser workaround: Found at least 4 unique vendors with both name and contact');\n                    validVendorCount = uniqueVendorCount;\n                }\n            }\n            // Log the vendor field counts\n            console.log('VALIDATE FORM - Vendor name fields count:', edgeVendorNameFields.length);\n            console.log('VALIDATE FORM - Vendor contact fields count:', edgeVendorContactFields.length);\n            if (validVendorCount < 4) {\n                console.log('VALIDATE FORM - Validation failed: Not enough vendor details', validVendorCount);\n                return {\n                    isValid: false,\n                    error: \"At least 4 complete vendor details (with both name and contact) are required for video uploads. You provided \".concat(validVendorCount, \"/4.\")\n                };\n            } else {\n                console.log('VALIDATE FORM - Vendor validation passed with', validVendorCount, 'vendors');\n            }\n        }\n        // Just log the detail fields count for now\n        console.log('Detail fields count:', Object.keys(state.detailFields).length);\n        // Log the detail fields that are present\n        console.log('Detail fields present:', Object.keys(state.detailFields));\n        console.log('Detail fields values:', state.detailFields);\n        console.log('Form validation passed');\n        return {\n            isValid: true\n        };\n    };\n    // Start upload with a specific category and video_category (used when correcting the category)\n    const startUploadWithCategory = async (category, videoCategory)=>{\n        console.log(\"Starting upload process with corrected category: \".concat(category));\n        console.log(\"Using video_category: \".concat(videoCategory || 'Not provided'));\n        // Try to get vendor details from localStorage\n        let vendorDetails = {};\n        try {\n            const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n            if (storedVendorDetails) {\n                vendorDetails = JSON.parse(storedVendorDetails);\n                console.log('UPLOAD CONTEXT - Retrieved vendor details from localStorage');\n            }\n        } catch (error) {\n            console.error('UPLOAD CONTEXT - Failed to retrieve vendor details from localStorage:', error);\n        }\n        // Create detail fields from vendor details\n        const detailFields = {\n            ...state.detailFields\n        };\n        // Process vendor details to create detail fields\n        if (Object.keys(vendorDetails).length > 0) {\n            console.log('UPLOAD CONTEXT - Processing vendor details from localStorage:', JSON.stringify(vendorDetails));\n            // Track how many complete vendor details we've added\n            let completeVendorCount = 0;\n            Object.entries(vendorDetails).forEach((param)=>{\n                let [vendorType, details] = param;\n                if (details && details.name && details.mobileNumber) {\n                    detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                    detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                    console.log(\"UPLOAD CONTEXT - Added vendor \".concat(vendorType, \" with name: \").concat(details.name, \" and contact: \").concat(details.mobileNumber));\n                    completeVendorCount++;\n                    // Also set normalized versions\n                    const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                    if (normalizedType !== vendorType) {\n                        detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                        detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                        console.log(\"UPLOAD CONTEXT - Also added normalized vendor \".concat(normalizedType));\n                    }\n                }\n            });\n            console.log(\"UPLOAD CONTEXT - Added \".concat(completeVendorCount, \" complete vendor details from localStorage to detailFields\"));\n            console.log('UPLOAD CONTEXT - Detail fields after adding vendors:', JSON.stringify(detailFields));\n        }\n        // Update the state with the corrected category and video_category if provided\n        const updatedState = {\n            ...state,\n            mediaSubtype: category,\n            category: category,\n            detailFields: detailFields\n        };\n        // If videoCategory is provided, update the detailFields\n        if (videoCategory) {\n            updatedState.detailFields.video_category = videoCategory;\n            console.log(\"Setting video_category in state to: \".concat(videoCategory));\n            // Also store in localStorage\n            try {\n                localStorage.setItem('wedzat_video_category', videoCategory);\n                console.log('UPLOAD CONTEXT - Stored video_category in localStorage:', videoCategory);\n            } catch (error) {\n                console.error('UPLOAD CONTEXT - Failed to store video_category in localStorage:', error);\n            }\n        }\n        // Apply the state update immediately\n        setState(updatedState);\n        // Then start the upload process with the updated category\n        const validation = validateForm();\n        if (!validation.isValid) {\n            console.log('Upload validation failed:', validation.error);\n            setState({\n                ...state,\n                mediaSubtype: category,\n                error: validation.error,\n                step: 'error'\n            });\n            return;\n        }\n        if (!state.file) {\n            console.log('No file to upload');\n            return;\n        }\n        setState({\n            ...state,\n            mediaSubtype: category,\n            isUploading: true,\n            progress: 0,\n            step: 'uploading',\n            error: undefined\n        });\n        try {\n            console.log('UPLOAD CONTEXT - Starting upload process with corrected category...');\n            console.log('UPLOAD CONTEXT - Upload details:', {\n                file: state.file ? state.file.name : 'No file',\n                fileSize: state.file ? Math.round(state.file.size / (1024 * 1024) * 100) / 100 + ' MB' : 'N/A',\n                mediaType: state.mediaType,\n                category: category,\n                title: state.title,\n                videoCategory: videoCategory || 'Not set'\n            });\n            // Log the video_category that will be used\n            console.log('UPLOAD CONTEXT - Using explicit video_category:', videoCategory);\n            console.log('UPLOAD CONTEXT - Video category from detail fields:', state.detailFields.video_category);\n            // Create a copy of the detail fields with the explicit video_category\n            const updatedDetailFields = {\n                ...state.detailFields\n            };\n            // If videoCategory is provided, use it\n            if (videoCategory) {\n                updatedDetailFields.video_category = videoCategory;\n                console.log('UPLOAD CONTEXT - Setting video_category in details to:', videoCategory);\n            }\n            // Use the upload service to handle the complete upload process with the corrected category\n            const result = await _services_api__WEBPACK_IMPORTED_MODULE_2__.uploadService.handleUpload(state.file, state.mediaType, category, state.title, state.description, state.tags, updatedDetailFields, state.duration, state.thumbnail, (progress)=>{\n                setState({\n                    ...state,\n                    mediaSubtype: category,\n                    progress\n                });\n            });\n            // Update the state with the upload result\n            setState({\n                ...state,\n                mediaSubtype: category,\n                isUploading: false,\n                progress: 100,\n                step: 'complete',\n                uploadResult: result\n            });\n        // Upload completed successfully\n        } catch (error) {\n            console.error('Upload failed:', error);\n            setState({\n                ...state,\n                mediaSubtype: category,\n                isUploading: false,\n                error: error instanceof Error ? error.message : 'Unknown error'\n            });\n            throw error;\n        }\n    };\n    const startUpload = async ()=>{\n        console.log('Starting upload process...');\n        // Try to get vendor details from localStorage\n        try {\n            const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n            if (storedVendorDetails) {\n                const vendorDetails = JSON.parse(storedVendorDetails);\n                console.log('UPLOAD CONTEXT - Retrieved vendor details from localStorage for startUpload:', storedVendorDetails);\n                // Create a new detailFields object to hold all the vendor details\n                const updatedDetailFields = {\n                    ...state.detailFields\n                };\n                let completeVendorCount = 0;\n                // Process vendor details to create detail fields\n                if (Object.keys(vendorDetails).length > 0) {\n                    Object.entries(vendorDetails).forEach((param)=>{\n                        let [vendorType, details] = param;\n                        if (details && details.name && details.mobileNumber) {\n                            // Add to the updated detail fields\n                            updatedDetailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                            updatedDetailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                            console.log(\"UPLOAD CONTEXT - Added vendor \".concat(vendorType, \" with name: \").concat(details.name, \" and contact: \").concat(details.mobileNumber));\n                            completeVendorCount++;\n                            // Also set normalized versions\n                            const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                            if (normalizedType !== vendorType) {\n                                updatedDetailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                                updatedDetailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                                console.log(\"UPLOAD CONTEXT - Also added normalized vendor \".concat(normalizedType));\n                            }\n                        }\n                    });\n                    // Update the state with all vendor details at once\n                    setState((prevState)=>({\n                            ...prevState,\n                            detailFields: updatedDetailFields\n                        }));\n                    console.log(\"UPLOAD CONTEXT - Added \".concat(completeVendorCount, \" complete vendor details from localStorage to state\"));\n                    console.log('UPLOAD CONTEXT - Updated detail fields:', JSON.stringify(updatedDetailFields));\n                }\n            }\n        } catch (error) {\n            console.error('UPLOAD CONTEXT - Failed to retrieve vendor details from localStorage:', error);\n        }\n        // Check if we have a video_category for videos\n        if (state.mediaType === 'video') {\n            // Try to get video_category from localStorage if not in state\n            let videoCategory = state.detailFields.video_category;\n            if (!videoCategory) {\n                try {\n                    const storedVideoCategory = localStorage.getItem('wedzat_video_category');\n                    if (storedVideoCategory) {\n                        videoCategory = storedVideoCategory;\n                        console.log('UPLOAD CONTEXT - Retrieved video_category from localStorage:', videoCategory);\n                        setDetailField('video_category', videoCategory);\n                    }\n                } catch (error) {\n                    console.error('UPLOAD CONTEXT - Failed to retrieve video_category from localStorage:', error);\n                }\n            }\n            console.log(\"UPLOAD CONTEXT - Current video_category: \".concat(videoCategory || 'Not set'));\n            // If we don't have a video_category, use a default one\n            if (!videoCategory) {\n                console.log('UPLOAD CONTEXT - No video_category found, using default: my_wedding');\n                // Use startUploadWithCategory to ensure the video_category is properly set\n                return startUploadWithCategory(state.mediaSubtype, 'my_wedding');\n            } else {\n                // Use startUploadWithCategory to ensure the video_category is properly passed\n                console.log(\"UPLOAD CONTEXT - Using existing video_category: \".concat(videoCategory));\n                return startUploadWithCategory(state.mediaSubtype, videoCategory);\n            }\n        }\n        // For photos, just use the regular upload flow\n        const validation = validateForm();\n        if (!validation.isValid) {\n            console.log('Upload validation failed:', validation.error);\n            setState({\n                ...state,\n                error: validation.error,\n                step: 'error'\n            });\n            return;\n        }\n        if (!state.file) {\n            console.log('No file to upload');\n            return;\n        }\n        setState({\n            ...state,\n            isUploading: true,\n            progress: 0,\n            step: 'uploading',\n            error: undefined\n        });\n        try {\n            console.log('UPLOAD CONTEXT - Starting upload process...');\n            console.log('UPLOAD CONTEXT - Upload details:', {\n                file: state.file ? state.file.name : 'No file',\n                fileSize: state.file ? Math.round(state.file.size / (1024 * 1024) * 100) / 100 + ' MB' : 'N/A',\n                mediaType: state.mediaType,\n                mediaSubtype: state.mediaSubtype,\n                title: state.title,\n                videoCategory: state.detailFields.video_category || 'Not set'\n            });\n            // Use the upload service to handle the complete upload process\n            const result = await _services_api__WEBPACK_IMPORTED_MODULE_2__.uploadService.handleUpload(state.file, state.mediaType, state.mediaSubtype, state.title, state.description, state.tags, state.detailFields, state.duration, state.thumbnail, (progress)=>{\n                console.log(\"Upload progress: \".concat(progress, \"%\"));\n                setState((prevState)=>{\n                    // Only update if the new progress is greater\n                    if (progress > prevState.progress) {\n                        return {\n                            ...prevState,\n                            progress,\n                            // Change to processing state when we reach 80%\n                            step: progress >= 80 && progress < 100 ? 'processing' : prevState.step\n                        };\n                    }\n                    return prevState;\n                });\n            });\n            console.log('Upload completed successfully:', result);\n            // Upload complete\n            setState({\n                ...state,\n                isUploading: false,\n                progress: 100,\n                step: 'complete',\n                response: result\n            });\n        } catch (error) {\n            console.error('Upload failed:', error);\n            setState({\n                ...state,\n                isUploading: false,\n                progress: 0,\n                step: 'error',\n                error: error instanceof Error ? error.message : 'Upload failed. Please try again.'\n            });\n        }\n    };\n    const goToStep = (step)=>{\n        setState({\n            ...state,\n            step\n        });\n    };\n    // Set video duration\n    const setDuration = (duration)=>{\n        setState({\n            ...state,\n            duration\n        });\n        console.log(\"Duration set to \".concat(duration, \" seconds\"));\n    };\n    // Effect to initialize the upload context and listen for vendor details updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UploadProvider.useEffect\": ()=>{\n            // Check if we have a video_category in localStorage\n            try {\n                const storedVideoCategory = localStorage.getItem('wedzat_video_category');\n                if (storedVideoCategory) {\n                    console.log('UPLOAD CONTEXT - Retrieved video_category from localStorage:', storedVideoCategory);\n                    setDetailField('video_category', storedVideoCategory);\n                }\n            } catch (error) {\n                console.error('UPLOAD CONTEXT - Failed to retrieve video_category from localStorage:', error);\n            }\n            // Add event listener for vendor details updates from API service\n            const handleVendorDetailsUpdate = {\n                \"UploadProvider.useEffect.handleVendorDetailsUpdate\": (event)=>{\n                    if (event.detail) {\n                        console.log('UPLOAD CONTEXT - Received vendor-details-update event:', event.detail);\n                        // Process vendor details from event\n                        const vendorDetails = event.detail;\n                        const updatedDetailFields = {\n                            ...state.detailFields\n                        };\n                        let completeVendorCount = 0;\n                        Object.entries(vendorDetails).forEach({\n                            \"UploadProvider.useEffect.handleVendorDetailsUpdate\": (param)=>{\n                                let [vendorType, details] = param;\n                                if (details && details.name && details.mobileNumber) {\n                                    // Add to detailFields\n                                    updatedDetailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                                    updatedDetailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                                    console.log(\"UPLOAD CONTEXT - Event handler added vendor \".concat(vendorType, \" with name: \").concat(details.name, \" and contact: \").concat(details.mobileNumber));\n                                    completeVendorCount++;\n                                    // Also add normalized versions\n                                    const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                                    if (normalizedType !== vendorType) {\n                                        updatedDetailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                                        updatedDetailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                                        console.log(\"UPLOAD CONTEXT - Event handler also added normalized vendor \".concat(normalizedType));\n                                    }\n                                }\n                            }\n                        }[\"UploadProvider.useEffect.handleVendorDetailsUpdate\"]);\n                        // Update the state with all vendor details at once\n                        setState({\n                            \"UploadProvider.useEffect.handleVendorDetailsUpdate\": (prevState)=>({\n                                    ...prevState,\n                                    detailFields: updatedDetailFields\n                                })\n                        }[\"UploadProvider.useEffect.handleVendorDetailsUpdate\"]);\n                        console.log(\"UPLOAD CONTEXT - Event handler added \".concat(completeVendorCount, \" complete vendor details to state\"));\n                        console.log('UPLOAD CONTEXT - Event handler updated detail fields:', JSON.stringify(updatedDetailFields));\n                    }\n                }\n            }[\"UploadProvider.useEffect.handleVendorDetailsUpdate\"];\n            // Add event listener\n            window.addEventListener('vendor-details-update', handleVendorDetailsUpdate);\n            // Remove event listener on cleanup\n            return ({\n                \"UploadProvider.useEffect\": ()=>{\n                    window.removeEventListener('vendor-details-update', handleVendorDetailsUpdate);\n                }\n            })[\"UploadProvider.useEffect\"];\n        }\n    }[\"UploadProvider.useEffect\"], []);\n    // Create the context value\n    const contextValue = {\n        state,\n        setFile,\n        setThumbnail,\n        setMediaType,\n        setMediaSubtype,\n        setCategory,\n        setTitle,\n        setDescription,\n        addTag,\n        removeTag,\n        setDetailField,\n        setPersonalDetails,\n        setVendorDetails,\n        setDuration,\n        resetUpload,\n        startUpload,\n        startUploadWithCategory,\n        validateForm,\n        goToStep\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UploadContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\contexts\\\\UploadContexts.tsx\",\n        lineNumber: 1162,\n        columnNumber: 5\n    }, undefined);\n};\n_s(UploadProvider, \"g9yWDQF6ixWa1r5sfsm7YAeGJG4=\");\n_c = UploadProvider;\n// Custom hook to use the context\nconst useUpload = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(UploadContext);\n    if (context === undefined) {\n        throw new Error('useUpload must be used within an UploadProvider');\n    }\n    return context;\n};\n_s1(useUpload, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"UploadProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/UploadContexts.tsx\n"));

/***/ })

});