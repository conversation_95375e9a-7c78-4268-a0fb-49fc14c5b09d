"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
import UserAvatar from "./HomeDashboard/UserAvatar";
import axios from "../services/axiosConfig";

// Define interface for photo items
interface Photo {
  photo_id: string;
  photo_name: string;
  photo_url: string;
  photo_description?: string;
  photo_tags?: string[];
  photo_subtype?: string;
  created_at: string;
  user_name?: string;
  is_own_content?: boolean;
  photo_views?: number;
  photo_likes?: number;
  photo_comments?: number;
}

interface ApiResponse {
  photos: Photo[];
  next_page: boolean;
  total_count: number;
  current_page: number;
}

const DetailPagePhotos: React.FC = () => {
  const [photos, setPhotos] = useState<Photo[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch photos from the API
  useEffect(() => {
    const fetchPhotos = async () => {
      try {
        setLoading(true);
        // Get token from localStorage - try multiple possible keys
        const token = typeof window !== 'undefined' ?
          (localStorage.getItem('token') ||
           localStorage.getItem('jwt_token') ||
           localStorage.getItem('auth_token')) : null;

        if (!token) {
          console.warn('No authentication token found in any storage key');
          setError('Authentication required');
          setPhotos(getFallbackPhotos());
          return;
        }

        const response = await axios.get<ApiResponse>(`/photos?page=1&limit=6`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });

        if (response.data && response.data.photos) {
          console.log('Photos API response for detail page:', response.data);
          setPhotos(response.data.photos);
        } else {
          console.warn('Unexpected API response format:', response.data);
          // Fallback to hardcoded data if API doesn't return expected format
          setPhotos(getFallbackPhotos());
        }
      } catch (err) {
        console.error('Error fetching photos:', err);
        setError('Failed to load photos');
        // Fallback to hardcoded data on error
        setPhotos(getFallbackPhotos());
      } finally {
        setLoading(false);
      }
    };

    fetchPhotos();
  }, []);

  // Fallback data if API fails
  const getFallbackPhotos = (): Photo[] => [
    {
      photo_id: "photo1",
      photo_name: "mn",
      photo_url: "/pics/placeholder.svg",
      photo_description: "Elegant wedding decoration with flowers",
      created_at: new Date().toISOString(),
      user_name: "Shanmukh Sai Devineni",
      is_own_content: false,
      photo_views: 5200,
      photo_likes: 420,
      photo_comments: 32
    },
    {
      photo_id: "photo2",
      photo_name: "fcgh",
      photo_url: "/pics/placeholder.svg",
      photo_description: "Bride getting ready for the ceremony",
      created_at: new Date().toISOString(),
      user_name: "Shanmukh Sai Devineni",
      is_own_content: false,
      photo_views: 3800,
      photo_likes: 310,
      photo_comments: 28
    },
    {
      photo_id: "photo3",
      photo_name: "gcfvhb",
      photo_url: "/pics/placeholder.svg",
      photo_description: "Three-tier wedding cake with floral decorations",
      created_at: new Date().toISOString(),
      user_name: "Shanmukh Sai Devineni",
      is_own_content: false,
      photo_views: 4100,
      photo_likes: 350,
      photo_comments: 30
    },
    {
      photo_id: "photo4",
      photo_name: "vfghu",
      photo_url: "/pics/placeholder.svg",
      photo_description: "Outdoor wedding venue setup",
      created_at: new Date().toISOString(),
      user_name: "Shanmukh Sai Devineni",
      is_own_content: false,
      photo_views: 3500,
      photo_likes: 280,
      photo_comments: 25
    },
    {
      photo_id: "photo5",
      photo_name: "mhb",
      photo_url: "/pics/placeholder.svg",
      photo_description: "Wedding rings on display",
      created_at: new Date().toISOString(),
      user_name: "Shanmukh Sai Devineni",
      is_own_content: false,
      photo_views: 4800,
      photo_likes: 390,
      photo_comments: 35
    }
  ];

  return (
    <section className="mb-8">
      <div className="flex items-center justify-between mb-4">
        <h2 className="font-inter text-[20px] leading-[18px] font-semibold text-black">
          Photos
        </h2>
        <a
          href="/home/<USER>"
          className="text-red-500 text-sm font-medium hover:underline z-10 relative"
        >
          See all
        </a>
      </div>

      {/* Loading state */}
      {loading && <div className="py-10 text-center text-black">Loading photos...</div>}

      {/* Error state */}
      {error && !loading && (
        <div className="py-10 text-center text-red-500">{error}</div>
      )}

      {/* Content */}
      {!loading && !error && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {photos.map((photo, index) => (
            <div
              key={`${photo.photo_id}-${index}`}
              className="rounded-lg overflow-hidden shadow-md relative hover:scale-105 transition-transform cursor-pointer"
              style={{
                height: "220px",
                border: "1px solid #e5e7eb"
              }}
              onClick={() => {
                window.location.href = `/home/<USER>/${photo.photo_id}`;
              }}
            >
              {/* User avatar */}
              <div className="absolute top-4 left-4 z-10">
                <UserAvatar
                  username={photo.user_name || "user"}
                  size="sm"
                  isGradientBorder={true}
                />
              </div>

              {/* Photo */}
              <div className="relative w-full h-full">
                <Image
                  src={photo.photo_url || `/pics/placeholder.svg`}
                  alt={photo.photo_name || "Photo"}
                  fill
                  sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                  className="object-cover"
                  priority={index < 4}
                  unoptimized={!photo.photo_url}
                  onError={(e) => {
                    console.error(`Failed to load photo: ${photo.photo_name}`);
                    const imgElement = e.target as HTMLImageElement;
                    if (imgElement) {
                      imgElement.src = '/pics/placeholder.svg';
                    }
                  }}
                />
              </div>

              {/* Photo info */}
              <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/80 to-transparent text-white">
                <div className="text-sm font-medium">{photo.photo_name}</div>
                <div className="text-xs">
                  {photo.user_name}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </section>
  );
};

export default DetailPagePhotos;
