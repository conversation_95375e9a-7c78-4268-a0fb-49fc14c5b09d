import json
import uuid
from datetime import datetime, timezone
import psycopg2
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

# Database connection parameters
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_PORT = os.getenv('DB_PORT')
DB_NAME = os.getenv('DB_NAME')

# JWT secret
JWT_SECRET = os.getenv('JWT_SECRET')

# Create a fresh connection for each function call
def get_db_connection():
    return psycopg2.connect(
        host=DB_HOST,
        user=DB_USER,
        password=DB_PASSWORD,
        port=DB_PORT,
        dbname=DB_NAME
    )

def verify_token(headers):
    """Verify JWT token from Authorization header"""
    import jwt
    from datetime import datetime, timezone
    
    auth_header = headers.get('Authorization', '')
    
    if not auth_header.startswith('Bearer '):
        return None, {
            'statusCode': 401,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': '*',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({"error": "Authorization header missing or invalid"})
        }
    
    token = auth_header.split(' ')[1]
    
    try:
        payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
        
        # Check if token is expired
        exp = payload.get('exp')
        if exp and datetime.now(timezone.utc).timestamp() > exp:
            return None, {
                'statusCode': 401,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': '*',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({"error": "Token expired"})
            }
        
        return payload.get('user_id'), None
    except jwt.PyJWTError as e:
        return None, {
            'statusCode': 401,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': '*',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({"error": f"Invalid token: {str(e)}"})
        }

def get_conversations(event):
    """Get all conversations for a user"""
    try:
        # Verify user token
        user_id, error = verify_token(event.get('headers', {}))
        if error:
            return error
        
        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Get all conversations where the user is a participant
        query = """
        SELECT c.conversation_id, c.name, c.is_group, c.updated_at,
               (SELECT COUNT(*) FROM messages m 
                WHERE m.conversation_id = c.conversation_id 
                AND m.sender_id != %s
                AND m.sent_at > cp.last_read_at) as unread_count,
               (SELECT m.content FROM messages m 
                WHERE m.conversation_id = c.conversation_id 
                ORDER BY m.sent_at DESC LIMIT 1) as last_message,
               (SELECT m.sent_at FROM messages m 
                WHERE m.conversation_id = c.conversation_id 
                ORDER BY m.sent_at DESC LIMIT 1) as last_message_time
        FROM conversations c
        JOIN conversation_participants cp ON c.conversation_id = cp.conversation_id
        WHERE cp.user_id = %s
        ORDER BY c.updated_at DESC
        """
        
        cursor.execute(query, (user_id, user_id))
        conversations = cursor.fetchall()
        
        # Format the response
        result = []
        for conv in conversations:
            conversation_id = conv[0]
            
            # For each conversation, get the other participants
            if not conv[2]:  # If not a group chat
                cursor.execute("""
                    SELECT u.user_id, u.name, u.face_image_url
                    FROM conversation_participants cp
                    JOIN users u ON cp.user_id = u.user_id
                    WHERE cp.conversation_id = %s AND cp.user_id != %s
                """, (conversation_id, user_id))
                other_user = cursor.fetchone()
                
                if other_user:
                    result.append({
                        'conversation_id': conversation_id,
                        'name': other_user[1],
                        'is_group': conv[2],
                        'updated_at': conv[3].isoformat() if conv[3] else None,
                        'unread_count': conv[4],
                        'last_message': conv[5],
                        'last_message_time': conv[6].isoformat() if conv[6] else None,
                        'other_user': {
                            'user_id': other_user[0],
                            'name': other_user[1],
                            'profile_picture': other_user[2]
                        }
                    })
            else:
                # For group chats
                cursor.execute("""
                    SELECT u.user_id, u.name, u.face_image_url
                    FROM conversation_participants cp
                    JOIN users u ON cp.user_id = u.user_id
                    WHERE cp.conversation_id = %s
                    LIMIT 3
                """, (conversation_id,))
                participants = cursor.fetchall()
                
                result.append({
                    'conversation_id': conversation_id,
                    'name': conv[1],
                    'is_group': conv[2],
                    'updated_at': conv[3].isoformat() if conv[3] else None,
                    'unread_count': conv[4],
                    'last_message': conv[5],
                    'last_message_time': conv[6].isoformat() if conv[6] else None,
                    'participants': [
                        {
                            'user_id': p[0],
                            'name': p[1],
                            'profile_picture': p[2]
                        } for p in participants
                    ]
                })
        
        cursor.close()
        conn.close()
        
        return {
            'statusCode': 200,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps(result)
        }
        
    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }

def create_conversation(event):
    """Create a new conversation"""
    try:
        # Verify user token
        user_id, error = verify_token(event.get('headers', {}))
        if error:
            return error
        
        data = json.loads(event['body'])
        
        # Validate input
        if 'participants' not in data or not data['participants']:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Participants are required'})
            }
        
        # Add the current user to participants if not already included
        participants = data['participants']
        if user_id not in participants:
            participants.append(user_id)
        
        is_group = len(participants) > 2 or 'name' in data
        name = data.get('name') if is_group else None
        
        # For direct messages, check if a conversation already exists
        if not is_group and len(participants) == 2:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # Find existing conversation between these two users
            query = """
            SELECT c.conversation_id
            FROM conversations c
            JOIN conversation_participants cp1 ON c.conversation_id = cp1.conversation_id
            JOIN conversation_participants cp2 ON c.conversation_id = cp2.conversation_id
            WHERE c.is_group = FALSE
            AND cp1.user_id = %s
            AND cp2.user_id = %s
            """
            
            cursor.execute(query, (participants[0], participants[1]))
            existing = cursor.fetchone()
            
            if existing:
                cursor.close()
                conn.close()
                return {
                    'statusCode': 200,
                    'headers': {
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'POST',
                        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                    },
                    'body': json.dumps({'conversation_id': existing[0]})
                }
        
        # Create new conversation
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute(
            "INSERT INTO conversations (name, is_group) VALUES (%s, %s) RETURNING conversation_id",
            (name, is_group)
        )
        conversation_id = cursor.fetchone()[0]
        
        # Add participants
        for participant_id in participants:
            cursor.execute(
                "INSERT INTO conversation_participants (conversation_id, user_id) VALUES (%s, %s)",
                (conversation_id, participant_id)
            )
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return {
            'statusCode': 201,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'POST',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'conversation_id': conversation_id})
        }
        
    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'POST',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }

def get_messages(event):
    """Get messages for a conversation"""
    try:
        # Verify user token
        user_id, error = verify_token(event.get('headers', {}))
        if error:
            return error
        
        # Get query parameters
        params = event.get('queryStringParameters', {}) or {}
        conversation_id = params.get('conversation_id')
        
        if not conversation_id:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'GET',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Conversation ID is required'})
            }
        
        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Verify user is a participant in this conversation
        cursor.execute(
            "SELECT 1 FROM conversation_participants WHERE conversation_id = %s AND user_id = %s",
            (conversation_id, user_id)
        )
        
        if not cursor.fetchone():
            cursor.close()
            conn.close()
            return {
                'statusCode': 403,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'GET',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'You are not a participant in this conversation'})
            }
        
        # Get messages
        limit = int(params.get('limit', 50))
        offset = int(params.get('offset', 0))
        
        cursor.execute("""
            SELECT m.message_id, m.sender_id, u.name as sender_name, u.face_image_url as sender_avatar,
                   m.content, m.sent_at, m.is_read, m.attachment_url
            FROM messages m
            JOIN users u ON m.sender_id = u.user_id
            WHERE m.conversation_id = %s
            ORDER BY m.sent_at DESC
            LIMIT %s OFFSET %s
        """, (conversation_id, limit, offset))
        
        messages = cursor.fetchall()
        
        # Format the response
        result = []
        for msg in messages:
            result.append({
                'message_id': msg[0],
                'sender_id': msg[1],
                'sender_name': msg[2],
                'sender_avatar': msg[3],
                'content': msg[4],
                'sent_at': msg[5].isoformat() if msg[5] else None,
                'is_read': msg[6],
                'attachment_url': msg[7],
                'is_mine': msg[1] == user_id
            })
        
        # Update last_read_at for the user
        cursor.execute(
            "UPDATE conversation_participants SET last_read_at = NOW() WHERE conversation_id = %s AND user_id = %s",
            (conversation_id, user_id)
        )
        
        # Mark messages as read
        cursor.execute(
            "UPDATE messages SET is_read = TRUE WHERE conversation_id = %s AND sender_id != %s AND is_read = FALSE",
            (conversation_id, user_id)
        )
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return {
            'statusCode': 200,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps(result)
        }
        
    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }

def send_message(event):
    """Send a new message"""
    try:
        # Verify user token
        user_id, error = verify_token(event.get('headers', {}))
        if error:
            return error
        
        data = json.loads(event['body'])
        
        # Validate input
        if 'conversation_id' not in data:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Conversation ID is required'})
            }
        
        if 'content' not in data and 'attachment_url' not in data:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Message content or attachment is required'})
            }
        
        conversation_id = data['conversation_id']
        content = data.get('content', '')
        attachment_url = data.get('attachment_url')
        
        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Verify user is a participant in this conversation
        cursor.execute(
            "SELECT 1 FROM conversation_participants WHERE conversation_id = %s AND user_id = %s",
            (conversation_id, user_id)
        )
        
        if not cursor.fetchone():
            cursor.close()
            conn.close()
            return {
                'statusCode': 403,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'You are not a participant in this conversation'})
            }
        
        # Insert the message
        cursor.execute(
            """
            INSERT INTO messages (conversation_id, sender_id, content, attachment_url)
            VALUES (%s, %s, %s, %s)
            RETURNING message_id, sent_at
            """,
            (conversation_id, user_id, content, attachment_url)
        )
        
        message_id, sent_at = cursor.fetchone()
        
        # Update the conversation's updated_at timestamp
        cursor.execute(
            "UPDATE conversations SET updated_at = NOW() WHERE conversation_id = %s",
            (conversation_id,)
        )
        
        # Get sender info for response
        cursor.execute(
            "SELECT name, face_image_url FROM users WHERE user_id = %s",
            (user_id,)
        )
        sender_info = cursor.fetchone()
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return {
            'statusCode': 201,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'POST',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({
                'message_id': message_id,
                'sender_id': user_id,
                'sender_name': sender_info[0] if sender_info else None,
                'sender_avatar': sender_info[1] if sender_info else None,
                'content': content,
                'sent_at': sent_at.isoformat() if sent_at else None,
                'is_read': False,
                'attachment_url': attachment_url,
                'is_mine': True
            })
        }
        
    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'POST',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': str(e)})
        }