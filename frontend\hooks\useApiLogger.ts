import { useEffect } from 'react';
import axios from '../services/axiosConfig';

/**
 * Hook to log all API requests and responses
 */
export const useApiLogger = () => {
  useEffect(() => {
    // Add request interceptor
    const requestInterceptor = axios.interceptors.request.use(
      (config) => {
        console.log(`[API] Request: ${config.method?.toUpperCase()} ${config.url}`, {
          headers: config.headers,
          params: config.params,
          data: config.data
        });
        return config;
      },
      (error) => {
        console.error('[API] Request error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor
    const responseInterceptor = axios.interceptors.response.use(
      (response) => {
        console.log(`[API] Response from ${response.config.url}:`, {
          status: response.status,
          statusText: response.statusText,
          data: response.data ? 
            (typeof response.data === 'object' ? 
              {
                ...response.data,
                // If there's an array in the response, log its length
                ...(response.data.glimpses ? { glimpsesCount: response.data.glimpses.length } : {}),
                ...(response.data.movies ? { moviesCount: response.data.movies.length } : {})
              } : 
              response.data) : 
            'No data'
        });
        return response;
      },
      (error) => {
        console.error('[API] Response error:', error.response ? {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data
        } : error.message);
        return Promise.reject(error);
      }
    );

    // Clean up interceptors when component unmounts
    return () => {
      axios.interceptors.request.eject(requestInterceptor);
      axios.interceptors.response.eject(responseInterceptor);
    };
  }, []);
};
