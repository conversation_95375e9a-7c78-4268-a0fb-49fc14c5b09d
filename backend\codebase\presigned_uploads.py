import os
import json
import uuid
import boto3
import jwt
import psycopg2
from dotenv import load_dotenv
from datetime import datetime, timedelta, date

# Load environment variables
load_dotenv()
# Database connection parameters
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_PORT = os.getenv('DB_PORT')
DB_NAME = os.getenv('DB_NAME')

# AWS parameters
AWS_ACCESS_KEY = os.getenv('ACCESS_KEY')
AWS_SECRET_KEY = os.getenv('SECRET_KEY')
S3_BUCKET = os.getenv('S3_BUCKET')
CLOUDFRONT_DOMAIN = os.getenv('CLOUDFRONT_DOMAIN')

# JWT secret
JWT_SECRET = os.getenv('JWT_SECRET')

# S3 client
s3_client = boto3.client(
    's3',
    aws_access_key_id=AWS_ACCESS_KEY,
    aws_secret_access_key=AWS_SECRET_KEY,
    region_name='ap-south-1',  # Mumbai region
    config=boto3.session.Config(signature_version='s3v4')  # Explicitly use SigV4
)

# Create a fresh connection for each function call
def get_db_connection():
    return psycopg2.connect(
        host=DB_HOST,
        user=DB_USER,
        password=DB_PASSWORD,
        port=DB_PORT,
        dbname=DB_NAME
    )

# Updated Media category restrictions for validation
VIDEO_RESTRICTIONS = {
    'story': {'max_size_mb': 5000, 'max_duration_seconds': 61},
    'flash': {'max_size_mb': 5000, 'max_duration_seconds': 91},
    'glimpse': {'max_size_mb': 5000, 'max_duration_seconds': 421},
    'movie': {'max_size_mb': 5000, 'max_duration_seconds': 3601}
}

PHOTO_RESTRICTIONS = {
    'story': {'max_size_mb': 5000},
    'post': {'max_size_mb': 5000}
}

# Valid video categories
VALID_VIDEO_CATEGORIES = ['my_wedding', 'wedding_influencer', 'friends_family_video']

# Custom JSON encoder to handle date objects
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, (date, datetime)):
            return obj.isoformat()
        return super().default(obj)

def validate_token(headers):
    """Validate the JWT token from the authorization header"""
    token = headers.get('Authorization')

    if not token:
        return None, {
            'statusCode': 401,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET, POST",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": "Token is required"})
        }

    try:
        if token.startswith('Bearer '):
            token = token[7:]  # Remove 'Bearer ' prefix
        else:
            # Fallback to old method if format is different
            token = token.split()[1]

        data = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
        return data['user_id'], None
    except jwt.ExpiredSignatureError:
        return None, {
            'statusCode': 401,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET, POST",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": "Token expired"})
        }
    except jwt.InvalidTokenError:
        return None, {
            'statusCode': 401,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET, POST",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": "Invalid token"})
        }

def get_presigned_url(event):
    """
    Generate presigned URLs for direct client-to-S3 uploads
    """
    try:
        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Parse request body
        data = json.loads(event['body'])

        media_type = data.get('media_type')  # 'video' or 'photo'
        media_subtype = data.get('media_subtype')  # 'story', 'flash', 'glimpse', 'movie' for video; 'story', 'post' for photo
        video_category = data.get('video_category')  # 'my_wedding', 'wedding_influencer', 'friends_family_video'
        filename = data.get('filename')
        content_type = data.get('content_type')
        file_size = data.get('file_size')  # Size in bytes

        # Basic validation
        if not all([media_type, media_subtype, filename, content_type, file_size]):
            return {
                'statusCode': 400,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Missing required fields"})
            }

        # Check if media type is valid
        if media_type not in ['video', 'photo']:
            return {
                'statusCode': 400,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Media type must be 'video' or 'photo'"})
            }

        # Validate media subtype
        if media_type == 'video':
            if media_subtype not in VIDEO_RESTRICTIONS:
                return {
                    'statusCode': 400,
                    'headers': {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, POST",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({
                        "error": f"Invalid video subtype. Must be one of {list(VIDEO_RESTRICTIONS.keys())}"
                    })
                }

            # Validate video category
            if not video_category or video_category not in VALID_VIDEO_CATEGORIES:
                return {
                    'statusCode': 400,
                    'headers': {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, POST",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({
                        "error": f"Invalid video category. Must be one of {VALID_VIDEO_CATEGORIES}"
                    })
                }
        elif media_type == 'photo':
            if media_subtype not in PHOTO_RESTRICTIONS:
                return {
                    'statusCode': 400,
                    'headers': {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, POST",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({
                        "error": f"Invalid photo subtype. Must be one of {list(PHOTO_RESTRICTIONS.keys())}"
                    })
                }

        # Check file size
        file_size_mb = file_size / (1024 * 1024)
        if media_type == 'video' and file_size_mb > VIDEO_RESTRICTIONS[media_subtype]['max_size_mb']:
            return {
                'statusCode': 400,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({
                    "error": f"Video size exceeds the limit for {media_subtype} type. "
                            f"Max: {VIDEO_RESTRICTIONS[media_subtype]['max_size_mb']} MB"
                })
            }
        elif media_type == 'photo' and file_size_mb > PHOTO_RESTRICTIONS[media_subtype]['max_size_mb']:
            return {
                'statusCode': 400,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({
                    "error": f"Photo size exceeds the limit for {media_subtype} type. "
                            f"Max: {PHOTO_RESTRICTIONS[media_subtype]['max_size_mb']} MB"
                })
            }

        # Check content type
        if media_type == 'video' and not content_type.startswith('video/'):
            return {
                'statusCode': 400,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Invalid video content type"})
            }
        elif media_type == 'photo' and not content_type.startswith('image/'):
            return {
                'statusCode': 400,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Invalid image content type"})
            }

        # Generate a unique media ID and S3 key
        media_id = str(uuid.uuid4())
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        extension = os.path.splitext(filename)[1]

        # Create S3 key patterns based on media type
        s3_folder = f"{user_id}/{media_id}"
        main_key = f"{s3_folder}/{timestamp}{extension}"
        thumbnail_key = f"{s3_folder}/thumbnail_{timestamp}.jpg" if media_type == 'video' else None

        # Generate presigned URL for main file
        main_presigned_url = s3_client.generate_presigned_url(
            'put_object',
            Params={
                'Bucket': S3_BUCKET,
                'Key': main_key,
                'ContentType': content_type
            },
            ExpiresIn=3600  # URL valid for 1 hour
        )

        # Generate presigned URL for thumbnail if this is a video
        thumbnail_presigned_url = None
        if thumbnail_key:
            thumbnail_presigned_url = s3_client.generate_presigned_url(
                'put_object',
                Params={
                    'Bucket': S3_BUCKET,
                    'Key': thumbnail_key,
                    'ContentType': 'image/jpeg'
                },
                ExpiresIn=3600  # URL valid for 1 hour
            )

        # URLs for accessing the files after upload (via CloudFront)
        main_url = f"https://{CLOUDFRONT_DOMAIN}/{main_key}"
        thumbnail_url = f"https://{CLOUDFRONT_DOMAIN}/{thumbnail_key}" if thumbnail_key else None

        # Store upload record in database
        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # Insert upload record with video_category when applicable
            if media_type == 'video':
                cursor.execute(
                    """INSERT INTO pending_uploads (upload_id, user_id, media_type, media_subtype,
                       video_category, media_id, main_key, thumbnail_key, expires_at, status)
                       VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)""",
                    (str(uuid.uuid4()), user_id, media_type, media_subtype, video_category, media_id,
                     main_key, thumbnail_key, datetime.now() + timedelta(hours=1), 'pending')
                )
            else:
                cursor.execute(
                    """INSERT INTO pending_uploads (upload_id, user_id, media_type, media_subtype,
                       media_id, main_key, thumbnail_key, expires_at, status)
                       VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)""",
                    (str(uuid.uuid4()), user_id, media_type, media_subtype, media_id, main_key,
                     thumbnail_key, datetime.now() + timedelta(hours=1), 'pending')
                )
            conn.commit()

        except Exception as e:
            conn.rollback()
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

        return {
            'statusCode': 200,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, POST",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({
                "upload_urls": {
                    "main": main_presigned_url,
                    "thumbnail": thumbnail_presigned_url
                },
                "access_urls": {
                    "main": main_url,
                    "thumbnail": thumbnail_url
                },
                "media_id": media_id,
                "expires_in_seconds": 3600
            })
        }

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, POST",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }

def complete_upload(event):
    """
    Complete the upload process and register the media in the database
    """
    try:
        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        conn = get_db_connection()
        cursor = conn.cursor()

        # Verify face
        cursor.execute("SELECT face_verified FROM users WHERE user_id = %s", (user_id,))
        result = cursor.fetchone()
        if not result or not result[0]:
            return {
                'statusCode': 403,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Face verification required before completing uploads"})
            }

        data = json.loads(event['body'])
        media_id = data.get('media_id')
        caption = data.get('caption')
        duration = data.get('duration')
        tags = data.get('tags', [])
        transcoded_versions = data.get('transcoded_versions', [])
        partner = data.get('partner')
        place = data.get('place')
        event_type = data.get('event_type')
        wedding_style = data.get('wedding_style')
        budget = data.get('budget')
        vendor_details = data.get('vendor_details', {})

        if not media_id:
            return {
                'statusCode': 400,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "media_id is required"})
            }

        # Fetch upload details
        cursor.execute("""
            SELECT main_key, thumbnail_key, media_type, media_subtype, video_category
            FROM pending_uploads
            WHERE media_id = %s AND user_id = %s AND status = 'pending'
        """, (media_id, user_id))
        result = cursor.fetchone()
        if not result:
            return {
                'statusCode': 404, 
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                }, 
                'body': json.dumps({"error": "Pending upload not found"})
            }

        main_key, thumbnail_key, media_type, media_subtype, video_category = result
        main_url = f"https://{CLOUDFRONT_DOMAIN}/{main_key}"
        thumbnail_url = f"https://{CLOUDFRONT_DOMAIN}/{thumbnail_key}" if thumbnail_key else None

        # Insert into media table
        cursor.execute("""
            INSERT INTO media (media_id, user_id, media_type, is_story, media_url, thumbnail_url, subtype)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """, (media_id, user_id, media_type, media_subtype == 'story', main_url, thumbnail_url, media_subtype if media_type == 'video' else None))

        if media_subtype == 'story':
            cursor.execute("UPDATE pending_uploads SET status = 'completed' WHERE media_id = %s", (media_id,))
            conn.commit()
            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({
                    "message": "Story uploaded successfully",
                    "media_id": media_id,
                    "url": main_url,
                    "thumbnail_url": thumbnail_url
                })
            }

        if media_type == 'photo':
            if not (caption and place and event_type):
                return {
                    'statusCode': 400, 
                    'headers': {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, POST",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    }, 
                    'body': json.dumps({"error": "Missing photo details"})
                }
            cursor.execute("""
                INSERT INTO media_photo_details (media_id, caption, place, event_type)
                VALUES (%s, %s, %s, %s)
            """, (media_id, caption, place, event_type))

        elif media_type == 'video':
            if not (caption and place and event_type and wedding_style and budget and video_category):
                return {
                    'statusCode': 400,
                    'headers': {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, POST",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    }, 
                    'body': json.dumps({"error": "Missing video details"})
                }
            cursor.execute("""
                INSERT INTO media_video_details (media_id, video_type, caption, partner, place, event_type, wedding_style, budget)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """, (media_id, video_category, caption, partner, place, event_type, wedding_style, budget))

            # Flexible vendor validation
            complete_vendors = 0
            vendor_keys = ['venue', 'photographer', 'makeup_artist', 'decoration', 'caterer']
            for key in vendor_keys:
                name = vendor_details.get(f"{key}_name")
                contact = vendor_details.get(f"{key}_contact")
                if name and contact:
                    complete_vendors += 1

            if video_category == 'my_wedding' and complete_vendors < 4:
                return {
                    'statusCode': 400,
                    'headers': {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, POST",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({
                        "error": f"'my_wedding' videos require at least 4 complete vendor details (name and contact). You provided {complete_vendors}."
                    })
                }

            if video_category == 'wedding_vlog' and complete_vendors < 1:
                return {
                    'statusCode': 400,
                    'headers': {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, POST",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({
                        "error": "'wedding_vlog' videos require at least 1 complete vendor detail (name and contact)."
                    })
                }

            # Insert vendor data
            if vendor_details:
                additional_vendors = {
                    k: v for k, v in vendor_details.items()
                    if k not in {
                        'venue_name', 'venue_contact',
                        'photographer_name', 'photographer_contact',
                        'makeup_artist_name', 'makeup_artist_contact',
                        'decoration_name', 'decoration_contact',
                        'caterer_name', 'caterer_contact'
                    }
                }

                cursor.execute("""
                    INSERT INTO media_vendor_details (
                        media_id, media_type, user_id,
                        venue_name, venue_contact,
                        photographer_name, photographer_contact,
                        makeup_artist_name, makeup_artist_contact,
                        decoration_name, decoration_contact,
                        caterer_name, caterer_contact,
                        additional_vendors
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    media_id, 'video', user_id,
                    vendor_details.get('venue_name'), vendor_details.get('venue_contact'),
                    vendor_details.get('photographer_name'), vendor_details.get('photographer_contact'),
                    vendor_details.get('makeup_artist_name'), vendor_details.get('makeup_artist_contact'),
                    vendor_details.get('decoration_name'), vendor_details.get('decoration_contact'),
                    vendor_details.get('caterer_name'), vendor_details.get('caterer_contact'),
                    json.dumps(additional_vendors)
                ))

        # Finalize
        cursor.execute("UPDATE pending_uploads SET status = 'completed' WHERE media_id = %s", (media_id,))
        conn.commit()

        return {
            'statusCode': 200,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, POST",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({
                "message": f"{media_type.capitalize()} uploaded successfully",
                "media_id": media_id,
                "url": main_url,
                "thumbnail_url": thumbnail_url
            })
        }

    except Exception as e:
        if 'cursor' in locals():
            cursor.execute("ROLLBACK")
        return {
            'statusCode': 500,
            'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Upload failed: {str(e)}"})
        }
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def verify_face(event):
    """
    Record face verification for a user and store the face image
    """
    try:
        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Parse request body
        data = json.loads(event['body'])

        # Get face image base64
        face_image = data.get('face_image')

        if not face_image:
            return {
                'statusCode': 400,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Face image is required"})
            }

        # Generate a unique ID for the face image
        face_id = str(uuid.uuid4())
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')

        # Create S3 key for face image
        face_key = f"{user_id}/face/{face_id}_{timestamp}.jpg"

        try:
            # Here we assume face_image is base64 encoded
            import base64
            from io import BytesIO

            # Remove data URL prefix if present (e.g., "data:image/jpeg;base64,")
            if "," in face_image:
                face_image = face_image.split(",")[1]

            image_data = base64.b64decode(face_image)

            # Upload to S3
            s3_client.put_object(
                Bucket=S3_BUCKET,
                Key=face_key,
                Body=BytesIO(image_data),
                ContentType='image/jpeg'
            )

            # Face image URL for future reference
            face_url = f"https://{CLOUDFRONT_DOMAIN}/{face_key}"

            # Update user record to mark face as verified
            conn = get_db_connection()
            cursor = conn.cursor()

            try:
                cursor.execute(
                    """UPDATE users SET face_verified = TRUE, face_image_url = %s,
                       updated_at = NOW() WHERE user_id = %s""",
                    (face_url, user_id)
                )
                conn.commit()

                return {
                    'statusCode': 200,
                    'headers': {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, POST",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({
                        "message": "Face verification successful",
                        "face_verified": True,
                        "face_image_url": face_url
                    })
                }

            except Exception as e:
                conn.rollback()
                return {
                    'statusCode': 500,
                    'headers': {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, POST",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({"error": f"Database error: {str(e)}"})
                }
            finally:
                cursor.close()
                conn.close()

        except Exception as e:
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Face image processing error: {str(e)}"})
            }

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, POST",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }