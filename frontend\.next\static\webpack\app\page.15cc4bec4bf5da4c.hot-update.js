"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./services/api.ts":
/*!*************************!*\
  !*** ./services/api.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authService: () => (/* binding */ authService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   uploadService: () => (/* binding */ uploadService),\n/* harmony export */   userService: () => (/* binding */ userService)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n// services/api.ts\n\n// Use environment variable or fallback to localhost for development\nconst BASE_URL = \"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub\" || 0;\n// Log the API URL being used\n// console.log(`API Service using base URL: ${BASE_URL}`);\n// For development, use a CORS proxy if needed\nconst API_URL = BASE_URL;\nif (true) {\n    // Uncomment the line below to use a CORS proxy in development if needed\n    // API_URL = `https://cors-anywhere.herokuapp.com/${BASE_URL}`;\n    // Log the API URL for debugging\n    console.log('Development mode detected, using API URL:', API_URL);\n}\n// Log the API URL being used\nconsole.log(\"API Service using base URL: \".concat(API_URL));\nconst TOKEN_KEY = 'token'; // Keep using your existing token key\nconst JWT_TOKEN_KEY = 'jwt_token'; // Alternative key for compatibility\n// Create axios instance\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_URL,\n    headers: {\n        'Content-Type': 'application/json'\n    },\n    timeout: 60000,\n    withCredentials: false // Helps with CORS issues\n});\n// Helper to get token from storage (checking both keys)\nconst getToken = ()=>{\n    if (false) {}\n    return localStorage.getItem(TOKEN_KEY);\n};\n// Helper to save token to storage (using both keys for compatibility)\nconst saveToken = (token)=>{\n    if (false) {}\n    console.log('Saving token to localStorage:', token.substring(0, 15) + '...');\n    localStorage.setItem(TOKEN_KEY, token);\n    localStorage.setItem(JWT_TOKEN_KEY, token); // For compatibility with other components\n};\n// Request interceptor to add auth token to all requests\napi.interceptors.request.use((config)=>{\n    const token = getToken();\n    if (token && config.headers) {\n        config.headers.Authorization = \"Bearer \".concat(token);\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Authentication services\nconst authService = {\n    // Register a new user\n    signup: async (signupData)=>{\n        try {\n            console.log('Attempting signup with data:', {\n                ...signupData,\n                password: signupData.password ? '********' : undefined\n            });\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"\".concat(BASE_URL, \"/signup\"), signupData, {\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            console.log('Signup response received:', {\n                success: true,\n                hasToken: !!response.data.token\n            });\n            if (response.data.token) {\n                saveToken(response.data.token);\n            } else {\n                console.warn('No token received in signup response');\n            }\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            console.error(\"Signup error:\", error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: \"An error occurred during signup\"\n            };\n        }\n    },\n    // Register a new vendor\n    registerVendor: async (vendorData)=>{\n        try {\n            console.log('Attempting vendor registration with data:', {\n                ...vendorData,\n                password: vendorData.password ? '********' : undefined\n            });\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"\".concat(BASE_URL, \"/register-vendor\"), vendorData, {\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            console.log('Vendor registration response received:', {\n                success: true,\n                hasToken: !!response.data.token\n            });\n            if (response.data.token) {\n                saveToken(response.data.token);\n            } else {\n                console.warn('No token received in vendor registration response');\n            }\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            console.error(\"Vendor registration error:\", error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: \"An error occurred during vendor registration\"\n            };\n        }\n    },\n    // Get business types\n    getBusinessTypes: async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(BASE_URL, \"/business-types\"));\n            return response.data.business_types;\n        } catch (error) {\n            var _error_response;\n            console.error(\"Get business types error:\", error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: \"An error occurred while fetching business types\"\n            };\n        }\n    },\n    // Get vendor profile\n    getVendorProfile: async ()=>{\n        try {\n            const token = getToken();\n            if (!token) {\n                throw {\n                    error: 'No authentication token found'\n                };\n            }\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(BASE_URL, \"/vendor-profile\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            return response.data.profile;\n        } catch (error) {\n            var _error_response;\n            console.error(\"Get vendor profile error:\", error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: \"An error occurred while fetching vendor profile\"\n            };\n        }\n    },\n    // Login with email/mobile and password\n    // In services/api.ts - login function\n    login: async (credentials)=>{\n        try {\n            console.log('Attempting login with:', {\n                email: credentials.email,\n                mobile_number: credentials.mobile_number,\n                password: '********'\n            });\n            const response = await api.post('/login', credentials);\n            console.log('Login response received:', {\n                success: true,\n                hasToken: !!response.data.token,\n                tokenPreview: response.data.token ? \"\".concat(response.data.token.substring(0, 10), \"...\") : 'none'\n            });\n            // Save token to localStorage with explicit console logs\n            if (response.data.token) {\n                console.log('Saving token to localStorage...');\n                localStorage.setItem('token', response.data.token);\n                // Verify token was saved\n                const savedToken = localStorage.getItem('token');\n                console.log(\"Token verification: \".concat(savedToken ? 'Successfully saved' : 'Failed to save'));\n            } else {\n                console.warn('No token received in login response');\n            }\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            console.error('Login error:', error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: 'Invalid credentials'\n            };\n        }\n    },\n    // Authenticate with Clerk\n    clerkAuth: async (clerkData)=>{\n        try {\n            console.log('Attempting Clerk authentication');\n            const response = await api.post('/clerk_auth', clerkData);\n            console.log('Clerk auth response received:', {\n                success: true,\n                hasToken: !!response.data.token\n            });\n            // Save token to localStorage\n            if (response.data.token) {\n                saveToken(response.data.token);\n            } else {\n                console.warn('No token received in Clerk auth response');\n            }\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            console.error('Clerk authentication error:', error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: 'Clerk authentication failed'\n            };\n        }\n    },\n    // Check if user profile is complete\n    checkProfile: async ()=>{\n        try {\n            const token = getToken();\n            if (!token) {\n                throw {\n                    error: 'No authentication token found'\n                };\n            }\n            console.log('Checking user profile');\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(BASE_URL, \"/check-profile\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            console.error('Profile check error:', error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: 'Failed to check profile'\n            };\n        }\n    },\n    // Get the current token\n    getToken: ()=>{\n        return getToken();\n    },\n    // Check if the user is authenticated\n    isAuthenticated: ()=>{\n        return !!getToken();\n    },\n    // Logout - clear token from localStorage\n    logout: ()=>{\n        console.log('Logging out and removing tokens');\n        localStorage.removeItem(TOKEN_KEY);\n        localStorage.removeItem(JWT_TOKEN_KEY);\n    }\n};\n// User services\nconst userService = {\n    // Get user details - uses GET method with explicit token in header\n    getUserDetails: async ()=>{\n        try {\n            const token = getToken();\n            if (!token) {\n                throw {\n                    error: 'No authentication token found'\n                };\n            }\n            console.log('Fetching user details');\n            // Set the correct Authorization format (Bearer + token)\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(BASE_URL, \"/user-details\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            console.error(\"Error fetching user details:\", error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: 'Failed to fetch user details'\n            };\n        }\n    },\n    // Update user details\n    updateUser: async (userData)=>{\n        try {\n            console.log('Updating user details');\n            const response = await api.put('/update-user', userData);\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            console.error('Update user error:', error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: 'Failed to update user'\n            };\n        }\n    }\n};\n// Helper to check if user is authenticated\nconst isAuthenticated = ()=>{\n    if (false) {}\n    const token = getToken();\n    return !!token; // Return true if token exists, false otherwise\n};\n// Upload services\nconst uploadService = {\n    /**\r\n   * Verify user's face using captured image\r\n   * @param faceImage Base64 encoded image data (without data URL prefix)\r\n   */ verifyFace: async (faceImage)=>{\n        try {\n            console.log('Sending face verification request');\n            const token = getToken();\n            // Create the request payload\n            const payload = {\n                face_image: faceImage\n            };\n            const payloadSize = JSON.stringify(payload).length;\n            console.log('Request payload size:', payloadSize);\n            // Check if payload is too large\n            if (payloadSize > 1000000) {\n                console.warn('Warning: Payload is very large, which may cause issues with the API');\n            }\n            // No mock implementation - always use the real API\n            console.log('Using real face verification API');\n            // Make the real API call\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"\".concat(API_URL, \"/verify-face\"), payload, {\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': token ? \"Bearer \".concat(token) : ''\n                },\n                timeout: 60000,\n                withCredentials: false\n            });\n            console.log('Face verification response:', response.data);\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            console.error('Error verifying face:', error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: 'Face verification failed'\n            };\n        }\n    },\n    /**\r\n   * Get pre-signed URLs for uploading media to S3\r\n   */ getPresignedUrl: async (request)=>{\n        try {\n            console.log('Getting presigned URL with request:', request);\n            // Ensure we have a valid token\n            const token = getToken();\n            if (!token) {\n                console.warn('No authentication token found when getting presigned URL');\n            }\n            // Make the API call with explicit headers\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"\".concat(API_URL, \"/get-upload-url\"), request, {\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': token ? \"Bearer \".concat(token) : ''\n                },\n                timeout: 30000 // 30 seconds timeout\n            });\n            console.log('Presigned URL response:', response.data);\n            // Validate the response\n            if (!response.data.media_id || !response.data.upload_urls || !response.data.upload_urls.main) {\n                throw new Error('Invalid response from get-upload-url API');\n            }\n            return response.data;\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error('Error getting presigned URL:', error);\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                throw {\n                    error: 'Authentication failed. Please log in again.'\n                };\n            }\n            throw ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data) || {\n                error: 'Failed to get upload URL'\n            };\n        }\n    },\n    /**\r\n   * Complete the upload process by notifying the backend\r\n   */ completeUpload: async (request)=>{\n        try {\n            console.log('Completing upload with request:', JSON.stringify(request, null, 2));\n            // Log the media_subtype for debugging\n            console.log(\"API SERVICE - Complete upload - Media subtype: \".concat(request.media_subtype || 'Not set'));\n            console.log(\"API SERVICE - Complete upload - Media type: \".concat(request.media_type));\n            console.log(\"API SERVICE - Complete upload - Media ID: \".concat(request.media_id));\n            // Validate the request\n            if (!request.media_id) {\n                console.error('Missing media_id in completeUpload request');\n                throw new Error('Missing media_id in request');\n            }\n            if (!request.title || !request.title.trim()) {\n                console.error('Missing title in completeUpload request');\n                throw new Error('Please provide a title for your upload');\n            }\n            // Ensure we have a valid token\n            const token = getToken();\n            if (!token) {\n                console.warn('No authentication token found when completing upload');\n            }\n            // Make the API call with explicit headers\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"\".concat(API_URL, \"/complete-upload\"), request, {\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': token ? \"Bearer \".concat(token) : ''\n                },\n                timeout: 60000 // 60 seconds timeout for completion\n            });\n            console.log('Upload completion response:', response.data);\n            // Validate the response\n            if (!response.data.message) {\n                throw new Error('Invalid response from complete-upload API');\n            }\n            return response.data;\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error('Error completing upload:', error);\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                throw {\n                    error: 'Authentication failed. Please log in again.'\n                };\n            }\n            throw ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data) || {\n                error: 'Failed to complete upload'\n            };\n        }\n    },\n    /**\r\n   * Upload a file to a presigned URL with optimized performance\r\n   */ uploadToPresignedUrl: async (url, file, onProgress)=>{\n        try {\n            console.log('Starting simple direct upload for file:', file.name);\n            console.log('File type:', file.type);\n            console.log('File size:', (file.size / (1024 * 1024)).toFixed(2) + ' MB');\n            console.log('Upload URL:', url);\n            // Report initial progress\n            if (onProgress) onProgress(10);\n            // Start timing the upload\n            const startTime = Date.now();\n            let lastProgressUpdate = 0;\n            // Use simple direct upload for all files\n            console.log('Using simple direct upload');\n            // Create simple headers for direct upload\n            const headers = {\n                'Content-Type': file.type,\n                'Content-Length': file.size.toString()\n            };\n            // Perform simple direct upload to the presigned URL\n            await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(url, file, {\n                headers,\n                timeout: 3600000,\n                maxBodyLength: Infinity,\n                maxContentLength: Infinity,\n                onUploadProgress: (progressEvent)=>{\n                    if (onProgress && progressEvent.total) {\n                        // Calculate raw percentage\n                        const rawPercent = Math.round(progressEvent.loaded * 100 / progressEvent.total);\n                        // Update UI in 5% increments for smoother progress\n                        if (rawPercent >= lastProgressUpdate + 5 || rawPercent === 100) {\n                            lastProgressUpdate = rawPercent;\n                            // Map to 10-95% range for UI\n                            const percentCompleted = 10 + Math.floor(rawPercent * 85 / 100);\n                            onProgress(percentCompleted);\n                            // Calculate and log upload speed\n                            const elapsedSeconds = (Date.now() - startTime) / 1000;\n                            if (elapsedSeconds > 0) {\n                                const speedMBps = (progressEvent.loaded / elapsedSeconds / (1024 * 1024)).toFixed(2);\n                                console.log(\"Upload progress: \".concat(percentCompleted, \"% at \").concat(speedMBps, \"MB/s\"));\n                            }\n                        }\n                    }\n                }\n            });\n            // Calculate final stats\n            const endTime = Date.now();\n            const elapsedSeconds = (endTime - startTime) / 1000;\n            const uploadSpeed = (file.size / elapsedSeconds / (1024 * 1024)).toFixed(2);\n            console.log(\"Upload completed in \".concat(elapsedSeconds.toFixed(2), \"s at \").concat(uploadSpeed, \"MB/s\"));\n            console.log('Response status: 200 (success)');\n            // Report completion\n            if (onProgress) onProgress(100);\n            console.log('File uploaded successfully');\n        } catch (error) {\n            console.error('Error uploading file:', error);\n            // Provide more detailed error information\n            let errorMessage = 'Failed to upload file';\n            if (error.message) {\n                if (error.message.includes('Network Error') || error.message.includes('CORS')) {\n                    errorMessage = 'Network error or CORS issue. Please try again or contact support.';\n                } else {\n                    errorMessage = \"Upload error: \".concat(error.message);\n                }\n            }\n            if (error.response) {\n                console.error('Response status:', error.response.status);\n                console.error('Response headers:', error.response.headers);\n                console.error('Response data:', error.response.data);\n                if (error.response.status === 403) {\n                    errorMessage = 'Permission denied. The upload URL may have expired.';\n                }\n            }\n            throw {\n                error: errorMessage\n            };\n        }\n    },\n    /**\r\n   * Handle the complete upload process\r\n   */ handleUpload: async (file, mediaType, category, title, description, tags, details, duration, thumbnail, onProgress)=>{\n        try {\n            console.log('API SERVICE - handleUpload called with params:', {\n                fileName: file === null || file === void 0 ? void 0 : file.name,\n                fileSize: Math.round(file.size / (1024 * 1024) * 100) / 100 + ' MB',\n                mediaType,\n                category,\n                title,\n                description,\n                tagsCount: tags === null || tags === void 0 ? void 0 : tags.length,\n                detailsCount: details ? Object.keys(details).length : 0,\n                videoCategory: (details === null || details === void 0 ? void 0 : details.video_category) || 'Not set',\n                duration,\n                hasThumbnail: !!thumbnail\n            });\n            // Log the video_category from details\n            if (mediaType === 'video') {\n                console.log('API SERVICE - Video category from details:', details === null || details === void 0 ? void 0 : details.video_category);\n                console.log('API SERVICE - All detail fields:', details ? JSON.stringify(details) : 'No details');\n            }\n            if (!file) {\n                throw new Error('No file provided');\n            }\n            // Log the file size and selected category without overriding the user's selection\n            if (mediaType === 'video') {\n                const fileSizeMB = Math.round(file.size / (1024 * 1024) * 100) / 100;\n                const categoryLimits = {\n                    'story': 50,\n                    'flash': 100,\n                    'glimpse': 250,\n                    'movie': 2000\n                };\n                const sizeLimit = categoryLimits[category] || 250;\n                console.log(\"API SERVICE - File size: \".concat(fileSizeMB, \" MB, Selected category: \").concat(category));\n                console.log(\"API SERVICE - Size limit for \".concat(category, \": \").concat(sizeLimit, \" MB\"));\n            // Warning is now handled in the presignedUrlRequest section\n            }\n            console.log('API SERVICE - Starting upload process for:', file.name);\n            // Update progress to 10%\n            if (onProgress) onProgress(10);\n            // Step 1: Get presigned URLs\n            console.log('API SERVICE - Creating presigned URL request with category:', category);\n            // Ensure we're using the correct backend category names\n            // Frontend: flashes, glimpses, movies, moments\n            // Backend: flash, glimpse, movie, story\n            let backendCategory = mediaType === 'photo' ? category === 'story' ? 'story' : 'post' // For photos: either 'story' or 'post'\n             : category; // For videos: keep the original category\n            // Double-check that we have a valid category\n            const validCategories = [\n                'story',\n                'flash',\n                'glimpse',\n                'movie',\n                'post'\n            ];\n            if (!validCategories.includes(backendCategory)) {\n                console.log(\"API SERVICE - WARNING: Invalid category '\".concat(backendCategory, \"'. Using 'flash' as fallback.\"));\n                console.log(\"API SERVICE - Valid categories are: \".concat(validCategories.join(', ')));\n                console.log(\"API SERVICE - Category type: \".concat(typeof backendCategory));\n                console.log(\"API SERVICE - Category value: '\".concat(backendCategory, \"'\"));\n                // Use 'flash' as the default for videos instead of 'glimpse'\n                backendCategory = 'flash';\n            }\n            console.log(\"API SERVICE - Original category from context: \".concat(category));\n            console.log(\"API SERVICE - Using backend category: \".concat(backendCategory));\n            // Log the file size to help with debugging\n            const fileSizeMB = Math.round(file.size / (1024 * 1024) * 100) / 100;\n            console.log(\"API SERVICE - File size: \".concat(fileSizeMB, \" MB\"));\n            // Log the category limits\n            const categoryLimits = {\n                'story': 50,\n                'flash': 100,\n                'glimpse': 250,\n                'movie': 2000\n            };\n            const sizeLimit = categoryLimits[backendCategory] || 250;\n            console.log(\"API SERVICE - Size limit for \".concat(backendCategory, \": \").concat(sizeLimit, \" MB\"));\n            // Log a warning if the file size exceeds the limit\n            if (fileSizeMB > sizeLimit) {\n                console.log(\"API SERVICE - WARNING: File size (\".concat(fileSizeMB, \" MB) exceeds the limit for \").concat(backendCategory, \" (\").concat(sizeLimit, \" MB).\"));\n                console.log(\"API SERVICE - The upload may fail. Consider selecting a category with a higher size limit.\");\n            }\n            const presignedUrlRequest = {\n                media_type: mediaType,\n                media_subtype: backendCategory,\n                filename: file.name,\n                content_type: file.type,\n                file_size: file.size\n            };\n            // Log the presigned URL request\n            console.log(\"API SERVICE - Sending presigned URL request with media_subtype: \".concat(backendCategory));\n            // Add video_category for videos\n            if (mediaType === 'video') {\n                // Get the video_category from details - no default value\n                console.log('API SERVICE - Details object:', details);\n                console.log('API SERVICE - Details keys:', details ? Object.keys(details) : 'No details');\n                const videoCategory = details === null || details === void 0 ? void 0 : details.video_category;\n                console.log('API SERVICE - Video category from details:', videoCategory);\n                // Make sure we're using a valid video_category\n                const allowedCategories = [\n                    'my_wedding',\n                    'wedding_vlog'\n                ];\n                // If no video_category is provided, use a default one\n                if (!videoCategory) {\n                    console.error(\"API SERVICE - Missing video_category. Using default 'my_wedding'.\");\n                    // Use 'my_wedding' as the default video_category\n                    presignedUrlRequest.video_category = 'my_wedding';\n                    console.log('API SERVICE - Using default video_category: my_wedding');\n                } else {\n                    // Map the UI category to the backend category if needed\n                    let backendVideoCategory = videoCategory;\n                    // If the category is in UI format, map it to backend format\n                    if (videoCategory === 'friends_family_videos') {\n                        backendVideoCategory = 'friends_family_video';\n                        console.log('API SERVICE - Mapped UI category to backend category:', backendVideoCategory);\n                    } else if (videoCategory === 'my_wedding_videos') {\n                        backendVideoCategory = 'my_wedding';\n                        console.log('API SERVICE - Mapped UI category to backend category:', backendVideoCategory);\n                    }\n                    // If the video_category is not allowed, throw an error\n                    if (!allowedCategories.includes(backendVideoCategory)) {\n                        console.error(\"API SERVICE - Invalid video_category: \".concat(backendVideoCategory, \". Must be one of \").concat(JSON.stringify(allowedCategories)));\n                        throw new Error(\"Invalid video category. Must be one of \".concat(JSON.stringify(allowedCategories)));\n                    }\n                    // Use the provided video_category\n                    presignedUrlRequest.video_category = backendVideoCategory;\n                    console.log('API SERVICE - Using video_category:', backendVideoCategory);\n                    console.log(\"API SERVICE - Final video category: \".concat(backendVideoCategory));\n                    console.log(\"API SERVICE - Complete presigned URL request:\", JSON.stringify(presignedUrlRequest, null, 2));\n                }\n                // Log the category and file size limits\n                console.log(\"API SERVICE - Original category from UI: \".concat(category));\n                console.log(\"API SERVICE - Using backend media subtype: \".concat(backendCategory));\n                // Log size limits based on category without overriding the user's selection\n                const categoryLimits = {\n                    'story': 50,\n                    'flash': 100,\n                    'glimpse': 250,\n                    'movie': 2000\n                };\n                const sizeLimit = categoryLimits[backendCategory] || 250;\n                console.log(\"API SERVICE - Size limit for \".concat(backendCategory, \": \").concat(sizeLimit, \" MB (file size: \").concat(fileSizeMB, \" MB)\"));\n                // Log a warning if the file size exceeds the limit for the selected category\n                if (fileSizeMB > sizeLimit) {\n                    console.log(\"API SERVICE - WARNING: File size (\".concat(fileSizeMB, \" MB) exceeds the limit for \").concat(backendCategory, \" (\").concat(sizeLimit, \" MB).\"));\n                    console.log(\"API SERVICE - The upload may fail. Consider selecting a category with a higher size limit.\");\n                }\n                // Add duration if available\n                if (duration) {\n                    console.log(\"Video duration: \".concat(duration, \" seconds\"));\n                // You could add this to the request if the API supports it\n                // presignedUrlRequest.duration = duration;\n                }\n            }\n            const presignedUrlResponse = await uploadService.getPresignedUrl(presignedUrlRequest);\n            // Update progress to 20%\n            if (onProgress) onProgress(20);\n            // Step 2: Upload the file to the presigned URL (20-70% of progress)\n            await uploadService.uploadToPresignedUrl(presignedUrlResponse.upload_urls.main, file, (uploadProgress)=>{\n                // Map the upload progress from 0-100 to 20-70 in our overall progress\n                if (onProgress) onProgress(20 + uploadProgress * 0.5);\n            });\n            // Step 3: Upload thumbnail if available (70-90% of progress)\n            if (thumbnail && presignedUrlResponse.upload_urls.thumbnail) {\n                console.log('Uploading thumbnail:', thumbnail.name);\n                // Update progress to 70%\n                if (onProgress) onProgress(70);\n                await uploadService.uploadToPresignedUrl(presignedUrlResponse.upload_urls.thumbnail, thumbnail, (uploadProgress)=>{\n                    // Map the upload progress from 0-100 to 70-90 in our overall progress\n                    if (onProgress) onProgress(70 + uploadProgress * 0.2);\n                });\n            }\n            // Update progress to 90%\n            if (onProgress) onProgress(90);\n            // Step 4: Complete the upload (90-100% of progress)\n            try {\n                // Validate title\n                if (!title || !title.trim()) {\n                    console.error('Title is empty or missing');\n                    throw new Error('Please provide a title for your upload');\n                }\n                console.log('Title is valid:', title);\n                // Prepare the complete upload request\n                const completeRequest = {\n                    media_id: presignedUrlResponse.media_id,\n                    media_type: mediaType,\n                    media_subtype: backendCategory,\n                    title: title.trim(),\n                    description: description || '',\n                    tags: tags || []\n                };\n                console.log(\"API SERVICE - Setting media_subtype in completeRequest: \".concat(backendCategory));\n                // Log the description being sent\n                console.log('Sending description:', description || '');\n                // Add duration for videos if available\n                if (mediaType === 'video' && duration) {\n                    completeRequest.duration = duration;\n                }\n                // Add backend-compatible fields directly to the request\n                completeRequest.caption = title.trim();\n                completeRequest.place = (details === null || details === void 0 ? void 0 : details.personal_place) || (details === null || details === void 0 ? void 0 : details.place) || '';\n                // Add content-type specific fields based on backend requirements\n                if (mediaType === 'photo' && backendCategory !== 'story') {\n                    // Photos (non-stories) require: caption, place, event_type\n                    completeRequest.event_type = (details === null || details === void 0 ? void 0 : details.personal_event_type) || (details === null || details === void 0 ? void 0 : details.eventType) || '';\n                    console.log('API SERVICE - Photo upload: Added event_type =', completeRequest.event_type);\n                } else if (mediaType === 'video') {\n                    // Videos require: caption, place, partner, budget, wedding_style, video_category\n                    completeRequest.partner = (details === null || details === void 0 ? void 0 : details.personal_life_partner) || (details === null || details === void 0 ? void 0 : details.lifePartner) || '';\n                    completeRequest.budget = (details === null || details === void 0 ? void 0 : details.personal_budget) || (details === null || details === void 0 ? void 0 : details.budget) || '';\n                    completeRequest.wedding_style = (details === null || details === void 0 ? void 0 : details.personal_wedding_style) || (details === null || details === void 0 ? void 0 : details.weddingStyle) || '';\n                    completeRequest.event_type = (details === null || details === void 0 ? void 0 : details.personal_event_type) || (details === null || details === void 0 ? void 0 : details.eventType) || '';\n                    completeRequest.video_category = (details === null || details === void 0 ? void 0 : details.video_category) || backendCategory;\n                    console.log('API SERVICE - Video upload: Added partner, budget, wedding_style, event_type, video_category');\n                } else if (mediaType === 'photo' && backendCategory === 'story') {\n                    // Moments (stories) only require caption and face verification - no additional fields\n                    console.log('API SERVICE - Moments upload: Only caption required, skipping additional fields');\n                }\n                // Extract vendor details from the details object\n                const vendorDetails = {};\n                // Process vendor details from the details object\n                if (details) {\n                    console.log('API SERVICE - Processing vendor details from details object');\n                    console.log('API SERVICE - Raw details object:', JSON.stringify(details, null, 2));\n                    // Count vendor fields in the details object\n                    let vendorFieldCount = 0;\n                    Object.keys(details).forEach((key)=>{\n                        if (key.startsWith('vendor_')) {\n                            vendorFieldCount++;\n                        }\n                    });\n                    console.log(\"API SERVICE - Found \".concat(vendorFieldCount, \" vendor-related fields in details object\"));\n                    // Keep track of which vendor types we've already processed\n                    const processedVendors = new Set();\n                    Object.entries(details).forEach((param)=>{\n                        let [key, value] = param;\n                        if (key.startsWith('vendor_') && value) {\n                            const parts = key.split('_');\n                            if (parts.length >= 3) {\n                                const vendorType = parts[1];\n                                const fieldType = parts.slice(2).join('_');\n                                // Normalize vendor type to avoid duplicates\n                                const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                                console.log(\"API SERVICE - Processing vendor field: \".concat(key, \" = \").concat(value), {\n                                    vendorType,\n                                    fieldType,\n                                    normalizedType,\n                                    alreadyProcessed: processedVendors.has(normalizedType)\n                                });\n                                // Skip if we've already processed this vendor type\n                                if (processedVendors.has(normalizedType)) {\n                                    console.log(\"API SERVICE - Skipping \".concat(key, \" as \").concat(normalizedType, \" is already processed\"));\n                                    return;\n                                }\n                                if (fieldType === 'name') {\n                                    vendorDetails[\"\".concat(normalizedType, \"_name\")] = value;\n                                    console.log(\"API SERVICE - Set \".concat(normalizedType, \"_name = \").concat(value));\n                                    // Also set the contact if available\n                                    const contactKey = \"vendor_\".concat(vendorType, \"_contact\");\n                                    if (details[contactKey]) {\n                                        vendorDetails[\"\".concat(normalizedType, \"_contact\")] = details[contactKey];\n                                        console.log(\"API SERVICE - Also set \".concat(normalizedType, \"_contact = \").concat(details[contactKey]));\n                                        processedVendors.add(normalizedType);\n                                        console.log(\"API SERVICE - Marked \".concat(normalizedType, \" as processed (has both name and contact)\"));\n                                    }\n                                } else if (fieldType === 'contact') {\n                                    vendorDetails[\"\".concat(normalizedType, \"_contact\")] = value;\n                                    console.log(\"API SERVICE - Set \".concat(normalizedType, \"_contact = \").concat(value));\n                                    // Also set the name if available\n                                    const nameKey = \"vendor_\".concat(vendorType, \"_name\");\n                                    if (details[nameKey]) {\n                                        vendorDetails[\"\".concat(normalizedType, \"_name\")] = details[nameKey];\n                                        console.log(\"API SERVICE - Also set \".concat(normalizedType, \"_name = \").concat(details[nameKey]));\n                                        processedVendors.add(normalizedType);\n                                        console.log(\"API SERVICE - Marked \".concat(normalizedType, \" as processed (has both name and contact)\"));\n                                    }\n                                }\n                            }\n                        }\n                    });\n                    // Log the processed vendor details\n                    // console.log('API SERVICE - Processed vendor details:', JSON.stringify(vendorDetails, null, 2));\n                    // console.log(`API SERVICE - Processed ${processedVendors.size} complete vendor types: ${Array.from(processedVendors).join(', ')}`);\n                    // Final check to ensure we have both name and contact for each vendor\n                    const vendorNames = new Set();\n                    const vendorContacts = new Set();\n                    let completeVendorCount = 0;\n                    Object.keys(vendorDetails).forEach((key)=>{\n                        if (key.endsWith('_name')) {\n                            vendorNames.add(key.replace('_name', ''));\n                        } else if (key.endsWith('_contact')) {\n                            vendorContacts.add(key.replace('_contact', ''));\n                        }\n                    });\n                    // Count complete pairs\n                    vendorNames.forEach((name)=>{\n                        if (vendorContacts.has(name)) {\n                            completeVendorCount++;\n                        } else {\n                            // If we have a name but no contact, add a default contact\n                            console.log(\"API SERVICE - WARNING: Vendor \".concat(name, \" has name but no contact, adding default contact\"));\n                            vendorDetails[\"\".concat(name, \"_contact\")] = '0000000000';\n                            completeVendorCount++;\n                        }\n                    });\n                    // Check for contacts without names\n                    vendorContacts.forEach((contact)=>{\n                        if (!vendorNames.has(contact)) {\n                            // If we have a contact but no name, add a default name\n                            console.log(\"API SERVICE - WARNING: Vendor \".concat(contact, \" has contact but no name, adding default name\"));\n                            if (typeof contact === 'string') {\n                                vendorDetails[\"\".concat(contact, \"_name\")] = \"Vendor \".concat(contact.charAt(0).toUpperCase() + contact.slice(1));\n                            }\n                            completeVendorCount++;\n                        }\n                    });\n                    console.log(\"API SERVICE - Final check: Found \".concat(completeVendorCount, \" complete vendor pairs\"));\n                    console.log('API SERVICE - Final vendor details:', JSON.stringify(vendorDetails, null, 2));\n                }\n                // Ensure we have the required vendor fields for wedding videos\n                if (mediaType === 'video' && (details === null || details === void 0 ? void 0 : details.video_category) && [\n                    'my_wedding',\n                    'wedding_influencer'\n                ].includes(details.video_category)) {\n                    // Try to get vendor details from localStorage if they're missing from the details object\n                    const initialVendorKeys = Object.keys(vendorDetails);\n                    const vendorCount = initialVendorKeys.filter((key)=>key.endsWith('_name')).length;\n                    console.log(\"API SERVICE - Initial vendor count: \".concat(vendorCount, \" name fields found\"));\n                    console.log('API SERVICE - Initial vendor details:', JSON.stringify(vendorDetails, null, 2));\n                    // Always check localStorage for vendor details to ensure we have the most complete set\n                    console.log('API SERVICE - Checking localStorage for vendor details');\n                    try {\n                        const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n                        if (storedVendorDetails) {\n                            const parsedVendorDetails = JSON.parse(storedVendorDetails);\n                            console.log('API SERVICE - Retrieved vendor details from localStorage:', storedVendorDetails);\n                            // Track how many complete vendor details we've added\n                            let completeVendorCount = 0;\n                            // Process vendor details from localStorage\n                            Object.entries(parsedVendorDetails).forEach((param)=>{\n                                let [vendorType, details] = param;\n                                if (details && details.name && details.mobileNumber) {\n                                    // Add to vendorDetails\n                                    const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                                    vendorDetails[\"\".concat(normalizedType, \"_name\")] = details.name;\n                                    vendorDetails[\"\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                                    console.log(\"API SERVICE - Added vendor \".concat(normalizedType, \" with name: \").concat(details.name, \" and contact: \").concat(details.mobileNumber));\n                                    completeVendorCount++;\n                                    // Also add the original type if it's different\n                                    if (normalizedType !== vendorType) {\n                                        vendorDetails[\"\".concat(vendorType, \"_name\")] = details.name;\n                                        vendorDetails[\"\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                                        console.log(\"API SERVICE - Also added original vendor \".concat(vendorType));\n                                    }\n                                }\n                            });\n                            console.log(\"API SERVICE - Added \".concat(completeVendorCount, \" complete vendor details from localStorage\"));\n                            console.log('API SERVICE - Updated vendor details:', JSON.stringify(vendorDetails, null, 2));\n                            // Force update the state with these vendor details\n                            try {\n                                // This is a direct state update to ensure the vendor details are available for validation\n                                if (window && window.dispatchEvent) {\n                                    const vendorUpdateEvent = new CustomEvent('vendor-details-update', {\n                                        detail: parsedVendorDetails\n                                    });\n                                    window.dispatchEvent(vendorUpdateEvent);\n                                    console.log('API SERVICE - Dispatched vendor-details-update event');\n                                }\n                            } catch (eventError) {\n                                console.error('API SERVICE - Failed to dispatch vendor-details-update event:', eventError);\n                            }\n                        } else {\n                            console.log('API SERVICE - No vendor details found in localStorage');\n                        }\n                    } catch (error) {\n                        console.error('API SERVICE - Failed to retrieve vendor details from localStorage:', error);\n                    }\n                    // Make sure we have at least 4 vendor details with both name and contact\n                    // Define required vendor types for fallback if needed\n                    const requiredVendorTypes = [\n                        'venue',\n                        'photographer',\n                        'makeup_artist',\n                        'decoration',\n                        'caterer'\n                    ];\n                    // Count all vendor details, including additional ones\n                    let validVendorCount = 0;\n                    // Count all vendor details where both name and contact are provided\n                    console.log('API SERVICE - Checking vendor details for validation:', JSON.stringify(vendorDetails, null, 2));\n                    const allVendorKeys = Object.keys(vendorDetails);\n                    console.log('API SERVICE - Vendor keys:', allVendorKeys.join(', '));\n                    // Keep track of which vendors we've already counted to avoid duplicates\n                    const countedVendors = new Set();\n                    // First pass: Check for standard vendor types\n                    for(let i = 0; i < allVendorKeys.length; i++){\n                        const key = allVendorKeys[i];\n                        if (key.endsWith('_name')) {\n                            const baseKey = key.replace('_name', '');\n                            const contactKey = \"\".concat(baseKey, \"_contact\");\n                            // Normalize the key to handle both frontend and backend naming\n                            const normalizedKey = baseKey === 'makeupArtist' ? 'makeup_artist' : baseKey === 'decorations' ? 'decoration' : baseKey;\n                            // Skip if we've already counted this vendor\n                            if (countedVendors.has(normalizedKey)) {\n                                continue;\n                            }\n                            console.log(\"Checking vendor \".concat(baseKey, \":\"), {\n                                name: vendorDetails[key],\n                                contact: vendorDetails[contactKey]\n                            });\n                            if (vendorDetails[key] && vendorDetails[contactKey]) {\n                                validVendorCount++;\n                                countedVendors.add(normalizedKey);\n                                console.log(\"Valid vendor found: \".concat(baseKey, \" with name: \").concat(vendorDetails[key], \" and contact: \").concat(vendorDetails[contactKey]));\n                            }\n                        }\n                    }\n                    console.log(\"Total valid vendor count after first pass: \".concat(validVendorCount));\n                    // Second pass: Check for additional vendors with different naming patterns\n                    for(let i = 1; i <= 10; i++){\n                        const nameKey = \"additional\".concat(i, \"_name\");\n                        const contactKey = \"additional\".concat(i, \"_contact\");\n                        // Skip if we've already counted this vendor\n                        if (countedVendors.has(\"additional\".concat(i))) {\n                            continue;\n                        }\n                        if (vendorDetails[nameKey] && vendorDetails[contactKey]) {\n                            validVendorCount++;\n                            countedVendors.add(\"additional\".concat(i));\n                            console.log(\"Valid additional vendor found: additional\".concat(i, \" with name: \").concat(vendorDetails[nameKey], \" and contact: \").concat(vendorDetails[contactKey]));\n                        }\n                    }\n                    // Third pass: Check for additionalVendor pattern (used in some parts of the code)\n                    for(let i = 1; i <= 10; i++){\n                        const nameKey = \"additionalVendor\".concat(i, \"_name\");\n                        const contactKey = \"additionalVendor\".concat(i, \"_contact\");\n                        // Skip if we've already counted this vendor\n                        if (countedVendors.has(\"additionalVendor\".concat(i))) {\n                            continue;\n                        }\n                        if (vendorDetails[nameKey] && vendorDetails[contactKey]) {\n                            validVendorCount++;\n                            countedVendors.add(\"additionalVendor\".concat(i));\n                            console.log(\"Valid additionalVendor found: additionalVendor\".concat(i, \" with name: \").concat(vendorDetails[nameKey], \" and contact: \").concat(vendorDetails[contactKey]));\n                        }\n                    }\n                    console.log(\"Total valid vendor count after all passes: \".concat(validVendorCount));\n                    // Map additional vendors to the predefined vendor types if needed\n                    // This ensures the backend will count them correctly\n                    if (validVendorCount < 4) {\n                        console.log('Need to map additional vendors to predefined types');\n                        // First, collect all additional vendors from all patterns\n                        const additionalVendors = [];\n                        // Collect all additional vendors with 'additional' prefix\n                        for(let i = 1; i <= 10; i++){\n                            const nameKey = \"additional\".concat(i, \"_name\");\n                            const contactKey = \"additional\".concat(i, \"_contact\");\n                            if (vendorDetails[nameKey] && vendorDetails[contactKey] && !countedVendors.has(\"additional\".concat(i))) {\n                                additionalVendors.push({\n                                    name: vendorDetails[nameKey],\n                                    contact: vendorDetails[contactKey],\n                                    key: \"additional\".concat(i)\n                                });\n                                countedVendors.add(\"additional\".concat(i));\n                                console.log(\"Found additional vendor \".concat(i, \": \").concat(vendorDetails[nameKey]));\n                            }\n                        }\n                        // Collect all additional vendors with 'additionalVendor' prefix\n                        for(let i = 1; i <= 10; i++){\n                            const nameKey = \"additionalVendor\".concat(i, \"_name\");\n                            const contactKey = \"additionalVendor\".concat(i, \"_contact\");\n                            if (vendorDetails[nameKey] && vendorDetails[contactKey] && !countedVendors.has(\"additionalVendor\".concat(i))) {\n                                additionalVendors.push({\n                                    name: vendorDetails[nameKey],\n                                    contact: vendorDetails[contactKey],\n                                    key: \"additionalVendor\".concat(i)\n                                });\n                                countedVendors.add(\"additionalVendor\".concat(i));\n                                console.log(\"Found additionalVendor \".concat(i, \": \").concat(vendorDetails[nameKey]));\n                            }\n                        }\n                        console.log(\"Found \".concat(additionalVendors.length, \" additional vendors to map\"));\n                        // Map additional vendors to empty predefined vendor slots\n                        let additionalIndex = 0;\n                        for (const type of requiredVendorTypes){\n                            // Check if this type is already filled\n                            const normalizedType = type === 'makeup_artist' ? 'makeupArtist' : type === 'decoration' ? 'decorations' : type;\n                            // Skip if we've already counted this vendor type\n                            if (countedVendors.has(type) || countedVendors.has(normalizedType)) {\n                                console.log(\"Skipping \".concat(type, \" as it's already counted\"));\n                                continue;\n                            }\n                            if (additionalIndex < additionalVendors.length) {\n                                // Map this additional vendor to this predefined type\n                                vendorDetails[\"\".concat(type, \"_name\")] = additionalVendors[additionalIndex].name;\n                                vendorDetails[\"\".concat(type, \"_contact\")] = additionalVendors[additionalIndex].contact;\n                                console.log(\"Mapped additional vendor \".concat(additionalVendors[additionalIndex].key, \" to \").concat(type, \": \").concat(additionalVendors[additionalIndex].name));\n                                additionalIndex++;\n                                validVendorCount++;\n                                countedVendors.add(type);\n                                if (validVendorCount >= 4) {\n                                    console.log(\"Reached 4 valid vendors after mapping, no need to continue\");\n                                    break;\n                                }\n                            }\n                        }\n                        // If we still don't have enough, add placeholders\n                        if (validVendorCount < 4) {\n                            console.log('Still need more vendors, adding placeholders');\n                            for (const type of requiredVendorTypes){\n                                // Check if this type is already filled\n                                const normalizedType = type === 'makeup_artist' ? 'makeupArtist' : type === 'decoration' ? 'decorations' : type;\n                                // Skip if we've already counted this vendor type\n                                if (countedVendors.has(type) || countedVendors.has(normalizedType)) {\n                                    console.log(\"Skipping \".concat(type, \" for placeholder as it's already counted\"));\n                                    continue;\n                                }\n                                // Add placeholder for this vendor type\n                                vendorDetails[\"\".concat(type, \"_name\")] = vendorDetails[\"\".concat(type, \"_name\")] || \"\".concat(type.charAt(0).toUpperCase() + type.slice(1));\n                                vendorDetails[\"\".concat(type, \"_contact\")] = vendorDetails[\"\".concat(type, \"_contact\")] || '0000000000';\n                                validVendorCount++;\n                                countedVendors.add(type);\n                                console.log(\"Added placeholder for \".concat(type, \": \").concat(vendorDetails[\"\".concat(type, \"_name\")]));\n                                if (validVendorCount >= 4) {\n                                    console.log(\"Reached 4 valid vendors after adding placeholders\");\n                                    break;\n                                }\n                            }\n                            // Final check - if we still don't have 4, force add the remaining required types\n                            if (validVendorCount < 4) {\n                                console.log('CRITICAL: Still don\\'t have 4 vendors, forcing placeholders for all required types');\n                                for (const type of requiredVendorTypes){\n                                    if (!vendorDetails[\"\".concat(type, \"_name\")] || !vendorDetails[\"\".concat(type, \"_contact\")]) {\n                                        vendorDetails[\"\".concat(type, \"_name\")] = \"\".concat(type.charAt(0).toUpperCase() + type.slice(1));\n                                        vendorDetails[\"\".concat(type, \"_contact\")] = '0000000000';\n                                        validVendorCount++;\n                                        console.log(\"Force added placeholder for \".concat(type));\n                                        if (validVendorCount >= 4) break;\n                                    }\n                                }\n                            }\n                        }\n                        // Final log of vendor count\n                        console.log(\"Final valid vendor count: \".concat(validVendorCount));\n                    }\n                }\n                // Add vendor details to the request\n                completeRequest.vendor_details = vendorDetails;\n                // Final check before sending - ensure we have at least 4 complete vendor details\n                if (mediaType === 'video') {\n                    // Count complete vendor details (with both name and contact)\n                    const vendorNames = new Set();\n                    const vendorContacts = new Set();\n                    let completeVendorCount = 0;\n                    if (vendorDetails) {\n                        Object.keys(vendorDetails).forEach((key)=>{\n                            if (key.endsWith('_name')) {\n                                vendorNames.add(key.replace('_name', ''));\n                            } else if (key.endsWith('_contact')) {\n                                vendorContacts.add(key.replace('_contact', ''));\n                            }\n                        });\n                        // Count complete pairs\n                        vendorNames.forEach((name)=>{\n                            if (vendorContacts.has(name)) {\n                                completeVendorCount++;\n                            }\n                        });\n                    }\n                    console.log(\"API SERVICE - FINAL CHECK: Found \".concat(completeVendorCount, \" complete vendor pairs before sending request\"));\n                    // If we don't have enough vendors, add placeholders to reach 4\n                    if (completeVendorCount < 4) {\n                        console.log(\"API SERVICE - FINAL CHECK: Adding placeholders to reach 4 vendors\");\n                        const requiredVendorTypes = [\n                            'venue',\n                            'photographer',\n                            'makeup_artist',\n                            'decoration',\n                            'caterer'\n                        ];\n                        for (const type of requiredVendorTypes){\n                            // Skip if this vendor is already complete\n                            if (vendorDetails[\"\".concat(type, \"_name\")] && vendorDetails[\"\".concat(type, \"_contact\")]) {\n                                continue;\n                            }\n                            // Add placeholder for this vendor\n                            vendorDetails[\"\".concat(type, \"_name\")] = vendorDetails[\"\".concat(type, \"_name\")] || \"\".concat(type.charAt(0).toUpperCase() + type.slice(1));\n                            vendorDetails[\"\".concat(type, \"_contact\")] = vendorDetails[\"\".concat(type, \"_contact\")] || '0000000000';\n                            completeVendorCount++;\n                            console.log(\"API SERVICE - FINAL CHECK: Added placeholder for \".concat(type));\n                            if (completeVendorCount >= 4) {\n                                break;\n                            }\n                        }\n                        // Update the request with the new vendor details\n                        completeRequest.vendor_details = vendorDetails;\n                        console.log(\"API SERVICE - FINAL CHECK: Now have \".concat(completeVendorCount, \" complete vendor pairs\"));\n                    }\n                }\n                // Log the final request structure for debugging\n                console.log('=== FINAL UPLOAD REQUEST ===');\n                console.log('Media Type:', completeRequest.media_type);\n                console.log('Media Subtype:', completeRequest.media_subtype);\n                console.log('Caption:', completeRequest.caption);\n                console.log('Place:', completeRequest.place);\n                if (completeRequest.event_type) console.log('Event Type:', completeRequest.event_type);\n                if (completeRequest.partner) console.log('Partner:', completeRequest.partner);\n                if (completeRequest.budget) console.log('Budget:', completeRequest.budget);\n                if (completeRequest.wedding_style) console.log('Wedding Style:', completeRequest.wedding_style);\n                if (completeRequest.video_category) console.log('Video Category:', completeRequest.video_category);\n                console.log('Vendor Details Count:', Object.keys(completeRequest.vendor_details || {}).length);\n                console.log('=== END UPLOAD REQUEST ===');\n                console.log('Sending complete upload request:', JSON.stringify(completeRequest, null, 2));\n                // Complete the upload\n                const completeResponse = await uploadService.completeUpload(completeRequest);\n                // Log the complete response\n                console.log('Complete upload response:', JSON.stringify(completeResponse, null, 2));\n                // Update progress to 100%\n                if (onProgress) onProgress(100);\n                console.log('Upload process completed successfully');\n                return completeResponse;\n            } catch (completeError) {\n                var _completeError_response_data, _completeError_response;\n                console.error('Error in complete upload step:', completeError);\n                // Check if the error is related to the title\n                if (completeError.message && completeError.message.includes('title')) {\n                    console.error('Title-related error detected:', completeError.message);\n                    throw {\n                        error: 'Please provide a title for your upload'\n                    };\n                }\n                // Handle other errors\n                const errorMessage = ((_completeError_response = completeError.response) === null || _completeError_response === void 0 ? void 0 : (_completeError_response_data = _completeError_response.data) === null || _completeError_response_data === void 0 ? void 0 : _completeError_response_data.error) || completeError.message || 'Failed to complete the upload process';\n                console.error('Throwing error:', errorMessage);\n                throw {\n                    error: errorMessage\n                };\n            }\n        } catch (error) {\n            var _error_response;\n            console.error('Error in upload process:', error);\n            // Check if the error is related to the title\n            if (error.error && typeof error.error === 'string' && error.error.includes('title')) {\n                console.error('Title-related error detected in main catch block');\n                throw {\n                    error: 'Please provide a title for your upload'\n                };\n            }\n            // If error is already formatted correctly, just pass it through\n            if (error.error) {\n                throw error;\n            }\n            // Otherwise, format the error\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: 'Upload process failed'\n            };\n        }\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    authService,\n    userService,\n    uploadService,\n    isAuthenticated\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./services/api.ts\n"));

/***/ })

});