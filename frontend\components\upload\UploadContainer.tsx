// components/upload/UploadContainer.tsx
'use client';

import React from 'react';
import { useUpload } from '../../contexts/UploadContexts';
import FileSelectionStep from './FileSectionStep';
import DetailsForm from './DetailsForm';
import UploadProgress from './UploadProgress';
import CompletedUpload from './CompletedUpload';
import ErrorUpload from './ErrorUpload';



const UploadContainer: React.FC = () => {
  const { state } = useUpload();
  
  return (
    <div className="p-6">
      {state.step === "selecting" && <FileSelectionStep />}
      {state.step === "details" && <DetailsForm />}
      {(state.step === "uploading" || state.step === "processing") && <UploadProgress onClose={() => console.log('Upload closed')} />}
      {state.step === "complete" && <CompletedUpload />}
      {state.step === "error" && <ErrorUpload />}
    </div>
  );
};

export default UploadContainer;