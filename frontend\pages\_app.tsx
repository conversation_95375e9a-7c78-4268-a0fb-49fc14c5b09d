import type { AppProps } from 'next/app';
import { useEffect } from 'react';

function MyApp({ Component, pageProps }: AppProps) {
  // Clean up browser extension attributes on mount
  useEffect(() => {
    const cleanupExtensionAttributes = () => {
      // Target common extension attributes
      const attributesToRemove = [
        'bis_skin_checked',
        '__processed_',
        'data-bis-'
      ];
      
      // Get all elements
      const allElements = document.querySelectorAll('*');
      
      // Remove attributes from each element
      allElements.forEach(el => {
        for (let i = 0; i < el.attributes.length; i++) {
          const attr = el.attributes[i];
          for (const badAttr of attributesToRemove) {
            if (attr.name.includes(badAttr)) {
              el.removeAttribute(attr.name);
              // Adjust index since we removed an attribute
              i--;
              break;
            }
          }
        }
      });
    };
    
    // Run immediately
    cleanupExtensionAttributes();
    
    // Also run after a short delay to catch any late additions
    const timeoutId = setTimeout(cleanupExtensionAttributes, 0);
    
    // Run again when DOM changes
    const observer = new MutationObserver(function(mutations) {
      cleanupExtensionAttributes();
    });
    
    // Start observing the document
    observer.observe(document.documentElement, {
      childList: true,
      subtree: true,
      attributes: true
    });
    
    // Cleanup
    return () => {
      clearTimeout(timeoutId);
      observer.disconnect();
    };
  }, []);
  
  return <Component {...pageProps} />;
}

export default MyApp;
