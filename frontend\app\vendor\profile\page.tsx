"use client";

import React from 'react';
import { useRouter } from 'next/navigation';

export default function VendorProfilePage() {
  const router = useRouter();
  
  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto bg-white rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold mb-6">Vendor Profile</h1>
        <p className="mb-4">This page is under construction. You will be able to edit your vendor profile here.</p>
        <button 
          onClick={() => router.push('/vendor/dashboard')}
          className="px-4 py-2 bg-red-700 text-white rounded-md hover:bg-red-800 transition duration-200"
        >
          Back to Dashboard
        </button>
      </div>
    </div>
  );
}
