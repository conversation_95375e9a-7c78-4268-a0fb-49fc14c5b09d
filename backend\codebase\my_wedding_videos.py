import json
import psycopg2
from psycopg2.extras import RealDictCursor
from psycopg2 import sql
import os
from datetime import datetime

# Database configuration - can be set via environment variables
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'database': os.getenv('DB_NAME', 'wedding_db'),
    'user': os.getenv('DB_USER', 'postgres'),
    'password': os.getenv('DB_PASSWORD', ''),
    'port': os.getenv('DB_PORT', 5432)
}

def get_db_connection():
    """Create and return a database connection"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except psycopg2.Error as e:
        print(f"Database connection error: {str(e)}")
        raise

def create_cors_response(status_code, body):
    """Helper function to create CORS-enabled responses"""
    return {
        'statusCode': status_code,
        'headers': {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
            'Content-Type': 'application/json'
        },
        'body': json.dumps(body, cls=CustomJSONEncoder) if isinstance(body, (dict, list)) else body
    }

def save_content_to_my_wedding(event):
    try:
        print(f"save_content_to_my_wedding called with event: {json.dumps(event)}")
        
        # Handle both direct body and API Gateway format
        if isinstance(event.get('body'), str):
            body = json.loads(event['body'])
        else:
            body = event.get('body', {})
            
        print(f"Parsed body: {json.dumps(body)}")
        
        user_id = body.get('user_id')
        content_id = body.get('content_id')
        content_type = body.get('content_type')  # 'flash', 'glimpse', 'movie', 'photo'
        title = body.get('title', '')
        
        print(f"Extracted data - user_id: {user_id}, content_id: {content_id}, content_type: {content_type}")
        
        if not all([user_id, content_id, content_type]):
            missing_fields = []
            if not user_id: missing_fields.append('user_id')
            if not content_id: missing_fields.append('content_id')
            if not content_type: missing_fields.append('content_type')
            
            error_msg = f'Missing required fields: {", ".join(missing_fields)}'
            print(f"Error: {error_msg}")
            return create_cors_response(400, {'error': error_msg})

        # Save the content to PostgreSQL
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # Insert or update the content (upsert)
            insert_query = """
                INSERT INTO wed_tab (user_id, content_id, content_type, title, saved_at) 
                VALUES (%s, %s, %s, %s, %s)
                ON CONFLICT (user_id, content_id) 
                DO UPDATE SET 
                    content_type = EXCLUDED.content_type,
                    title = EXCLUDED.title,
                    saved_at = EXCLUDED.saved_at
            """
            
            cursor.execute(insert_query, (
                user_id, 
                content_id, 
                content_type, 
                title, 
                datetime.now().isoformat()
            ))
            
            conn.commit()
            cursor.close()
            conn.close()
            
            print(f"Successfully saved content: {content_id} for user: {user_id}")
        
        except psycopg2.Error as e:
            print(f"PostgreSQL error: {str(e)}")
            if 'relation "wed_tab" does not exist' in str(e):
                return create_cors_response(500, {'error': 'Database table not found. Please contact support.'})
            else:
                return create_cors_response(500, {'error': f'Database error: {str(e)}'})
        
        return create_cors_response(200, {'message': f'{content_type} saved successfully'})
        
    except json.JSONDecodeError as e:
        print(f"JSON decode error: {str(e)}")
        return create_cors_response(400, {'error': 'Invalid JSON format'})
    except Exception as e:
        print(f"Unexpected error in save_content_to_my_wedding: {str(e)}")
        return create_cors_response(500, {'error': f'Internal server error: {str(e)}'})

def get_my_wedding_content(event):
    try:
        print(f"get_my_wedding_content called with event: {json.dumps(event)}")
        
        # Handle query parameters
        query_params = event.get('queryStringParameters') or {}
        user_id = query_params.get('user_id')
        content_type = query_params.get('content_type', None)  # Optional filter
        
        # Pagination parameters
        page = int(query_params.get('page', 1))
        limit = int(query_params.get('limit', 20))  # Default 20 items per page
        
        # Validate pagination parameters
        if page < 1:
            page = 1
        if limit < 1 or limit > 100:  # Max 100 items per page
            limit = 20
            
        offset = (page - 1) * limit
        
        print(f"Query params - user_id: {user_id}, content_type: {content_type}, page: {page}, limit: {limit}")
        
        if not user_id:
            return create_cors_response(400, {'error': 'Missing user_id parameter'})

        try:
            conn = get_db_connection()
            cursor = conn.cursor(cursor_factory=RealDictCursor)
            
            # First, get total count for pagination info
            if content_type:
                if content_type == 'photo':
                    count_query = """
                        SELECT COUNT(*) as total_count
                        FROM wed_tab w
                        JOIN photos p ON w.content_id::uuid = p.photo_id
                        WHERE w.user_id = %s AND w.content_type = %s
                    """
                    cursor.execute(count_query, (user_id, content_type))
                else:
                    count_query = """
                        SELECT COUNT(*) as total_count
                        FROM wed_tab w
                        JOIN videos v ON w.content_id::uuid = v.video_id
                        WHERE w.user_id = %s AND w.content_type = %s
                    """
                    cursor.execute(count_query, (user_id, content_type))
            else:
                count_query = """
                    SELECT 
                        (SELECT COUNT(*) FROM wed_tab w JOIN videos v ON w.content_id::uuid = v.video_id WHERE w.user_id = %s) +
                        (SELECT COUNT(*) FROM wed_tab w JOIN photos p ON w.content_id::uuid = p.photo_id WHERE w.user_id = %s) as total_count
                """
                cursor.execute(count_query, (user_id, user_id))
            
            total_count_result = cursor.fetchone()
            total_count = total_count_result['total_count'] if total_count_result else 0
            
            # Calculate pagination info
            total_pages = (total_count + limit - 1) // limit  # Ceiling division
            has_next = page < total_pages
            has_previous = page > 1
            
            # Build the main query based on content_type with pagination
            if content_type:
                if content_type == 'photo':
                    # Query for photos only
                    select_query = """
                        SELECT 
                            p.photo_id AS content_id,
                            p.photo_name AS content_name,
                            p.photo_description AS content_description,
                            p.photo_url AS content_url,
                            p.photo_tags AS content_tags,
                            p.photo_category AS content_category,
                            p.created_at,
                            u.name AS user_name,
                            p.user_id,
                            CASE WHEN p.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content,
                            COALESCE(ps.photo_views, 0) AS content_views,
                            COALESCE(ps.photo_likes, 0) AS content_likes,
                            COALESCE(ps.photo_comments, 0) AS content_comments,
                            w.content_type,
                            NULL AS video_duration,
                            NULL AS video_thumbnail,
                            w.saved_at
                        FROM wed_tab w
                        JOIN photos p ON w.content_id::uuid = p.photo_id
                        JOIN users u ON p.user_id = u.user_id
                        LEFT JOIN photo_stats ps ON p.photo_id = ps.photo_id
                        WHERE w.user_id = %s AND w.content_type = %s
                        ORDER BY w.saved_at DESC
                        LIMIT %s OFFSET %s
                    """
                    cursor.execute(select_query, (user_id, user_id, content_type, limit, offset))
                else:
                    # Query for videos (flash, glimpse, movie)
                    select_query = """
                        SELECT 
                            v.video_id AS content_id,
                            v.video_name AS content_name,
                            v.video_description AS content_description,
                            v.video_url AS content_url,
                            v.video_tags AS content_tags,
                            v.video_category AS content_category,
                            v.video_duration,
                            v.video_thumbnail,
                            v.created_at,
                            u.name AS user_name,
                            v.user_id,
                            CASE WHEN v.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content,
                            COALESCE(vs.video_views, 0) AS content_views,
                            COALESCE(vs.video_likes, 0) AS content_likes,
                            COALESCE(vs.video_comments, 0) AS content_comments,
                            w.content_type,
                            w.saved_at
                        FROM wed_tab w
                        JOIN videos v ON w.content_id::uuid = v.video_id
                        JOIN users u ON v.user_id = u.user_id
                        LEFT JOIN video_stats vs ON v.video_id = vs.video_id
                        WHERE w.user_id = %s AND w.content_type = %s
                        ORDER BY w.saved_at DESC
                        LIMIT %s OFFSET %s
                    """
                    cursor.execute(select_query, (user_id, user_id, content_type, limit, offset))
            else:
                # Query for all content types (videos and photos)
                select_query = """
                    (
                        SELECT 
                            v.video_id AS content_id,
                            v.video_name AS content_name,
                            v.video_description AS content_description,
                            v.video_url AS content_url,
                            v.video_tags AS content_tags,
                            v.video_category AS content_category,
                            v.video_duration,
                            v.video_thumbnail,
                            v.created_at,
                            u.name AS user_name,
                            v.user_id,
                            CASE WHEN v.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content,
                            COALESCE(vs.video_views, 0) AS content_views,
                            COALESCE(vs.video_likes, 0) AS content_likes,
                            COALESCE(vs.video_comments, 0) AS content_comments,
                            w.content_type,
                            w.saved_at
                        FROM wed_tab w
                        JOIN videos v ON w.content_id::uuid = v.video_id
                        JOIN users u ON v.user_id = u.user_id
                        LEFT JOIN video_stats vs ON v.video_id = vs.video_id
                        WHERE w.user_id = %s
                    )
                    UNION ALL
                    (
                        SELECT 
                            p.photo_id AS content_id,
                            p.photo_name AS content_name,
                            p.photo_description AS content_description,
                            p.photo_url AS content_url,
                            p.photo_tags AS content_tags,
                            p.photo_category AS content_category,
                            NULL AS video_duration,
                            NULL AS video_thumbnail,
                            p.created_at,
                            u.name AS user_name,
                            p.user_id,
                            CASE WHEN p.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content,
                            COALESCE(ps.photo_views, 0) AS content_views,
                            COALESCE(ps.photo_likes, 0) AS content_likes,
                            COALESCE(ps.photo_comments, 0) AS content_comments,
                            w.content_type,
                            w.saved_at
                        FROM wed_tab w
                        JOIN photos p ON w.content_id::uuid = p.photo_id
                        JOIN users u ON p.user_id = u.user_id
                        LEFT JOIN photo_stats ps ON p.photo_id = ps.photo_id
                        WHERE w.user_id = %s
                    )
                    ORDER BY saved_at DESC
                    LIMIT %s OFFSET %s
                """
                cursor.execute(select_query, (user_id, user_id, user_id, user_id, limit, offset))
            
            results = cursor.fetchall()

            # Convert and format the results
            items = []
            for row in results:
                item = dict(row)
                
                # Handle datetime fields
                if isinstance(item.get('saved_at'), datetime):
                    item['saved_at'] = item['saved_at'].isoformat()
                if isinstance(item.get('created_at'), datetime):
                    item['created_at'] = item['created_at'].isoformat()
                
                # Handle tags if they're stored as JSON string
                tags_field = item.get('content_tags')
                if tags_field:
                    if isinstance(tags_field, str):
                        try:
                            item['content_tags'] = json.loads(tags_field)
                        except json.JSONDecodeError:
                            item['content_tags'] = []
                    elif tags_field is None:
                        item['content_tags'] = []
                else:
                    item['content_tags'] = []
                
                # Format the response based on content type
                if item.get('content_type') == 'photo':
                    # Format as photo - FIXED: Added content_type field
                    formatted_item = {
                        "photo_id": str(item.get('content_id')),
                        "photo_name": item.get('content_name'),
                        "photo_description": item.get('content_description', ''),
                        "photo_url": item.get('content_url'),
                        "photo_tags": item.get('content_tags', []),
                        "photo_category": item.get('content_category'),
                        "created_at": item.get('created_at'),
                        "user_name": item.get('user_name'),
                        "user_id": str(item.get('user_id')),
                        "is_own_content": bool(item.get('is_own_content', False)),
                        "photo_views": int(item.get('content_views', 0)),
                        "photo_likes": int(item.get('content_likes', 0)),
                        "photo_comments": int(item.get('content_comments', 0)),
                        "content_type": item.get('content_type'),  # FIXED: Added this line
                        "media_type": item.get('content_type'),    # Alternative field name
                        "saved_at": item.get('saved_at')
                    }
                else:
                    # Format as video (flash, glimpse, movie)
                    formatted_item = {
                        "video_id": str(item.get('content_id')),
                        "video_name": item.get('content_name'),
                        "video_description": item.get('content_description', ''),
                        "video_url": item.get('content_url'),
                        "video_thumbnail": item.get('video_thumbnail'),
                        "video_duration": float(item.get('video_duration', 0.0)),
                        "video_tags": item.get('content_tags', []),
                        "video_category": item.get('content_category'),
                        "created_at": item.get('created_at'),
                        "user_name": item.get('user_name'),
                        "user_id": str(item.get('user_id')),
                        "is_own_content": bool(item.get('is_own_content', False)),
                        "video_views": int(item.get('content_views', 0)),
                        "video_likes": int(item.get('content_likes', 0)),
                        "video_comments": int(item.get('content_comments', 0)),
                        "content_type": item.get('content_type'),  # This was already present
                        "media_type": item.get('content_type'),    # Alternative field name
                        "saved_at": item.get('saved_at')
                    }
                
                items.append(formatted_item)
            
            cursor.close()
            conn.close()
            
            # Prepare paginated response
            response_data = {
                "data": items,
                "pagination": {
                    "current_page": page,
                    "per_page": limit,
                    "total_items": total_count,
                    "total_pages": total_pages,
                    "has_next": has_next,
                    "has_previous": has_previous
                }
            }
            
            print(f"Query returned {len(items)} items (page {page} of {total_pages})")
            return create_cors_response(200, response_data)
            
        except psycopg2.Error as e:
            print(f"PostgreSQL error: {str(e)}")
            if 'relation' in str(e) and 'does not exist' in str(e):
                return create_cors_response(500, {'error': 'Database table not found. Please check your database schema.'})
            elif 'column' in str(e) and 'does not exist' in str(e):
                return create_cors_response(500, {'error': 'Database schema mismatch. Please check column names.'})
            else:
                return create_cors_response(500, {'error': f'Database query failed: {str(e)}'})
            
    except ValueError as e:
        print(f"Invalid pagination parameters: {str(e)}")
        return create_cors_response(400, {'error': 'Invalid pagination parameters. Page and limit must be numbers.'})
    except Exception as e:
        print(f"Unexpected error in get_my_wedding_content: {str(e)}")
        return create_cors_response(500, {'error': f'Internal server error: {str(e)}'})
        
def remove_content_from_my_wedding(event):
    try:
        print(f"remove_content_from_my_wedding called with event: {json.dumps(event)}")
        
        # Handle both direct body and API Gateway format
        if isinstance(event.get('body'), str):
            body = json.loads(event['body'])
        else:
            body = event.get('body', {})
            
        print(f"Parsed body: {json.dumps(body)}")
        
        user_id = body.get('user_id')
        content_id = body.get('content_id')
        
        if not all([user_id, content_id]):
            missing_fields = []
            if not user_id: missing_fields.append('user_id')
            if not content_id: missing_fields.append('content_id')
            
            error_msg = f'Missing required fields: {", ".join(missing_fields)}'
            return create_cors_response(400, {'error': error_msg})

        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # Delete the content
            delete_query = "DELETE FROM wed_tab WHERE user_id = %s AND content_id = %s"
            cursor.execute(delete_query, (user_id, content_id))
            
            rows_affected = cursor.rowcount
            conn.commit()
            cursor.close()
            conn.close()
            
            if rows_affected > 0:
                print(f"Successfully removed content: {content_id} for user: {user_id}")
                return create_cors_response(200, {'message': 'Content removed successfully'})
            else:
                print(f"No content found to remove: {content_id} for user: {user_id}")
                return create_cors_response(404, {'error': 'Content not found in saved items'})
        
        except psycopg2.Error as e:
            print(f"PostgreSQL error: {str(e)}")
            return create_cors_response(500, {'error': f'Failed to remove content: {str(e)}'})
        
    except json.JSONDecodeError as e:
        print(f"JSON decode error: {str(e)}")
        return create_cors_response(400, {'error': 'Invalid JSON format'})
    except Exception as e:
        print(f"Unexpected error in remove_content_from_my_wedding: {str(e)}")
        return create_cors_response(500, {'error': f'Internal server error: {str(e)}'})

def check_content_saved_status(event):
    try:
        print(f"check_content_saved_status called with event: {json.dumps(event)}")
        
        # Handle query parameters
        query_params = event.get('queryStringParameters') or {}
        user_id = query_params.get('user_id')
        content_id = query_params.get('content_id')
        
        print(f"Query params - user_id: {user_id}, content_id: {content_id}")
        
        if not all([user_id, content_id]):
            missing_fields = []
            if not user_id: missing_fields.append('user_id')
            if not content_id: missing_fields.append('content_id')
            
            error_msg = f'Missing required parameters: {", ".join(missing_fields)}'
            return create_cors_response(400, {'error': error_msg})

        try:
            conn = get_db_connection()
            cursor = conn.cursor(cursor_factory=RealDictCursor)
            
            # Check if content exists and get its type
            check_query = "SELECT content_type FROM wed_tab WHERE user_id = %s AND content_id = %s"
            cursor.execute(check_query, (user_id, content_id))
            
            result = cursor.fetchone()
            is_saved = result is not None
            content_type = result['content_type'] if result else None
            
            cursor.close()
            conn.close()
            
            print(f"Content saved status: {is_saved}, content_type: {content_type}")
            
            response_data = {
                'is_saved': is_saved,
                'content_type': content_type,
                'media_type': content_type  # Alternative field name
            }
            
            return create_cors_response(200, response_data)
            
        except psycopg2.Error as e:
            print(f"PostgreSQL error: {str(e)}")
            return create_cors_response(500, {'error': f'Database query failed: {str(e)}'})
        
    except Exception as e:
        print(f"Unexpected error in check_content_saved_status: {str(e)}")
        return create_cors_response(500, {'error': f'Internal server error: {str(e)}'})

# Additional function to handle OPTIONS requests (CORS preflight)
def handle_options(event):
    """Handle CORS preflight OPTIONS requests"""
    return create_cors_response(200, {})

# Lambda handler function (if you're using AWS Lambda)
def lambda_handler(event, context):
    """Main Lambda handler function"""
    try:
        print(f"Lambda handler called with event: {json.dumps(event)}")
        
        # Handle CORS preflight requests
        if event.get('httpMethod') == 'OPTIONS':
            return handle_options(event)
        
        # Get the path and method from the event
        path = event.get('path', '')
        method = event.get('httpMethod', '')
        
        # Route requests based on path and method
        if '/my-wedding-videos' in path:
            if method == 'GET':
                return get_my_wedding_content(event)
            elif method == 'POST':
                return save_content_to_my_wedding(event)
            elif method == 'DELETE':
                return remove_content_from_my_wedding(event)
        elif '/check-saved-status' in path and method == 'GET':
            return check_content_saved_status(event)
        else:
            return create_cors_response(404, {'error': 'Endpoint not found'})
            
    except Exception as e:
        print(f"Lambda handler error: {str(e)}")
        return create_cors_response(500, {'error': f'Internal server error: {str(e)}'})

# Custom JSON encoder to handle date objects
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        from datetime import date, datetime
        if isinstance(obj, (date, datetime)):
            return obj.isoformat()
        return super().default(obj)

# Maintain backward compatibility with existing function names
save_video_to_my_wedding = save_content_to_my_wedding
get_my_wedding_videos = get_my_wedding_content
remove_video_from_my_wedding = remove_content_from_my_wedding
check_video_saved_status = check_content_saved_status