import { NextResponse } from 'next/server';

// This API provides a collection of wedding-related images with direct URLs
export async function GET(request: Request) {
  try {
    // Get the search query from the URL
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('query')?.toLowerCase() || '';

    // Collection of wedding images by category
    const imageCollections = {
      // General wedding images
      wedding: [
        'https://images.unsplash.com/photo-1519741497674-611481863552?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1465495976277-4387d4b0b4c6?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1511285560929-80b456fea0bc?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1532712938310-34cb3982ef74?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1529636798458-92182e662485?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1606800052052-a08af7148866?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1583939003579-730e3918a45a?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1550005809-91ad75fb315f?ixlib=rb-4.0.3'
      ],
      // Beach wedding images
      beach: [
        'https://images.unsplash.com/photo-1544078751-58fee2d8a03b?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1546032996-6dfacbacbf3f?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1583608564770-462b2a6d6b29?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1545809278-56c8739a30f0?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1506929562872-bb421503ef21?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1437719417032-8595fd9e9dc6?ixlib=rb-4.0.3'
      ],
      // Wedding flowers
      flowers: [
        'https://images.unsplash.com/photo-1522673607200-164d1b6ce486?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1561731216-c3a4d99437d5?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1464982326199-86f32f81b211?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1444312645910-ffa973656eba?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1526047932273-341f2a7631f9?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1519378058457-4c29a0a2efac?ixlib=rb-4.0.3'
      ],
      // Wedding venues
      venue: [
        'https://images.unsplash.com/photo-1519167758481-83f550bb49b3?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1439539698758-ba2680ecadb9?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1505944270255-72b8c68c6a70?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1604004555489-723a93d6ce74?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1519225421980-715cb0215aed?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1464366400600-7168b8af9bc3?ixlib=rb-4.0.3'
      ],
      // Rustic wedding
      rustic: [
        'https://images.unsplash.com/photo-1510076857177-7470076d4098?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1482482097755-0b595893ba63?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1475714622877-641e013c6096?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1528495612343-9ca9f4a9f67c?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1464366400600-7168b8af9bc3?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1537633552985-df8429e8048b?ixlib=rb-4.0.3'
      ],
      // Elegant wedding
      elegant: [
        'https://images.unsplash.com/photo-1507504031003-b417219a0fde?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1519225421980-715cb0215aed?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1469371670807-013ccf25f16a?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1522673607200-164d1b6ce486?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1513278974582-3e1b4a4fa21e?ixlib=rb-4.0.3',
        'https://images.unsplash.com/photo-1533174072545-7a4b6ad7a6a3?ixlib=rb-4.0.3'
      ]
    };

    // Determine which collection to use based on the query
    let selectedImages = imageCollections.wedding; // Default to general wedding images

    // Define the type for the keys of imageCollections
    type CollectionKey = keyof typeof imageCollections;

    // Check if the query contains any of our collection keywords
    (Object.keys(imageCollections) as CollectionKey[]).forEach(key => {
      if (query.includes(key)) {
        selectedImages = imageCollections[key];
      }
    });

    // Create photo objects in the format expected by the frontend
    const photos = selectedImages.map((url, index) => ({
      id: `${index}`,
      src: {
        medium: url,
        original: url
      },
      photographer: 'Unsplash'
    }));

    // Return the data in a format similar to Pexels API
    return NextResponse.json({
      photos,
      total_results: photos.length,
      page: 1,
      per_page: photos.length
    });
  } catch (error) {
    console.error('Error generating image URLs:', error);
    return NextResponse.json(
      { error: 'Failed to generate image URLs' },
      { status: 500 }
    );
  }
}
