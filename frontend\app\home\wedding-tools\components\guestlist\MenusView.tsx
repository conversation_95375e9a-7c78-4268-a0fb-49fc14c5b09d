"use client";
import React, { useState } from 'react';
import { Edit, Trash2, Plus } from 'lucide-react';
import axios from 'axios';
import { getAuthToken } from '../../utils';
import { Guest, MenuOption } from './GuestList';
import AddMenuModal from './AddMenuModal';

interface MenusViewProps {
  guests: Guest[];
  menuOptions: MenuOption[];
  setMenuOptions: React.Dispatch<React.SetStateAction<MenuOption[]>>;
  setGuests: React.Dispatch<React.SetStateAction<Guest[]>>;
  fetchData: () => Promise<void>;
  setError: (error: string | null) => void;
  setSuccessMessage: (message: string | null) => void;
}

const MenusView: React.FC<MenusViewProps> = ({
  guests,
  menuOptions,
  setMenuOptions,
  setGuests,
  fetchData,
  setError,
  setSuccessMessage
}) => {

  const [selectedMenu, setSelectedMenu] = useState<MenuOption | null>(null);
  const [showAddMenuModal, setShowAddMenuModal] = useState(false);
  const [showDeleteMenuConfirm, setShowDeleteMenuConfirm] = useState(false);

  // Group guests by menu - use string comparison to ensure matching works correctly
  const guestsByMenu = menuOptions.map(menu => {
    const menuGuests = guests.filter(guest => String(guest.menu_id) === String(menu.menu_id));
    console.log(`Menu ${menu.name} (${menu.menu_id}) has ${menuGuests.length} guests`);
    if (menuGuests.length > 0) {
      console.log('First guest in menu:', menuGuests[0]);
    }

    // Check for any guests with this menu_id using partial matching
    const partialMatches = guests.filter(g => g.menu_id && g.menu_id.includes(menu.menu_id.substring(0, 8)));
    if (partialMatches.length > 0 && menuGuests.length === 0) {
      console.log('Found guests with partial menu_id match:', partialMatches);
    }

    return {
      ...menu,
      guests: menuGuests,
      guest_count: menuGuests.length
    };
  });

  // Debug logs
  console.log('All menu options:', menuOptions);
  console.log('All guests:', guests);

  // Check for any guests with menu_id that doesn't match any menu option
  const orphanedGuests = guests.filter(guest =>
    !menuOptions.some(menu => String(guest.menu_id) === String(menu.menu_id)));
  if (orphanedGuests.length > 0) {
    console.log('Guests with menu_id not matching any menu option:', orphanedGuests);
  }

  // Add a new menu option
  const handleAddMenu = () => {
    setSelectedMenu(null);
    setShowAddMenuModal(true);
  };

  // Edit a menu option
  const handleEditMenu = (menu: MenuOption) => {
    setSelectedMenu(menu);
    setShowAddMenuModal(true);
  };

  // Delete a menu option
  const handleDeleteMenu = async (menuId: string) => {
    try {
      const token = getAuthToken();

      if (!token) {
        console.warn('No authentication token found');
        setError('Authentication required');
        return;
      }

      await axios.delete('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/delete-menu-option', {
        headers: { Authorization: `Bearer ${token}` },
        data: { menu_id: menuId }
      });

      setMenuOptions(prevMenus => prevMenus.filter(m => m.menu_id !== menuId));
      setSuccessMessage('Menu option deleted successfully');
      setShowDeleteMenuConfirm(false);

    } catch (err: any) {
      console.error('Error deleting menu option:', err);
      setError(err.response?.data?.error || 'Failed to delete menu option');
    }
  };

  return (
    <div>
      {/* Add Menu Button */}
      <div className="mb-4">
        <button
          onClick={handleAddMenu}
          className="flex items-center text-[#B31B1E] hover:bg-red-50 px-3 py-2 rounded"
        >
          <Plus size={16} className="mr-1" />
          Add Menu Option
        </button>
      </div>

      {/* Menu Options and their guests */}
      {guestsByMenu.map(menu => (
        <div key={menu.menu_id} className="mb-6">
          {/* Menu header */}
          <div className="flex justify-between items-center mb-2 border-b pb-2">
            <div className="flex items-center">
              <h3 className="font-semibold text-lg text-black">{menu.name}</h3>
              <span className="ml-2 text-gray-500 text-sm">{menu.guest_count}</span>
            </div>
            <div className="flex items-center">
              {!menu.is_default && (
                <>
                  <button
                    onClick={() => handleEditMenu(menu)}
                    className="text-gray-500 mr-2 p-1 hover:bg-gray-100 rounded"
                  >
                    <Edit size={16} />
                  </button>
                  <button
                    onClick={() => {
                      setSelectedMenu(menu);
                      setShowDeleteMenuConfirm(true);
                    }}
                    className="text-gray-500 p-1 hover:bg-gray-100 rounded"
                  >
                    <Trash2 size={16} />
                  </button>
                </>
              )}
              {menu.is_default && (
                <span className="text-xs text-gray-500 italic">Default</span>
              )}
            </div>
          </div>

          {/* Guest list for this menu */}
          {menu.guests.length > 0 ? (
            <div className="space-y-2">
              {menu.guests.map(guest => (
                <div key={guest.guest_id} className="flex items-center justify-between p-2 hover:bg-gray-50 rounded">
                  <div className="flex-1 text-black">
                    {guest.first_name} {guest.last_name}
                  </div>
                  <div className="text-gray-600 text-sm">
                    {guest.group_name}
                  </div>
                  <div className="text-gray-600 text-sm capitalize">
                    {guest.attendance_status}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-gray-500 italic p-2">No guests using this menu</div>
          )}
        </div>
      ))}

      {/* Delete Menu Confirmation Modal */}
      {showDeleteMenuConfirm && selectedMenu && (
        <div className="fixed inset-0 backdrop-brightness-30 flex items-center justify-center z-50 p-4">
          <div
            className="rounded-lg shadow-lg w-full max-w-md relative overflow-hidden"
            style={{
              background: "linear-gradient(to bottom, #FAE6C4, #FFFFFF)",
              boxShadow: "0 10px 25px rgba(0, 0, 0, 0.2)",
            }}
          >
            <h3 className="text-xl font-bold mb-4 text-black">Delete Menu Option</h3>
            <p className="mb-6 text-gray-700">
              Are you sure you want to delete the menu option "{selectedMenu.name}"?
              {selectedMenu.guests && selectedMenu.guests.length > 0 && (
                <span className="block mt-2 text-red-600">
                  This will remove the menu selection from {selectedMenu.guests.length} guests.
                </span>
              )}
            </p>
            <div className="flex justify-end space-x-2">
              <button
                onClick={() => setShowDeleteMenuConfirm(false)}
                className="px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => handleDeleteMenu(selectedMenu.menu_id)}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Add/Edit Menu Modal */}
      {showAddMenuModal && (
        <AddMenuModal
          onClose={() => {
            setShowAddMenuModal(false);
            setSelectedMenu(null);
          }}
          onSave={fetchData}
          menu={selectedMenu}
          setError={setError}
          setSuccessMessage={setSuccessMessage}
        />
      )}
    </div>
  );
};

export default MenusView;
