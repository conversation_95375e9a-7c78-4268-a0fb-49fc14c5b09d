// frontend/components/PhoneVerification.tsx
'use client';

import { useState, useRef, useEffect } from 'react';
import { setupRecaptcha, sendOTPWithFirebase, verifyOTPWithFirebase } from '../lib/firebasePhone';
import { RecaptchaVerifier } from 'firebase/auth';

interface PhoneVerificationProps {
  onVerificationSuccess: (phoneNumber: string) => void;
  onError: (error: string) => void;
}

export default function PhoneVerification({ onVerificationSuccess, onError }: PhoneVerificationProps) {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [otp, setOtp] = useState('');
  const [step, setStep] = useState<'input' | 'verify'>('input');
  const [loading, setLoading] = useState(false);
  const [timeLeft, setTimeLeft] = useState(0);
  const recaptchaContainerRef = useRef<HTMLDivElement>(null);
  const recaptchaVerifierRef = useRef<RecaptchaVerifier | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  
  // Clear timer on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);
  
  // Start countdown timer
  const startTimer = () => {
    setTimeLeft(30); // 30 seconds countdown
    
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    
    timerRef.current = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          clearInterval(timerRef.current!);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };
  
  // Handle phone number submission
  const handleSendOTP = async () => {
    try {
      setLoading(true);
      
      // Setup reCAPTCHA if not already set up
      if (!recaptchaVerifierRef.current && recaptchaContainerRef.current) {
        recaptchaVerifierRef.current = setupRecaptcha('recaptcha-container');
      }
      
      // Send OTP
      await sendOTPWithFirebase(phoneNumber, recaptchaVerifierRef.current!);
      
      // Update UI
      setStep('verify');
      startTimer();
      
    } catch (error) {
      console.error('Error sending OTP:', error);
      onError('Failed to send verification code. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle OTP verification
  const handleVerifyOTP = async () => {
    try {
      setLoading(true);
      
      // Verify OTP
      const result = await verifyOTPWithFirebase(otp);
      
      if (result.success) {
        // Notify parent component
        onVerificationSuccess(result.phoneNumber!);
        
        // Reset form
        setOtp('');
        setStep('input');
      }
    } catch (error) {
      console.error('Error verifying OTP:', error);
      onError('Invalid verification code. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle resend OTP
  const handleResendOTP = async () => {
    if (timeLeft > 0) return;
    
    try {
      setLoading(true);
      
      // Reset reCAPTCHA
      if (recaptchaContainerRef.current) {
        recaptchaVerifierRef.current = setupRecaptcha('recaptcha-container');
      }
      
      // Resend OTP
      await sendOTPWithFirebase(phoneNumber, recaptchaVerifierRef.current!);
      
      // Restart timer
      startTimer();
      
    } catch (error) {
      console.error('Error resending OTP:', error);
      onError('Failed to resend verification code. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="phone-verification">
      {step === 'input' ? (
        <div className="phone-input-container">
          <input
            type="tel"
            placeholder="Enter phone number"
            value={phoneNumber}
            onChange={(e) => setPhoneNumber(e.target.value)}
            disabled={loading}
            className="phone-input"
          />
          <button 
            onClick={handleSendOTP} 
            disabled={!phoneNumber || loading}
            className="send-otp-button"
          >
            {loading ? 'Sending...' : 'Send Code'}
          </button>
        </div>
      ) : (
        <div className="otp-verification-container">
          <input
            type="text"
            placeholder="Enter verification code"
            value={otp}
            onChange={(e) => setOtp(e.target.value)}
            disabled={loading}
            className="otp-input"
          />
          <button 
            onClick={handleVerifyOTP} 
            disabled={!otp || loading}
            className="verify-button"
          >
            {loading ? 'Verifying...' : 'Verify'}
          </button>
          <div className="resend-container">
            {timeLeft > 0 ? (
              <p>Resend in {timeLeft} seconds</p>
            ) : (
              <button 
                onClick={handleResendOTP} 
                disabled={loading}
                className="resend-button"
              >
                Resend Code
              </button>
            )}
          </div>
          <button 
            onClick={() => setStep('input')}
            className="back-button"
          >
            Change Phone Number
          </button>
        </div>
      )}
      
      {/* reCAPTCHA container - invisible but needed */}
      <div id="recaptcha-container" ref={recaptchaContainerRef}></div>
    </div>
  );
}