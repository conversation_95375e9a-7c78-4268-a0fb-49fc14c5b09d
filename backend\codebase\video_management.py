import os
import json
import boto3
import jwt
import psycopg2
from dotenv import load_dotenv
from datetime import datetime

# Load environment variables
load_dotenv()

# Database connection parameters
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_PORT = os.getenv('DB_PORT')
DB_NAME = os.getenv('DB_NAME')

# AWS parameters
AWS_ACCESS_KEY = os.getenv('AWS_ACCESS_KEY')
AWS_SECRET_KEY = os.getenv('AWS_SECRET_KEY')
S3_BUCKET = os.getenv('S3_BUCKET')

# JWT secret
JWT_SECRET = os.getenv('JWT_SECRET')

# S3 client
s3_client = boto3.client(
    's3',
    aws_access_key_id=AWS_ACCESS_KEY,
    aws_secret_access_key=AWS_SECRET_KEY
)

# Create a fresh connection for each function call
def get_db_connection():
    return psycopg2.connect(
        host=DB_HOST,
        user=DB_USER,
        password=DB_PASSWORD,
        port=DB_PORT,
        dbname=DB_NAME
    )

def validate_token(headers):
    token = headers.get('Authorization')
    
    if not token:
        return None, {
            'statusCode': 401,
            'body': json.dumps({"error": "Token is required"})
        }
        
    try:
        data = jwt.decode(token.split()[1], JWT_SECRET, algorithms=['HS256'])
        return data['user_id'], None
    except jwt.ExpiredSignatureError:
        return None, {
            'statusCode': 401,
            'body': json.dumps({"error": "Token expired"})
        }
    except jwt.InvalidTokenError:
        return None, {
            'statusCode': 401,
            'body': json.dumps({"error": "Invalid token"})
        }

def delete_video(event):
    try:
        # Validate token
        user_id, error = validate_token(event['headers'])
        if error:
            return error
            
        # Parse request body
        data = json.loads(event['body'])
        video_id = data.get('video_id')
        
        if not video_id:
            return {
                'statusCode': 400,
                'body': json.dumps({"error": "Video ID is required"})
            }
            
        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Check if video exists and belongs to user
        cursor.execute(
            "SELECT video_url, video_thumbnail, transcoded_versions FROM videos WHERE video_id = %s AND user_id = %s",
            (video_id, user_id)
        )
        result = cursor.fetchone()
        
        if not result:
            return {
                'statusCode': 404,
                'body': json.dumps({"error": "Video not found or not authorized"})
            }
            
        video_url, thumbnail_url, transcoded_versions = result
        
        try:
            cursor.execute("BEGIN")
            
            # Delete from database tables
            cursor.execute("DELETE FROM video_details WHERE video_id = %s", (video_id,))
            cursor.execute("DELETE FROM video_stats WHERE video_id = %s", (video_id,))
            cursor.execute("DELETE FROM comments WHERE content_id = %s AND content_type = 'video'", (video_id,))
            cursor.execute("DELETE FROM likes WHERE content_id = %s AND content_type = 'video'", (video_id,))
            cursor.execute("DELETE FROM videos WHERE video_id = %s", (video_id,))
            
            cursor.execute("COMMIT")
            
            # Delete files from S3
            try:
                # Get S3 keys from URLs
                s3_keys = []
                
                # Main video URL
                if video_url:
                    key = video_url.split('/')[-2] + '/' + video_url.split('/')[-1]
                    s3_keys.append(key)
                
                # Thumbnail URL
                if thumbnail_url:
                    key = thumbnail_url.split('/')[-2] + '/' + thumbnail_url.split('/')[-1]
                    s3_keys.append(key)
                
                # Transcoded versions
                if transcoded_versions:
                    versions = json.loads(transcoded_versions)
                    for version in versions:
                        url = version.get('url')
                        if url:
                            key = url.split('/')[-2] + '/' + url.split('/')[-1]
                            s3_keys.append(key)
                
                # Delete files
                for key in s3_keys:
                    try:
                        s3_client.delete_object(Bucket=S3_BUCKET, Key=key)
                    except Exception as e:
                        print(f"Error deleting S3 object {key}: {str(e)}")
                
            except Exception as e:
                print(f"Error deleting S3 objects: {str(e)}")
                # Continue with response, even if S3 deletion fails
            
            return {
                'statusCode': 200,
                'body': json.dumps({"message": "Video deleted successfully"})
            }
            
        except Exception as e:
            cursor.execute("ROLLBACK")
            return {
                'statusCode': 500,
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
            
    except Exception as e:
        return {
            'statusCode': 500,
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    finally:
        if 'conn' in locals() and conn:
            cursor.close()
            conn.close()

def get_videos(event):
    try:
        # Validate token
        user_id, error = validate_token(event['headers'])
        if error:
            return error
            
        # Get query parameters
        params = event.get('queryStringParameters', {}) or {}
        page = int(params.get('page', 1))
        page_size = min(int(params.get('page_size', 10)), 50)  # Limit to max 50 per page
        category = params.get('category')
        tag = params.get('tag')
        user_filter = params.get('user_id')
        
        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Build the query
        query = """
            SELECT v.video_id, v.video_name, v.video_url, v.video_description, 
                   v.video_tags, v.video_category, v.video_thumbnail, v.video_duration, 
                   v.video_date, v.user_id, u.name AS username, 
                   vs.video_views, vs.video_likes, vs.video_comments
            FROM videos v
            JOIN users u ON v.user_id = u.user_id
            LEFT JOIN video_stats vs ON v.video_id = vs.video_id
            WHERE 1=1
        """
        query_params = []
        
        # Add filters
        if category:
            query += " AND v.video_category = %s"
            query_params.append(category)
            
        if tag:
            query += " AND v.video_tags @> %s"
            query_params.append(json.dumps([tag]))
            
        if user_filter:
            query += " AND v.user_id = %s"
            query_params.append(user_filter)
            
        # Add ordering
        query += " ORDER BY v.video_date DESC"
        
        # Add pagination
        query += " LIMIT %s OFFSET %s"
        query_params.extend([page_size, (page - 1) * page_size])
        
        # Execute query
        cursor.execute(query, query_params)
        videos = cursor.fetchall()
        
        # Get total count for pagination
        count_query = """
            SELECT COUNT(*)
            FROM videos v
            WHERE 1=1
        """
        count_params = []
        
        if category:
            count_query += " AND v.video_category = %s"
            count_params.append(category)
            
        if tag:
            count_query += " AND v.video_tags @> %s"
            count_params.append(json.dumps([tag]))
            
        if user_filter:
            count_query += " AND v.user_id = %s"
            count_params.append(user_filter)
            
        cursor.execute(count_query, count_params)
        total_count = cursor.fetchone()[0]
        
        # Format results
        results = []
        for video in videos:
            # Check if user has liked the video
            cursor.execute(
                "SELECT 1 FROM likes WHERE content_id = %s AND content_type = 'video' AND user_id = %s",
                (video[0], user_id)
            )
            liked = bool(cursor.fetchone())
            
            results.append({
                "video_id": video[0],
                "video_name": video[1],
                "video_url": video[2],
                "video_description": video[3],
                "video_tags": json.loads(video[4]),
                "video_category": video[5],
                "video_thumbnail": video[6],
                "video_duration": video[7],
                "video_date": video[8].isoformat() if video[8] else None,
                "user_id": video[9],
                "username": video[10],
                "views": video[11] or 0,
                "likes": video[12] or 0,
                "comments": video[13] or 0,
                "liked": liked
            })
        
        return {
            'statusCode': 200,
            'body': json.dumps({
                "videos": results,
                "total": total_count,
                "page": page,
                "page_size": page_size,
                "total_pages": (total_count + page_size - 1) // page_size
            })
        }
            
    except Exception as e:
        return {
            'statusCode': 500,
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    finally:
        if 'conn' in locals() and conn:
            cursor.close()
            conn.close()

def get_video(event):
    try:
        # Validate token
        user_id, error = validate_token(event['headers'])
        if error:
            return error
            
        # Get video ID from path parameters
        video_id = event.get('pathParameters', {}).get('video_id')
        
        if not video_id:
            return {
                'statusCode': 400,
                'body': json.dumps({"error": "Video ID is required"})
            }
            
        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Get video details
        cursor.execute("""
            SELECT v.video_id, v.video_name, v.video_url, v.video_description, 
                   v.video_tags, v.video_category, v.video_thumbnail, v.video_duration, 
                   v.video_date, v.user_id, u.name AS username, v.transcoded_versions,
                   vs.video_views, vs.video_likes, vs.video_comments
            FROM videos v
            JOIN users u ON v.user_id = u.user_id
            LEFT JOIN video_stats vs ON v.video_id = vs.video_id
            WHERE v.video_id = %s
        """, (video_id,))
        
        video = cursor.fetchone()
        
        if not video:
            return {
                'statusCode': 404,
                'body': json.dumps({"error": "Video not found"})
            }
            
        # Check if user has liked the video
        cursor.execute(
            "SELECT 1 FROM likes WHERE content_id = %s AND content_type = 'video' AND user_id = %s",
            (video_id, user_id)
        )
        liked = bool(cursor.fetchone())
        
        # Get video details
        cursor.execute("SELECT details FROM video_details WHERE video_id = %s", (video_id,))
        details_row = cursor.fetchone()
        details = json.loads(details_row[0]) if details_row else {}
        
        # Get recent comments
        cursor.execute("""
            SELECT c.comment_id, c.user_id, u.name AS username, c.comment_text, c.created_at
            FROM comments c
            JOIN users u ON c.user_id = u.user_id
            WHERE c.content_id = %s AND c.content_type = 'video' AND c.parent_comment_id IS NULL
            ORDER BY c.created_at DESC
            LIMIT 10
        """, (video_id,))
        
        comments = [{
            "comment_id": row[0],
            "user_id": row[1],
            "username": row[2],
            "comment_text": row[3],
            "created_at": row[4].isoformat() if row[4] else None
        } for row in cursor.fetchall()]
        
        # Increment view count if this is not the owner viewing
        if user_id != video[9]:
            try:
                cursor.execute("BEGIN")
                
                # Update view count
                cursor.execute(
                    "UPDATE video_stats SET video_views = video_views + 1 WHERE video_id = %s",
                    (video_id,)
                )
                
                # Record view for analytics
                cursor.execute(
                    """INSERT INTO views (view_id, content_id, content_type, user_id, view_timestamp, device_info)
                       VALUES (uuid_generate_v4(), %s, 'video', %s, NOW(), %s)""",
                    (video_id, user_id, '{}')
                )
                
                cursor.execute("COMMIT")
            except Exception as e:
                cursor.execute("ROLLBACK")
                print(f"Error incrementing view count: {str(e)}")
        
        # Format result
        result = {
            "video_id": video[0],
            "video_name": video[1],
            "video_url": video[2],
            "video_description": video[3],
            "video_tags": json.loads(video[4]),
            "video_category": video[5],
            "video_thumbnail": video[6],
            "video_duration": video[7],
            "video_date": video[8].isoformat() if video[8] else None,
            "user_id": video[9],
            "username": video[10],
            "transcoded_versions": json.loads(video[11]) if video[11] else [],
            "views": video[12] or 0,
            "likes": video[13] or 0,
            "comments_count": video[14] or 0,
            "liked": liked,
            "details": details,
            "comments": comments,
            "is_owner": user_id == video[9]
        }
        
        return {
            'statusCode': 200,
            'body': json.dumps(result)
        }
            
    except Exception as e:
        return {
            'statusCode': 500,
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    finally:
        if 'conn' in locals() and conn:
            cursor.close()
            conn.close()

def search_videos(event):
    try:
        # Validate token
        user_id, error = validate_token(event['headers'])
        if error:
            return error
            
        # Get query parameters
        params = event.get('queryStringParameters', {}) or {}
        query = params.get('q', '').strip()
        page = int(params.get('page', 1))
        page_size = min(int(params.get('page_size', 10)), 50)  # Limit to max 50 per page
        
        if not query:
            return {
                'statusCode': 400,
                'body': json.dumps({"error": "Search query is required"})
            }
            
        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Search videos by name, description, and tags
        search_query = """
            SELECT v.video_id, v.video_name, v.video_url, v.video_description, 
                   v.video_tags, v.video_category, v.video_thumbnail, v.video_duration, 
                   v.video_date, v.user_id, u.name AS username, 
                   vs.video_views, vs.video_likes, vs.video_comments
            FROM videos v
            JOIN users u ON v.user_id = u.user_id
            LEFT JOIN video_stats vs ON v.video_id = vs.video_id
            WHERE (
                v.video_name ILIKE %s OR
                v.video_description ILIKE %s OR
                EXISTS (
                    SELECT 1 FROM jsonb_array_elements_text(v.video_tags) tag
                    WHERE tag ILIKE %s
                )
            )
            ORDER BY 
                CASE 
                    WHEN v.video_name ILIKE %s THEN 1
                    WHEN v.video_name ILIKE %s THEN 2
                    ELSE 3
                END,
                v.video_date DESC
            LIMIT %s OFFSET %s
        """
        
        search_params = [
            f"%{query}%",    # For name ILIKE
            f"%{query}%",    # For description ILIKE
            f"%{query}%",    # For tags ILIKE
            f"{query}%",     # For exact match at beginning (priority 1)
            f"%{query}%",    # For contains match (priority 2)
            page_size,
            (page - 1) * page_size
        ]
        
        cursor.execute(search_query, search_params)
        videos = cursor.fetchall()
        
        # Get total count for pagination
        count_query = """
            SELECT COUNT(*)
            FROM videos v
            WHERE (
                v.video_name ILIKE %s OR
                v.video_description ILIKE %s OR
                EXISTS (
                    SELECT 1 FROM jsonb_array_elements_text(v.video_tags) tag
                    WHERE tag ILIKE %s
                )
            )
        """
        
        count_params = [
            f"%{query}%",    # For name ILIKE
            f"%{query}%",    # For description ILIKE
            f"%{query}%",    # For tags ILIKE
        ]
        
        cursor.execute(count_query, count_params)
        total_count = cursor.fetchone()[0]
        
        # Format results
        results = []
        for video in videos:
            # Check if user has liked the video
            cursor.execute(
                "SELECT 1 FROM likes WHERE content_id = %s AND content_type = 'video' AND user_id = %s",
                (video[0], user_id)
            )
            liked = bool(cursor.fetchone())
            
            results.append({
                "video_id": video[0],
                "video_name": video[1],
                "video_url": video[2],
                "video_description": video[3],
                "video_tags": json.loads(video[4]),
                "video_category": video[5],
                "video_thumbnail": video[6],
                "video_duration": video[7],
                "video_date": video[8].isoformat() if video[8] else None,
                "user_id": video[9],
                "username": video[10],
                "views": video[11] or 0,
                "likes": video[12] or 0,
                "comments": video[13] or 0,
                "liked": liked
            })
        
        return {
            'statusCode': 200,
            'body': json.dumps({
                "videos": results,
                "total": total_count,
                "page": page,
                "page_size": page_size,
                "total_pages": (total_count + page_size - 1) // page_size,
                "query": query
            })
        }
            
    except Exception as e:
        return {
            'statusCode': 500,
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    finally:
        if 'conn' in locals() and conn:
            cursor.close()
            conn.close()