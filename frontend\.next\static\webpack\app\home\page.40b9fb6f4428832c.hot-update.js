"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./contexts/UploadContexts.tsx":
/*!*************************************!*\
  !*** ./contexts/UploadContexts.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UploadProvider: () => (/* binding */ UploadProvider),\n/* harmony export */   useUpload: () => (/* binding */ useUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/api */ \"(app-pages-browser)/./services/api.ts\");\n/* harmony import */ var _utils_uploadUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/uploadUtils */ \"(app-pages-browser)/./utils/uploadUtils.ts\");\n// contexts/UploadContext.tsx\n/* __next_internal_client_entry_do_not_use__ UploadProvider,useUpload auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Initial state\nconst initialState = {\n    file: null,\n    thumbnail: null,\n    mediaType: 'photo',\n    mediaSubtype: 'story',\n    category: '',\n    title: '',\n    description: '',\n    tags: [],\n    detailFields: {},\n    step: 'selecting',\n    progress: 0,\n    isUploading: false\n};\n// Create context\nconst UploadContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Provider component\nconst UploadProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialState);\n    // Set file and automatically determine media type\n    const setFile = async (file)=>{\n        if (!file) {\n            setState({\n                ...state,\n                file: null\n            });\n            return;\n        }\n        const isVideo = file.type.startsWith('video/');\n        const mediaType = isVideo ? 'video' : 'photo';\n        // Default media subtypes based on media type\n        const mediaSubtype = isVideo ? 'glimpse' : 'post'; // Default to 'post' for photos\n        setState({\n            ...state,\n            file,\n            mediaType,\n            mediaSubtype,\n            step: 'details'\n        });\n    };\n    // Set thumbnail image\n    const setThumbnail = (thumbnail)=>{\n        setState({\n            ...state,\n            thumbnail\n        });\n    };\n    const setMediaType = (type)=>{\n        // Don't set a default category - let the user's selection flow through the process\n        // Just update the media type\n        setState({\n            ...state,\n            mediaType: type\n        });\n    };\n    const setMediaSubtype = (mediaSubtype)=>{\n        setState({\n            ...state,\n            mediaSubtype\n        });\n    };\n    // Keep the old function for backward compatibility\n    const setCategory = (category)=>{\n        console.log('UPLOAD CONTEXT - setCategory is deprecated, use setMediaSubtype instead');\n        setState({\n            ...state,\n            mediaSubtype: category\n        });\n    };\n    const setTitle = (title)=>{\n        // Ensure title is not empty\n        if (!title || !title.trim()) {\n            console.warn('Attempted to set empty title');\n            return;\n        }\n        console.log('Setting title to:', title.trim());\n        setState({\n            ...state,\n            title: title.trim()\n        });\n    };\n    const setDescription = (description)=>{\n        setState({\n            ...state,\n            description\n        });\n    };\n    const addTag = (tag)=>{\n        if (tag.trim() && !state.tags.includes(tag.trim())) {\n            setState({\n                ...state,\n                tags: [\n                    ...state.tags,\n                    tag.trim()\n                ]\n            });\n        }\n    };\n    const removeTag = (tag)=>{\n        setState({\n            ...state,\n            tags: state.tags.filter((t)=>t !== tag)\n        });\n    };\n    const setDetailField = (field, value)=>{\n        // Special handling for video_category to ensure it's properly set\n        if (field === 'video_category') {\n            console.log(\"UPLOAD CONTEXT - Setting video_category to: \".concat(value));\n            // Store video_category in localStorage\n            try {\n                localStorage.setItem('wedzat_video_category', value);\n                console.log(\"UPLOAD CONTEXT - Stored video_category in localStorage: \".concat(value));\n            } catch (error) {\n                console.error('UPLOAD CONTEXT - Failed to store video_category in localStorage:', error);\n            }\n        }\n        // Special handling for vendor fields to ensure they're properly set\n        if (field.startsWith('vendor_')) {\n            // If this is a vendor field, update the vendor details in localStorage\n            try {\n                // Get existing vendor details from localStorage\n                const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n                let vendorDetails = storedVendorDetails ? JSON.parse(storedVendorDetails) : {};\n                // If this is a vendor name field, extract the vendor type and update the name\n                if (field.endsWith('_name')) {\n                    const vendorType = field.replace('vendor_', '').replace('_name', '');\n                    // Check if we already have this vendor in the details\n                    if (!vendorDetails[vendorType]) {\n                        vendorDetails[vendorType] = {\n                            name: value,\n                            mobileNumber: ''\n                        };\n                    } else {\n                        vendorDetails[vendorType].name = value;\n                    }\n                }\n                // If this is a vendor contact field, extract the vendor type and update the contact\n                if (field.endsWith('_contact')) {\n                    const vendorType = field.replace('vendor_', '').replace('_contact', '');\n                    // Check if we already have this vendor in the details\n                    if (!vendorDetails[vendorType]) {\n                        vendorDetails[vendorType] = {\n                            name: '',\n                            mobileNumber: value\n                        };\n                    } else {\n                        vendorDetails[vendorType].mobileNumber = value;\n                    }\n                }\n                // Store the updated vendor details in localStorage\n                localStorage.setItem('wedzat_vendor_details', JSON.stringify(vendorDetails));\n            } catch (error) {\n                console.error('UPLOAD CONTEXT - Failed to update vendor details in localStorage:', error);\n            }\n        }\n        // Create a new detailFields object with the updated field\n        const updatedDetailFields = {\n            ...state.detailFields,\n            [field]: value\n        };\n        // Update the state with the new detailFields\n        setState((prevState)=>({\n                ...prevState,\n                detailFields: updatedDetailFields\n            }));\n        // For video_category, log the updated state after a short delay\n        if (field === 'video_category') {\n            setTimeout(()=>{\n                console.log(\"UPLOAD CONTEXT - Verified video_category is set to: \".concat(state.detailFields.video_category || 'Not set'));\n            }, 100);\n        }\n    };\n    // Set all personal details at once and update the title and related detail fields\n    const setPersonalDetails = (details)=>{\n        // console.log('Setting all personal details:', details);\n        // Validate caption/title\n        if (!details.caption || !details.caption.trim()) {\n            console.warn('Attempted to set personal details with empty caption/title');\n            return;\n        }\n        // Update title\n        const title = details.caption.trim();\n        // console.log('Setting title from personal details:', title);\n        // Update detail fields with backend-compatible field names\n        const updatedDetailFields = {\n            ...state.detailFields,\n            'personal_caption': title,\n            'personal_life_partner': details.lifePartner || '',\n            'personal_wedding_style': details.weddingStyle || '',\n            'personal_place': details.place || '',\n            'personal_event_type': details.eventType || '',\n            'personal_budget': details.budget || '',\n            // Keep legacy field names for compatibility\n            'lifePartner': details.lifePartner || '',\n            'location': details.place || '',\n            'place': details.place || '',\n            'eventType': details.eventType || '',\n            'budget': details.budget || '',\n            'weddingStyle': details.weddingStyle || ''\n        };\n        // Update state with all changes at once\n        setState({\n            ...state,\n            title,\n            description: details.weddingStyle || '',\n            detailFields: updatedDetailFields\n        });\n        // Log the description being set\n        console.log('Setting description to:', details.weddingStyle || '');\n        // Log the updated state after a short delay to ensure state has updated\n        setTimeout(()=>{\n            console.log('Personal details set successfully');\n            console.log('Title after update:', title);\n        }, 0);\n    };\n    // Set all vendor details at once and update the related detail fields\n    const setVendorDetails = (vendorDetails)=>{\n        console.log('UPLOAD CONTEXT - Setting all vendor details:', JSON.stringify(vendorDetails, null, 2));\n        // Create a copy of the current detail fields\n        const updatedDetailFields = {\n            ...state.detailFields\n        };\n        // Save the video_category if it exists\n        const videoCategory = state.detailFields.video_category;\n        console.log('UPLOAD CONTEXT - Preserving video_category:', videoCategory);\n        // Count how many complete vendor details we're receiving\n        const completeVendorCount = Object.entries(vendorDetails).filter((param)=>{\n            let [_, detail] = param;\n            return detail && detail.name && detail.mobileNumber && detail.name.trim() !== '' && detail.mobileNumber.trim() !== '';\n        }).length;\n        console.log(\"UPLOAD CONTEXT - Received \".concat(completeVendorCount, \" complete vendor details\"));\n        // Process vendor details\n        Object.entries(vendorDetails).forEach((param)=>{\n            let [vendorType, details] = param;\n            if (details) {\n                // Only include vendors that have BOTH name AND mobile number\n                if (details.name && details.mobileNumber && details.name.trim() !== '' && details.mobileNumber.trim() !== '') {\n                    // Handle special mappings for makeup_artist and decoration\n                    let backendVendorType = vendorType;\n                    // Map frontend field names to backend field names\n                    if (vendorType === 'makeupArtist') {\n                        backendVendorType = 'makeup_artist';\n                    } else if (vendorType === 'decorations') {\n                        backendVendorType = 'decoration';\n                    }\n                    // Store vendor details in the format expected by the backend\n                    updatedDetailFields[\"vendor_\".concat(backendVendorType, \"_name\")] = details.name || '';\n                    updatedDetailFields[\"vendor_\".concat(backendVendorType, \"_contact\")] = details.mobileNumber || '';\n                    // Always store with the original vendorType to ensure we count it correctly\n                    // This ensures both frontend and backend field names are present\n                    // This is especially important for Edge browser compatibility\n                    if (vendorType !== backendVendorType) {\n                        updatedDetailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name || '';\n                        updatedDetailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber || '';\n                    }\n                    // Also store with common vendor types to ensure cross-browser compatibility\n                    if (vendorType === 'makeupArtist' || vendorType === 'makeup_artist') {\n                        // Ensure both makeupArtist and makeup_artist are present\n                        updatedDetailFields[\"vendor_makeupArtist_name\"] = details.name || '';\n                        updatedDetailFields[\"vendor_makeupArtist_contact\"] = details.mobileNumber || '';\n                        updatedDetailFields[\"vendor_makeup_artist_name\"] = details.name || '';\n                        updatedDetailFields[\"vendor_makeup_artist_contact\"] = details.mobileNumber || '';\n                    } else if (vendorType === 'decorations' || vendorType === 'decoration') {\n                        // Ensure both decorations and decoration are present\n                        updatedDetailFields[\"vendor_decorations_name\"] = details.name || '';\n                        updatedDetailFields[\"vendor_decorations_contact\"] = details.mobileNumber || '';\n                        updatedDetailFields[\"vendor_decoration_name\"] = details.name || '';\n                        updatedDetailFields[\"vendor_decoration_contact\"] = details.mobileNumber || '';\n                    }\n                // console.log(`UPLOAD CONTEXT - Setting vendor detail: ${vendorType} (backend: ${backendVendorType})`, {\n                //   name: details.name || '',\n                //   contact: details.mobileNumber || ''\n                // });\n                } else {\n                    console.log(\"UPLOAD CONTEXT - Skipping incomplete vendor detail: \".concat(vendorType));\n                }\n            }\n        });\n        // Don't update state here - we'll do it after restoring the video_category\n        // console.log('UPLOAD CONTEXT - Vendor details set successfully');\n        // console.log('UPLOAD CONTEXT - Total detail fields count:', Object.keys(updatedDetailFields).length);\n        // Count how many complete vendor details we have after processing\n        let completeVendorPairs = 0;\n        const vendorNames = new Set();\n        const vendorContacts = new Set();\n        // Log all vendor details for debugging\n        Object.keys(updatedDetailFields).forEach((key)=>{\n            if (key.startsWith('vendor_')) {\n                // console.log(`UPLOAD CONTEXT - Vendor field: ${key} = ${updatedDetailFields[key]}`);\n                if (key.endsWith('_name')) {\n                    vendorNames.add(key.replace('vendor_', '').replace('_name', ''));\n                } else if (key.endsWith('_contact')) {\n                    vendorContacts.add(key.replace('vendor_', '').replace('_contact', ''));\n                }\n            }\n        });\n        // Count complete pairs (both name and contact)\n        vendorNames.forEach((name)=>{\n            if (vendorContacts.has(name)) {\n                completeVendorPairs++;\n            }\n        });\n        // console.log(`UPLOAD CONTEXT - Found ${completeVendorPairs} complete vendor pairs (name + contact)`);\n        // console.log(`UPLOAD CONTEXT - Vendor names: ${Array.from(vendorNames).join(', ')}`);\n        // console.log(`UPLOAD CONTEXT - Vendor contacts: ${Array.from(vendorContacts).join(', ')}`);\n        // Restore the video_category if it exists\n        if (videoCategory) {\n            console.log('UPLOAD CONTEXT - Restoring video_category:', videoCategory);\n            updatedDetailFields.video_category = videoCategory;\n        }\n        // Log the detail fields before updating state\n        console.log('UPLOAD CONTEXT - Detail fields before update:', JSON.stringify(state.detailFields, null, 2));\n        console.log('UPLOAD CONTEXT - Updated detail fields to be set:', JSON.stringify(updatedDetailFields, null, 2));\n        // Create a completely new state object to ensure Edge updates correctly\n        const newState = {\n            ...state,\n            detailFields: {\n                ...updatedDetailFields\n            }\n        };\n        // For Edge browser compatibility, directly set the vendor fields in the state\n        // This is a workaround for Edge where the state update doesn't properly preserve vendor details\n        if ( true && /Edge|Edg/.test(window.navigator.userAgent)) {\n            console.log('UPLOAD CONTEXT - Edge browser detected, using direct state update');\n            // Create a direct reference to the state object\n            const directState = state;\n            // Directly set the detailFields\n            directState.detailFields = {\n                ...updatedDetailFields\n            };\n            // Log the direct update\n            console.log('UPLOAD CONTEXT - Direct state update:', JSON.stringify(directState.detailFields, null, 2));\n        }\n        // Update the state with the updated detail fields\n        setState(newState);\n        // Force a re-render to ensure the state is updated\n        setTimeout(()=>{\n            console.log('UPLOAD CONTEXT - Vendor details set successfully');\n            console.log('UPLOAD CONTEXT - Total detail fields count:', Object.keys(state.detailFields).length);\n            console.log('UPLOAD CONTEXT - Detail fields after update:', JSON.stringify(state.detailFields, null, 2));\n            // Double-check that the vendor details were set correctly\n            const vendorNameFields = Object.keys(state.detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_name'));\n            const vendorContactFields = Object.keys(state.detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_contact'));\n            console.log('UPLOAD CONTEXT - Vendor name fields count:', vendorNameFields.length);\n            console.log('UPLOAD CONTEXT - Vendor contact fields count:', vendorContactFields.length);\n        }, 100);\n    };\n    const resetUpload = ()=>{\n        console.log('UPLOAD CONTEXT - Completely resetting upload state');\n        // Create a fresh copy of the initial state\n        const freshState = {\n            file: null,\n            thumbnail: null,\n            mediaType: '',\n            mediaSubtype: '',\n            title: '',\n            description: '',\n            tags: [],\n            detailFields: {},\n            step: 'select',\n            duration: 0\n        };\n        // Set the state to the fresh state\n        setState(freshState);\n        // Log the reset\n        console.log('UPLOAD CONTEXT - Upload state reset to:', JSON.stringify(freshState, null, 2));\n    };\n    // Helper function to detect Edge browser\n    const isEdgeBrowser = ()=>{\n        if (true) {\n            return /Edge|Edg/.test(window.navigator.userAgent);\n        }\n        return false;\n    };\n    const validateForm = ()=>{\n        // For moments (stories), only validate file and title - skip all other validations\n        if (state.mediaSubtype === 'story') {\n            console.log('VALIDATE FORM - Moments/Stories detected, using simplified validation');\n            // Check if file is selected\n            if (!state.file) {\n                console.log('Validation failed: No file selected for moments');\n                return {\n                    isValid: false,\n                    error: 'Please select a file to upload'\n                };\n            }\n            // Validate file type and size\n            const fileValidation = (0,_utils_uploadUtils__WEBPACK_IMPORTED_MODULE_3__.validateFile)(state.file, state.mediaType);\n            if (!fileValidation.isValid) {\n                console.log('Validation failed: File validation failed for moments', fileValidation);\n                return fileValidation;\n            }\n            // Check if title is provided\n            if (!state.title || !state.title.trim()) {\n                console.log('Validation failed: Title is empty for moments');\n                return {\n                    isValid: false,\n                    error: 'Please provide a title for your upload'\n                };\n            }\n            console.log('VALIDATE FORM - Moments validation passed');\n            return {\n                isValid: true\n            };\n        }\n        // Check if we're running in Edge browser\n        const isEdge = isEdgeBrowser();\n        if (isEdge) {\n            console.log('VALIDATE FORM - Running in Edge browser, applying special handling');\n        }\n        // console.log('VALIDATE FORM - Validating form with state:', {\n        //   file: state.file ? state.file.name : 'No file',\n        //   mediaType: state.mediaType,\n        //   title: state.title,\n        //   description: state.description,\n        //   detailFieldsCount: Object.keys(state.detailFields).length,\n        //   tags: state.tags\n        // });\n        // Log all detail fields for debugging\n        // console.log('VALIDATE FORM - All detail fields:', JSON.stringify(state.detailFields, null, 2));\n        // Check if file is selected\n        if (!state.file) {\n            // console.log('Validation failed: No file selected');\n            return {\n                isValid: false,\n                error: 'Please select a file to upload'\n            };\n        }\n        // Validate file type and size\n        const fileValidation = (0,_utils_uploadUtils__WEBPACK_IMPORTED_MODULE_3__.validateFile)(state.file, state.mediaType);\n        if (!fileValidation.isValid) {\n            console.log('Validation failed: File validation failed', fileValidation);\n            return fileValidation;\n        }\n        // Check if title is provided\n        if (!state.title || !state.title.trim()) {\n            console.log('Validation failed: Title is empty');\n            return {\n                isValid: false,\n                error: 'Please provide a title for your upload'\n            };\n        }\n        // First, try to get vendor details from localStorage\n        let detailFields = {\n            ...state.detailFields\n        };\n        let vendorDetailsFromStorage = null;\n        try {\n            const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n            if (storedVendorDetails) {\n                vendorDetailsFromStorage = JSON.parse(storedVendorDetails);\n                console.log('VALIDATE FORM - Retrieved vendor details from localStorage:', storedVendorDetails);\n                // Process vendor details from localStorage\n                if (vendorDetailsFromStorage && Object.keys(vendorDetailsFromStorage).length > 0) {\n                    Object.entries(vendorDetailsFromStorage).forEach((param)=>{\n                        let [vendorType, details] = param;\n                        if (details && details.name && details.mobileNumber) {\n                            // Add to detailFields\n                            detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                            detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                            console.log(\"VALIDATE FORM - Added vendor \".concat(vendorType, \" with name: \").concat(details.name, \" and contact: \").concat(details.mobileNumber));\n                            // Also add normalized versions\n                            const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                            if (normalizedType !== vendorType) {\n                                detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                                detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                                console.log(\"VALIDATE FORM - Also added normalized vendor \".concat(normalizedType));\n                            }\n                        }\n                    });\n                    console.log('VALIDATE FORM - Updated detail fields with vendor details from localStorage');\n                }\n            }\n        } catch (error) {\n            console.error('VALIDATE FORM - Failed to retrieve vendor details from localStorage:', error);\n        }\n        // Now use the updated detailFields for validation\n        console.log('Detail fields count:', Object.keys(detailFields).length);\n        console.log('Detail fields present:', Object.keys(detailFields));\n        console.log('Detail fields values:', detailFields);\n        // For videos, check if required vendor details are present based on video category\n        if (state.mediaType === 'video') {\n            // Determine required vendor count based on video category\n            const videoCategory = detailFields.video_category || 'my_wedding';\n            const requiredVendorCount = videoCategory === 'wedding_vlog' ? 1 : 4;\n            console.log(\"VALIDATE FORM - Video category: \".concat(videoCategory, \", Required vendors: \").concat(requiredVendorCount));\n            // Special handling for Edge browser\n            if (isEdge) {\n                console.log('VALIDATE FORM - Edge browser detected, checking vendor details directly');\n                // In Edge, we'll count vendor details directly from the detailFields\n                const vendorNameFields = Object.keys(detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_name'));\n                const vendorContactFields = Object.keys(detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_contact'));\n                console.log('VALIDATE FORM - Edge browser - Vendor name fields:', vendorNameFields);\n                console.log('VALIDATE FORM - Edge browser - Vendor contact fields:', vendorContactFields);\n                // If we have at least 4 vendor name fields and 4 vendor contact fields, assume we have enough vendors\n                if (vendorNameFields.length >= 4 && vendorContactFields.length >= 4) {\n                    console.log('VALIDATE FORM - Edge browser - Found enough vendor fields, validation passed');\n                    return {\n                        isValid: true\n                    };\n                }\n                // Edge browser workaround - if we're uploading a video, assume vendor details are valid\n                // This is a temporary workaround for Edge browser compatibility\n                if (state.mediaType === 'video') {\n                    console.log('VALIDATE FORM - Edge browser workaround: Assuming vendor details are valid for video upload');\n                    return {\n                        isValid: true\n                    };\n                }\n            }\n            console.log('VALIDATE FORM - Checking vendor details for video upload');\n            console.log('VALIDATE FORM - Detail fields:', JSON.stringify(detailFields, null, 2));\n            // Count how many complete vendor details we have (where BOTH name AND contact are provided)\n            let validVendorCount = 0;\n            // Include both frontend and backend field names to ensure we count all vendor details\n            const vendorPrefixes = [\n                'venue',\n                'photographer',\n                'makeup_artist',\n                'makeupArtist',\n                'decoration',\n                'decorations',\n                'caterer',\n                'additional1',\n                'additional2',\n                'additionalVendor1',\n                'additionalVendor2'\n            ];\n            console.log('VALIDATE FORM - Checking these vendor prefixes:', vendorPrefixes.join(', '));\n            // Keep track of which vendors we've already counted to avoid duplicates\n            const countedVendors = new Set();\n            // First, log all vendor-related fields for debugging\n            const vendorFields = Object.keys(detailFields).filter((key)=>key.startsWith('vendor_'));\n            console.log('VALIDATE FORM - All vendor fields:', vendorFields.join(', '));\n            for (const prefix of vendorPrefixes){\n                // Skip if we've already counted this vendor (to avoid counting both makeupArtist and makeup_artist)\n                const normalizedPrefix = prefix === 'makeupArtist' ? 'makeup_artist' : prefix === 'decorations' ? 'decoration' : prefix;\n                if (countedVendors.has(normalizedPrefix)) {\n                    console.log(\"VALIDATE FORM - Skipping \".concat(prefix, \" as we already counted \").concat(normalizedPrefix));\n                    continue;\n                }\n                const nameField = \"vendor_\".concat(prefix, \"_name\");\n                const contactField = \"vendor_\".concat(prefix, \"_contact\");\n                console.log(\"VALIDATE FORM - Checking vendor \".concat(prefix, \":\"), {\n                    nameField,\n                    nameValue: detailFields[nameField],\n                    contactField,\n                    contactValue: detailFields[contactField],\n                    hasName: !!detailFields[nameField],\n                    hasContact: !!detailFields[contactField]\n                });\n                if (detailFields[nameField] && detailFields[contactField]) {\n                    validVendorCount++;\n                    countedVendors.add(normalizedPrefix);\n                    console.log(\"VALIDATE FORM - Found valid vendor: \".concat(prefix, \" with name: \").concat(detailFields[nameField], \" and contact: \").concat(detailFields[contactField]));\n                }\n            }\n            // Also check for any other vendor_ fields that might have been added\n            console.log('VALIDATE FORM - Checking for additional vendor fields');\n            Object.keys(detailFields).forEach((key)=>{\n                if (key.startsWith('vendor_') && key.endsWith('_name')) {\n                    const baseKey = key.replace('vendor_', '').replace('_name', '');\n                    const contactKey = \"vendor_\".concat(baseKey, \"_contact\");\n                    // Skip if we've already counted this vendor\n                    const normalizedPrefix = baseKey === 'makeupArtist' ? 'makeup_artist' : baseKey === 'decorations' ? 'decoration' : baseKey;\n                    console.log(\"VALIDATE FORM - Checking additional vendor \".concat(baseKey, \":\"), {\n                        normalizedPrefix,\n                        alreadyCounted: countedVendors.has(normalizedPrefix),\n                        hasName: !!detailFields[key],\n                        hasContact: !!detailFields[contactKey]\n                    });\n                    if (!countedVendors.has(normalizedPrefix) && detailFields[key] && detailFields[contactKey]) {\n                        validVendorCount++;\n                        countedVendors.add(normalizedPrefix);\n                        console.log(\"VALIDATE FORM - Found additional valid vendor: \".concat(baseKey, \" with name: \").concat(detailFields[key], \" and contact: \").concat(detailFields[contactKey]));\n                    }\n                }\n            });\n            console.log(\"VALIDATE FORM - Total valid vendor count: \".concat(validVendorCount));\n            console.log(\"VALIDATE FORM - Counted vendors: \".concat(Array.from(countedVendors).join(', ')));\n            // Special handling for Edge browser - if we have vendor fields but they're not being counted correctly\n            let edgeVendorNameFields = Object.keys(detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_name'));\n            let edgeVendorContactFields = Object.keys(detailFields).filter((key)=>key.startsWith('vendor_') && key.endsWith('_contact'));\n            console.log('VALIDATE FORM - Edge browser check - Vendor name fields:', edgeVendorNameFields);\n            console.log('VALIDATE FORM - Edge browser check - Vendor contact fields:', edgeVendorContactFields);\n            // If we have at least required vendor name fields and contact fields, but validVendorCount is less than required,\n            // this is likely an Edge browser issue where the fields aren't being properly counted\n            if (validVendorCount < requiredVendorCount && edgeVendorNameFields.length >= requiredVendorCount && edgeVendorContactFields.length >= requiredVendorCount) {\n                console.log('VALIDATE FORM - Edge browser workaround: Found enough vendor fields but they were not counted correctly');\n                console.log('VALIDATE FORM - Vendor name fields:', edgeVendorNameFields.join(', '));\n                console.log('VALIDATE FORM - Vendor contact fields:', edgeVendorContactFields.join(', '));\n                // Count unique vendor prefixes (excluding the _name/_contact suffix)\n                const vendorPrefixSet = new Set();\n                edgeVendorNameFields.forEach((field)=>{\n                    const prefix = field.replace('vendor_', '').replace('_name', '');\n                    if (edgeVendorContactFields.includes(\"vendor_\".concat(prefix, \"_contact\"))) {\n                        vendorPrefixSet.add(prefix);\n                    }\n                });\n                const uniqueVendorCount = vendorPrefixSet.size;\n                console.log(\"VALIDATE FORM - Unique vendor count: \".concat(uniqueVendorCount));\n                if (uniqueVendorCount >= requiredVendorCount) {\n                    console.log(\"VALIDATE FORM - Edge browser workaround: Found at least \".concat(requiredVendorCount, \" unique vendors with both name and contact\"));\n                    validVendorCount = uniqueVendorCount;\n                }\n            }\n            // Log the vendor field counts\n            console.log('VALIDATE FORM - Vendor name fields count:', edgeVendorNameFields.length);\n            console.log('VALIDATE FORM - Vendor contact fields count:', edgeVendorContactFields.length);\n            if (validVendorCount < requiredVendorCount) {\n                console.log('VALIDATE FORM - Validation failed: Not enough vendor details', validVendorCount);\n                const categoryText = videoCategory === 'wedding_vlog' ? 'wedding vlog' : 'my wedding';\n                return {\n                    isValid: false,\n                    error: \"At least \".concat(requiredVendorCount, \" complete vendor detail\").concat(requiredVendorCount > 1 ? 's' : '', \" (with both name and contact) \").concat(requiredVendorCount > 1 ? 'are' : 'is', \" required for \").concat(categoryText, \" videos. You provided \").concat(validVendorCount, \"/\").concat(requiredVendorCount, \".\")\n                };\n            } else {\n                console.log('VALIDATE FORM - Vendor validation passed with', validVendorCount, 'vendors');\n            }\n        }\n        // Just log the detail fields count for now\n        console.log('Detail fields count:', Object.keys(state.detailFields).length);\n        // Log the detail fields that are present\n        console.log('Detail fields present:', Object.keys(state.detailFields));\n        console.log('Detail fields values:', state.detailFields);\n        console.log('Form validation passed');\n        return {\n            isValid: true\n        };\n    };\n    // Start upload with a specific category and video_category (used when correcting the category)\n    const startUploadWithCategory = async (category, videoCategory)=>{\n        console.log(\"Starting upload process with corrected category: \".concat(category));\n        console.log(\"Using video_category: \".concat(videoCategory || 'Not provided'));\n        // Try to get vendor details from localStorage\n        let vendorDetails = {};\n        try {\n            const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n            if (storedVendorDetails) {\n                vendorDetails = JSON.parse(storedVendorDetails);\n                console.log('UPLOAD CONTEXT - Retrieved vendor details from localStorage');\n            }\n        } catch (error) {\n            console.error('UPLOAD CONTEXT - Failed to retrieve vendor details from localStorage:', error);\n        }\n        // Create detail fields from vendor details\n        const detailFields = {\n            ...state.detailFields\n        };\n        // Process vendor details to create detail fields\n        if (Object.keys(vendorDetails).length > 0) {\n            console.log('UPLOAD CONTEXT - Processing vendor details from localStorage:', JSON.stringify(vendorDetails));\n            // Track how many complete vendor details we've added\n            let completeVendorCount = 0;\n            Object.entries(vendorDetails).forEach((param)=>{\n                let [vendorType, details] = param;\n                if (details && details.name && details.mobileNumber) {\n                    detailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                    detailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                    console.log(\"UPLOAD CONTEXT - Added vendor \".concat(vendorType, \" with name: \").concat(details.name, \" and contact: \").concat(details.mobileNumber));\n                    completeVendorCount++;\n                    // Also set normalized versions\n                    const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                    if (normalizedType !== vendorType) {\n                        detailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                        detailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                        console.log(\"UPLOAD CONTEXT - Also added normalized vendor \".concat(normalizedType));\n                    }\n                }\n            });\n            console.log(\"UPLOAD CONTEXT - Added \".concat(completeVendorCount, \" complete vendor details from localStorage to detailFields\"));\n            console.log('UPLOAD CONTEXT - Detail fields after adding vendors:', JSON.stringify(detailFields));\n        }\n        // Update the state with the corrected category and video_category if provided\n        const updatedState = {\n            ...state,\n            mediaSubtype: category,\n            category: category,\n            detailFields: detailFields\n        };\n        // If videoCategory is provided, update the detailFields\n        if (videoCategory) {\n            updatedState.detailFields.video_category = videoCategory;\n            console.log(\"Setting video_category in state to: \".concat(videoCategory));\n            // Also store in localStorage\n            try {\n                localStorage.setItem('wedzat_video_category', videoCategory);\n                console.log('UPLOAD CONTEXT - Stored video_category in localStorage:', videoCategory);\n            } catch (error) {\n                console.error('UPLOAD CONTEXT - Failed to store video_category in localStorage:', error);\n            }\n        }\n        // Apply the state update immediately\n        setState(updatedState);\n        // Then start the upload process with the updated category\n        const validation = validateForm();\n        if (!validation.isValid) {\n            console.log('Upload validation failed:', validation.error);\n            setState({\n                ...state,\n                mediaSubtype: category,\n                error: validation.error,\n                step: 'error'\n            });\n            return;\n        }\n        if (!state.file) {\n            console.log('No file to upload');\n            return;\n        }\n        setState({\n            ...state,\n            mediaSubtype: category,\n            isUploading: true,\n            progress: 0,\n            step: 'uploading',\n            error: undefined\n        });\n        try {\n            console.log('UPLOAD CONTEXT - Starting upload process with corrected category...');\n            console.log('UPLOAD CONTEXT - Upload details:', {\n                file: state.file ? state.file.name : 'No file',\n                fileSize: state.file ? Math.round(state.file.size / (1024 * 1024) * 100) / 100 + ' MB' : 'N/A',\n                mediaType: state.mediaType,\n                category: category,\n                title: state.title,\n                videoCategory: videoCategory || 'Not set'\n            });\n            // Log the video_category that will be used\n            console.log('UPLOAD CONTEXT - Using explicit video_category:', videoCategory);\n            console.log('UPLOAD CONTEXT - Video category from detail fields:', state.detailFields.video_category);\n            // Create a copy of the detail fields with the explicit video_category\n            const updatedDetailFields = {\n                ...state.detailFields\n            };\n            // If videoCategory is provided, use it\n            if (videoCategory) {\n                updatedDetailFields.video_category = videoCategory;\n                console.log('UPLOAD CONTEXT - Setting video_category in details to:', videoCategory);\n            }\n            // Use the upload service to handle the complete upload process with the corrected category\n            const result = await _services_api__WEBPACK_IMPORTED_MODULE_2__.uploadService.handleUpload(state.file, state.mediaType, category, state.title, state.description, state.tags, updatedDetailFields, state.duration, state.thumbnail, (progress)=>{\n                setState({\n                    ...state,\n                    mediaSubtype: category,\n                    progress\n                });\n            });\n            // Update the state with the upload result\n            setState({\n                ...state,\n                mediaSubtype: category,\n                isUploading: false,\n                progress: 100,\n                step: 'complete',\n                uploadResult: result\n            });\n        // Upload completed successfully\n        } catch (error) {\n            console.error('Upload failed:', error);\n            setState({\n                ...state,\n                mediaSubtype: category,\n                isUploading: false,\n                error: error instanceof Error ? error.message : 'Unknown error'\n            });\n            throw error;\n        }\n    };\n    const startUpload = async ()=>{\n        console.log('Starting upload process...');\n        // For moments (stories), skip vendor details processing and go directly to upload\n        if (state.mediaSubtype === 'story') {\n            console.log('UPLOAD CONTEXT - Moments/Stories detected, skipping vendor details processing');\n            await performUpload();\n            return;\n        }\n        // Try to get vendor details from localStorage\n        try {\n            const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n            if (storedVendorDetails) {\n                const vendorDetails = JSON.parse(storedVendorDetails);\n                console.log('UPLOAD CONTEXT - Retrieved vendor details from localStorage for startUpload:', storedVendorDetails);\n                // Create a new detailFields object to hold all the vendor details\n                const updatedDetailFields = {\n                    ...state.detailFields\n                };\n                let completeVendorCount = 0;\n                // Process vendor details to create detail fields\n                if (Object.keys(vendorDetails).length > 0) {\n                    Object.entries(vendorDetails).forEach((param)=>{\n                        let [vendorType, details] = param;\n                        if (details && details.name && details.mobileNumber) {\n                            // Add to the updated detail fields\n                            updatedDetailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                            updatedDetailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                            console.log(\"UPLOAD CONTEXT - Added vendor \".concat(vendorType, \" with name: \").concat(details.name, \" and contact: \").concat(details.mobileNumber));\n                            completeVendorCount++;\n                            // Also set normalized versions\n                            const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                            if (normalizedType !== vendorType) {\n                                updatedDetailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                                updatedDetailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                                console.log(\"UPLOAD CONTEXT - Also added normalized vendor \".concat(normalizedType));\n                            }\n                        }\n                    });\n                    // Update the state with all vendor details at once\n                    setState((prevState)=>({\n                            ...prevState,\n                            detailFields: updatedDetailFields\n                        }));\n                    console.log(\"UPLOAD CONTEXT - Added \".concat(completeVendorCount, \" complete vendor details from localStorage to state\"));\n                    console.log('UPLOAD CONTEXT - Updated detail fields:', JSON.stringify(updatedDetailFields));\n                }\n            }\n        } catch (error) {\n            console.error('UPLOAD CONTEXT - Failed to retrieve vendor details from localStorage:', error);\n        }\n        // Check if we have a video_category for videos\n        if (state.mediaType === 'video') {\n            // Try to get video_category from localStorage if not in state\n            let videoCategory = state.detailFields.video_category;\n            if (!videoCategory) {\n                try {\n                    const storedVideoCategory = localStorage.getItem('wedzat_video_category');\n                    if (storedVideoCategory) {\n                        videoCategory = storedVideoCategory;\n                        console.log('UPLOAD CONTEXT - Retrieved video_category from localStorage:', videoCategory);\n                        setDetailField('video_category', videoCategory);\n                    }\n                } catch (error) {\n                    console.error('UPLOAD CONTEXT - Failed to retrieve video_category from localStorage:', error);\n                }\n            }\n            console.log(\"UPLOAD CONTEXT - Current video_category: \".concat(videoCategory || 'Not set'));\n            // If we don't have a video_category, use a default one\n            if (!videoCategory) {\n                console.log('UPLOAD CONTEXT - No video_category found, using default: my_wedding');\n                // Use startUploadWithCategory to ensure the video_category is properly set\n                return startUploadWithCategory(state.mediaSubtype, 'my_wedding');\n            } else {\n                // Use startUploadWithCategory to ensure the video_category is properly passed\n                console.log(\"UPLOAD CONTEXT - Using existing video_category: \".concat(videoCategory));\n                return startUploadWithCategory(state.mediaSubtype, videoCategory);\n            }\n        }\n        // For photos, just use the regular upload flow\n        const validation = validateForm();\n        if (!validation.isValid) {\n            console.log('Upload validation failed:', validation.error);\n            setState({\n                ...state,\n                error: validation.error,\n                step: 'error'\n            });\n            return;\n        }\n        if (!state.file) {\n            console.log('No file to upload');\n            return;\n        }\n        setState({\n            ...state,\n            isUploading: true,\n            progress: 0,\n            step: 'uploading',\n            error: undefined\n        });\n        try {\n            console.log('UPLOAD CONTEXT - Starting upload process...');\n            console.log('UPLOAD CONTEXT - Upload details:', {\n                file: state.file ? state.file.name : 'No file',\n                fileSize: state.file ? Math.round(state.file.size / (1024 * 1024) * 100) / 100 + ' MB' : 'N/A',\n                mediaType: state.mediaType,\n                mediaSubtype: state.mediaSubtype,\n                title: state.title,\n                videoCategory: state.detailFields.video_category || 'Not set'\n            });\n            // Use the upload service to handle the complete upload process\n            const result = await _services_api__WEBPACK_IMPORTED_MODULE_2__.uploadService.handleUpload(state.file, state.mediaType, state.mediaSubtype, state.title, state.description, state.tags, state.detailFields, state.duration, state.thumbnail, (progress)=>{\n                console.log(\"Upload progress: \".concat(progress, \"%\"));\n                setState((prevState)=>{\n                    // Only update if the new progress is greater\n                    if (progress > prevState.progress) {\n                        return {\n                            ...prevState,\n                            progress,\n                            // Change to processing state when we reach 80%\n                            step: progress >= 80 && progress < 100 ? 'processing' : prevState.step\n                        };\n                    }\n                    return prevState;\n                });\n            });\n            console.log('Upload completed successfully:', result);\n            // Upload complete\n            setState({\n                ...state,\n                isUploading: false,\n                progress: 100,\n                step: 'complete',\n                response: result\n            });\n        } catch (error) {\n            console.error('Upload failed:', error);\n            setState({\n                ...state,\n                isUploading: false,\n                progress: 0,\n                step: 'error',\n                error: error instanceof Error ? error.message : 'Upload failed. Please try again.'\n            });\n        }\n    };\n    const goToStep = (step)=>{\n        setState({\n            ...state,\n            step\n        });\n    };\n    // Set video duration\n    const setDuration = (duration)=>{\n        setState({\n            ...state,\n            duration\n        });\n        console.log(\"Duration set to \".concat(duration, \" seconds\"));\n    };\n    // Effect to initialize the upload context and listen for vendor details updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UploadProvider.useEffect\": ()=>{\n            // Check if we have a video_category in localStorage\n            try {\n                const storedVideoCategory = localStorage.getItem('wedzat_video_category');\n                if (storedVideoCategory) {\n                    console.log('UPLOAD CONTEXT - Retrieved video_category from localStorage:', storedVideoCategory);\n                    setDetailField('video_category', storedVideoCategory);\n                }\n            } catch (error) {\n                console.error('UPLOAD CONTEXT - Failed to retrieve video_category from localStorage:', error);\n            }\n            // Add event listener for vendor details updates from API service\n            const handleVendorDetailsUpdate = {\n                \"UploadProvider.useEffect.handleVendorDetailsUpdate\": (event)=>{\n                    if (event.detail) {\n                        console.log('UPLOAD CONTEXT - Received vendor-details-update event:', event.detail);\n                        // Process vendor details from event\n                        const vendorDetails = event.detail;\n                        const updatedDetailFields = {\n                            ...state.detailFields\n                        };\n                        let completeVendorCount = 0;\n                        Object.entries(vendorDetails).forEach({\n                            \"UploadProvider.useEffect.handleVendorDetailsUpdate\": (param)=>{\n                                let [vendorType, details] = param;\n                                if (details && details.name && details.mobileNumber) {\n                                    // Add to detailFields\n                                    updatedDetailFields[\"vendor_\".concat(vendorType, \"_name\")] = details.name;\n                                    updatedDetailFields[\"vendor_\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                                    console.log(\"UPLOAD CONTEXT - Event handler added vendor \".concat(vendorType, \" with name: \").concat(details.name, \" and contact: \").concat(details.mobileNumber));\n                                    completeVendorCount++;\n                                    // Also add normalized versions\n                                    const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                                    if (normalizedType !== vendorType) {\n                                        updatedDetailFields[\"vendor_\".concat(normalizedType, \"_name\")] = details.name;\n                                        updatedDetailFields[\"vendor_\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                                        console.log(\"UPLOAD CONTEXT - Event handler also added normalized vendor \".concat(normalizedType));\n                                    }\n                                }\n                            }\n                        }[\"UploadProvider.useEffect.handleVendorDetailsUpdate\"]);\n                        // Update the state with all vendor details at once\n                        setState({\n                            \"UploadProvider.useEffect.handleVendorDetailsUpdate\": (prevState)=>({\n                                    ...prevState,\n                                    detailFields: updatedDetailFields\n                                })\n                        }[\"UploadProvider.useEffect.handleVendorDetailsUpdate\"]);\n                        console.log(\"UPLOAD CONTEXT - Event handler added \".concat(completeVendorCount, \" complete vendor details to state\"));\n                        console.log('UPLOAD CONTEXT - Event handler updated detail fields:', JSON.stringify(updatedDetailFields));\n                    }\n                }\n            }[\"UploadProvider.useEffect.handleVendorDetailsUpdate\"];\n            // Add event listener\n            window.addEventListener('vendor-details-update', handleVendorDetailsUpdate);\n            // Remove event listener on cleanup\n            return ({\n                \"UploadProvider.useEffect\": ()=>{\n                    window.removeEventListener('vendor-details-update', handleVendorDetailsUpdate);\n                }\n            })[\"UploadProvider.useEffect\"];\n        }\n    }[\"UploadProvider.useEffect\"], []);\n    // Create the context value\n    const contextValue = {\n        state,\n        setFile,\n        setThumbnail,\n        setMediaType,\n        setMediaSubtype,\n        setCategory,\n        setTitle,\n        setDescription,\n        addTag,\n        removeTag,\n        setDetailField,\n        setPersonalDetails,\n        setVendorDetails,\n        setDuration,\n        resetUpload,\n        startUpload,\n        startUploadWithCategory,\n        validateForm,\n        goToStep\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UploadContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\WEDZAT_\\\\frontend\\\\contexts\\\\UploadContexts.tsx\",\n        lineNumber: 1197,\n        columnNumber: 5\n    }, undefined);\n};\n_s(UploadProvider, \"g9yWDQF6ixWa1r5sfsm7YAeGJG4=\");\n_c = UploadProvider;\n// Custom hook to use the context\nconst useUpload = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(UploadContext);\n    if (context === undefined) {\n        throw new Error('useUpload must be used within an UploadProvider');\n    }\n    return context;\n};\n_s1(useUpload, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"UploadProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/UploadContexts.tsx\n"));

/***/ })

});