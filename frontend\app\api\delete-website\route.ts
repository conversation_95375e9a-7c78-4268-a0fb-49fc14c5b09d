/**
 * Server-side API route to delete a website
 * This avoids CORS issues by making the request from the server
 */
export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const websiteId = searchParams.get('website_id');
    const token = request.headers.get('Authorization');
    
    if (!websiteId) {
      return new Response(JSON.stringify({ success: false, error: 'Website ID is required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    if (!token) {
      return new Response(JSON.stringify({ success: false, error: 'Authorization is required' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Make the request to the actual API
    const response = await fetch(
      `https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/delete-website?website_id=${websiteId}`,
      {
        method: 'DELETE',
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json'
        }
      }
    );
    
    // If that fails, try the POST approach
    if (!response.ok) {
      const postResponse = await fetch(
        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/website',
        {
          method: 'POST',
          headers: {
            'Authorization': token,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            action: 'delete',
            website_id: websiteId
          })
        }
      );
      
      if (postResponse.ok) {
        return new Response(JSON.stringify({ success: true }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      }
      
      // Return the original error if both approaches fail
      const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
      return new Response(JSON.stringify({ success: false, error: errorData }), {
        status: response.status,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Return success response
    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error in delete-website API route:', error);
    return new Response(JSON.stringify({ success: false, error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
