// components/login/login.tsx
import React, { useState, ChangeEvent, MouseEvent, useRef, useEffect } from "react";
import { FcGoogle } from "react-icons/fc";
import { FaApple, FaFacebookF } from "react-icons/fa";
import { SiGmail } from "react-icons/si";
import Image from "next/image";
import { useRouter } from 'next/navigation';
import { authService } from "../../services/api"; // Import the API service
import { Eye, EyeOff } from "lucide-react";
import { RecaptchaVerifier, PhoneAuthProvider, signInWithPhoneNumber, signInWithCredential } from 'firebase/auth';
import { auth } from "../../lib/firebase"; // Import the Firebase auth instance

type SocialProvider = "Google" | "Apple" | "Facebook" | "Gmail";

interface LoginProps {
  onOtpRequest?: (mobileNumber: string) => void;
  onPasswordCreation?: (mobileNumber: string, password: string) => void;
  onSocialLogin?: (provider: SocialProvider) => void;
  onLogin?: () => void;
  onVendorSignup?: () => void;
}

// Registration steps for mobile flow
type MobileRegistrationStep = 'enterMobile' | 'verifyOtp' | 'createPassword' | 'login';

const Login: React.FC<LoginProps> = ({
  onOtpRequest,
  onPasswordCreation,
  onSocialLogin,
  onLogin,
  onVendorSignup,
}) => {
  const router = useRouter();
  const [mobileNumber, setMobileNumber] = useState<string>("");
  const [password, setPassword] = useState<string>("");
  const [confirmPassword, setConfirmPassword] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>("");
  const [registrationStep, setRegistrationStep] = useState<MobileRegistrationStep>('enterMobile');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [debugInfo, setDebugInfo] = useState<string>("");
  const [otpVerified, setOtpVerified] = useState<boolean>(false);
  const [otp, setOtp] = useState<string>("");
  const [timeLeft, setTimeLeft] = useState<number>(0);
  
  // Firebase related states
  const [verificationId, setVerificationId] = useState<string>("");
  const recaptchaVerifierRef = useRef<RecaptchaVerifier | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Clear timer on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);
  
  // Setup reCAPTCHA when component mounts
  useEffect(() => {
    // Initialize reCAPTCHA when component mounts
    const setupRecaptcha = () => {
      if (!recaptchaVerifierRef.current) {
        try {
          // Remove any existing reCAPTCHA if present
          const existingRecaptcha = document.querySelector(`#recaptcha-container div`);
          if (existingRecaptcha) {
            existingRecaptcha.remove();
          }
          
          recaptchaVerifierRef.current = new RecaptchaVerifier(auth, 'recaptcha-container', {
            size: 'invisible',
            callback: () => {
              console.log('reCAPTCHA solved');
            }
          });
        } catch (error) {
          console.error('Error setting up reCAPTCHA:', error);
        }
      }
    };
    
    setupRecaptcha();
    
    // Cleanup function
    return () => {
      if (recaptchaVerifierRef.current) {
        try {
          // @ts-ignore - Firebase doesn't expose clear() in types but it exists
          recaptchaVerifierRef.current.clear();
          recaptchaVerifierRef.current = null;
        } catch (error) {
          console.error('Error clearing reCAPTCHA:', error);
        }
      }
    };
  }, []);

  const startTimer = () => {
    setTimeLeft(30); // 30 seconds countdown
    
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    
    timerRef.current = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          if (timerRef.current) clearInterval(timerRef.current);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };
  
  const formatPhoneNumber = (phone: string): string => {
    // Format the phone number to E.164 format if not already formatted
    let formattedNumber = phone;
    if (!phone.startsWith('+')) {
      // Default to India country code if not specified
      formattedNumber = `+91${phone}`;
    }
    return formattedNumber;
  };

  const handleMobileNumberChange = (e: ChangeEvent<HTMLInputElement>): void => {
    setMobileNumber(e.target.value);
    // Clear any previous errors
    if (error) setError("");
  };

  const handlePasswordChange = (e: ChangeEvent<HTMLInputElement>): void => {
    setPassword(e.target.value);
    if (error) setError("");
  };

  const handleConfirmPasswordChange = (e: ChangeEvent<HTMLInputElement>): void => {
    setConfirmPassword(e.target.value);
    if (error) setError("");
  };
  
  const handleOtpChange = (e: ChangeEvent<HTMLInputElement>): void => {
    setOtp(e.target.value);
    if (error) setError("");
  };

  const handleGetOTP = async (e: MouseEvent<HTMLButtonElement>): Promise<void> => {
    e.preventDefault();
    
    // Validate mobile number
    if (mobileNumber.trim().length < 10) {
      setError("Please enter a valid mobile number");
      return;
    }
    
    setIsLoading(true);
    setError("");
    setDebugInfo("Sending OTP to your mobile number...");
    
    try {
      // Format the phone number properly
      const formattedNumber = formatPhoneNumber(mobileNumber);
      
      // Make sure reCAPTCHA is initialized
      if (!recaptchaVerifierRef.current) {
        throw new Error("reCAPTCHA not initialized. Please refresh and try again.");
      }
      
      // Send verification code via Firebase
      const confirmationResult = await signInWithPhoneNumber(
        auth, 
        formattedNumber, 
        recaptchaVerifierRef.current
      );
      
      // Store the verification ID to verify the code later
      setVerificationId(confirmationResult.verificationId);
      
      // Update UI state
      setDebugInfo(`OTP sent to ${formattedNumber}. Please check your messages.`);
      setRegistrationStep('verifyOtp');
      
      // Start countdown for resend
      startTimer();
      
      // For development mode - in real Firebase we can't see the OTP
      if (process.env.NODE_ENV === 'development') {
        setDebugInfo(`OTP sent to ${formattedNumber}. For dev testing, enter any 6 digits. The actual OTP will be used when Firebase verifies it.`);
      }
    } catch (error: any) {
      console.error("Error sending verification code:", error);
      
      // Handle specific Firebase errors
      if (error.code === 'auth/invalid-phone-number') {
        setError("Invalid phone number format. Please use a valid number.");
      } else if (error.code === 'auth/too-many-requests') {
        setError("Too many requests. Please try again later.");
      } else if (error.code === 'auth/captcha-check-failed') {
        setError("reCAPTCHA verification failed. Please refresh and try again.");
        // Reset reCAPTCHA
        try {
          // @ts-ignore - Firebase doesn't expose clear() in types but it exists
          recaptchaVerifierRef.current?.clear();
          recaptchaVerifierRef.current = null;
          
          // Re-initialize reCAPTCHA
          recaptchaVerifierRef.current = new RecaptchaVerifier(auth, 'recaptcha-container', {
            size: 'invisible'
          });
        } catch (e) {
          console.error('Error resetting reCAPTCHA:', e);
        }
      } else {
        setError("Failed to send verification code. Please try again.");
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyOtp = async (e: MouseEvent<HTMLButtonElement>): Promise<void> => {
    e.preventDefault();
    
    if (!otp || otp.length < 6) {
      setError("Please enter a valid 6-digit verification code");
      return;
    }
    
    setIsLoading(true);
    setError("");
    
    try {
      // Create a credential with the verification ID and OTP
      const credential = PhoneAuthProvider.credential(verificationId, otp);
      
      // Sign in with the credential
      await signInWithCredential(auth, credential);
      
      // Verify complete, notify server
      const response = await fetch('/api/verify-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firebaseVerified: true,
          verifiedPhone: formatPhoneNumber(mobileNumber),
          otp: 'firebase-verified' // Placeholder, not actually used
        }),
      });
      
      const data = await response.json();
      
      if (response.ok && data.success) {
        // OTP verification successful, proceed to password creation
        setOtpVerified(true);
        setRegistrationStep('createPassword');
        setDebugInfo("Phone number verified successfully. Please create a password.");
        
        // Store the verified phone number for later use
        sessionStorage.setItem('verifiedMobile', mobileNumber);
      } else {
        setError(data.message || "Server verification failed. Please try again.");
      }
    } catch (error: any) {
      console.error("Error verifying OTP:", error);
      
      // Handle specific Firebase errors
      if (error.code === 'auth/invalid-verification-code') {
        setError("Invalid verification code. Please check and try again.");
      } else if (error.code === 'auth/code-expired') {
        setError("Verification code has expired. Please request a new one.");
      } else {
        setError("Failed to verify code. Please try again.");
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOtp = async (): Promise<void> => {
    if (timeLeft > 0) return;
    
    setIsLoading(true);
    setError("");
    
    try {
      // Reset reCAPTCHA
      try {
        // @ts-ignore - Firebase doesn't expose clear() in types but it exists
        recaptchaVerifierRef.current?.clear();
        recaptchaVerifierRef.current = null;
      } catch (e) {
        console.error('Error clearing previous reCAPTCHA:', e);
      }
      
      // Re-initialize reCAPTCHA
      recaptchaVerifierRef.current = new RecaptchaVerifier(auth, 'recaptcha-container', {
        size: 'invisible'
      });
      
      // Format the phone number properly
      const formattedNumber = formatPhoneNumber(mobileNumber);
      
      // Resend verification code via Firebase
      const confirmationResult = await signInWithPhoneNumber(
        auth, 
        formattedNumber, 
        recaptchaVerifierRef.current
      );
      
      // Update verification ID
      setVerificationId(confirmationResult.verificationId);
      
      // Start countdown for resend
      startTimer();
      
      setDebugInfo(`OTP resent to ${formattedNumber}. Please check your messages.`);
    } catch (error: any) {
      console.error("Error resending OTP:", error);
      setError("Failed to resend verification code. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreatePassword = async (e: MouseEvent<HTMLButtonElement>): Promise<void> => {
    e.preventDefault();
    
    // Validate password
    if (!password) {
      setError("Please enter a password");
      return;
    }
    
    if (password.length < 6) {
      setError("Password must be at least 6 characters long");
      return;
    }
    
    if (password !== confirmPassword) {
      setError("Passwords do not match");
      return;
    }
    
    setIsLoading(true);
    setDebugInfo("Creating your password...");
    
    try {
      // Store password in session storage for registration
      sessionStorage.setItem('userPassword', password);
      
      // Call the password creation handler if provided
      if (onPasswordCreation) {
        onPasswordCreation(mobileNumber, password);
      } else {
        // If no handler provided, redirect to registration page
        setDebugInfo("Password created successfully. Redirecting to registration form...");
        
        // Add small delay before navigation
        setTimeout(() => {
          router.push('/register');
        }, 1000);
      }
      
      // Sign out of Firebase (since we're just using it for verification)
      await auth.signOut();
    } catch (error) {
      console.error("Error in password creation:", error);
      setError("An unexpected error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogin = async (e: MouseEvent<HTMLButtonElement>): Promise<void> => {
    e.preventDefault();
    
    // If we're not on the login step, handle the appropriate action for current step
    if (registrationStep === 'enterMobile') {
      return handleGetOTP(e);
    } else if (registrationStep === 'verifyOtp') {
      return handleVerifyOtp(e);
    } else if (registrationStep === 'createPassword') {
      return handleCreatePassword(e);
    }
    
    // Otherwise, proceed with login
    
    // Validate inputs
    if (mobileNumber.trim().length < 10) {
      setError("Please enter a valid mobile number");
      return;
    }
    
    if (!password.trim()) {
      setError("Please enter your password");
      return;
    }
    
    setIsLoading(true);
    setError("");
    setDebugInfo("Logging in...");
    
    try {
      // Call the login API
      await authService.login({
        mobile_number: mobileNumber,
        password: password
      });
      
      setDebugInfo("Login successful. Checking profile...");
      
      // Check if profile is complete
      try {
        await authService.checkProfile();
        setDebugInfo("Profile check successful. Redirecting to home page...");
        // If successful, route to home page
        router.push('/home');
      } catch (profileError: any) {
        // If profile is incomplete, route to update profile page
        if (profileError.message?.includes('Mobile number is missing')) {
          setDebugInfo("Profile incomplete. Redirecting to profile update page...");
          router.push('/profile/update');
        } else {
          // For other errors, still go to home
          setDebugInfo("Redirecting to home page...");
          router.push('/home');
        }
      }
    } catch (error: any) {
      console.error("Login error:", error);
      setError(error.error || "Invalid credentials. Please try again.");
      setDebugInfo(`Login error: ${JSON.stringify(error)}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSocialLogin = (provider: SocialProvider): void => {
    // Call the onSocialLogin prop
    if (onSocialLogin) {
      onSocialLogin(provider);
    }
  };

  // Modified handleLoginClick to use the onLogin prop to navigate to user-login
  const handleLoginClick = (e: MouseEvent<HTMLAnchorElement>): void => {
    e.preventDefault();
    // Call the onLogin prop to navigate to user-login
    if (onLogin) {
      onLogin();
    }
  };

  const handleVendorSignupClick = (e: MouseEvent<HTMLAnchorElement>): void => {
    e.preventDefault();
    if (onVendorSignup) {
      onVendorSignup();
    }
  };

  const togglePasswordVisibility = (): void => {
    setShowPassword(!showPassword);
  };

  const toggleConfirmPasswordVisibility = (): void => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  const handleBackToPhoneInput = (): void => {
    setRegistrationStep('enterMobile');
    setOtp("");
    setError("");
  };

  return (
    <div className="flex justify-center items-center">
      <div className="w-full px-6 py-6 rounded-lg">
        {/* Logo */}
        <div className="flex justify-center mb-6">
          <div className="text-red-600">
            <Image
              src="/pics/logo.png"
              alt="Wedzat logo"
              width={40}
              height={40}
              className="object-cover"
            />
          </div>
        </div>

        {/* Heading */}
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold">
            Welcome to <span style={{ color: "#B31B1E" }}>Wedzat</span>
          </h1>
          <p className="text-sm mt-1">
            {registrationStep === 'enterMobile' && "Register With Mobile Number"}
            {registrationStep === 'verifyOtp' && "Verify Your Mobile Number"}
            {registrationStep === 'createPassword' && "Create Your Password"}
            {registrationStep === 'login' && "Login to Your Account"}
          </p>
        </div>

        {/* Form */}
        <div className="flex flex-col gap-2">
          <div className="mb-4">
            {registrationStep === 'enterMobile' && (
              <input
                type="tel"
                placeholder="Enter Mobile Number"
                className={`w-full flex-1 p-3 border ${error ? 'border-red-500' : 'border-gray-300'} rounded-lg bg-white flex justify-center`}
                value={mobileNumber}
                onChange={handleMobileNumberChange}
                disabled={isLoading}
              />
            )}
            
            {registrationStep === 'verifyOtp' && (
              <div className="flex flex-col space-y-4">
                <input
                  type="text"
                  placeholder="Enter 6-digit verification code"
                  className={`w-full flex-1 p-3 border ${error ? 'border-red-500' : 'border-gray-300'} rounded-lg bg-white flex justify-center`}
                  value={otp}
                  onChange={handleOtpChange}
                  disabled={isLoading}
                  maxLength={6}
                />
                
                {/* Resend timer/button */}
                <div className="flex justify-between items-center">
                  <button
                    type="button"
                    className="text-blue-600 text-sm"
                    onClick={handleBackToPhoneInput}
                    disabled={isLoading}
                  >
                    Change Phone Number
                  </button>
                  
                  {timeLeft > 0 ? (
                    <p className="text-gray-500 text-sm">Resend in {timeLeft}s</p>
                  ) : (
                    <button
                      type="button"
                      className="text-blue-600 text-sm"
                      onClick={handleResendOtp}
                      disabled={isLoading}
                    >
                      Resend Code
                    </button>
                  )}
                </div>
              </div>
            )}
            
            {registrationStep === 'createPassword' && (
              <>
                <div className="relative mb-4">
                  <input
                    type={showPassword ? "text" : "password"}
                    placeholder="Create Password"
                    className={`w-full flex-1 p-3 border ${error ? 'border-red-500' : 'border-gray-300'} rounded-lg bg-white flex justify-center`}
                    value={password}
                    onChange={handlePasswordChange}
                    disabled={isLoading}
                  />
                  <button
                    type="button"
                    onClick={togglePasswordVisibility}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                  >
                    {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
                <div className="relative">
                  <input
                    type={showConfirmPassword ? "text" : "password"}
                    placeholder="Confirm Password"
                    className={`w-full flex-1 p-3 border ${error ? 'border-red-500' : 'border-gray-300'} rounded-lg bg-white flex justify-center`}
                    value={confirmPassword}
                    onChange={handleConfirmPasswordChange}
                    disabled={isLoading}
                  />
                  <button
                    type="button"
                    onClick={toggleConfirmPasswordVisibility}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                  >
                    {showConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  Password must be at least 6 characters long
                </p>
              </>
            )}
            
            {registrationStep === 'login' && (
              <>
                <input
                  type="tel"
                  placeholder="Enter Mobile Number"
                  className={`w-full flex-1 p-3 border ${error ? 'border-red-500' : 'border-gray-300'} rounded-lg bg-white flex justify-center mb-4`}
                  value={mobileNumber}
                  onChange={handleMobileNumberChange}
                  disabled={isLoading}
                />
                <div className="relative">
                  <input
                    type={showPassword ? "text" : "password"}
                    placeholder="Enter Password"
                    className={`w-full flex-1 p-3 border ${error ? 'border-red-500' : 'border-gray-300'} rounded-lg bg-white flex justify-center`}
                    value={password}
                    onChange={handlePasswordChange}
                    disabled={isLoading}
                  />
                  <button
                    type="button"
                    onClick={togglePasswordVisibility}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                  >
                    {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
              </>
            )}
            
            {error && (
              <p className="text-red-500 text-sm mt-1">{error}</p>
            )}
            
            {debugInfo && process.env.NODE_ENV === 'development' && (
              <div className="bg-gray-100 p-2 mt-2 rounded-md">
                <p className="text-gray-700 text-xs">{debugInfo}</p>
              </div>
            )}
          </div>

          {/* Button - changes based on registration step */}
          <button
            className="w-full bg-red-700 text-white py-3 rounded-md mb-4 hover:bg-red-800 transition duration-200 disabled:bg-red-300"
            onClick={
              registrationStep === 'enterMobile' 
                ? handleGetOTP 
                : registrationStep === 'verifyOtp'
                  ? handleVerifyOtp
                  : registrationStep === 'createPassword'
                    ? handleCreatePassword
                    : handleLogin
            }
            disabled={isLoading}
          >
            {isLoading 
              ? "Please wait..." 
              : (registrationStep === 'enterMobile' 
                  ? "Continue" 
                  : registrationStep === 'verifyOtp'
                    ? "Verify Code"
                    : registrationStep === 'createPassword'
                      ? "Create Password"
                      : "Login")}
          </button>

          {/* Divider */}
          <div className="flex items-center my-2">
            <div className="flex-grow border-t border-gray-300"></div>
            <span className="mx-4 text-gray-500 text-sm">or continue with</span>
            <div className="flex-grow border-t border-gray-300"></div>
          </div>

          {/* Social Login Options */}
          <div className="flex justify-between gap-4 mb-6">
            <button
              onClick={() => handleSocialLogin("Google")}
              className="w-12 h-12 border border-gray-300 rounded-lg bg-white flex items-center justify-center"
              disabled={isLoading}
            >
              <FcGoogle size={20} />
            </button>
            <button
              onClick={() => handleSocialLogin("Apple")}
              className="w-12 h-12 border border-gray-300 rounded-lg bg-white flex items-center justify-center"
              disabled={isLoading}
            >
              <FaApple size={20} />
            </button>
            <button
              onClick={() => handleSocialLogin("Facebook")}
              className="w-12 h-12 border border-gray-300 rounded-lg bg-white flex items-center justify-center"
              disabled={isLoading}
            >
              <FaFacebookF size={20} color="#1877F2" />
            </button>
            <button
              onClick={() => handleSocialLogin("Gmail")}
              className="w-12 h-12 border border-gray-300 rounded-lg bg-white flex items-center justify-center"
              disabled={isLoading}
            >
              <SiGmail size={20} color="#EA4335" />
            </button>
          </div>
        </div>

        {/* Footer Links */}
        <div className="text-center p-2">
          <div className="rounded-lg mb-2 mt-4">
            <span className="text-gray-700 text-sm">Already A member - </span>
            <a
              href="#"
              className="text-[#6A39A4] text-sm font-bold"
              onClick={handleLoginClick}
            >
              Log in
            </a>
          </div>
          <div className="rounded-lg">
            <span className="text-gray-700 text-sm">Are you a Vendor - </span>
            <a
              href="#"
              className="text-blue-600 text-sm font-bold"
              onClick={handleVendorSignupClick}
            >
              Create Account
            </a>
          </div>
        </div>
      </div>
      
      {/* Hidden reCAPTCHA container */}
      <div id="recaptcha-container" className="invisible"></div>
    </div>
  );
};

export default Login;