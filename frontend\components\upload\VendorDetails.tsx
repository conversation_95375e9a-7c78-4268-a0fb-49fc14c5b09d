// components/upload/VendorDetails.tsx
'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { X, Plus, Info } from 'lucide-react';

interface VendorDetail {
  name: string;
  mobileNumber: string;
}

interface VendorDetailsProps {
  onNext: (vendorDetails: Record<string, VendorDetail>) => void;
  onBack: () => void;
  onClose: () => void;
  initialVendorDetails?: Record<string, VendorDetail>; // Initial vendor details for persistence between screens
  videoCategory?: string; // Backend video category (my_wedding or wedding_vlog)
}

const VendorDetails: React.FC<VendorDetailsProps> = ({
  onNext,
  onBack,
  onClose,
  initialVendorDetails,
  videoCategory = 'my_wedding' // Default to my_wedding if not provided
}) => {
  // Create default vendor details
  const defaultVendorDetails = {
    venue: { name: '', mobileNumber: '' },
    photographer: { name: '', mobileNumber: '' },
    makeupArtist: { name: '', mobileNumber: '' },
    decorations: { name: '', mobileNumber: '' },
    caterer: { name: '', mobileNumber: '' }
  };

  // Merge initialVendorDetails with default values to ensure all fields exist
  // Also handle mapping between frontend and backend field names
  const mergedVendorDetails = initialVendorDetails
    ? {
        venue: initialVendorDetails.venue || defaultVendorDetails.venue,
        photographer: initialVendorDetails.photographer || defaultVendorDetails.photographer,
        // Handle both makeupArtist and makeup_artist (backend name)
        makeupArtist: initialVendorDetails.makeupArtist || initialVendorDetails.makeup_artist || defaultVendorDetails.makeupArtist,
        // Handle both decorations and decoration (backend name)
        decorations: initialVendorDetails.decorations || initialVendorDetails.decoration || defaultVendorDetails.decorations,
        caterer: initialVendorDetails.caterer || defaultVendorDetails.caterer,
        ...Object.entries(initialVendorDetails)
          .filter(([key]) => !['venue', 'photographer', 'makeupArtist', 'makeup_artist', 'decorations', 'decoration', 'caterer'].includes(key))
          .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {})
      }
    : defaultVendorDetails;

  // Log the merged vendor details to help with debugging
  // console.log('Merged vendor details:', mergedVendorDetails);

  // Use the merged vendor details
  const [vendorDetails, setVendorDetails] = useState<Record<string, VendorDetail>>(mergedVendorDetails);

  // Log the initial vendor details for debugging
  React.useEffect(() => {
    console.log('VendorDetails component initialized with:', {
      initialVendorDetails,
      currentVendorDetails: vendorDetails,
      videoCategory: videoCategory
    });
  }, []);

  // Extract additional vendor types from initialVendorDetails
  const initialAdditionalVendors = initialVendorDetails
    ? Object.keys(initialVendorDetails).filter(
        key => !['venue', 'photographer', 'makeupArtist', 'decorations', 'caterer'].includes(key)
      )
    : [];

  const [additionalVendors, setAdditionalVendors] = useState<string[]>(initialAdditionalVendors);
  const [errors, setErrors] = useState<{ general?: string; [key: string]: string | undefined }>({});

  const handleInputChange = (
    vendorType: string,
    field: 'name' | 'mobileNumber',
    value: string
  ) => {
    // Clear error for this field when user types
    if (field === 'name' && errors[`${vendorType}_name`]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[`${vendorType}_name`];
        return newErrors;
      });
    } else if (field === 'mobileNumber' && errors[`${vendorType}_mobile`]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[`${vendorType}_mobile`];
        return newErrors;
      });
    }

    // Clear general error if we're filling in a field
    if (errors.general) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.general;
        return newErrors;
      });
    }

    setVendorDetails(prev => ({
      ...prev,
      [vendorType]: {
        ...prev[vendorType],
        [field]: value
      }
    }));
  };

  const addMoreVendor = () => {
    // Logic to add more vendor types if needed
    const newVendorType = `additionalVendor${additionalVendors.length + 1}`;
    setAdditionalVendors(prev => [...prev, newVendorType]);
    setVendorDetails(prev => ({
      ...prev,
      [newVendorType]: { name: '', mobileNumber: '' }
    }));
  };

  const validateVendorDetail = (_vendorType: string, detail: VendorDetail): string[] => {
    const fieldErrors: string[] = [];

    // Check if detail exists
    if (!detail) {
      fieldErrors.push('missing');
      return fieldErrors;
    }

    // Check if name exists and is not empty
    if (!detail.name || detail.name.trim() === '') {
      fieldErrors.push('name');
    }

    // Check if mobileNumber exists and is not empty
    if (!detail.mobileNumber || detail.mobileNumber.trim() === '') {
      fieldErrors.push('mobileNumber');
    } else if (!/^\d{10}$/.test(detail.mobileNumber.trim())) {
      fieldErrors.push('invalidMobileNumber');
    }

    return fieldErrors;
  };

  const handleSubmit = () => {
    // Clear previous errors
    setErrors({});

    // Determine required vendor count based on video category
    const requiredVendorCount = videoCategory === 'wedding_vlog' ? 1 : 4;

    // Validate if required number of vendor details are filled
    const filledVendors = Object.entries(vendorDetails).filter(
      ([_, detail]) => detail && detail.name && detail.mobileNumber &&
                      detail.name.trim() !== '' && detail.mobileNumber.trim() !== ''
    );

    // Collect validation errors
    const newErrors: { [key: string]: string | undefined } = {};

    // Check each vendor that has at least one field filled
    Object.entries(vendorDetails).forEach(([vendorType, detail]) => {
      // Skip if detail is undefined
      if (!detail) {
        console.warn(`Vendor detail for ${vendorType} is undefined`);
        return;
      }

      // Only validate if at least one field has been filled
      if ((detail.name && detail.name.trim() !== '') ||
          (detail.mobileNumber && detail.mobileNumber.trim() !== '')) {
        const fieldErrors = validateVendorDetail(vendorType, detail);

        if (fieldErrors.includes('missing')) {
          newErrors[`${vendorType}_name`] = 'Vendor details are missing';
          return;
        }

        if (fieldErrors.includes('name')) {
          newErrors[`${vendorType}_name`] = 'Vendor name is required';
        }

        if (fieldErrors.includes('mobileNumber')) {
          newErrors[`${vendorType}_mobile`] = 'Mobile number is required';
        } else if (fieldErrors.includes('invalidMobileNumber')) {
          newErrors[`${vendorType}_mobile`] = 'Please enter a valid 10-digit mobile number';
        }
      }
    });

    // Check if we have enough complete vendor details
    if (filledVendors.length < requiredVendorCount) {
      const categoryText = videoCategory === 'wedding_vlog' ? 'wedding vlog' : 'my wedding';
      newErrors.general = `At least ${requiredVendorCount} complete vendor detail${requiredVendorCount > 1 ? 's' : ''} (with both name and contact) ${requiredVendorCount > 1 ? 'are' : 'is'} required for ${categoryText} videos. You provided ${filledVendors.length}/${requiredVendorCount}.`;
    }

    // Set errors if any
    setErrors(newErrors);

    // Only proceed if we have enough complete vendor details and no errors
    if (filledVendors.length >= requiredVendorCount && Object.keys(newErrors).length === 0) {
      // Map our vendor details to the format expected by the backend
      const mappedVendorDetails: Record<string, VendorDetail> = {};

      // Count how many valid vendors we have
      let validVendorCount = 0;

      // Map the vendor types to the backend expected format
      // Only include vendors that have BOTH name AND mobile number
      if (vendorDetails.venue && vendorDetails.venue.name && vendorDetails.venue.mobileNumber) {
        mappedVendorDetails.venue = vendorDetails.venue;
        validVendorCount++;
        console.log('Added venue vendor');
      }

      if (vendorDetails.photographer && vendorDetails.photographer.name && vendorDetails.photographer.mobileNumber) {
        mappedVendorDetails.photographer = vendorDetails.photographer;
        validVendorCount++;
        console.log('Added photographer vendor');
      }

      if (vendorDetails.makeupArtist && vendorDetails.makeupArtist.name && vendorDetails.makeupArtist.mobileNumber) {
        // Add both frontend and backend field names for cross-browser compatibility
        mappedVendorDetails.makeupArtist = vendorDetails.makeupArtist;
        mappedVendorDetails.makeup_artist = vendorDetails.makeupArtist;
        validVendorCount++;
        console.log('Added makeup artist vendor');
      }

      if (vendorDetails.decorations && vendorDetails.decorations.name && vendorDetails.decorations.mobileNumber) {
        // Add both frontend and backend field names for cross-browser compatibility
        mappedVendorDetails.decorations = vendorDetails.decorations;
        mappedVendorDetails.decoration = vendorDetails.decorations;
        validVendorCount++;
        console.log('Added decorations vendor');
      }

      if (vendorDetails.caterer && vendorDetails.caterer.name && vendorDetails.caterer.mobileNumber) {
        mappedVendorDetails.caterer = vendorDetails.caterer;
        validVendorCount++;
        console.log('Added caterer vendor');
      }

      // Log the current valid vendor count
      // console.log(`Current valid vendor count before additional vendors: ${validVendorCount}`);
      // console.log(`Additional vendors to process: ${additionalVendors.length}`);

      // Debug all vendor details
      // console.log('All vendor details:', JSON.stringify(vendorDetails, null, 2));

      // Add any additional vendors - only if they have BOTH name AND mobile number
      // If we don't have enough predefined vendors, map additional vendors to the predefined types
      const emptyPredefinedTypes: string[] = [];
      if (validVendorCount < 4) {
        // Check which predefined types are empty
        if (!mappedVendorDetails.venue) emptyPredefinedTypes.push('venue');
        if (!mappedVendorDetails.photographer) emptyPredefinedTypes.push('photographer');
        // Check both frontend and backend field names
        if (!mappedVendorDetails.makeupArtist && !mappedVendorDetails.makeup_artist) {
          emptyPredefinedTypes.push('makeupArtist'); // Use frontend name for consistency
        }
        // Check both frontend and backend field names
        if (!mappedVendorDetails.decorations && !mappedVendorDetails.decoration) {
          emptyPredefinedTypes.push('decorations'); // Use frontend name for consistency
        }
        if (!mappedVendorDetails.caterer) emptyPredefinedTypes.push('caterer');

        console.log('Empty predefined types:', emptyPredefinedTypes);
      }

      // Collect valid additional vendors
      const validAdditionalVendors: {type: string, detail: VendorDetail}[] = [];
      additionalVendors.forEach((vendorType) => {
        if (vendorDetails[vendorType]?.name && vendorDetails[vendorType]?.mobileNumber) {
          validAdditionalVendors.push({
            type: vendorType,
            detail: vendorDetails[vendorType]
          });
          console.log(`Found valid additional vendor: ${vendorType}`);
        }
      });

      // If we need more vendors to reach 4, map additional vendors to predefined types
      if (validVendorCount < 4 && validAdditionalVendors.length > 0) {
        let additionalIndex = 0;
        for (const type of emptyPredefinedTypes) {
          if (additionalIndex < validAdditionalVendors.length) {
            mappedVendorDetails[type] = validAdditionalVendors[additionalIndex].detail;
            console.log(`Mapped additional vendor ${validAdditionalVendors[additionalIndex].type} to predefined type ${type}`);
            additionalIndex++;
            validVendorCount++;

            if (validVendorCount >= 4) break;
          }
        }
      }

      // If we still have additional vendors, add them with the additional prefix
      additionalVendors.forEach((vendorType, index) => {
        if (vendorDetails[vendorType]?.name && vendorDetails[vendorType]?.mobileNumber) {
          // Check if this vendor was already mapped to a predefined type
          let alreadyMapped = false;
          for (const type of emptyPredefinedTypes) {
            if (mappedVendorDetails[type] === vendorDetails[vendorType]) {
              alreadyMapped = true;
              break;
            }
          }

          // If not already mapped, add it as an additional vendor
          if (!alreadyMapped) {
            mappedVendorDetails[`additional${index + 1}`] = vendorDetails[vendorType];
            console.log(`Adding additional vendor ${index + 1}:`, vendorDetails[vendorType]);
          }
        }
      });

      // Log the final vendor details being sent to the next step
      // console.log('VENDOR DETAILS - Final mapped vendor details being sent to next step:', JSON.stringify(mappedVendorDetails, null, 2));

      // Count how many complete vendor details we're sending
      const completeVendorCount = Object.entries(mappedVendorDetails).filter(([_, detail]) =>
        detail && detail.name && detail.mobileNumber &&
        detail.name.trim() !== '' && detail.mobileNumber.trim() !== ''
      ).length;

      console.log(`VENDOR DETAILS - Sending ${completeVendorCount} complete vendor details`);
      console.log('VENDOR DETAILS - Final mapped vendor details:', JSON.stringify(mappedVendorDetails, null, 2));

      // Add a small delay before proceeding to ensure state updates properly in Edge
      setTimeout(() => {
        // Double-check that we have enough complete vendor details
        const requiredCount = videoCategory === 'wedding_vlog' ? 1 : 4;
        if (completeVendorCount >= requiredCount) {
          console.log('VENDOR DETAILS - Proceeding with', completeVendorCount, 'vendor details');
          onNext(mappedVendorDetails);
        } else {
          console.error('VENDOR DETAILS - Not enough complete vendor details:', completeVendorCount);
          const categoryText = videoCategory === 'wedding_vlog' ? 'wedding vlog' : 'my wedding';
          alert(`Please provide at least ${requiredCount} complete vendor detail${requiredCount > 1 ? 's' : ''} (with both name and contact) for ${categoryText} videos`);
        }
      }, 100);
    }
  };

  // Count how many vendors have both name and mobile filled
  const filledVendorCount = Object.values(vendorDetails).filter(
    vendor => vendor && vendor.name && vendor.mobileNumber &&
              vendor.name.trim() !== '' && vendor.mobileNumber.trim() !== ''
  ).length;

  // Check if required number of vendors have both name and mobile filled
  const requiredVendorCount = videoCategory === 'wedding_vlog' ? 1 : 4;
  const isValid = filledVendorCount >= requiredVendorCount;

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 p-4">
      <div className="bg-[#FFF7E8] rounded-2xl w-full max-w-[50%] p-4 sm:p-6 relative overflow-y-auto max-h-[90vh] md:max-w-[50%] sm:max-w-[70%] xs:max-w-[90%]">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-800 hover:text-red-600"
        >
          <X size={24} />
        </button>

        {/* Header */}
        <div className="flex items-center mb-4">
          <div className="flex items-center mr-2">
            <Image
              src="/pics/logo.png"
              alt="Wedzat logo"
              width={32}
              height={32}
              className="object-cover"
            />
          </div>
          <h2 className="text-xl font-bold">Vendor Details</h2>
          <div className="ml-2 text-gray-500 cursor-help" title={`At least ${requiredVendorCount} complete vendor detail${requiredVendorCount > 1 ? 's' : ''} (with both name and contact) ${requiredVendorCount > 1 ? 'are' : 'is'} required for ${videoCategory === 'wedding_vlog' ? 'wedding vlog' : 'my wedding'} videos.`}>
            <Info size={16} />
          </div>
          <div className="ml-2">
            <Image
              src="/pics/umoments.png"
              alt="Moments"
              width={20}
              height={20}
              className="object-cover"
            />
          </div>
        </div>

        {/* Monetization note */}
        <div className="flex items-center mb-4">
          <div className="bg-gray-200 text-sm rounded-md px-3 py-1 inline-block">
            More vendor details, more monetization
          </div>
          <div className="ml-2 bg-blue-100 text-blue-800 px-2 py-1 rounded-md text-xs">
            {filledVendorCount}/{requiredVendorCount} complete
          </div>
        </div>

        {/* General error message */}
        {errors.general && (
          <div className="bg-red-100 text-red-800 p-3 rounded-md mb-4">
            {errors.general}
          </div>
        )}

        <div className="flex items-center mb-6">
          <Image
            src="/pics/store-front.png"
            alt="Store"
            width={24}
            height={24}
            className="object-cover mr-2"
          />
          <p className="text-base font-medium">
            {videoCategory === 'wedding_vlog'
              ? '1 Complete Vendor Detail Is Mandatory (Both Name and Contact)'
              : '4 Complete Vendor Details Are Mandatory (Both Name and Contact)'}
          </p>

          <div className="ml-auto">
            <button
              onClick={addMoreVendor}
              className="flex items-center bg-gray-200 rounded-full px-3 py-1 text-sm"
            >
              Add More
              <Plus size={16} className="ml-1" />
            </button>
          </div>
        </div>

        {/* Vendor form fields */}
        <div className="space-y-4">
          <div className="grid grid-cols-5 gap-4 items-center">
            <div className="text-sm font-medium">Venue</div>
            <div className="col-span-2">
              <input
                type="text"
                placeholder="Name (required)"
                value={vendorDetails.venue.name}
                onChange={(e) => handleInputChange('venue', 'name', e.target.value)}
                className={`w-full p-3 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors.venue_name ? 'border-red-500' : 'border-gray-300'}`}
              />
              {errors.venue_name && (
                <p className="text-red-500 text-xs mt-1">{errors.venue_name}</p>
              )}
            </div>
            <div className="col-span-2">
              <input
                type="text"
                placeholder="Mobile Number (required)"
                value={vendorDetails.venue.mobileNumber}
                onChange={(e) => handleInputChange('venue', 'mobileNumber', e.target.value)}
                className={`w-full p-3 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors.venue_mobile ? 'border-red-500' : 'border-gray-300'}`}
              />
              {errors.venue_mobile && (
                <p className="text-red-500 text-xs mt-1">{errors.venue_mobile}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-5 gap-4 items-center">
            <div className="text-sm font-medium">Photograph</div>
            <div className="col-span-2">
              <input
                type="text"
                placeholder="Name (required)"
                value={vendorDetails.photographer.name}
                onChange={(e) => handleInputChange('photographer', 'name', e.target.value)}
                className={`w-full p-3 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors.photographer_name ? 'border-red-500' : 'border-gray-300'}`}
              />
              {errors.photographer_name && (
                <p className="text-red-500 text-xs mt-1">{errors.photographer_name}</p>
              )}
            </div>
            <div className="col-span-2">
              <input
                type="text"
                placeholder="Mobile Number (required)"
                value={vendorDetails.photographer.mobileNumber}
                onChange={(e) => handleInputChange('photographer', 'mobileNumber', e.target.value)}
                className={`w-full p-3 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors.photographer_mobile ? 'border-red-500' : 'border-gray-300'}`}
              />
              {errors.photographer_mobile && (
                <p className="text-red-500 text-xs mt-1">{errors.photographer_mobile}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-5 gap-4 items-center">
            <div className="text-sm font-medium">Make up Artist</div>
            <div className="col-span-2">
              <input
                type="text"
                placeholder="Name (required)"
                value={vendorDetails.makeupArtist.name}
                onChange={(e) => handleInputChange('makeupArtist', 'name', e.target.value)}
                className={`w-full p-3 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors.makeupArtist_name ? 'border-red-500' : 'border-gray-300'}`}
              />
              {errors.makeupArtist_name && (
                <p className="text-red-500 text-xs mt-1">{errors.makeupArtist_name}</p>
              )}
            </div>
            <div className="col-span-2">
              <input
                type="text"
                placeholder="Mobile Number (required)"
                value={vendorDetails.makeupArtist.mobileNumber}
                onChange={(e) => handleInputChange('makeupArtist', 'mobileNumber', e.target.value)}
                className={`w-full p-3 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors.makeupArtist_mobile ? 'border-red-500' : 'border-gray-300'}`}
              />
              {errors.makeupArtist_mobile && (
                <p className="text-red-500 text-xs mt-1">{errors.makeupArtist_mobile}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-5 gap-4 items-center">
            <div className="text-sm font-medium">Decorations</div>
            <div className="col-span-2">
              <input
                type="text"
                placeholder="Name (required)"
                value={vendorDetails.decorations.name}
                onChange={(e) => handleInputChange('decorations', 'name', e.target.value)}
                className={`w-full p-3 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors.decorations_name ? 'border-red-500' : 'border-gray-300'}`}
              />
              {errors.decorations_name && (
                <p className="text-red-500 text-xs mt-1">{errors.decorations_name}</p>
              )}
            </div>
            <div className="col-span-2">
              <input
                type="text"
                placeholder="Mobile Number (required)"
                value={vendorDetails.decorations.mobileNumber}
                onChange={(e) => handleInputChange('decorations', 'mobileNumber', e.target.value)}
                className={`w-full p-3 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors.decorations_mobile ? 'border-red-500' : 'border-gray-300'}`}
              />
              {errors.decorations_mobile && (
                <p className="text-red-500 text-xs mt-1">{errors.decorations_mobile}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-5 gap-4 items-center">
            <div className="text-sm font-medium">Caterer</div>
            <div className="col-span-2">
              <input
                type="text"
                placeholder="Name (required)"
                value={vendorDetails.caterer.name}
                onChange={(e) => handleInputChange('caterer', 'name', e.target.value)}
                className={`w-full p-3 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors.caterer_name ? 'border-red-500' : 'border-gray-300'}`}
              />
              {errors.caterer_name && (
                <p className="text-red-500 text-xs mt-1">{errors.caterer_name}</p>
              )}
            </div>
            <div className="col-span-2">
              <input
                type="text"
                placeholder="Mobile Number (required)"
                value={vendorDetails.caterer.mobileNumber}
                onChange={(e) => handleInputChange('caterer', 'mobileNumber', e.target.value)}
                className={`w-full p-3 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors.caterer_mobile ? 'border-red-500' : 'border-gray-300'}`}
              />
              {errors.caterer_mobile && (
                <p className="text-red-500 text-xs mt-1">{errors.caterer_mobile}</p>
              )}
            </div>
          </div>

          {/* Additional vendor fields */}
          {additionalVendors.map((vendorType, index) => (
            <div key={vendorType} className="grid grid-cols-5 gap-4 items-center">
              <div className="text-sm font-medium">Additional {index + 1}</div>
              <div className="col-span-2">
                <input
                  type="text"
                  placeholder="Name (required)"
                  value={vendorDetails[vendorType]?.name || ''}
                  onChange={(e) => handleInputChange(vendorType, 'name', e.target.value)}
                  className={`w-full p-3 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors[`${vendorType}_name`] ? 'border-red-500' : 'border-gray-300'}`}
                />
                {errors[`${vendorType}_name`] && (
                  <p className="text-red-500 text-xs mt-1">{errors[`${vendorType}_name`]}</p>
                )}
              </div>
              <div className="col-span-2">
                <input
                  type="text"
                  placeholder="Mobile Number (required)"
                  value={vendorDetails[vendorType]?.mobileNumber || ''}
                  onChange={(e) => handleInputChange(vendorType, 'mobileNumber', e.target.value)}
                  className={`w-full p-3 border rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-red-500 ${errors[`${vendorType}_mobile`] ? 'border-red-500' : 'border-gray-300'}`}
                />
                {errors[`${vendorType}_mobile`] && (
                  <p className="text-red-500 text-xs mt-1">{errors[`${vendorType}_mobile`]}</p>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Navigation buttons */}
        <div className="flex justify-between mt-8">
          <button
            onClick={onBack}
            className="flex items-center justify-center px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-1"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z"
                clipRule="evenodd"
              />
            </svg>
            Back
          </button>

          <div className="flex flex-col items-end">
            {!isValid && (
              <div className="text-red-600 text-sm mb-2">
                Please complete at least {requiredVendorCount} vendor detail{requiredVendorCount > 1 ? 's' : ''} ({filledVendorCount}/{requiredVendorCount})
              </div>
            )}
            <button
              onClick={handleSubmit}
              disabled={!isValid}
              className={`flex items-center justify-center px-6 py-2 rounded-md ${
                isValid
                  ? 'bg-red-600 text-white hover:bg-red-700'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              } transition duration-200`}
            >
              Next
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 ml-1"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VendorDetails;