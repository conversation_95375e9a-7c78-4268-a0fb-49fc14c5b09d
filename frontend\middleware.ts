import { NextResponse } from "next/server";
import type { NextRequest } from 'next/server';

// Middleware function that supports JWT authentication
export default async function middleware(req: NextRequest) {
  // Get the pathname of the request
  const path = req.nextUrl.pathname;

  // Pages that require authentication
  const protectedPaths = ['/home', '/dashboard', '/profile', '/protected'];
  const isProtectedPath = protectedPaths.some(
    (protectedPath) => path === protectedPath || path.startsWith(`${protectedPath}/`)
  );

  // Debug logging
  console.log(`Path: ${path}, Protected: ${isProtectedPath}`);

  // Only check auth for protected paths
  if (isProtectedPath) {
    // Check for token in cookie or localStorage (via cookie)
    const cookieToken = req.cookies.get('token')?.value || req.cookies.get('jwt_token')?.value || req.cookies.get('wedzat_token')?.value;

    // Check for vendor flag in cookie
    const isVendor = req.cookies.get('is_vendor')?.value === 'true';

    // Check for token in authorization header
    const authHeader = req.headers.get('authorization');
    const headerToken = authHeader ? authHeader.replace('Bearer ', '') : null;

    // Log token presence for debugging
    console.log(`Auth check - Cookie token: ${cookieToken}, Header token: ${headerToken}, Is Vendor: ${isVendor}`);

    // If any token exists, allow access
    if (cookieToken || headerToken) {
      console.log('Authentication token found, allowing access');

      // If this is a vendor token, set the is_vendor cookie
      if (isVendor) {
        const response = NextResponse.next();
        response.cookies.set('is_vendor', 'true', { path: '/', maxAge: 86400 });
        return response;
      }

      return NextResponse.next();
    }

    // If we're in development mode, check for a special bypass parameter
    if (process.env.NODE_ENV === 'development') {
      const bypassAuth = req.nextUrl.searchParams.get('bypassAuth');
      if (bypassAuth === 'true') {
        console.log('Development bypass activated, allowing access');
        return NextResponse.next();
      }
    }

    // Check for special paths that should bypass auth
    if (path === '/auth/callback') {
      console.log('Callback page detected, allowing access');
      return NextResponse.next();
    }

    // No tokens found, redirect to login
    console.log('No authentication tokens found, redirecting to login');

    // Add flag to prevent potential redirect loops
    const url = new URL('/', req.url);
    if (!req.nextUrl.searchParams.has('auth_redirect')) {
      url.searchParams.set('auth_redirect', 'true');
    }

    return NextResponse.redirect(url);
  }

  // Continue normal processing for non-protected paths
  return NextResponse.next();
}

// Configure middleware to run on specific paths
export const config = {
  matcher: [
    // Exclude static files and Next.js internals
    '/((?!_next|_vercel|api|.*\\..*|favicon.ico).*)',
  ],
};