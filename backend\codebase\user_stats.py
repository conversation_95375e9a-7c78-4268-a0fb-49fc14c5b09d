import os
import json
from datetime import datetime, date
import jwt
import psycopg2
import psycopg2.extras
from dotenv import load_dotenv
# Load environment variables
load_dotenv()

# Database connection parameters
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_PORT = os.getenv('DB_PORT')
DB_NAME = os.getenv('DB_NAME')
REDIS_HOST = os.getenv('REDIS_HOST')
REDIS_PORT = "6379"

# JWT secret
JWT_SECRET = os.getenv('JWT_SECRET')

# Create a fresh connection for each function call
def get_db_connection():
    return psycopg2.connect(
        host=DB_HOST,
        user=DB_USER,
        password=DB_PASSWORD,
        port=DB_PORT,
        dbname=DB_NAME,
        cursor_factory=psycopg2.extras.DictCursor  # Return results as dictionaries
    )

import redis
import os
import json
import ssl

def get_redis_connection():
    host = os.environ.get('REDIS_HOST')
    port = int(os.environ.get('REDIS_PORT', 6379))

    return redis.Redis(
        host=host,
        port=port,
        ssl=True,  # Required for TLS
        ssl_cert_reqs=None,
        decode_responses=True
    )

# Custom JSON encoder to handle date objects
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, (date, datetime)):
            return obj.isoformat()
        return super().default(obj)

def validate_token(headers):
    """Validate the JWT token from the authorization header"""
    token = headers.get('Authorization')

    if not token:
        return None, {
            'statusCode': 401,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET, POST",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": "Token is required"})
        }

    try:
        if token.startswith('Bearer '):
            token = token[7:]  # Remove 'Bearer ' prefix
        else:
            # Fallback to old method if format is different
            token = token.split()[1]

        data = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
        return data['user_id'], None
    except jwt.ExpiredSignatureError:
        return None, {
            'statusCode': 401,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET, POST",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": "Token expired"})
        }
    except jwt.InvalidTokenError:
        return None, {
            'statusCode': 401,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET, POST",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": "Invalid token"})
        }

def cors_headers():
    return {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "OPTIONS, POST",
        "Access-Control-Allow-Headers": "Content-Type, Authorization"
    }

def success_response(message):
    return {
        'statusCode': 200,
        'headers': cors_headers(),
        'body': json.dumps({"message": message})
    }

def error_response(status, error_msg):
    return {
        'statusCode': status,
        'headers': cors_headers(),
        'body': json.dumps({"error": error_msg})
    }

def follow_user(event):
    conn, cursor = None, None
    try:
        user_id, error = validate_token(event['headers'])
        if error:
            return error

        data = json.loads(event['body'])
        target_user_id = data.get('target_user_id')

        if not target_user_id:
            return error_response(400, "Target user ID is required")
        if user_id == target_user_id:
            return error_response(400, "Cannot follow yourself")

        redis_client = get_redis_connection()
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT user_id FROM users WHERE user_id = %s", (target_user_id,))
        if not cursor.fetchone():
            return error_response(404, "Target user not found")

        cursor.execute("BEGIN")

        cursor.execute("""
            INSERT INTO follows (follower_id, followee_id, status)
            VALUES (%s, %s, TRUE)
            ON CONFLICT (follower_id, followee_id)
            DO UPDATE SET status = TRUE, updated_at = NOW()
        """, (user_id, target_user_id))

        redis_client.incr(f"user:{target_user_id}:followers")
        redis_client.incr(f"user:{user_id}:following")

        conn.commit()
        return success_response("Successfully followed user")

    except Exception as e:
        if conn:
            conn.rollback()
        return error_response(500, f"Internal server error: {str(e)}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def unfollow_user(event):
    conn, cursor = None, None
    try:
        user_id, error = validate_token(event['headers'])
        if error:
            return error

        data = json.loads(event['body'])
        target_user_id = data.get('target_user_id')

        if not target_user_id:
            return error_response(400, "Target user ID is required")

        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            UPDATE follows
            SET status = FALSE, updated_at = NOW()
            WHERE follower_id = %s AND followee_id = %s AND status = TRUE
        """, (user_id, target_user_id))


        if cursor.rowcount == 0:
            return error_response(400, "Not following this user")

        redis_client = get_redis_connection()
        redis_client.decr(f"user:{target_user_id}:followers")
        redis_client.decr(f"user:{user_id}:following")

        conn.commit()
        return success_response("Successfully unfollowed user")

    except Exception as e:
        if conn:
            conn.rollback()
        return error_response(500, f"Internal server error: {str(e)}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def get_user_follow_stats(event):
    conn = None
    cursor = None
    try:
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        query_params = event.get('queryStringParameters', {}) or {}
        target_user_id = query_params.get('user_id', user_id)

        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

        cursor.execute("SELECT user_id FROM users WHERE user_id = %s", (target_user_id,))
        if not cursor.fetchone():
            return {
                'statusCode': 404,
                'headers': cors_headers(),
                'body': json.dumps({"error": "User not found"})
            }

        # --- Redis: get follower/following counts ---
        follower_key = f"followers_count:{target_user_id}"
        following_key = f"following_count:{target_user_id}"
        valkey_client = get_redis_connection()
        follower_count = valkey_client.get(follower_key)
        following_count = valkey_client.get(following_key)

        if follower_count is None:
            cursor.execute("SELECT COUNT(*) FROM follows WHERE followee_id = %s AND status = TRUE", (target_user_id,))
            follower_count = cursor.fetchone()[0]
            valkey_client.set(follower_key, follower_count)

        else:
            follower_count = int(follower_count)

        if following_count is None:
            cursor.execute("SELECT COUNT(*) FROM follows WHERE follower_id = %s AND status = TRUE", (target_user_id,))
            following_count = cursor.fetchone()[0]
            valkey_client.set(following_key, following_count)

        else:
            following_count = int(following_count)

        # Get actual followers and following user_ids
        cursor.execute("SELECT follower_id FROM follows WHERE followee_id = %s AND status = TRUE", (target_user_id,))
        followers = [str(row['follower_id']) for row in cursor.fetchall()]

        cursor.execute("SELECT followee_id FROM follows WHERE follower_id = %s AND status = TRUE", (target_user_id,))
        following = [str(row['followee_id']) for row in cursor.fetchall()]

        # Get user data
        follower_users = []
        if followers:
            cursor.execute("SELECT user_id, name FROM users WHERE user_id IN %s", (tuple(followers),))
            follower_users = [{"user_id": str(row['user_id']), "name": row['name']} for row in cursor.fetchall()]

        following_users = []
        if following:
            cursor.execute("SELECT user_id, name FROM users WHERE user_id IN %s", (tuple(following),))
            following_users = [{"user_id": str(row['user_id']), "name": row['name']} for row in cursor.fetchall()]

        is_following = None
        if user_id != target_user_id:
            cursor.execute(
                "SELECT 1 FROM follows WHERE follower_id = %s AND followee_id = %s AND status = TRUE",
                (user_id, target_user_id)
            )
            is_following = cursor.fetchone() is not None

        return {
            'statusCode': 200,
            'headers': cors_headers(),
            'body': json.dumps({
                "user_id": target_user_id,
                "following_count": following_count,
                "followers_count": follower_count,
                "following": following_users,
                "followers": follower_users,
                "is_following": is_following
            }, cls=CustomJSONEncoder)
        }

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': cors_headers(),
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    finally:
        if cursor: cursor.close()
        if conn: conn.close()

def get_user_profile(event):
    conn = None
    cursor = None
    try:
        current_user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        query_params = event.get('queryStringParameters', {}) or {}
        target_user_id = query_params.get('user_id', current_user_id)

        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

        # Get user details
        cursor.execute("""
            SELECT user_id, name, email, mobile_number, dob, marital_status, place, user_type,
                   user_avatar, bio, user_short_audio, chapters_of_love, face_verified, face_image_url
            FROM users WHERE user_id = %s
        """, (target_user_id,))
        user = cursor.fetchone()

        if not user:
            return {
                'statusCode': 404,
                'headers': cors_headers(),
                'body': json.dumps({"error": "User not found"})
            }

        user_dict = dict(user)
        user_dict['chapters_of_love'] = json.loads(user_dict['chapters_of_love']) if user_dict['chapters_of_love'] else []

        # --- Redis for follower/following counts ---
        follower_key = f"followers_count:{target_user_id}"
        following_key = f"following_count:{target_user_id}"
        valkey_client = get_redis_connection()
        follower_count = valkey_client.get(follower_key)
        if follower_count is None:
            cursor.execute("SELECT COUNT(*) FROM follows WHERE followee_id = %s AND status = TRUE", (target_user_id,))
            follower_count = cursor.fetchone()[0]
            valkey_client.set(follower_key, follower_count)
        else:
            follower_count = int(follower_count)

        following_count = valkey_client.get(following_key)
        if following_count is None:
            cursor.execute("SELECT COUNT(*) FROM follows WHERE follower_id = %s AND status = TRUE", (target_user_id,))
            following_count = cursor.fetchone()[0]
            valkey_client.set(following_key, following_count)
        else:
            following_count = int(following_count)

        user_dict['followers_count'] = follower_count
        user_dict['following_count'] = following_count

        # Check if current user is following target user
        if current_user_id != target_user_id:
            cursor.execute("""
                SELECT 1 FROM follows
                WHERE follower_id = %s AND followee_id = %s AND status = TRUE
            """, (current_user_id, target_user_id))
            user_dict['is_following'] = cursor.fetchone() is not None
        else:
            user_dict['is_following'] = False

        # --- Post counts ---
        cursor.execute("""
            SELECT
                (SELECT COUNT(*) FROM videos WHERE user_id = %s AND video_subtype = 'flash') AS flash_count,
                (SELECT COUNT(*) FROM videos WHERE user_id = %s AND video_subtype = 'glimpse') AS glimpse_count,
                (SELECT COUNT(*) FROM videos WHERE user_id = %s AND video_subtype = 'movie') AS movie_count,
                (SELECT COUNT(*) FROM photos WHERE user_id = %s AND photo_subtype = 'post') AS photo_count
        """, (target_user_id, target_user_id, target_user_id, target_user_id))
        counts = cursor.fetchone()

        user_dict['flash_count'] = counts['flash_count'] or 0
        user_dict['glimpse_count'] = counts['glimpse_count'] or 0
        user_dict['movie_count'] = counts['movie_count'] or 0
        user_dict['photo_count'] = counts['photo_count'] or 0
        user_dict['posts_count'] = (
            user_dict['flash_count'] +
            user_dict['glimpse_count'] +
            user_dict['movie_count'] +
            user_dict['photo_count']
        )

        return {
            'statusCode': 200,
            'headers': cors_headers(),
            'body': json.dumps(user_dict, cls=CustomJSONEncoder)
        }

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': cors_headers(),
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    finally:
        if cursor: cursor.close()
        if conn: conn.close()


def update_user(event):
    conn = None
    cursor = None
    try:
        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()

        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        try:
            update_data = json.loads(event['body'])
            name = update_data.get('name')
            email = update_data.get('email')
            mobile_number = update_data.get('mobile_number')
            dob = update_data.get('dob')
            marital_status = update_data.get('marital_status')
            place = update_data.get('place')

            # Get new fields
            user_avatar = update_data.get('user_avatar')
            bio = update_data.get('bio')
            user_short_audio = update_data.get('user_short_audio')
            chapters_of_love = update_data.get('chapters_of_love')

            # Convert chapters_of_love to JSON string if it's provided
            if chapters_of_love is not None:
                chapters_of_love = json.dumps(chapters_of_love)

            email = email.lower() if email else None

            if not (name and email):
                return {
                    'statusCode': 400,
                    "headers": {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, POST",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({"error": "Name and Email are required"})
                }

            # Check for existing email or mobile number in other accounts
            if mobile_number:
                cursor.execute(
                    '''SELECT user_id FROM users WHERE (LOWER(email) = LOWER(%s) OR mobile_number = %s) AND user_id != %s''',
                    (email, mobile_number, user_id)
                )
            else:
                cursor.execute(
                    '''SELECT user_id FROM users WHERE LOWER(email) = LOWER(%s) AND user_id != %s''',
                    (email, user_id)
                )

            existing_user = cursor.fetchone()

            if existing_user:
                return {
                    'statusCode': 400,
                    "headers": {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, POST",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({"error": "Email or phone number already exists"})
                }

            # Update user details including new fields
            cursor.execute(
                '''UPDATE users
                   SET name = %s, email = %s, mobile_number = %s, dob = %s, marital_status = %s, place = %s,
                       user_avatar = COALESCE(%s, user_avatar),
                       bio = COALESCE(%s, bio),
                       user_short_audio = COALESCE(%s, user_short_audio),
                       chapters_of_love = COALESCE(%s, chapters_of_love)
                   WHERE user_id = %s''',
                (name, email, mobile_number, dob, marital_status, place,
                 user_avatar, bio, user_short_audio, chapters_of_love, user_id)
            )
            conn.commit()

            return {
                'statusCode': 200,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"message": "User updated successfully"})
            }

        except Exception as e:
            if conn:
                conn.rollback()
            return {
                'statusCode': 500,
                "headers": {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, POST",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
    except Exception as e:
        return {
            'statusCode': 500,
            "headers": {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, POST",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

