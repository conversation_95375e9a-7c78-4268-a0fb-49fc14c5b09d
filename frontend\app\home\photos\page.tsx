"use client";
import React, { useState, useEffect, useRef } from "react";
import { TopNavigation, SideNavigation, MobileNavigation } from "../../../components/HomeDashboard/Navigation";
import axios from "../../../services/axiosConfig";
import Image from "next/image";
import UserAvatar from "../../../components/HomeDashboard/UserAvatar";
import useSWR from 'swr';

// Define interface for photo items
interface Photo {
  photo_id: string;
  photo_name: string;
  photo_url: string;
  photo_description?: string;
  photo_tags?: string[];
  photo_subtype?: string;
  created_at: string;
  user_name?: string;
  is_own_content?: boolean;
  photo_views?: number;
  photo_likes?: number;
  photo_comments?: number;
}

interface ApiResponse {
  photos: Photo[];
  next_page: boolean;
  total_count: number;
  current_page: number;
}

// SWR fetcher function
const fetcher = async (url: string, token: string) => {
  const response = await axios.get<ApiResponse>(url, {
    headers: { Authorization: `Bearer ${token}` }
  });
  return response.data;
};

export default function PhotosPage() {
  const [photos, setPhotos] = useState<Photo[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [isClient, setIsClient] = useState(false);
  const [sidebarExpanded, setSidebarExpanded] = useState(false);
  // Removed right sidebar state

  useEffect(() => setIsClient(true), []);

  // Process image URL to handle different URL formats (same pattern as working components)
  const processImageUrl = (url: string): string => {
    if (!url) return '/pics/placeholder.svg';

    // If it's already a local URL starting with /, return as is
    if (url.startsWith('/')) return url;

    // For cloudfront URLs, ensure they're returned as is
    if (url.includes('cloudfront.net') || url.includes('amazonaws.com')) {
      return url;
    }

    // For other external URLs (https://, http://), return as is
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }

    // For relative paths like 'pics/image.png', add leading slash
    if (!url.startsWith('/')) {
      return `/${url}`;
    }

    return url;
  };

  // Get token from localStorage (client only)
  const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;

  // Use SWR for fetching photos
  const { data, error: swrError, isValidating } = useSWR(
    token ? [`/photos?page=${page}&limit=10`, token] : null,
    ([url, token]) => fetcher(url, token),
    {
      revalidateOnFocus: false,
      dedupingInterval: 2 * 60 * 1000, // 2 minutes
      keepPreviousData: true,
    }
  );

  useEffect(() => {
    setLoading(isValidating);
    setError(swrError ? 'Failed to load photos' : null);
    if (data && data.photos) {
      if (page === 1) {
        setPhotos(data.photos);
      } else {
        setPhotos(prev => [...prev, ...data.photos]);
      }
      setHasMore(data.next_page);
    }
  }, [data, swrError, isValidating, page]);

  // Reference for the last item in the list
  const lastPhotoRef = useRef<HTMLDivElement | null>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Setup Intersection Observer for lazy loading
  useEffect(() => {
    // Disconnect previous observer if it exists
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    // Don't observe if we're loading or there are no more items
    if (loading || !hasMore) return;

    // Create new observer
    const observer = new IntersectionObserver(
      (entries) => {
        // If the last item is visible and we're not already loading
        if (entries[0]?.isIntersecting && !loading && hasMore) {
          console.log('Last photo is visible, loading more...');
          // Add a small delay to make loading feel more natural
          setTimeout(() => {
            setPage(prevPage => prevPage + 1);
          }, 300);
        }
      },
      { threshold: 0.5, rootMargin: '0px 0px 200px 0px' } // Load when item is 50% visible or 200px before it comes into view
    );

    // Get the last item element
    const lastElement = lastPhotoRef.current;
    if (lastElement) {
      observer.observe(lastElement);
    }

    // Save observer to ref
    observerRef.current = observer;

    // Cleanup
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [loading, hasMore, photos.length]);

  return (
    <div className={isClient ? "flex flex-col min-h-screen bg-white w-full" : "opacity-0"}>
      {/* Top Navigation Bar */}
      <TopNavigation />

      <div className="flex flex-1 bg-white">
        <SideNavigation
          expanded={sidebarExpanded}
          onExpand={() => setSidebarExpanded(true)}
          onCollapse={() => setSidebarExpanded(false)}
        />

        {/* Main Content */}
        <main
          className={`flex-1 p-4 bg-white mt-20 ${sidebarExpanded ? "md:ml-48" : "md:ml-20"}`}
          style={{
            transition: "all 300ms ease-in-out",
            overflowY: "auto",
            overflowX: "hidden",
          }}
        >
          <div className="max-w-7xl mx-auto">
            <h1 className="text-2xl font-bold mb-6">Photos</h1>

            {/* Loading state for initial load */}
            {loading && page === 1 && (
              <div className="py-10 text-center flex items-center justify-center">
                <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                <span className="text-gray-600">Loading photos</span>
              </div>
            )}

            {/* Error state */}
            {error && !loading && (
              <div className="py-10 text-center text-red-500">{error}</div>
            )}

            {/* Content */}
            {photos.length > 0 && (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {photos.map((photo, index) => {
                  // Determine if this is the last item to observe
                  const isLastItem = index === photos.length - 1;

                  return (
                  <div
                    key={`${photo.photo_id}-${index}`}
                    // Apply ref to the last item for intersection observer
                    ref={isLastItem ? lastPhotoRef : null}
                    className="rounded-lg overflow-hidden shadow-md relative hover:scale-105 transition-transform cursor-pointer"
                    onClick={() => {
                      window.location.href = `/home/<USER>/${photo.photo_id}`;
                    }}
                  >
                    {/* User avatar */}
                    <div className="absolute top-2 left-2 z-10">
                      <UserAvatar
                        username={photo.user_name || "user"}
                        size="sm"
                        isGradientBorder={true}
                      />
                    </div>

                    {/* Photo */}
                    <div className="relative w-full h-48">
                      <Image
                        src={processImageUrl(photo.photo_url)}
                        alt={photo.photo_name || "Photo"}
                        fill
                        sizes="(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 20vw"
                        className="object-cover"
                        priority={index < 10} // Only prioritize the first ten images
                        unoptimized={true} // Always unoptimized for better compatibility
                        onError={(e) => {
                          console.error(`Failed to load photo: ${photo.photo_name}, URL: ${photo.photo_url}`);
                          // Use placeholder as fallback
                          const imgElement = e.target as HTMLImageElement;
                          if (imgElement) {
                            imgElement.src = '/pics/placeholder.svg';
                          }
                        }}
                      />
                    </div>

                    {/* Photo info */}
                    <div className="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/80 to-transparent text-white">
                      <div className="text-sm font-medium truncate">{photo.photo_name}</div>
                      <div className="text-xs flex justify-between">
                        <span>{photo.user_name}</span>
                        <span>
                          {photo.photo_likes ? `${photo.photo_likes} likes` : ''}
                        </span>
                      </div>
                    </div>
                  </div>
                  );
                })}
              </div>
            )}

            {/* Loading more indicator - subtle version */}
            {loading && page > 1 && (
              <div className="py-4 text-center flex items-center justify-center">
                <div className="inline-block w-5 h-5 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                <span className="text-gray-500 text-sm">Loading more</span>
              </div>
            )}

            {/* No more content indicator */}
            {!loading && !hasMore && photos.length > 0 && (
              <div className="py-6 text-center text-gray-500">No more photos to load</div>
            )}

            {/* No content state */}
            {!loading && photos.length === 0 && !error && (
              <div className="py-10 text-center text-gray-500">No photos available</div>
            )}
          </div>
        </main>

        {/* Right sidebar removed */}
      </div>

      <MobileNavigation />
    </div>
  );
}
