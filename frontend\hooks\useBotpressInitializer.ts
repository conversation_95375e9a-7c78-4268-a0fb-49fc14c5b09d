"use client";

import { useEffect, useState } from 'react';

/**
 * Custom hook to initialize Botpress webchat and provide a safe way to open it
 */
export function useBotpressInitializer() {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);

  // Function to safely open the Botpress webchat
  const openBotpressChat = () => {
    if (typeof window === 'undefined') return;

    // If Botpress is already initialized, use it directly
    if (window.botpressWebChat) {
      try {
        window.botpressWebChat.sendEvent({ type: 'show' });
        console.log('Botpress webchat opened successfully');
        return true;
      } catch (error) {
        console.error('Error opening Botpress webchat:', error);
        
        // Fallback: Try to click the webchat button
        try {
          const botpressButton = document.querySelector('.bp-widget-button');
          if (botpressButton) {
            (botpressButton as HTMLElement).click();
            console.log('Clicked Botpress button as fallback');
            return true;
          }
        } catch (fallbackError) {
          console.error('Error with fallback method:', fallbackError);
        }
      }
    }

    // If Botpress is not initialized, set a flag to open it when ready
    console.warn('Botpress webchat not initialized yet, will try to open when ready');
    window.openBotpressChatWhenReady = true;
    
    // Ensure scripts are loaded
    ensureBotpressScriptsLoaded();
    
    return false;
  };

  // Function to ensure Botpress scripts are loaded
  const ensureBotpressScriptsLoaded = () => {
    if (typeof window === 'undefined' || isInitializing || isInitialized) return;
    
    setIsInitializing(true);
    
    // Check if scripts are already loaded
    const injectScriptExists = document.querySelector('script[src="https://cdn.botpress.cloud/webchat/v2.3/inject.js"]');
    const configScriptExists = document.querySelector('script[src="https://files.bpcontent.cloud/2025/04/23/14/20250423141656-8AYDGFUF.js"]');
    
    if (!injectScriptExists) {
      const injectScript = document.createElement('script');
      injectScript.src = 'https://cdn.botpress.cloud/webchat/v2.3/inject.js';
      injectScript.async = true;
      injectScript.onload = () => {
        console.log('Botpress inject script loaded');
        
        // Load config script after inject script is loaded
        if (!configScriptExists) {
          const configScript = document.createElement('script');
          configScript.src = 'https://files.bpcontent.cloud/2025/04/23/14/20250423141656-8AYDGFUF.js';
          configScript.async = true;
          configScript.onload = () => {
            console.log('Botpress config script loaded');
            setIsInitialized(true);
            setIsInitializing(false);
            
            // If there's a pending request to open the chat, do it now
            setTimeout(() => {
              if (window.openBotpressChatWhenReady && window.botpressWebChat) {
                window.botpressWebChat.sendEvent({ type: 'show' });
                console.log('Botpress webchat opened after initialization');
                window.openBotpressChatWhenReady = false;
              }
            }, 1000);
          };
          document.head.appendChild(configScript);
        }
      };
      document.head.appendChild(injectScript);
    } else if (!configScriptExists) {
      // If inject script exists but config script doesn't
      const configScript = document.createElement('script');
      configScript.src = 'https://files.bpcontent.cloud/2025/04/23/14/20250423141656-8AYDGFUF.js';
      configScript.async = true;
      configScript.onload = () => {
        console.log('Botpress config script loaded');
        setIsInitialized(true);
        setIsInitializing(false);
        
        // If there's a pending request to open the chat, do it now
        setTimeout(() => {
          if (window.openBotpressChatWhenReady && window.botpressWebChat) {
            window.botpressWebChat.sendEvent({ type: 'show' });
            console.log('Botpress webchat opened after initialization');
            window.openBotpressChatWhenReady = false;
          }
        }, 1000);
      };
      document.head.appendChild(configScript);
    } else {
      // Both scripts already exist
      setIsInitialized(true);
      setIsInitializing(false);
    }
  };

  // Initialize Botpress on component mount
  useEffect(() => {
    // Define the global openBotpressChat function
    if (typeof window !== 'undefined') {
      window.openBotpressChat = openBotpressChat;
      
      // Ensure scripts are loaded
      ensureBotpressScriptsLoaded();
      
      // Set up a check to see if Botpress is initialized
      const checkInterval = setInterval(() => {
        if (window.botpressWebChat) {
          setIsInitialized(true);
          setIsInitializing(false);
          clearInterval(checkInterval);
        }
      }, 500);
      
      // Clear the interval after 10 seconds to avoid infinite checking
      setTimeout(() => clearInterval(checkInterval), 10000);
    }
    
    return () => {
      // Cleanup
    };
  }, []);

  return { isInitialized, openBotpressChat };
}

// Add type definitions for the global window object
declare global {
  interface Window {
    botpressWebChat?: {
      sendEvent: (event: { type: string }) => void;
    };
    openBotpressChat: () => boolean | void;
    openBotpressChatWhenReady?: boolean;
  }
}
